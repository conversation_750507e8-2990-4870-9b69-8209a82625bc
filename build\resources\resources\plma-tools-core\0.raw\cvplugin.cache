#Plugin properties
#Tue Jul 08 15:22:26 IST 2025
component.0.class=com.exalead.analysis.date.JodaSimpleDateFormaterDocProc
component.0.description=Date formatter document processor (use Java SimpleDateFormat syntax)
component.0.interface.0.class=com.exalead.analysis.date.JodaSimpleDateFormaterDocProc
component.0.interface.1.class=com.exalead.mercury.component.CVComponent
component.0.interface.2.class=com.exalead.pdoc.analysis.CustomDocumentProcessor
component.0.interface.3.class=com.exalead.pdoc.analysis.DocumentProcessor
component.0.interface.4.class=com.exalead.mercury.component.CVComponent
component.0.interface.5.class=java.lang.Object
component.0.label=JodaTime date formatter document processor
component.1.class=com.exalead.analysis.date.SimpleDateFormaterDocProc
component.1.description=Date formatter document processor (use Java SimpleDateFormat syntax)
component.1.interface.0.class=com.exalead.analysis.date.SimpleDateFormaterDocProc
component.1.interface.1.class=com.exalead.mercury.component.CVComponent
component.1.interface.2.class=com.exalead.pdoc.analysis.CustomDocumentProcessor
component.1.interface.3.class=com.exalead.pdoc.analysis.DocumentProcessor
component.1.interface.4.class=com.exalead.mercury.component.CVComponent
component.1.interface.5.class=java.lang.Object
component.1.label=Simple date formatter document processor
component.10.class=com.exalead.consobox.aggregation.NumericalAggregationTraverser
component.10.description=Traverse graph and calculates sum, avg, count of configured expressions
component.10.interface.0.class=com.exalead.consobox.aggregation.NumericalAggregationTraverser
component.10.interface.1.class=com.exalead.consobox.aggregation.TraverserAggregationProcessor
component.10.interface.2.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.10.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.10.interface.4.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.10.interface.5.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.10.interface.6.class=java.lang.Object
component.10.label=Numerical aggregation traverser
component.11.class=com.exalead.consobox.aggregation.PathToCategoriesAggProcessor
component.11.description=Generate classification metadata (with title if configured). This processor get graph nodes and generates metadata representing paths nodes ('node1_id/node2_id/node3_id...'), this hierarchical facet can be reverted (.../node3_id/node2_id/node1_id), for example, for assemblies representations.Category separator can be modified (only in XML configuration).
component.11.interface.0.class=com.exalead.consobox.aggregation.PathToCategoriesAggProcessor
component.11.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.11.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.11.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.11.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.11.interface.5.class=java.lang.Object
component.11.label=Classification aggregation processor
component.12.class=com.exalead.consobox.aggregation.RegexpToStringAggregationProcessor
component.12.description=Extract matching expression group and format another string (EX\: input \= '02/15/2016', pattern \= '([0-9]{2})/([0-9]{2})/([0-9]{4})', output format '%3$s-%1$s-%2$s', result is '2016-02-15'
component.12.interface.0.class=com.exalead.consobox.aggregation.RegexpToStringAggregationProcessor
component.12.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.12.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.12.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.12.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.12.interface.5.class=java.lang.Object
component.12.label=Patterns groups to formatted string
component.13.class=com.exalead.consobox.aggregation.ValueSelectorAggregationProcessor
component.13.description=Get first value from specified metadata names and create a new metadata
component.13.interface.0.class=com.exalead.consobox.aggregation.ValueSelectorAggregationProcessor
component.13.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.13.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.13.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.13.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.13.interface.5.class=java.lang.Object
component.13.label=Value selector  processor
component.14.class=com.exalead.consobox.transformation.AddArcTransformationProcessor
component.14.description=Add arcs to documents based on specified types and target URIs patterns (Ex\:'{meta1}_{meta2}')
component.14.interface.0.class=com.exalead.consobox.transformation.AddArcTransformationProcessor
component.14.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.14.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.14.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.14.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.14.interface.5.class=java.lang.Object
component.14.label=Add arc(s) transformation processor
component.15.class=com.exalead.consobox.transformation.CategoriesCleanTransformationProcessor
component.15.description=Clean categories metadata, replace '/' character by '_' in category ID, can also keep initial value as title
component.15.interface.0.class=com.exalead.consobox.transformation.CategoriesCleanTransformationProcessor
component.15.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.15.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.15.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.15.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.15.interface.5.class=java.lang.Object
component.15.label=Clean categories transformation processor
component.16.class=com.exalead.consobox.transformation.ConditionalTypeSetterTransformationProcessor
component.16.description=Set document type based on condition(s), uses JEXL library
component.16.interface.0.class=com.exalead.consobox.transformation.ConditionalTypeSetterTransformationProcessor
component.16.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.16.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.16.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.16.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.16.interface.5.class=java.lang.Object
component.16.label=Conditional type setter transformation processor
component.17.class=com.exalead.consobox.transformation.CreateMetasOrDirectivesTransformationProcessor
component.17.description=Add metadata or directives to the document, these values can be forged using document existing metadata or directives, uses JEXL library
component.17.interface.0.class=com.exalead.consobox.transformation.CreateMetasOrDirectivesTransformationProcessor
component.17.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.17.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.17.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.17.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.17.interface.5.class=java.lang.Object
component.17.label=Metadata or directive creation transformation processor
component.18.class=com.exalead.consobox.transformation.DeleteArcTransformationProcessor
component.18.description=Delete arcs to documents based on specified types and target URIs patterns (Ex\:'{meta1}_{meta2}')
component.18.interface.0.class=com.exalead.consobox.transformation.DeleteArcTransformationProcessor
component.18.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.18.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.18.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.18.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.18.interface.5.class=java.lang.Object
component.18.label=Delete arc(s) transformation processor
component.19.class=com.exalead.consobox.transformation.DeleteMetasTransformationProcessor
component.19.description=Delete metadata or directives from document
component.19.interface.0.class=com.exalead.consobox.transformation.DeleteMetasTransformationProcessor
component.19.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.19.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.19.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.19.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.19.interface.5.class=java.lang.Object
component.19.label=Delete metadata or directive creation transformation processor
component.2.class=com.exalead.analysis.date.WeekOfYearDocumentProcessor
component.2.description=Get week of year and add it to numerical value (Ex. '2015/12/30' --> '201553'). This processor uses Joda Time library.
component.2.interface.0.class=com.exalead.analysis.date.WeekOfYearDocumentProcessor
component.2.interface.1.class=com.exalead.mercury.component.CVComponent
component.2.interface.2.class=com.exalead.pdoc.analysis.CustomDocumentProcessor
component.2.interface.3.class=com.exalead.pdoc.analysis.DocumentProcessor
component.2.interface.4.class=com.exalead.mercury.component.CVComponent
component.2.interface.5.class=java.lang.Object
component.2.label=Week of year
component.20.class=com.exalead.consobox.transformation.DiscardTransformationProcessor
component.20.description=Discard document if it does not match condition
component.20.interface.0.class=com.exalead.consobox.transformation.DiscardTransformationProcessor
component.20.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.20.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.20.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.20.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.20.interface.5.class=java.lang.Object
component.20.label=Discard document transformation processor
component.21.class=com.exalead.consobox.transformation.DocTimestampTransformationProcessor
component.21.description=Create an image of document to keep all versions of indexed documents over time
component.21.interface.0.class=com.exalead.consobox.transformation.DocTimestampTransformationProcessor
component.21.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.21.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.21.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.21.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.21.interface.5.class=java.lang.Object
component.21.label=Document timestamp transformation processor
component.22.class=com.exalead.consobox.transformation.HierarchicalTypesSetter
component.22.description=Set document types based DataModel class hierarchy
component.22.interface.0.class=com.exalead.consobox.transformation.HierarchicalTypesSetter
component.22.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.22.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.22.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.22.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.22.interface.5.class=java.lang.Object
component.22.label=Hierarchical type setter transformation processor
component.23.class=com.exalead.consobox.transformation.MetaMappingsTransformationProcessor
component.23.description=Copy metadata from names to other metadata
component.23.interface.0.class=com.exalead.consobox.transformation.MetaMappingsTransformationProcessor
component.23.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.23.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.23.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.23.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.23.interface.5.class=java.lang.Object
component.23.label=Metadata mapping(s) transformation processor
component.24.class=com.exalead.consobox.transformation.RegexpToStringTransformationProcessor
component.24.description=Extract matching expression group and format another string (EX\: input \= '02/15/2016', pattern \= '([0-9]{2})/([0-9]{2})/([0-9]{4})', output format '%3$s-%1$s-%2$s', result is '2016-02-15'
component.24.interface.0.class=com.exalead.consobox.transformation.RegexpToStringTransformationProcessor
component.24.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.24.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.24.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.24.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.24.interface.5.class=java.lang.Object
component.24.label=Patterns groups to formatted string
component.25.class=com.exalead.consobox.transformation.ValueSelectorTransformationProcessor
component.25.description=Get first value from specified metadata names and create a new metadata
component.25.interface.0.class=com.exalead.consobox.transformation.ValueSelectorTransformationProcessor
component.25.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.25.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.25.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.25.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.25.interface.5.class=java.lang.Object
component.25.label=Value selector  processor
component.26.class=com.exalead.papifilter.TimestampPAPIFilter
component.26.description=Timestamp PAPI Filter (add time information in URI, day, week or year)
component.26.interface.0.class=com.exalead.papifilter.TimestampPAPIFilter
component.26.interface.1.class=com.exalead.mercury.component.CVComponent
component.26.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilter
component.26.interface.3.class=com.exalead.papi.helper.pipe.PipedPushAPI
component.26.interface.4.class=com.exalead.papi.helper.PushAPI
component.26.interface.5.class=java.lang.Object
component.26.label=Timestamp PAPI Filter
component.27.class=com.exalead.papifilter.TimestampPAPIFilterConfig
component.27.description=Timestamp PAPI Filter
component.27.interface.0.class=com.exalead.papifilter.TimestampPAPIFilterConfig
component.27.interface.1.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig$DefaultImpl
component.27.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig
component.27.interface.3.class=java.lang.Object
component.27.label=Timestamp PAPI Filter
component.28.class=com.exalead.tools.diagnostics.modules.AccessPagesComplexity
component.28.description=Check Access pages configuration complexity
component.28.interface.0.class=com.exalead.tools.diagnostics.modules.AccessPagesComplexity
component.28.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.28.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.28.interface.3.class=com.exalead.mercury.component.CVComponent
component.28.interface.4.class=java.lang.Object
component.28.label=Check Access pages configuration complexity
component.29.class=com.exalead.tools.diagnostics.modules.ConnectorsConfigModule
component.29.description=Check connectors operations configuration
component.29.interface.0.class=com.exalead.tools.diagnostics.modules.ConnectorsConfigModule
component.29.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.29.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.29.interface.3.class=com.exalead.mercury.component.CVComponent
component.29.interface.4.class=java.lang.Object
component.29.label=Check connectors operations configuration
component.3.class=com.exalead.consobox.aggregation.AggregateMetasProcessor
component.3.description=Add metadata fom matching path documents based on specified paths and meta neme patterns
component.3.interface.0.class=com.exalead.consobox.aggregation.AggregateMetasProcessor
component.3.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.3.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.3.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.3.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.3.interface.5.class=java.lang.Object
component.3.label=Metas aggregation processor
component.30.class=com.exalead.tools.diagnostics.modules.ConsolidationMatchingRulesModule
component.30.description=Check consolidation generated matching rules
component.30.interface.0.class=com.exalead.tools.diagnostics.modules.ConsolidationMatchingRulesModule
component.30.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.30.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.30.interface.3.class=com.exalead.mercury.component.CVComponent
component.30.interface.4.class=java.lang.Object
component.30.label=Check consolidation generated matching rules
component.31.class=com.exalead.tools.diagnostics.modules.ConsolidationMetasAggPathsModule
component.31.description=Check consolidation duplicated or non optimal paths
component.31.interface.0.class=com.exalead.tools.diagnostics.modules.ConsolidationMetasAggPathsModule
component.31.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.31.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.31.interface.3.class=com.exalead.mercury.component.CVComponent
component.31.interface.4.class=java.lang.Object
component.31.label=Check consolidation duplicated or non optimal paths
component.32.class=com.exalead.tools.diagnostics.modules.ConsolidationProcsConfigModule
component.32.description=Check consolidation document types options
component.32.interface.0.class=com.exalead.tools.diagnostics.modules.ConsolidationProcsConfigModule
component.32.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.32.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.32.interface.3.class=com.exalead.mercury.component.CVComponent
component.32.interface.4.class=java.lang.Object
component.32.label=Check consolidation document types options
component.33.class=com.exalead.tools.diagnostics.modules.ConsolidationTriggersModule
component.33.description=Check consolidation commit and aggregation triggers configuration
component.33.interface.0.class=com.exalead.tools.diagnostics.modules.ConsolidationTriggersModule
component.33.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.33.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.33.interface.3.class=com.exalead.mercury.component.CVComponent
component.33.interface.4.class=java.lang.Object
component.33.label=Check consolidation commit and aggregation triggers configuration
component.34.class=com.exalead.tools.diagnostics.modules.DataModelsCheckModule
component.34.description=Check DataModels properties configuration
component.34.interface.0.class=com.exalead.tools.diagnostics.modules.DataModelsCheckModule
component.34.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.34.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.34.interface.3.class=com.exalead.mercury.component.CVComponent
component.34.interface.4.class=java.lang.Object
component.34.label=Check DataModels properties configuration
component.35.class=com.exalead.tools.diagnostics.modules.DataModelsOptionsModule
component.35.description=Check DataModels options configuration
component.35.interface.0.class=com.exalead.tools.diagnostics.modules.DataModelsOptionsModule
component.35.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.35.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.35.interface.3.class=com.exalead.mercury.component.CVComponent
component.35.interface.4.class=java.lang.Object
component.35.label=Check DataModels options configuration
component.36.class=com.exalead.tools.diagnostics.modules.ERConnectorConfigModule
component.36.description=Check Enovia ER connector(s) configuration
component.36.interface.0.class=com.exalead.tools.diagnostics.modules.ERConnectorConfigModule
component.36.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.36.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.36.interface.3.class=com.exalead.mercury.component.CVComponent
component.36.interface.4.class=java.lang.Object
component.36.label=Check Enovia ER connector(s) configuration
component.37.class=com.exalead.tools.diagnostics.modules.IndexingConfigModule
component.37.description=Check indexing triggers configuration
component.37.interface.0.class=com.exalead.tools.diagnostics.modules.IndexingConfigModule
component.37.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.37.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.37.interface.3.class=com.exalead.mercury.component.CVComponent
component.37.interface.4.class=java.lang.Object
component.37.label=Check indexing triggers configuration
component.38.class=com.exalead.tools.diagnostics.modules.RamUsageModule
component.38.description=Check DataModels classes numerical fields RAM usage
component.38.interface.0.class=com.exalead.tools.diagnostics.modules.RamUsageModule
component.38.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.38.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.38.interface.3.class=com.exalead.mercury.component.CVComponent
component.38.interface.4.class=java.lang.Object
component.38.label=Check DataModels classes numerical fields RAM usage
component.39.class=com.exalead.tools.diagnostics.modules.ReportingConfigModule
component.39.description=Check reporting publishers configuration
component.39.interface.0.class=com.exalead.tools.diagnostics.modules.ReportingConfigModule
component.39.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.39.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.39.interface.3.class=com.exalead.mercury.component.CVComponent
component.39.interface.4.class=java.lang.Object
component.39.label=Check reporting publishers configuration
component.4.class=com.exalead.consobox.aggregation.CategoriesCleanAggregationProcessor
component.4.description=Clean categories metadata, replace '/' character by '_' in category ID, can also keep initial value as title
component.4.interface.0.class=com.exalead.consobox.aggregation.CategoriesCleanAggregationProcessor
component.4.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.4.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.4.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.4.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.4.interface.5.class=java.lang.Object
component.4.label=Clean categories aggregation processor
component.40.class=com.exalead.tools.diagnostics.modules.UnusedRamFields
component.40.description=Check RAM fields usage (including in Mashup applications)
component.40.interface.0.class=com.exalead.tools.diagnostics.modules.UnusedRamFields
component.40.interface.1.class=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule
component.40.interface.2.class=com.exalead.cloudview.diagnostic.CustomComponentValidator
component.40.interface.3.class=com.exalead.mercury.component.CVComponent
component.40.interface.4.class=java.lang.Object
component.40.label=Check RAM fields usage (including in Mashup applications)
component.41.class=com.exalead.apps.plma_tools.core.consobox.DeleteChildDocumentAggregationProcessor
component.41.description=When a document is deleted, its children documents are automatically deleted too, except if they have been created during the Aggregation phase.In that case, using this aggregation processor will ensure they are.
component.41.interface.0.class=com.exalead.apps.plma_tools.core.consobox.DeleteChildDocumentAggregationProcessor
component.41.interface.1.class=com.exalead.consobox.commons.CustomDeleteAggProcessor
component.41.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaDeleteAggregationProcessor
component.41.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.41.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.41.interface.5.class=java.lang.Object
component.42.class=com.exalead.apps.plma_tools.core.consobox.enovia.HistoryAggregationProcessor
component.42.description=Push different objects to indexing server (current value of an object, all versions of the object over the time and snapshots of object over the time with configurable granularity\: day, month, year...).Snapshots and transitions can be pushed or not depending on the configuration. Historization mechanism is based on timestamp meta value, if you want to use this aggregation processor, you need to set this metadata at indexing time (using a PAPI Filter or directly a metadata coming from source) and generate graph containing current node and historical nodes. Do do that, you can use 'Timestamp PAPI Filter'.Also creates meta sort_history_type_code has code for different history types i.e (current\: -1 ,snapshot\: 0,transition\: 1).
component.42.interface.0.class=com.exalead.apps.plma_tools.core.consobox.enovia.HistoryAggregationProcessor
component.42.interface.1.class=com.exalead.consobox.aggregation.HistoryAggregationProcessor
component.42.interface.2.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.42.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.42.interface.4.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.42.interface.5.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.42.interface.6.class=java.lang.Object
component.42.label=History aggregation processor
component.43.class=com.exalead.apps.plma_tools.core.consobox.enovia.HistoryTransformationProcessor
component.43.description=Parse 'history' metadata and generate one JSON with only relevant events, the main goal of this component is to avoid having huge attribute in consolidation document (one value per event and can contain megabytes of data), it has impact on document payload during aggregation.\n\nThis component allows to parse some events and keep only relevant ones (also configurable and extensible) and add a document attribute which contains JSON representation of these events.\nGenerated JSON can be used by HistoryAggregationProcessor\n\nOOTB event parsers are\:\n- state\: config can contain all events to keep in comma separated values\n- modify\: config can contain all metadata to keep in comma separated values\n- connect\: parse connection events
component.43.interface.0.class=com.exalead.apps.plma_tools.core.consobox.enovia.HistoryTransformationProcessor
component.43.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.43.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.43.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.43.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.43.interface.5.class=java.lang.Object
component.43.label=Parse history transformation processor
component.44.class=com.exalead.apps.plma_tools.core.consobox.HistoricalStatesAggregationProcessor
component.44.description=History Aggregation Processor based on state metas ('state_.*_start')
component.44.interface.0.class=com.exalead.apps.plma_tools.core.consobox.HistoricalStatesAggregationProcessor
component.44.interface.1.class=com.exalead.consobox.aggregation.HistoryAggregationProcessor
component.44.interface.2.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.44.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.44.interface.4.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.44.interface.5.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.44.interface.6.class=java.lang.Object
component.44.label=History Aggregation Processor based on state metas
component.45.class=com.exalead.apps.plma_tools.core.consobox.MetaValueMappingAggregationProcessor
component.45.description=Allows to transform values from a given meta and add them as another meta. The original meta is not modifed.
component.45.interface.0.class=com.exalead.apps.plma_tools.core.consobox.MetaValueMappingAggregationProcessor
component.45.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.45.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.45.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.45.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.45.interface.5.class=java.lang.Object
component.45.label=Meta values mapping
component.46.class=com.exalead.apps.plma_tools.core.consobox.NumericalAggregationProcessor
component.46.description=Aggregates Numerical values from the nodes connected to root node using the path specified. Stores the aggregated values in the given root node meta
component.46.interface.0.class=com.exalead.apps.plma_tools.core.consobox.NumericalAggregationProcessor
component.46.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.46.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.46.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.46.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.46.interface.5.class=java.lang.Object
component.46.label=Numerical Aggregation Processor
component.47.class=com.exalead.apps.plma_tools.core.consobox.resource.ResourceFTETransformationProcessor
component.47.description=Add document children according FTE values
component.47.interface.0.class=com.exalead.apps.plma_tools.core.consobox.resource.ResourceFTETransformationProcessor
component.47.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.47.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.47.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.47.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.47.interface.5.class=java.lang.Object
component.47.label=Add fte(s) data transformation processor
component.48.class=com.exalead.apps.plma_tools.core.consobox.SortMetaAggregationProcessor
component.48.description=Convert sorted field from categorical values to numerical values to manage specific sorting
component.48.interface.0.class=com.exalead.apps.plma_tools.core.consobox.SortMetaAggregationProcessor
component.48.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.48.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.48.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.48.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.48.interface.5.class=java.lang.Object
component.48.label=Sort Meta Aggregation Processor
component.49.class=com.exalead.apps.plma_tools.core.consobox.transformation.BizDaysTransformationProcessor
component.49.description=calculates business days and business seconds based on given xml configuration and input property
component.49.interface.0.class=com.exalead.apps.plma_tools.core.consobox.transformation.BizDaysTransformationProcessor
component.49.interface.1.class=com.exalead.consobox.commons.CustomTransformationProcessor
component.49.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.49.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaTransformationListener
component.49.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.49.interface.5.class=java.lang.Object
component.49.label=Calculation of working days/seconds for given date metas
component.5.class=com.exalead.consobox.aggregation.CreateMetasOrDirectivesAggregationProcessor
component.5.description=Add metadata or directives to the document, these values can be forged using document existing metadata or directives, uses JEXL library
component.5.interface.0.class=com.exalead.consobox.aggregation.CreateMetasOrDirectivesAggregationProcessor
component.5.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.5.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.5.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.5.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.5.interface.5.class=java.lang.Object
component.5.label=Metadata or directive creation aggregation processor
component.6.class=com.exalead.consobox.aggregation.DiscardAggregationProcessor
component.6.description=Discard document if matches one or many condition(s) (Empty conditions \= true), can also send delete document(s) order, for example if a document is already indexed but now needs to be deleted if discarded
component.6.interface.0.class=com.exalead.consobox.aggregation.DiscardAggregationProcessor
component.6.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.6.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.6.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.6.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.6.interface.5.class=java.lang.Object
component.6.label=Discard aggregation processor
component.7.class=com.exalead.consobox.aggregation.DocTimestampAggregationProcessor
component.7.description=Push a copy of document (to historize some dashboards and keep a snapshot version of a document during indexation)
component.7.interface.0.class=com.exalead.consobox.aggregation.DocTimestampAggregationProcessor
component.7.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.7.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.7.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.7.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.7.interface.5.class=java.lang.Object
component.7.label=Document timestamp aggregation processor
component.8.class=com.exalead.consobox.aggregation.FetchAggProcessor
component.8.description=Fetch document based on an URL pattern in the consolidation box (call configured connector, different from document source)
component.8.interface.0.class=com.exalead.consobox.aggregation.FetchAggProcessor
component.8.interface.1.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.8.interface.2.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.8.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.8.interface.4.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.8.interface.5.class=java.lang.Object
component.8.label=Fetch aggregation processor
component.9.class=com.exalead.consobox.aggregation.HistoricalAggregationProcessor
component.9.description=Push different objects to indexing server (current value of an object, all versions of the object over the time and snapshots of object over the time with configurable granularity\: day, month, year...).Snapshots and transitions can be pushed or not depending on the configuration. History mechanism is based on timestamp meta value, if you want to use this aggregation processor, you need to set this metadata at indexing time (using a PAPI Filter or directly a metadata coming from source) and generate graph containing current node and timestamp nodes. Do do that, you can use 'Timestamp PAPI Filter'.
component.9.interface.0.class=com.exalead.consobox.aggregation.HistoricalAggregationProcessor
component.9.interface.1.class=com.exalead.consobox.aggregation.HistoryAggregationProcessor
component.9.interface.2.class=com.exalead.consobox.commons.CustomAggregationProcessor
component.9.interface.3.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.9.interface.4.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAggregationListener
component.9.interface.5.class=com.exalead.consobox.commons.CustomConsolidationProcessor
component.9.interface.6.class=java.lang.Object
component.9.label=History aggregation processor
debug.classes.count=715
debug.destination.path=D\:\\WS\\15000_PIU\\bin\\..\\tmp\\plugin-install
debug.jar.count=7
lastmodified=1751968345621
plugin.reverse-classloading=false
timestamp=1751968345601
