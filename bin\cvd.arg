${NGINSTALLDIR}/${NGARCH}/bin/exa
-b
${NGINSTALLDIR}/${NGARCH}/bin/mercury.bin
-daemon
${NGRUNDIR}/hostagent
exa.bee.Queen.main
--preStartHookProcess
${NGDATADIR}/bin/rebuildDataDir.bat
--key
win32.serviceName
Exalead CloudView - cvdefault
--key
installName
${NGINSTALLNAME}
--key
processName
hostagent
--key
ken.agent.dir
${NGRUNDIR}
--configuration
config:///version.xml
--configuration
config:///mercury.xml
