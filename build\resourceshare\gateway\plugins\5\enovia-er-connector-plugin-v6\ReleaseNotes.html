<html><head><title>V6R2024x.FD01.V4</title></head><body>This files stores all release notes for ER API versions<br/>V6R2020x.GA.V9<br/> Fixes sql query for swym connectivity handler (IR-711165-3DEXPERIENCER2020x)<br/> Removes dependency to APISecurity.jar in ERAgent. Now this jar is delivered in 3DSpace (IR-711165-3DEXPERIENCER2020x)<br/>V6R2020x.GA.V8<br/> Fixes bad sql requests by using their r420 version(IR-711165-3DEXPERIENCER2020x)<br/> Fixes Too long string when getReport is in verbose mode(IR-691854-3DEXPERIENCER2020x)<br/> Fixes Loss of Interfaces attributes in indexed business objects when two different interfaces have an attribute with the same name<br/>V6R2020x.GA.V7<br/> Fixes Selectable fields are not retrieve by the ER connector (IR-705432-3DEXPERIENCER2020x) <br/>V6R2020x.GA.V6<br/> Fixes not displayed user defined tags which make them accessible via search refinement (IR-660262-3DEXPERIENCER2020x) <br/> Fixes type of onedrive, thumbnails and adding of owner as meta (IR-683540-3DEXPERIENCER2020x)<br/> Fixes MultiCollab Security source and ER connector does not set the token the same way(IR-704674-3DEXPERIENCER2020x)<br/> Fixes role when using ERConnector with CAS (IR-700257-3DEXPERIENCER2019x)<br/> Fixes bad parenthesis where clause when emitted (IR-687933-3DEXPERIENCER2019x)<br/> Fixes missing path indexation when a path element is shared between two path objects (IR-688485-3DEXPERIENCER2019x)<br/> Fixes DBCS indexation (IR-677189-3DEXPERIENCER2019x)<br/> Fixes default security handler (IR-574627-3DEXPERIENCER2019x)<br/> Fixes authentication retry mechanism that stops the indexing for long moments (IR-680607-3DEXPERIENCER2019x)<br/> Fixes non visible 6W Tag/Tags in community what's new page in timeline view (IR-661244-3DEXPERIENCER2019x)<br/> Fixes non working pagination one OneDrive (IR-675127-3DEXPERIENCER2019x)<br/> Fixes empty meta data description field of VPM component (IR-650679-EXALEAD6R2018x)<br/> Refactores ERAgent code for better packaging dependencies (IR-689658-3DEXPERIENCER2020x)<br/> Adds the projects to the security meta (MercuryRefs:#10120)<br/> Adds officialy path indexing and path configuration in queries configuration (IR-677888-3DEXPERIENCER2019x)<br/> Adds EXADrive and drives fws<br/> ERAgent uses logback instead of log4j<br/><br/></body></html>