<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Fri Jan 11 15:16:21 2019
 By Aleksey,,,
Copyright (c) 2010 by Dassault Systemes Company. All rights reserved.
</metadata>
<defs>
<font id="3ds-Regular" horiz-adv-x="605" >
  <font-face 
    font-family="3ds"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 3 2 0 0 2 0 4"
    ascent="800"
    descent="-200"
    x-height="482"
    cap-height="665"
    bbox="-28 -232 1193 1017"
    underline-thickness="50"
    underline-position="-150"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="597" 
d="M103 0v665h391v-665h-391zM187 75h223v515h-223v-515z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="585" 
d="M106 0v409h-77v73h77v80q0 47 17 81t46 55.5t69 32t87 10.5q45 0 87.5 -5.5t68.5 -12.5l-11 -78q-26 7 -63.5 12t-75.5 5q-55 0 -89 -22t-34 -77v-81h293v-482h-101v409h-192v-409h-102z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="618" 
d="M106 0v409h-77v73h77v80q0 49 17.5 83t47.5 55.5t70.5 31t86.5 9.5q45 0 87 -4.5t85 -12.5v-724h-100v658q-14 2 -31 3t-34 1q-24 0 -46.5 -4t-40.5 -14.5t-29 -30t-11 -50.5v-81h115v-73h-115v-409h-102z" />
    <glyph glyph-name=".notdef" horiz-adv-x="597" 
d="M103 0v665h391v-665h-391zM187 75h223v515h-223v-515z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="269" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="269" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="300" 
d="M111 168l-14 497h108l-14 -497h-80zM105 4q-17 15 -17 41t17 40.5t46 14.5q28 0 44.5 -14.5t16.5 -40.5t-16.5 -40.5t-44.5 -14.5q-29 0 -46 14z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="462" 
d="M284 452l-16 213h104l-17 -213h-71zM111 452l-16 213h104l-17 -213h-71z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="715" 
d="M139 0l36 192h-133l13 71h134l27 136h-134l14 71h134l38 195h80l-38 -195h144l39 195h84l-38 -195h133l-14 -71h-133l-26 -136h133l-14 -71h-134l-36 -192h-84l37 192h-145l-37 -192h-80zM269 263h146l26 136h-145z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M279 -85v100q-57 2 -111 13t-88 24l15 78q38 -11 87.5 -22.5t103.5 -11.5q27 0 50.5 2.5t41 10.5t27.5 22.5t10 37.5q0 31 -24.5 54t-61.5 44t-80.5 41.5t-80.5 46t-61.5 58.5t-24.5 78q0 41 14.5 68.5t40 45t60 26.5t74.5 12v86h74v-84q40 -1 80 -5.5t69 -9.5l-9 -80
q-16 3 -36 6t-41 5.5t-42 3.5t-38 1q-29 0 -54.5 -2.5t-44.5 -10t-30 -21t-11 -36.5q0 -27 25 -48t62 -41t80.5 -41t80.5 -48t62 -62t25 -82q0 -39 -13 -66.5t-35.5 -45.5t-53.5 -28.5t-68 -14.5v-104h-74z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="860" 
d="M151 408q11 -13 25 -19t30 -6t29.5 6t24 19.5t16.5 36t6 55.5q0 34 -6 56.5t-16.5 35.5t-24 18.5t-29.5 5.5t-30 -5.5t-25 -18.5t-17.5 -35t-6.5 -56t6.5 -56.5t17.5 -36.5zM142 334q-29 10 -50 31.5t-33 55t-12 80.5q0 48 12 81t33 53.5t50 30t64 9.5q34 0 63 -9.5
t50 -30t33 -54t12 -81.5q0 -47 -12 -80.5t-33 -54.5t-50 -31t-63 -10q-35 0 -64 10zM599 76q11 -13 25.5 -19t29.5 -6q16 0 30 5.5t24.5 19t16.5 36t6 55.5q0 34 -6 56.5t-16.5 36t-24.5 19t-30 5.5q-15 0 -29.5 -5.5t-25 -19t-17 -35.5t-6.5 -56q0 -33 6.5 -55.5
t16.5 -36.5zM591 1q-29 10 -50 31.5t-33 55t-12 80.5q0 48 12 81.5t33 54t50 30t63 9.5t63 -9.5t50 -30.5t33 -54.5t12 -81.5q0 -93 -44.5 -134.5t-113.5 -41.5q-34 0 -63 10zM169 0l429 665h85l-430 -665h-84z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="632" 
d="M506 0l-50 57q-36 -31 -83.5 -49.5t-109.5 -18.5q-106 0 -164 46t-58 138q0 35 10.5 63t29.5 51.5t45 43.5t57 38q-16 17 -31.5 35.5t-27.5 38t-19.5 41.5t-7.5 46q0 72 48.5 108.5t131.5 36.5q80 0 129.5 -34.5t49.5 -105.5q0 -59 -35.5 -103.5t-88.5 -79.5l137 -160
q17 21 32 51.5t24 63.5l83 -20q-11 -44 -33 -89t-50 -76l107 -122h-126zM246 302q-25 -15 -44 -28t-31.5 -27.5t-19 -32t-6.5 -41.5q0 -59 35 -82.5t91 -23.5q40 0 74 15t60 38zM271 413q38 26 62.5 53.5t24.5 67.5q0 38 -22.5 52.5t-57.5 14.5q-33 0 -56.5 -14t-23.5 -53
q0 -33 22 -61t51 -60z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="279" 
d="M103 452l-16 213h104l-17 -213h-71z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="324" 
d="M220 -188q-83 91 -124.5 210t-41.5 248q0 130 41.5 249.5t124.5 210.5h90q-38 -48 -68.5 -98t-52.5 -105.5t-34 -118.5t-12 -138q0 -74 12 -137t34 -118t52.5 -105t68.5 -98h-90z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="324" 
d="M14 -188q37 48 68 98t53 105t34 118t12 137q0 75 -12 138t-34 118.5t-53 105.5t-68 98h90q83 -91 124.5 -210.5t41.5 -249.5q0 -129 -41.5 -248t-124.5 -210h-90z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="427" 
d="M74 412l89 86l-131 31l24 65l127 -45l-10 116h81l-10 -116l127 45l24 -65l-131 -31l90 -86l-59 -48l-82 101l-81 -101z" />
    <glyph glyph-name="plus" unicode="+" 
d="M67 284v70h195v170h80v-170h195v-70h-195v-171h-80v171h-195z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="275" 
d="M26 -133l72 235h104l-98 -235h-78z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="392" 
d="M62 232v78h267v-78h-267z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="274" 
d="M90 6q-18 16 -18 43t18 42t47 15q30 0 47.5 -15t17.5 -42t-17.5 -42.5t-47.5 -15.5q-29 0 -47 15z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="479" 
d="M22 -53l356 783h87l-357 -783h-86z" />
    <glyph glyph-name="zero" unicode="0" 
d="M302 -11q-128 0 -188 81t-60 260q0 91 15 156.5t45.5 107.5t77 61.5t110.5 19.5t110.5 -19.5t77 -61.5t45.5 -107t15 -155t-15 -155t-45.5 -107t-77 -61.5t-110.5 -19.5zM191 126q18 -31 45 -44.5t66 -13.5t66 13.5t44.5 44.5t25.5 81.5t8 123.5q0 74 -8 124.5t-25.5 82
t-44.5 45t-66 13.5t-66 -13.5t-44.5 -45t-25.5 -82t-8 -124.5q0 -73 8 -123.5t25 -81.5z" />
    <glyph glyph-name="one" unicode="1" 
d="M279 0v575l-152 -39v84l171 45h82v-665h-101z" />
    <glyph glyph-name="two" unicode="2" 
d="M59 0v75l202 203q28 28 51.5 54t40 51.5t25.5 52t9 56.5q0 56 -36.5 80t-102.5 24q-18 0 -40 -2t-44.5 -5t-42.5 -7t-33 -7l-10 80q36 11 84.5 15.5t92.5 4.5q51 0 95 -9.5t76.5 -31t51 -56.5t18.5 -86q0 -42 -12.5 -77t-33.5 -66.5t-49.5 -61.5t-60.5 -62l-149 -145h313
v-80h-445z" />
    <glyph glyph-name="three" unicode="3" 
d="M75 91q38 -10 83 -16.5t91 -6.5q36 0 65 6.5t49.5 21.5t31.5 40t11 63q0 62 -40 90t-110 28h-76v68l186 200h-295v80h423v-70l-197 -206h1q37 0 75.5 -10t69.5 -32.5t50.5 -57.5t19.5 -86q0 -60 -19 -101t-53 -65.5t-81.5 -35.5t-103.5 -11q-52 0 -100.5 6t-89.5 16z" />
    <glyph glyph-name="four" unicode="4" 
d="M28 159v69l311 437h137v-429h85v-77h-85v-159h-98v159h-350zM132 236h246v341z" />
    <glyph glyph-name="five" unicode="5" 
d="M93 90q42 -10 82 -16t83 -6q33 0 61 5.5t48 20.5t31 41t11 67q0 38 -11 63t-30.5 39.5t-47 20.5t-60.5 6q-32 0 -71 -4.5t-71 -9.5l-25 26l29 322h373v-83h-290l-16 -178q15 2 39.5 4t46.5 2q54 0 98 -10t75.5 -33.5t49 -63t17.5 -98.5q0 -60 -18.5 -101.5t-51.5 -67
t-79 -36.5t-102 -11q-52 0 -94.5 5.5t-85.5 16.5z" />
    <glyph glyph-name="six" unicode="6" 
d="M505 579q-36 7 -76.5 12t-77.5 5q-41 0 -73.5 -11t-55 -36.5t-34.5 -67t-12 -102.5q32 10 70.5 17t79.5 7q52 0 93 -11t69.5 -34.5t43.5 -60.5t15 -89q0 -101 -59.5 -160t-169.5 -59q-61 0 -107 18.5t-77.5 59t-47.5 104.5t-16 156q0 99 19.5 165.5t55.5 107t87.5 58
t116.5 17.5q18 0 40 -1t44.5 -3.5t44 -5.5t38.5 -6zM200 140q15 -32 43 -52t75 -20q58 0 91.5 37t33.5 100q0 66 -30.5 92t-95.5 26q-33 0 -70.5 -7t-70.5 -19q0 -42 4.5 -83t19.5 -74z" />
    <glyph glyph-name="seven" unicode="7" 
d="M112 0l303 583h-341v82h456v-69l-301 -596h-117z" />
    <glyph glyph-name="eight" unicode="8" 
d="M161 180q0 -54 36 -83t105 -29q68 0 104 29t36 83q0 63 -37.5 91.5t-102.5 28.5q-66 0 -103.5 -28.5t-37.5 -91.5zM133 322q23 13 48 20q-54 17 -82.5 54.5t-28.5 100.5q0 45 17.5 78t48 55.5t73 33.5t93.5 11q50 0 92.5 -11t73 -33.5t48 -55.5t17.5 -78
q0 -63 -28.5 -100.5t-82.5 -54.5q25 -7 48 -20t39.5 -33t26.5 -47.5t10 -63.5q0 -89 -64 -139t-180 -50t-180.5 50t-64.5 139q0 36 10 63.5t27 47.5t39 33zM174 496q0 -57 32.5 -84t95.5 -27q62 0 94.5 27t32.5 84q0 49 -33.5 74.5t-93.5 25.5q-61 0 -94.5 -25.5
t-33.5 -74.5z" />
    <glyph glyph-name="nine" unicode="9" 
d="M100 87q34 -8 72.5 -13.5t73.5 -5.5q43 0 77 10.5t57.5 35.5t36 67t12.5 104q-32 -10 -70.5 -17t-79.5 -7q-104 0 -162.5 45.5t-58.5 149.5q0 101 60.5 160t170.5 59q61 0 107 -18.5t77 -59t46.5 -104.5t15.5 -156q0 -99 -20 -166t-57.5 -107.5t-91.5 -57.5t-121 -17
q-38 0 -79.5 5t-76.5 13zM162 459q0 -66 30.5 -92t95.5 -26q33 0 70.5 7t70.5 19q0 42 -4 83t-18.5 73.5t-42.5 52.5t-75 20q-58 0 -92.5 -37t-34.5 -100z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="275" 
d="M90 392q-18 16 -18 43t18 42t47 15q30 0 47.5 -15t17.5 -42t-17.5 -42.5t-47.5 -15.5q-29 0 -47 15zM90 7q-18 16 -18 43t18 42t47 15q30 0 47.5 -15t17.5 -42t-17.5 -42.5t-47.5 -15.5q-29 0 -47 15z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="275" 
d="M26 -133l72 235h104l-98 -235h-78zM90 392q-18 16 -18 43t18 42t47 15q30 0 47.5 -15t17.5 -42t-17.5 -42.5t-47.5 -15.5q-29 0 -47 15z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M67 285v68l470 178v-75l-370 -137l370 -138v-75z" />
    <glyph glyph-name="equal" unicode="=" 
d="M67 185v69h470v-69h-470zM67 384v69h470v-69h-470z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M67 106v75l370 138l-370 137v75l470 -178v-68z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="432" 
d="M160 167v63q0 40 10.5 70t26.5 53.5t34.5 42t34.5 36.5t26.5 36.5t10.5 42.5q0 46 -25.5 65.5t-78.5 19.5q-37 0 -76.5 -8.5t-65.5 -19.5l-19 77q29 13 74 21.5t96 8.5q42 0 78 -9.5t62 -29t40.5 -49t14.5 -70.5q0 -38 -11.5 -66t-29 -50t-37.5 -40t-37.5 -38t-29 -43
t-11.5 -54v-59h-87zM158 4q-17 15 -17 41t17 40.5t46 14.5q27 0 44 -14.5t17 -40.5t-17 -40.5t-44 -14.5q-29 0 -46 14z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="881" 
d="M504 342q-14 3 -30.5 5t-28.5 2q-44 0 -65.5 -30t-21.5 -95q0 -41 6 -66t15.5 -39.5t22.5 -19.5t27 -5q24 0 42 9.5t33 23.5v215zM704 -130q-56 -22 -122.5 -36.5t-138.5 -14.5q-113 0 -189 32t-121.5 87t-65 129.5t-19.5 160.5q0 81 22.5 153t70 126t122.5 85.5
t180 31.5q108 0 182 -27.5t120.5 -74t67 -108.5t20.5 -130q0 -127 -42.5 -196.5t-132.5 -69.5q-51 0 -79.5 15t-42.5 36q-22 -18 -50 -32.5t-70 -14.5q-32 0 -59 11t-47 35.5t-31.5 63t-11.5 94.5q0 48 13 83.5t36 59.5t55 35.5t71 11.5q35 0 75.5 -6.5t74.5 -17.5v-221
q0 -18 2.5 -33.5t10 -26t21 -16.5t35.5 -6q49 0 71.5 45.5t22.5 151.5q0 60 -17 110t-54.5 86t-97 56t-143.5 20q-87 0 -147.5 -25.5t-98 -69.5t-54 -104.5t-16.5 -130.5q0 -75 14 -137.5t49.5 -107.5t97 -70t156.5 -25q69 0 128.5 13.5t113.5 33.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="677" 
d="M85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="631" 
d="M205 78q30 -5 61 -7.5t53 -2.5q41 0 69 7.5t44.5 22.5t23.5 36t7 48q0 57 -30.5 85.5t-109.5 28.5h-118v-218zM205 374h117q59 0 91.5 27.5t32.5 84.5q0 55 -30.5 78.5t-96.5 23.5h-114v-214zM100 665h216q55 0 99.5 -9t75 -29.5t47 -53t16.5 -79.5q0 -67 -28 -102.5
t-76 -51.5q24 -6 47 -17.5t40 -30.5t27.5 -46.5t10.5 -65.5q0 -93 -63.5 -142t-190.5 -49q-42 0 -102 7t-119 19v650z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="577" 
d="M528 10q-40 -11 -86 -16t-88 -5q-69 0 -124 17t-93.5 57t-58.5 106t-20 164t20 164t58.5 105.5t94.5 56t127 16.5q43 0 87 -5t80 -12l-12 -80q-35 8 -78.5 13t-81.5 5q-47 0 -81.5 -12.5t-57.5 -42.5t-34.5 -80.5t-11.5 -126.5q0 -79 12 -130.5t35.5 -81.5t59 -42
t82.5 -12q38 0 81.5 6.5t81.5 14.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="681" 
d="M100 665h225q71 0 126.5 -19t93.5 -59t58 -101.5t20 -146.5q0 -82 -16 -147t-53 -110t-96.5 -69t-146.5 -24q-50 0 -102.5 6t-108.5 19v651zM205 77q49 -9 109 -9q105 0 153 65t48 205q0 67 -13 114.5t-37 77t-59.5 43t-80.5 13.5h-120v-509z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="566" 
d="M287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="555" 
d="M103 0v665h404v-79h-299v-218h262v-79h-262v-289h-105z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="696" 
d="M608 22q-49 -14 -114.5 -23.5t-128.5 -9.5q-72 0 -128.5 16.5t-96.5 56.5t-61 107t-21 167q0 93 21.5 157.5t62 105t99.5 58.5t135 18q30 0 61 -1.5t59.5 -4.5t53 -6.5t41.5 -6.5l-9 -82q-37 8 -89.5 15t-115.5 7q-54 0 -93.5 -14t-65 -45.5t-38 -81t-12.5 -120.5
q0 -78 12.5 -129.5t37.5 -82t61 -43t83 -12.5q18 0 37.5 1t38.5 3t36 4t29 5v210h-145v81h250v-350z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="748" 
d="M103 0v665h105v-281h332v281h105v-665h-105v301h-332v-301h-105z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="311" 
d="M103 0v665h105v-665h-105z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="306" 
d="M-28 -136q34 29 59 54.5t41 50t23.5 50t7.5 55.5v591h105v-589q0 -46 -13.5 -83.5t-38 -70t-59 -62t-75.5 -59.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="626" 
d="M103 0v665h105v-354l300 354h116l-258 -303q36 -11 64 -30t49.5 -44.5t39.5 -58t35 -70.5l71 -159h-117l-58 134q-26 60 -63 103t-93 50l-86 -95v-192h-105z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="515" 
d="M103 0v665h105v-586h284v-79h-389z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="914" 
d="M103 0v665h115l239 -359l240 359h114v-665h-103v523l-209 -314h-84l-208 314v-523h-104z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="755" 
d="M103 0v665h84l363 -389v389h102v-665h-102v150l-346 366v-516h-101z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="687" 
d="M220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5t55 44.5
t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="629" 
d="M208 328q56 -10 106 -10q84 0 118 34t34 102q0 38 -8.5 63.5t-26.5 40.5t-46 21.5t-67 6.5h-110v-258zM103 0v665h215q63 0 110.5 -10.5t79.5 -35.5t48 -65t16 -100q0 -59 -17 -100.5t-49.5 -67t-78 -37t-102.5 -11.5q-53 0 -117 11v-249h-105z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="689" 
d="M296 -62q26 0 62 -8t72.5 -18t68.5 -18t51 -8q28 0 48 9.5t39 26.5l42 -66q-22 -24 -54 -39t-74 -15q-28 0 -63.5 9t-71 20t-67.5 20t-53 9v78zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5
t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5
t11 -124.5t33 -81.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="641" 
d="M103 0v665h215q63 0 110.5 -10t79.5 -34t48 -63.5t16 -98.5q0 -40 -10 -69.5t-27 -50.5t-40.5 -35t-51.5 -23q37 -14 57 -42t37 -75l59 -164h-109l-52 141q-9 24 -19 43.5t-24 34t-34 22.5t-48 8q-24 0 -49.5 1.5t-52.5 6.5v-257h-105zM208 336q29 -5 55.5 -7t50.5 -2
q84 0 118 32t34 100q0 38 -8.5 62.5t-26.5 39t-46 20t-67 5.5h-110v-250z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="560" 
d="M75 102q38 -11 87.5 -22.5t106.5 -11.5q53 0 87.5 16t34.5 60q0 31 -24.5 59t-62 55t-80.5 55t-80.5 59t-62 66t-24.5 78q0 45 19.5 75.5t52.5 49t78 26.5t96 8q22 0 46 -1.5t46.5 -3.5t41.5 -5t33 -6l-9 -80q-15 3 -35 6t-41.5 5.5t-43 4t-38.5 1.5q-30 0 -55 -2.5
t-43 -10.5t-28 -22.5t-10 -37.5q0 -27 24.5 -52t61.5 -50t80 -52.5t80 -59.5t61.5 -70.5t24.5 -85.5q0 -45 -16.5 -76.5t-46.5 -51t-72.5 -28t-93.5 -8.5q-63 0 -116.5 11.5t-91.5 23.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="575" 
d="M235 0v586h-218v79h541v-79h-218v-586h-105z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="705" 
d="M196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="603" 
d="M250 0l-250 665h111l205 -558q25 56 52.5 118t53.5 131.5t47.5 146.5t34.5 162h99q-12 -93 -37 -179.5t-57 -168.5t-70.5 -161t-77.5 -156h-111z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="910" 
d="M193 0l-183 665h107l140 -548l152 419h98l149 -423q49 112 86.5 246.5t58.5 305.5h99q-26 -185 -74 -346t-123 -319h-98l-149 424l-159 -424h-104z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="593" 
d="M7 0l230 337l-217 328h121l161 -247l168 247h112l-225 -325l228 -340h-123l-170 260l-174 -260h-111z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="596" 
d="M243 0v227l-253 438h125l195 -339q34 33 63 73.5t51.5 84t37.5 90t23 91.5h106q-8 -60 -30 -122t-54.5 -119t-73 -107.5t-85.5 -87.5v-229h-105z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="606" 
d="M50 0v72l371 514h-359v79h489v-73l-368 -513h377v-79h-510z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="324" 
d="M106 -188v919h203v-66h-116v-787h116v-66h-203z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="479" 
d="M369 -53l-357 783h87l356 -783h-86z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="324" 
d="M14 -188v66h116v787h-116v66h203v-919h-203z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M92 361l174 304h72l174 -304h-86l-124 224l-124 -224h-86z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="686" 
d="M0 -75v64h686v-64h-686z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="550" 
d="M259 569l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="517" 
d="M344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="568" 
d="M183 477q28 6 58 10.5t59 4.5q46 0 86 -12.5t69.5 -41.5t46 -76t16.5 -117q0 -75 -18 -124t-48.5 -78.5t-72 -41t-88.5 -11.5q-24 0 -51.5 2.5t-55 6.5t-54 9t-48.5 11v711h101v-253zM183 78q26 -5 53 -8.5t53 -3.5q29 0 52 7.5t39 28t24.5 55t8.5 88.5q0 51 -8.5 83.5
t-24.5 52t-37.5 27t-47.5 7.5q-27 0 -56 -4.5t-56 -10.5v-322z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="445" 
d="M408 8q-29 -8 -62 -13t-67 -5q-51 0 -93 13t-72.5 42.5t-47 77.5t-16.5 119q0 72 17 120.5t48 77t74 40.5t95 12q31 0 63 -4t55 -9l-10 -76q-24 5 -52 8.5t-57 3.5q-30 0 -54 -7.5t-40.5 -27t-25 -53t-8.5 -86.5q0 -48 7 -81.5t22.5 -54t40.5 -29.5t60 -9q31 0 58.5 4.5
t55.5 11.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="568" 
d="M385 400q-27 7 -54 11t-54 4q-25 0 -47.5 -7.5t-39 -27t-26 -52t-9.5 -83.5q0 -54 8.5 -88.5t24.5 -55t39 -28t52 -7.5q26 0 53 3.5t53 8.5v322zM486 19q-45 -11 -103 -20t-106 -9t-89 11.5t-72 41t-48.5 78.5t-17.5 124q0 70 17.5 117t47.5 76t70.5 41.5t87.5 12.5
q29 0 55.5 -4t55.5 -10v252h102v-711z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="518" 
d="M156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="370" 
d="M107 0v407h-78v75h78v88q0 88 52.5 129.5t140.5 41.5q23 0 48 -2t45 -6l-11 -75q-17 2 -36 4t-37 2q-21 0 -39.5 -4.5t-32 -15t-21.5 -28.5t-8 -46v-88h156v-75h-156v-407h-101z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="572" 
d="M106 -99q33 -8 77 -15.5t81 -7.5q64 0 93 25.5t29 82.5v27q-26 -5 -57 -9t-60 -4q-44 0 -84 10t-70 37.5t-47.5 75.5t-17.5 124q0 69 19 116t51 75.5t75.5 41t91.5 12.5q51 0 102 -11t98 -26v-458q0 -55 -15 -92t-43.5 -60t-69 -33t-92.5 -10q-43 0 -91 7t-81 16z
M386 400q-20 5 -46.5 10t-51.5 5q-27 0 -51 -7t-42 -26t-28.5 -51.5t-10.5 -84.5q0 -54 8.5 -87t24.5 -51.5t38.5 -24.5t49.5 -6q26 0 53 4t56 9v310z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="587" 
d="M85 0v730h101v-254q29 6 60.5 11t62.5 5q42 0 79 -8.5t64 -28.5t43 -52.5t16 -80.5v-322h-101v318q0 28 -8.5 47t-24 30t-36 15.5t-44.5 4.5q-28 0 -58 -5t-53 -10v-400h-101z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="273" 
d="M86 0v482h101v-482h-101zM89 620q-17 15 -17 42q0 28 17 43t47 15q64 0 64 -58q0 -27 -16.5 -42t-47.5 -15q-30 0 -47 15z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="273" 
d="M-25 -144q35 31 57 55.5t34 48.5t16 49t4 57v416h101v-415q0 -45 -8 -79.5t-27 -64.5t-51 -59t-79 -63zM89 620q-17 15 -17 42q0 28 17 43t47 15q64 0 64 -58q0 -27 -16.5 -42t-47.5 -15q-30 0 -47 15z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="539" 
d="M86 0v730h101v-472l214 224h111l-185 -194q57 -14 91.5 -55.5t54.5 -98.5l46 -134h-109l-43 124q-17 50 -46 72t-67 26l-67 -64v-158h-101z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="273" 
d="M86 0v730h101v-730h-101z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="877" 
d="M85 0v455q39 12 94 24.5t113 12.5q44 0 81 -10t64 -32q37 18 84 30t92 12q40 0 74.5 -8.5t59.5 -28.5t39.5 -52.5t14.5 -80.5v-322h-101v318q0 29 -8 47.5t-21.5 29.5t-32.5 15.5t-40 4.5q-35 0 -63.5 -9t-51.5 -19q10 -27 10 -65v-322h-101v318q0 29 -8 47.5t-21.5 29.5
t-32 15.5t-40.5 4.5q-26 0 -54 -4.5t-50 -10.5v-400h-101z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="587" 
d="M85 0v455q19 6 43.5 12.5t52.5 12t58.5 9t60.5 3.5q45 0 83.5 -8.5t67 -28t44.5 -52.5t16 -81v-322h-101v318q0 28 -8.5 47t-23.5 30t-36 15.5t-45 4.5q-28 0 -58 -4.5t-53 -10.5v-400h-101z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="557" 
d="M278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="570" 
d="M84 -188v643q47 15 103.5 26t107.5 11q48 0 89 -12.5t71.5 -41.5t47.5 -76t17 -116q0 -76 -17.5 -125.5t-47.5 -78.5t-69.5 -40.5t-84.5 -11.5q-29 0 -59.5 4t-56.5 9v-191h-101zM185 80q28 -5 55 -9t54 -4t49.5 7.5t38.5 27.5t24.5 54.5t8.5 88.5q0 52 -8.5 85t-24.5 52
t-38.5 26t-50.5 7q-12 0 -26.5 -1.5t-29.5 -3.5t-28.5 -4.5t-23.5 -5.5v-320z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="574" 
d="M386 -188v191q-26 -5 -57 -9t-60 -4q-44 0 -84 11t-70 39t-47.5 77t-17.5 125q0 69 19 116.5t51 77.5t75.5 43t91.5 13q51 0 102 -11t98 -26v-643h-101zM386 400q-20 5 -46.5 10t-51.5 5q-27 0 -51 -8t-42 -27.5t-28.5 -53t-10.5 -85.5q0 -54 8.5 -88t24.5 -53t38.5 -26
t49.5 -7q26 0 53 4t56 9v320z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="365" 
d="M84 0v455q62 19 124.5 27.5t137.5 8.5v-80q-11 1 -23 1h-24q-32 0 -62 -4t-52 -10v-398h-101z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="459" 
d="M66 91q14 -5 32.5 -9t39.5 -7.5t42 -5.5t41 -2q46 0 69 10.5t23 41.5q0 23 -19.5 39.5t-49 31.5t-63.5 30t-63.5 34t-49 45t-19.5 62q0 39 17 64.5t46.5 40t69 20.5t83.5 6q37 0 73 -3.5t59 -7.5l-9 -75q-24 5 -56 8.5t-68 3.5q-53 0 -83.5 -11t-30.5 -43q0 -19 20 -34
t49.5 -29.5t64.5 -30t64.5 -36.5t49.5 -48.5t20 -66.5q0 -35 -15 -59.5t-41 -40t-61.5 -22.5t-77.5 -7q-48 0 -92.5 7t-75.5 18z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="382" 
d="M206 120q0 -29 16 -40.5t42 -11.5q23 0 48 4.5t42 9.5l3 -76q-23 -8 -50 -12t-55 -4q-65 0 -106 31t-41 102v284h-79v75h79l23 139h78v-139h140v-75h-140v-287z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="577" 
d="M177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="493" 
d="M194 0l-186 482h105l146 -396q37 76 75.5 173.5t58.5 222.5h92q-19 -135 -66 -252.5t-112 -229.5h-113z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="837" 
d="M172 0l-158 482h104l123 -396l130 396h107l143 -401q37 76 67 176t44 225h91q-16 -135 -53 -252t-98 -230h-114l-134 378l-127 -378h-125z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="478" 
d="M5 0l169 243l-161 239h115l116 -176l119 176h104l-164 -237l169 -245h-115l-124 180l-124 -180h-104z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="578" 
d="M111 -99q33 -8 77 -15t81 -7q64 0 93 24t29 81v28q-26 -5 -53.5 -8.5t-53.5 -3.5q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v308h101v-309q0 -27 8.5 -46t23.5 -30t34.5 -16t42.5 -5q28 0 57 4.5t48 10.5v391h101v-484q0 -55 -15 -92.5t-43 -60.5t-69 -33t-93 -10
q-43 0 -90.5 7.5t-81.5 16.5z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="492" 
d="M41 0v66l281 341h-272v75h395v-65l-278 -342h288v-75h-414z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="372" 
d="M354 -190q-18 -5 -38.5 -6.5t-40.5 -1.5q-81 0 -119.5 41.5t-38.5 120.5q0 24 3 51.5t6.5 54.5t6.5 52t3 46q0 38 -24 50t-70 13v80q47 1 70.5 12.5t23.5 49.5q0 21 -3 46.5t-6.5 52.5t-6.5 54.5t-3 52.5q0 79 38.5 120.5t119.5 41.5q25 0 45 -2.5t34 -5.5l-8 -75
q-12 3 -28 5t-30 2q-49 0 -66 -21.5t-17 -59.5q0 -24 2.5 -51t6 -55t6 -55t2.5 -50q0 -26 -7 -43.5t-18 -29.5t-25.5 -19t-30.5 -10q17 -4 32 -11t26 -19t17 -30t6 -45q0 -23 -2.5 -49.5t-6 -54.5t-6 -54.5t-2.5 -48.5q0 -41 17 -61t65 -20q14 0 30.5 2t28.5 5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="295" 
d="M106 -200v1000h83v-1000h-83z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="372" 
d="M25 -115q12 -3 28.5 -5t30.5 -2q48 0 65 20t17 61q0 22 -2.5 48.5t-6 54.5t-6 54.5t-2.5 49.5q0 53 23 75t58 30q-16 3 -30.5 10t-26 19t-18 29.5t-6.5 43.5q0 23 2.5 50t6 55t6 55t2.5 51q0 38 -17 59.5t-66 21.5q-14 0 -30 -2t-28 -5l-8 75q14 3 34 5.5t45 2.5
q81 0 119.5 -41.5t38.5 -120.5q0 -25 -3 -52.5t-6.5 -54.5t-6.5 -52.5t-3 -46.5q0 -38 23.5 -49.5t70.5 -12.5v-80q-46 -1 -70 -13t-24 -50q0 -21 3 -46t6.5 -52t6.5 -54.5t3 -51.5q0 -79 -38.5 -120.5t-119.5 -41.5q-20 0 -40.5 1.5t-38.5 6.5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M550 353q-15 -42 -48 -68t-87 -26q-26 0 -57.5 8t-63 18t-59 18t-45.5 8q-29 0 -44.5 -13.5t-27.5 -38.5q-16 7 -32 13t-33 13q15 40 46 66.5t85 26.5q26 0 58 -8t64.5 -17t61 -17t46.5 -8q30 0 45 12.5t27 38.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="269" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="300" 
d="M97 -188l14 498h80l14 -498h-108zM105 397q-17 15 -17 41t17 40t46 14q28 0 44.5 -14t16.5 -40t-16.5 -40.5t-44.5 -14.5q-29 0 -46 14z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M294 -84v149q-47 6 -84.5 23.5t-63.5 50t-40.5 80t-14.5 113.5q0 65 14.5 112t40.5 78.5t63 49t82 23.5v135h74v-129q41 -1 83 -7.5t68 -12.5l-12 -79q-12 3 -29.5 6.5t-38 6.5t-42 5t-40.5 2q-36 0 -65.5 -8t-51 -29t-33 -58t-11.5 -95q0 -56 11 -92.5t32.5 -59
t53 -31.5t72.5 -9q37 0 77.5 7t69.5 17l10 -77q-29 -11 -69 -18t-82 -9v-144h-74z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M47 0v64q34 20 51 44.5t25.5 54.5t12 66.5t9.5 81.5h-99v70h107l9 85q6 60 24.5 100.5t47 64.5t67.5 34t86 10q45 0 85 -5t69 -11l-12 -80q-29 7 -66 12t-68 5q-54 0 -85.5 -21.5t-37.5 -78.5l-13 -115h233v-70h-241q-5 -45 -8.5 -80t-11 -62.5t-21.5 -49.5t-40 -40h359
v-79h-482z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M266 0v213h-204v64h199l-56 92h-143v61h106l-142 235h115l176 -307l159 307h102l-127 -235h114v-61h-143l-47 -92h190v-64h-198v-213h-101z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="295" 
d="M106 399v401h83v-401h-83zM106 -200v401h83v-401h-83z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="563" 
d="M65 -86q35 -12 83.5 -23.5t100.5 -11.5q26 0 48.5 4t39 14t26 26t9.5 40q0 31 -23 55t-57 44.5t-74 40.5t-74 45t-57 57t-23 76q0 48 19 77t51 46q-24 21 -38.5 47.5t-14.5 61.5q0 47 18.5 78t50.5 50t74.5 26.5t91.5 7.5q40 0 85 -5t76 -11l-9 -76q-15 3 -34.5 5.5
t-39.5 5t-40.5 3.5t-36.5 1q-27 0 -51.5 -3.5t-42.5 -12.5t-28.5 -24.5t-10.5 -39.5q0 -27 24 -47.5t60.5 -39.5t78.5 -39.5t78.5 -47t60.5 -61.5t24 -83q0 -49 -19.5 -79.5t-55.5 -47.5q18 -21 28.5 -47t10.5 -58q0 -47 -17.5 -78.5t-47.5 -51t-71 -28t-88 -8.5
q-27 0 -55.5 3t-55 8t-50 11.5t-40.5 13.5zM305 168q37 -18 69 -40q17 9 28.5 25t11.5 41q0 28 -20.5 49.5t-51.5 40t-67.5 35.5t-68.5 36q-20 -9 -33 -25.5t-13 -41.5t21.5 -45t54 -38t69.5 -37z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="550" 
d="M139 614q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM323 614q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="723" 
d="M487 158q-20 -8 -51.5 -13t-60.5 -5q-42 0 -75 10t-56 32.5t-35 59.5t-12 90q0 52 12 88t34.5 58.5t55 33t74.5 10.5q29 0 60.5 -5t49.5 -11l-8 -61q-19 5 -45.5 9.5t-51.5 4.5t-43 -5.5t-30 -19.5t-18 -38.5t-6 -63.5q0 -40 6 -65t19 -39.5t31.5 -19.5t44.5 -5
q24 0 50.5 4.5t46.5 12.5zM359 -10q-338 0 -338 342q0 171 84.5 257t253.5 86t253 -86t84 -257q0 -342 -337 -342zM147 115q68 -72 212 -72t211.5 72t67.5 217q0 144 -67.5 216t-211.5 72t-212 -72t-68 -216q0 -145 68 -217z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="358" 
d="M292 391q-19 -8 -55 -17.5t-77 -9.5q-26 0 -48.5 5t-39 16t-26 29.5t-9.5 44.5q0 28 10.5 46t27.5 28t38 14t42 4q16 0 32.5 -2t30.5 -5v29q0 27 -16.5 37.5t-42.5 10.5t-48.5 -4.5t-41.5 -9.5l-10 54q25 7 51 10.5t56 3.5q27 0 50 -5.5t40 -17.5t26.5 -31t9.5 -45v-185z
M218 495q-14 3 -29.5 5t-30.5 2q-20 0 -35.5 -9t-15.5 -35t16 -35.5t39 -9.5q31 0 56 7v75z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="575" 
d="M206 90l-152 181l152 181h91l-134 -181l134 -181h-91zM430 90l-138 181l138 181h91l-120 -181l120 -181h-91z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M67 384v69h470v-268h-76v199h-394z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="392" 
d="M62 232zM62 232v78h267v-78h-267z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="627" 
d="M273 437q4 -1 12 -2t19 -1q26 0 43 8t17 37q0 24 -12.5 33t-41.5 9h-37v-84zM211 283v283h103q51 0 82 -21t31 -63q0 -35 -14.5 -50.5t-35.5 -23.5q14 -8 22 -19.5t15 -30.5l28 -75h-66l-29 75q-8 20 -17.5 24.5t-24.5 5.5h-15.5t-16.5 2v-107h-62zM104 226
q-73 67 -73 191q0 125 73.5 191t208.5 66q134 0 208 -66t74 -191q0 -124 -74 -190.5t-208 -66.5q-135 0 -209 66zM88 417q0 -208 225 -208q112 0 168.5 52t56.5 156q0 105 -56.5 156.5t-168.5 51.5q-225 0 -225 -208z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="550" 
d="M144 616v66h262v-66h-262z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="385" 
d="M125 561q0 -29 18 -46.5t50 -17.5q31 0 49.5 17.5t18.5 46.5t-18.5 46.5t-49.5 17.5q-32 0 -50 -17.5t-18 -46.5zM102 479q-35 32 -35 82t35.5 82t90.5 32t90.5 -32t35.5 -82t-35.5 -82t-90.5 -32t-91 32z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M67 0v70h470v-70h-470zM67 284v70h195v170h80v-170h195v-70h-195v-171h-80v171h-195z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="330" 
d="M43 332v49l97 96q34 33 49 52.5t15 42.5q0 26 -17 35.5t-44 9.5q-23 0 -46.5 -4t-40.5 -9l-7 57q44 14 103 14q55 0 90.5 -24t35.5 -75q0 -39 -21 -67.5t-55 -60.5l-63 -59h142v-57h-238z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="330" 
d="M47 394q17 -7 44 -12.5t53 -5.5q29 0 47 11t18 43q0 29 -21 38t-53 9h-45v46l90 86h-140v56h231v-51l-100 -88h4q21 0 40.5 -6t35 -17.5t24.5 -29t9 -41.5q0 -59 -38.5 -87t-96.5 -28q-33 0 -62.5 6t-49.5 14z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="550" 
d="M207 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="580" 
d="M79 -188v670h101v-319q0 -27 8.5 -46t23.5 -30t34.5 -16t42.5 -5q28 0 57 5t48 10v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-32 0 -59 6t-48 21v-205h-101z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="672" 
d="M342 -158v389q-12 -1 -27 -1h-32q-63 0 -108 11.5t-74 37.5t-43 68.5t-14 104.5q0 61 13.5 101.5t42.5 65.5t74.5 35.5t110.5 10.5h131v-823h-74zM495 -158v823h74v-823h-74z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="275" 
d="M90 227q-18 16 -18 43t18 42t47 15q30 0 47.5 -15t17.5 -42t-17.5 -42.5t-47.5 -15.5q-29 0 -47 15z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="550" 
d="M196 -125q11 -4 25 -6.5t24 -2.5q19 0 25 6.5t6 20.5t-7 29.5t-16 31.5h74q13 -18 22 -37.5t9 -38.5q0 -35 -21.5 -55.5t-72.5 -20.5q-20 0 -43.5 4t-37.5 9z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="313" 
d="M125 331v269l-81 -20v61l100 24h55v-334h-74z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="358" 
d="M72 396q-39 37 -39 121t39 120.5t108 36.5q68 0 107 -36.5t39 -120.5t-39.5 -121t-106.5 -37q-69 0 -108 37zM127 436q20 -22 52 -22t52 22.5t20 80.5q0 57 -20 79.5t-52 22.5t-51.5 -22.5t-19.5 -79.5q0 -58 19 -81z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="575" 
d="M277 90l136 181l-136 181h92l152 -181l-152 -181h-92zM54 90l121 181l-121 181h91l138 -181l-138 -181h-91z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="798" 
d="M483 73v48l132 222h95v-215h47v-55h-47v-73h-72v73h-155zM553 128h85v140zM125 0l428 665h83l-429 -665h-82zM125 331v269l-81 -20v61l100 24h55v-334h-74z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="798" 
d="M121 0l428 665h83l-429 -665h-82zM125 331v269l-81 -20v61l100 24h55v-334h-74zM490 0v49l97 96q34 33 49 52.5t15 42.5q0 26 -17 35.5t-44 9.5q-23 0 -46.5 -4t-40.5 -9l-7 57q44 14 103 14q55 0 90.5 -24t35.5 -75q0 -39 -21 -67.5t-55 -60.5l-63 -59h142v-57h-238z
" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="798" 
d="M484 73v48l132 222h95v-215h47v-55h-47v-73h-72v73h-155zM554 128h85v140zM162 0l428 665h83l-429 -665h-82zM51 394q17 -7 44 -12.5t53 -5.5q29 0 47 11t18 43q0 29 -21 38t-53 9h-45v46l90 86h-140v56h231v-51l-100 -88h4q21 0 40.5 -6t35 -17.5t24.5 -29t9 -41.5
q0 -59 -38.5 -87t-96.5 -28q-33 0 -62.5 6t-49.5 14z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="390" 
d="M373 -168q-29 -12 -73.5 -21t-95.5 -9q-42 0 -78 9.5t-62.5 29t-41 49t-14.5 70.5q0 38 11.5 66t29 50t37.5 40t37.5 38t29 43t11.5 54v59h87v-63q0 -40 -10.5 -70t-26.5 -53.5t-34.5 -42t-34.5 -36.5t-26.5 -37t-10.5 -42q0 -46 27.5 -65t80.5 -19q37 0 75 8t63 19z
M163 396q-17 15 -17 41t17 40.5t44 14.5q29 0 46 -14.5t17 -40.5t-17 -40.5t-46 -14.5q-27 0 -44 14z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="677" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110zM306 755l-116 161h112
l86 -161h-82z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="677" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110zM304 755l86 161h112
l-115 -161h-83z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="677" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110zM197 755l102 161h94
l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="677" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110zM190 804q8 31 31 55.5
t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="677" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110zM211 799q-14 13 -14 36
t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM395 799q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="677" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110zM260 771q-28 22 -28 64
q0 43 28.5 64.5t77.5 21.5q50 0 78.5 -21.5t28.5 -64.5q0 -42 -28.5 -63.5t-78.5 -21.5q-49 0 -78 21zM297 835q0 -38 41 -38q23 0 32.5 9.5t9.5 28.5q0 20 -9.5 29t-32.5 9q-41 0 -41 -38z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="960" 
d="M85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5q56 0 93.5 -14.5t59.5 -39.5v44h395v-79h-293v-206h264v-79h-264v-145q0 -44 26.5 -60.5t64.5 -16.5h216v-79h-220q-100 0 -146 41t-46 125v135h-299v-301h-104zM189 380h299v43q0 39 -6.5 71t-23.5 54.5t-46 35
t-74 12.5t-73.5 -12.5t-45.5 -35t-23.5 -54.5t-6.5 -71v-43z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="577" 
d="M58 0zM528 10q-40 -11 -86 -16t-88 -5q-69 0 -124 17t-93.5 57t-58.5 106t-20 164t20 164t58.5 105.5t94.5 56t127 16.5q43 0 87 -5t80 -12l-12 -80q-35 8 -78.5 13t-81.5 5q-47 0 -81.5 -12.5t-57.5 -42.5t-34.5 -80.5t-11.5 -126.5q0 -79 12 -130.5t35.5 -81.5t59 -42
t82.5 -12q38 0 81.5 6.5t81.5 14.5zM232 -125q11 -4 25 -6.5t24 -2.5q19 0 25 6.5t6 20.5t-7 29.5t-16 31.5h74q13 -18 22 -37.5t9 -38.5q0 -35 -21.5 -55.5t-72.5 -20.5q-20 0 -43.5 4t-37.5 9z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="566" 
d="M95 0zM287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220zM271 755l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="566" 
d="M95 0zM287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220zM246 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="566" 
d="M95 0zM287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220zM144 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="566" 
d="M95 0zM287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220zM160 799q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM344 799q-14 13 -14 36t14.5 36t42.5 13
t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="311" 
d="M0 0zM103 0v665h105v-665h-105zM112 755l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="311" 
d="M103 0zM103 0v665h105v-665h-105zM116 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="311" 
d="M6 0zM103 0v665h105v-665h-105zM6 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="311" 
d="M8 0zM103 0v665h105v-665h-105zM22 799q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM206 799q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="692" 
d="M20 297v79h91v289h225q71 0 126.5 -19t93.5 -59t58 -102t20 -147q0 -81 -16 -146t-53 -110t-96.5 -69t-146.5 -24q-50 0 -102.5 6.5t-108.5 18.5v283h-91zM216 77q49 -9 109 -9q105 0 153 64.5t48 205.5q0 67 -13 114t-37 77t-59.5 43.5t-80.5 13.5h-120v-210h161v-79
h-161v-220z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="755" 
d="M103 0zM103 0v665h84l363 -389v389h102v-665h-102v150l-346 366v-516h-101zM221 803q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10
t-12.5 -24z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="689" 
d="M58 0zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5
t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5zM303 755l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="689" 
d="M58 0zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5
t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5zM297 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="689" 
d="M58 0zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5
t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5zM194 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="689" 
d="M58 0zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5
t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5zM189 803q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60
t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="689" 
d="M58 0zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5
t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5zM209 799q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM393 799q-14 13 -14 36
t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M84 169l161 149l-158 148l53 51l161 -148l165 151l54 -52l-162 -150l160 -148l-52 -52l-165 150l-164 -150z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="687" 
d="M71 -26l61 94q-74 82 -74 264q0 92 18.5 157t54.5 106.5t89.5 60.5t123.5 19q48 0 88.5 -9t72.5 -29l57 89l52 -33l-61 -94q38 -42 57 -107.5t19 -159.5q0 -92 -18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-50 0 -91 9.5t-73 29.5l-57 -87zM230 105
q22 -20 50.5 -28.5t63.5 -8.5q43 0 76 13.5t55 44.5t33 81.5t11 124.5q0 57 -7.5 99t-21.5 71zM454 561q-41 35 -110 35q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5q0 -111 28 -168z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM309 755l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM308 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM203 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM218 799q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13z
M402 799q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="596" 
d="M258 755l86 161h112l-115 -161h-83zM243 0v227l-253 438h125l195 -339q34 33 63 73.5t51.5 84t37.5 90t23 91.5h106q-8 -60 -30 -122t-54.5 -119t-73 -107.5t-85.5 -87.5v-229h-105z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="620" 
d="M103 0v665h105v-114h110q63 0 110.5 -10.5t79.5 -35.5t48 -65t16 -100q0 -59 -17 -100.5t-49.5 -67t-78 -37t-102.5 -11.5q-53 0 -117 11v-135h-105zM208 214q56 -10 106 -10q84 0 118 34t34 102q0 38 -8.5 63.5t-26.5 40.5t-46 21.5t-67 6.5h-110v-258z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="546" 
d="M84 0v570q0 82 48.5 126.5t149.5 44.5q48 0 84 -12.5t60.5 -34t36.5 -51t12 -63.5q0 -46 -16 -75t-35 -51t-35 -41t-16 -45q0 -17 12 -33.5t29.5 -35t38 -38t38 -42.5t29.5 -49.5t12 -56.5q0 -35 -12.5 -58.5t-34 -37.5t-51 -20.5t-62.5 -6.5q-28 0 -57.5 4t-54.5 10
l6 73q21 -5 48.5 -9t48.5 -4q35 0 53 11t18 47q0 20 -11 37t-28 32.5t-36 32t-36 36t-28 43.5t-11 56q0 39 15 64t33 47t33 46.5t15 61.5q0 46 -26.5 66.5t-70.5 20.5q-98 0 -98 -95v-570h-101z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="517" 
d="M42 0zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311zM233 569l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="517" 
d="M42 0zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311zM206 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="517" 
d="M42 0zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311zM115 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="517" 
d="M42 0zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311zM109 618q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5l64 -20
q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="517" 
d="M42 0zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311zM130 614q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM314 614
q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="517" 
d="M42 0zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311zM187 584q-28 22 -28 64q0 43 28.5 64.5t77.5 21.5q50 0 78.5 -21.5t28.5 -64.5q0 -42 -28.5 -63.5t-78.5 -21.5
q-49 0 -78 21zM224 648q0 -38 41 -38q23 0 32.5 9.5t9.5 28.5q0 20 -9.5 29t-32.5 9q-41 0 -41 -38z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="806" 
d="M89 474q32 7 70 12.5t75 5.5q52 0 98.5 -15t73.5 -48q25 33 63.5 47.5t91.5 14.5q102 0 149.5 -54.5t47.5 -145.5v-79h-314q1 -37 9 -64.5t26 -45.5t48 -27t75 -9q16 0 35.5 1.5t38 4.5t35 6.5t27.5 6.5l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-52 0 -95.5 10t-75.5 33
q-38 -19 -83 -31t-98 -12q-41 0 -77 8t-63 26t-42.5 48t-15.5 75q0 42 16 70.5t43 45t62 23.5t72 7q28 0 57 -3t52 -7v49q0 25 -9 41.5t-24.5 26t-36.5 13t-45 3.5q-33 0 -69 -5.5t-65 -11.5zM360 89q-18 51 -19 124q-21 2 -45.5 5t-48.5 3q-22 0 -41.5 -3t-34.5 -11.5
t-23.5 -24t-8.5 -40.5q0 -43 28 -62t67 -19q34 0 68 7t58 21zM661 282q0 139 -103 139q-57 0 -83 -35.5t-30 -103.5h216z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="445" 
d="M50 0zM408 8q-29 -8 -62 -13t-67 -5q-51 0 -93 13t-72.5 42.5t-47 77.5t-16.5 119q0 72 17 120.5t48 77t74 40.5t95 12q31 0 63 -4t55 -9l-10 -76q-24 5 -52 8.5t-57 3.5q-30 0 -54 -7.5t-40.5 -27t-25 -53t-8.5 -86.5q0 -48 7 -81.5t22.5 -54t40.5 -29.5t60 -9
q31 0 58.5 4.5t55.5 11.5zM165 -125q11 -4 25 -6.5t24 -2.5q19 0 25 6.5t6 20.5t-7 29.5t-16 31.5h74q13 -18 22 -37.5t9 -38.5q0 -35 -21.5 -55.5t-72.5 -20.5q-20 0 -43.5 4t-37.5 9z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="518" 
d="M50 0zM156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216zM231 569l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="518" 
d="M50 0zM156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216zM225 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="518" 
d="M50 0zM156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216zM121 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="518" 
d="M50 0zM156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216zM136 614q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM320 614q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="273" 
d="M0 0zM86 0v482h101v-482h-101zM96 569l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="273" 
d="M86 0zM86 0v482h101v-482h-101zM94 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="273" 
d="M0 0zM86 0v482h101v-482h-101zM-14 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="273" 
d="M0 0zM86 0v482h101v-482h-101zM2 614q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM186 614q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="557" 
d="M166 543v64l148 27q-17 25 -35 47.5t-41 48.5h99q19 -20 34 -38.5t29 -41.5l112 21v-63l-77 -15q40 -83 56 -165t16 -172q0 -131 -58 -198.5t-171 -67.5q-53 0 -95 14t-71.5 44t-45.5 77.5t-16 115.5q0 129 58 190t162 61q95 0 131 -50q-8 29 -22 67.5t-31 66.5zM187 103
q16 -20 38.5 -28t52.5 -8q29 0 52 8t39 28t24.5 53t8.5 83q0 41 -5 74t-19 55.5t-38 34.5t-62 12q-30 0 -52.5 -8t-38.5 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="587" 
d="M85 0zM85 0v455q19 6 43.5 12.5t52.5 12t58.5 9t60.5 3.5q45 0 83.5 -8.5t67 -28t44.5 -52.5t16 -81v-322h-101v318q0 28 -8.5 47t-23.5 30t-36 15.5t-45 4.5q-28 0 -58 -4.5t-53 -10.5v-400h-101zM143 618q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5
q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="557" 
d="M50 0zM278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM235 569
l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="557" 
d="M50 0zM278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM235 569
l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="557" 
d="M50 0zM278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM128 569
l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="557" 
d="M50 0zM278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM122 618
q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="557" 
d="M50 0zM278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM142 614
q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM326 614q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M262 456q-15 14 -15 38t15.5 37t42.5 13t42.5 -13t15.5 -37t-15.5 -37.5t-42.5 -13.5t-43 13zM67 284v70h470v-70h-470zM262 107q-15 13 -15 38q0 24 15.5 37t42.5 13t42.5 -13t15.5 -37q0 -25 -15.5 -38t-42.5 -13t-43 13z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="557" 
d="M60 -56l60 95q-70 59 -70 202q0 133 59 192t169 59q67 0 114 -22l54 84l49 -29l-54 -86q66 -61 66 -198q0 -133 -59.5 -192t-169.5 -59q-65 0 -110 19l-58 -93zM214 81q25 -14 65 -14q30 0 52.5 8t38.5 28t24 53.5t8 84.5q0 73 -17 112zM348 399q-14 9 -31.5 12.5
t-37.5 3.5q-30 0 -53 -8t-39 -28t-24 -53.5t-8 -84.5q0 -76 18 -115z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM249 569l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM236 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM136 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM150 614q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36
t-42.5 -13q-29 0 -44 13zM334 614q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="577" 
d="M76 0zM111 -99q33 -8 77 -15t81 -7q64 0 93 24t29 81v28q-26 -5 -53.5 -8.5t-53.5 -3.5q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v308h101v-309q0 -27 8.5 -46t23.5 -30t34.5 -16t42.5 -5q28 0 57 4.5t48 10.5v391h101v-484q0 -55 -15 -92.5t-43 -60.5t-69 -33
t-93 -10q-43 0 -90.5 7.5t-81.5 16.5zM236 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="572" 
d="M86 -188v918h101v-251q28 6 56 9.5t54 3.5q48 0 89 -12.5t71.5 -41.5t47.5 -76.5t17 -116.5q0 -76 -17.5 -125.5t-47.5 -78t-69.5 -39.5t-84.5 -11q-29 0 -59.5 3.5t-56.5 7.5v-190h-101zM187 79q28 -5 55 -9t54 -4t49.5 7.5t38.5 27.5t24.5 55t8.5 89q0 51 -9 84t-25 52
t-38.5 26.5t-49.5 7.5q-25 0 -57 -5t-51 -10v-321z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="577" 
d="M76 0zM111 -99q33 -8 77 -15t81 -7q64 0 93 24t29 81v28q-26 -5 -53.5 -8.5t-53.5 -3.5q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v308h101v-309q0 -27 8.5 -46t23.5 -30t34.5 -16t42.5 -5q28 0 57 4.5t48 10.5v391h101v-484q0 -55 -15 -92.5t-43 -60.5t-69 -33
t-93 -10q-43 0 -90.5 7.5t-81.5 16.5zM151 614q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM335 614q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="677" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110zM215 803v66h262v-66h-262
z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="517" 
d="M42 0zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311zM134 616v66h262v-66h-262z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="677" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110zM273 901
q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="517" 
d="M42 0zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7
q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311zM192 715q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5
q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="677" 
d="M85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432q-16 -15 -33.5 -40t-17.5 -48q0 -16 10 -22t26 -6q9 0 23 2t25 7l11 -62q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 37 18 64.5t36 41.5h-28v246h-299v-246h-103z
M188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="517" 
d="M489 -145q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 26 9 46.5t21 35.5q-24 -4 -50.5 -7t-54.5 -3q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5q-34 0 -70 -6t-64 -12
l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311q-16 -15 -33.5 -40t-17.5 -48q0 -16 10 -22t26 -6q9 0 23 2t25 7zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9
v138z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="577" 
d="M58 0zM528 10q-40 -11 -86 -16t-88 -5q-69 0 -124 17t-93.5 57t-58.5 106t-20 164t20 164t58.5 105.5t94.5 56t127 16.5q43 0 87 -5t80 -12l-12 -80q-35 8 -78.5 13t-81.5 5q-47 0 -81.5 -12.5t-57.5 -42.5t-34.5 -80.5t-11.5 -126.5q0 -79 12 -130.5t35.5 -81.5t59 -42
t82.5 -12q38 0 81.5 6.5t81.5 14.5zM273 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="445" 
d="M50 0zM408 8q-29 -8 -62 -13t-67 -5q-51 0 -93 13t-72.5 42.5t-47 77.5t-16.5 119q0 72 17 120.5t48 77t74 40.5t95 12q31 0 63 -4t55 -9l-10 -76q-24 5 -52 8.5t-57 3.5q-30 0 -54 -7.5t-40.5 -27t-25 -53t-8.5 -86.5q0 -48 7 -81.5t22.5 -54t40.5 -29.5t60 -9
q31 0 58.5 4.5t55.5 11.5zM212 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="577" 
d="M58 0zM528 10q-40 -11 -86 -16t-88 -5q-69 0 -124 17t-93.5 57t-58.5 106t-20 164t20 164t58.5 105.5t94.5 56t127 16.5q43 0 87 -5t80 -12l-12 -80q-35 8 -78.5 13t-81.5 5q-47 0 -81.5 -12.5t-57.5 -42.5t-34.5 -80.5t-11.5 -126.5q0 -79 12 -130.5t35.5 -81.5t59 -42
t82.5 -12q38 0 81.5 6.5t81.5 14.5zM165 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="445" 
d="M50 0zM408 8q-29 -8 -62 -13t-67 -5q-51 0 -93 13t-72.5 42.5t-47 77.5t-16.5 119q0 72 17 120.5t48 77t74 40.5t95 12q31 0 63 -4t55 -9l-10 -76q-24 5 -52 8.5t-57 3.5q-30 0 -54 -7.5t-40.5 -27t-25 -53t-8.5 -86.5q0 -48 7 -81.5t22.5 -54t40.5 -29.5t60 -9
q31 0 58.5 4.5t55.5 11.5zM105 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="577" 
d="M58 0zM528 10q-40 -11 -86 -16t-88 -5q-69 0 -124 17t-93.5 57t-58.5 106t-20 164t20 164t58.5 105.5t94.5 56t127 16.5q43 0 87 -5t80 -12l-12 -80q-35 8 -78.5 13t-81.5 5q-47 0 -81.5 -12.5t-57.5 -42.5t-34.5 -80.5t-11.5 -126.5q0 -79 12 -130.5t35.5 -81.5t59 -42
t82.5 -12q38 0 81.5 6.5t81.5 14.5zM271 798q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="445" 
d="M50 0zM408 8q-29 -8 -62 -13t-67 -5q-51 0 -93 13t-72.5 42.5t-47 77.5t-16.5 119q0 72 17 120.5t48 77t74 40.5t95 12q31 0 63 -4t55 -9l-10 -76q-24 5 -52 8.5t-57 3.5q-30 0 -54 -7.5t-40.5 -27t-25 -53t-8.5 -86.5q0 -48 7 -81.5t22.5 -54t40.5 -29.5t60 -9
q31 0 58.5 4.5t55.5 11.5zM211 613q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="577" 
d="M58 0zM528 10q-40 -11 -86 -16t-88 -5q-69 0 -124 17t-93.5 57t-58.5 106t-20 164t20 164t58.5 105.5t94.5 56t127 16.5q43 0 87 -5t80 -12l-12 -80q-35 8 -78.5 13t-81.5 5q-47 0 -81.5 -12.5t-57.5 -42.5t-34.5 -80.5t-11.5 -126.5q0 -79 12 -130.5t35.5 -81.5t59 -42
t82.5 -12q38 0 81.5 6.5t81.5 14.5zM267 755l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="445" 
d="M50 0zM408 8q-29 -8 -62 -13t-67 -5q-51 0 -93 13t-72.5 42.5t-47 77.5t-16.5 119q0 72 17 120.5t48 77t74 40.5t95 12q31 0 63 -4t55 -9l-10 -76q-24 5 -52 8.5t-57 3.5q-30 0 -54 -7.5t-40.5 -27t-25 -53t-8.5 -86.5q0 -48 7 -81.5t22.5 -54t40.5 -29.5t60 -9
q31 0 58.5 4.5t55.5 11.5zM208 569l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="681" 
d="M100 0zM100 665h225q71 0 126.5 -19t93.5 -59t58 -101.5t20 -146.5q0 -82 -16 -147t-53 -110t-96.5 -69t-146.5 -24q-50 0 -102.5 6t-108.5 19v651zM205 77q49 -9 109 -9q105 0 153 65t48 205q0 67 -13 114.5t-37 77t-59.5 43t-80.5 13.5h-120v-509zM289 755l-102 161h87
l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="662" 
d="M385 400q-27 7 -54 11t-54 4q-25 0 -47.5 -7.5t-39 -27t-26 -52t-9.5 -83.5q0 -54 8.5 -88.5t24.5 -55t39 -28t52 -7.5q26 0 53 3.5t53 8.5v322zM486 19q-45 -11 -103 -20t-106 -9t-89 11.5t-72 41t-48.5 78.5t-17.5 124q0 70 17.5 117t47.5 76t70.5 41.5t87.5 12.5
q29 0 56 -4t56 -10v252h101v-711zM536 452l61 213h98l-82 -213h-77z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="692" 
d="M20 0zM20 297v79h91v289h225q71 0 126.5 -19t93.5 -59t58 -102t20 -147q0 -81 -16 -146t-53 -110t-96.5 -69t-146.5 -24q-50 0 -102.5 6.5t-108.5 18.5v283h-91zM216 77q49 -9 109 -9q105 0 153 64.5t48 205.5q0 67 -13 114t-37 77t-59.5 43.5t-80.5 13.5h-120v-210h161
v-79h-161v-220z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="610" 
d="M486 19q-45 -11 -103 -20t-106 -9t-89 11.5t-72 41t-48.5 78.5t-17.5 124q0 70 17.5 117t47.5 76t70.5 41.5t87.5 12.5q29 0 56 -4t56 -10v95h-159v67h159v90h101v-90h94v-67h-94v-554zM385 400q-27 7 -54 11t-54 4q-25 0 -47.5 -7.5t-39 -27t-26 -52t-9.5 -83.5
q0 -54 8.5 -88.5t24.5 -55t39 -28t52 -7.5q26 0 53 3.5t53 8.5v322z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="566" 
d="M95 0zM287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220zM165 802v66h262v-66h-262z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="518" 
d="M50 0zM156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216zM140 616v66h262v-66h-262z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="566" 
d="M95 0zM287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220zM223 900q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24
t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="518" 
d="M50 0zM156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216zM198 715q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="566" 
d="M95 0zM287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220zM253 798q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="518" 
d="M50 0zM156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216zM227 613q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="566" 
d="M287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-33q-16 -15 -33.5 -40t-17.5 -48q0 -16 10 -22t26 -6q9 0 23 2t25 7l11 -62q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 37 18 64.5t36 41.5h-110z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="518" 
d="M488 -163q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 29 11.5 52.5t26.5 39.5q-11 -1 -23 -1.5t-24 -0.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314q1 -36 9 -63.5
t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-22 -8 -39.5 -28t-22.5 -34.5t-5 -22.5q-1 -15 6.5 -25.5t30.5 -10.5q9 0 23 2t25 7zM373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="566" 
d="M95 0zM287 0q-90 0 -141 37.5t-51 120.5v507h399v-79h-294v-206h265v-79h-265v-144q0 -44 24.5 -61t66.5 -17h216v-79h-220zM248 755l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="518" 
d="M50 0zM156 212q1 -36 9 -63.5t26 -45.5t48 -27t76 -9q16 0 35 1.5t37.5 4.5t35 6t27.5 6l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-57 0 -104 11t-80 39.5t-51.5 77t-18.5 123.5q0 70 15.5 118t44.5 77.5t70.5 42.5t92.5 13t88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314z
M373 282q0 139 -103 139q-57 0 -83 -35t-30 -104h216zM223 569l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="685" 
d="M58 0zM608 22q-49 -14 -114.5 -23.5t-128.5 -9.5q-72 0 -128.5 16.5t-96.5 56.5t-61 107t-21 167q0 93 21.5 157.5t62 105t99.5 58.5t135 18q30 0 61 -1.5t59.5 -4.5t53 -6.5t41.5 -6.5l-9 -82q-37 8 -89.5 15t-115.5 7q-54 0 -93.5 -14t-65 -45.5t-38 -81t-12.5 -120.5
q0 -78 12.5 -129.5t37.5 -82t61 -43t83 -12.5q18 0 37.5 1t38.5 3t36 4t29 5v210h-145v81h250v-350zM190 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="572" 
d="M50 0zM106 -99q33 -8 77 -15.5t81 -7.5q64 0 93 25.5t29 82.5v27q-26 -5 -57 -9t-60 -4q-44 0 -84 10t-70 37.5t-47.5 75.5t-17.5 124q0 69 19 116t51 75.5t75.5 41t91.5 12.5q51 0 102 -11t98 -26v-458q0 -55 -15 -92t-43.5 -60t-69 -33t-92.5 -10q-43 0 -91 7t-81 16z
M386 400q-20 5 -46.5 10t-51.5 5q-27 0 -51 -7t-42 -26t-28.5 -51.5t-10.5 -84.5q0 -54 8.5 -87t24.5 -51.5t38.5 -24.5t49.5 -6q26 0 53 4t56 9v310zM123 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="685" 
d="M58 0zM608 22q-49 -14 -114.5 -23.5t-128.5 -9.5q-72 0 -128.5 16.5t-96.5 56.5t-61 107t-21 167q0 93 21.5 157.5t62 105t99.5 58.5t135 18q30 0 61 -1.5t59.5 -4.5t53 -6.5t41.5 -6.5l-9 -82q-37 8 -89.5 15t-115.5 7q-54 0 -93.5 -14t-65 -45.5t-38 -81t-12.5 -120.5
q0 -78 12.5 -129.5t37.5 -82t61 -43t83 -12.5q18 0 37.5 1t38.5 3t36 4t29 5v210h-145v81h250v-350zM268 900q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="572" 
d="M50 0zM106 -99q33 -8 77 -15.5t81 -7.5q64 0 93 25.5t29 82.5v27q-26 -5 -57 -9t-60 -4q-44 0 -84 10t-70 37.5t-47.5 75.5t-17.5 124q0 69 19 116t51 75.5t75.5 41t91.5 12.5q51 0 102 -11t98 -26v-458q0 -55 -15 -92t-43.5 -60t-69 -33t-92.5 -10q-43 0 -91 7t-81 16z
M386 400q-20 5 -46.5 10t-51.5 5q-27 0 -51 -7t-42 -26t-28.5 -51.5t-10.5 -84.5q0 -54 8.5 -87t24.5 -51.5t38.5 -24.5t49.5 -6q26 0 53 4t56 9v310zM199 715q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5
q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="685" 
d="M58 0zM608 22q-49 -14 -114.5 -23.5t-128.5 -9.5q-72 0 -128.5 16.5t-96.5 56.5t-61 107t-21 167q0 93 21.5 157.5t62 105t99.5 58.5t135 18q30 0 61 -1.5t59.5 -4.5t53 -6.5t41.5 -6.5l-9 -82q-37 8 -89.5 15t-115.5 7q-54 0 -93.5 -14t-65 -45.5t-38 -81t-12.5 -120.5
q0 -78 12.5 -129.5t37.5 -82t61 -43t83 -12.5q18 0 37.5 1t38.5 3t36 4t29 5v210h-145v81h250v-350zM296 798q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="572" 
d="M50 0zM106 -99q33 -8 77 -15.5t81 -7.5q64 0 93 25.5t29 82.5v27q-26 -5 -57 -9t-60 -4q-44 0 -84 10t-70 37.5t-47.5 75.5t-17.5 124q0 69 19 116t51 75.5t75.5 41t91.5 12.5q51 0 102 -11t98 -26v-458q0 -55 -15 -92t-43.5 -60t-69 -33t-92.5 -10q-43 0 -91 7t-81 16z
M386 400q-20 5 -46.5 10t-51.5 5q-27 0 -51 -7t-42 -26t-28.5 -51.5t-10.5 -84.5q0 -54 8.5 -87t24.5 -51.5t38.5 -24.5t49.5 -6q26 0 53 4t56 9v310zM229 613q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="685" 
d="M58 0zM608 22q-49 -14 -114.5 -23.5t-128.5 -9.5q-72 0 -128.5 16.5t-96.5 56.5t-61 107t-21 167q0 93 21.5 157.5t62 105t99.5 58.5t135 18q30 0 61 -1.5t59.5 -4.5t53 -6.5t41.5 -6.5l-9 -82q-37 8 -89.5 15t-115.5 7q-54 0 -93.5 -14t-65 -45.5t-38 -81t-12.5 -120.5
q0 -78 12.5 -129.5t37.5 -82t61 -43t83 -12.5q18 0 37.5 1t38.5 3t36 4t29 5v210h-145v81h250v-350zM253 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="572" 
d="M106 -99q33 -8 77 -15.5t81 -7.5q64 0 93 25.5t29 82.5v27q-26 -5 -57 -9t-60 -4q-44 0 -84 10t-70 37.5t-47.5 75.5t-17.5 124q0 69 19 116t51 75.5t75.5 41t91.5 12.5q51 0 102 -11t98 -26v-458q0 -55 -15 -92t-43.5 -60t-69 -33t-92.5 -10q-43 0 -91 7t-81 16z
M386 400q-20 5 -46.5 10t-51.5 5q-27 0 -51 -7t-42 -26t-28.5 -51.5t-10.5 -84.5q0 -54 8.5 -87t24.5 -51.5t38.5 -24.5t49.5 -6q26 0 53 4t56 9v310zM213 569l115 161h83l-86 -161h-112z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="748" 
d="M103 0zM103 0v665h105v-281h332v281h105v-665h-105v301h-332v-301h-105zM224 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="587" 
d="M0 0zM85 0v730h101v-254q29 6 60.5 11t62.5 5q42 0 79 -8.5t64 -28.5t43 -52.5t16 -80.5v-322h-101v318q0 28 -8.5 47t-24 30t-36 15.5t-44.5 4.5q-28 0 -58 -5t-53 -10v-400h-101zM-14 782l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="749" 
d="M111 0v492h-91v65h91v108h105v-108h332v108h105v-108h91v-65h-91v-492h-105v301h-332v-301h-105zM216 384h332v108h-332v-108z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="614" 
d="M112 0v572h-98v68h98v90h101v-90h159v-68h-159v-96q29 6 60.5 11t62.5 5q42 0 79 -8.5t64 -28.5t43 -52.5t16 -80.5v-322h-101v318q0 55 -31 76t-82 21q-28 0 -58 -5t-53 -10v-400h-101z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="311" 
d="M1 0zM103 0v665h105v-665h-105zM1 803q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="273" 
d="M0 0zM86 0v482h101v-482h-101zM-21 618q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="311" 
d="M26 0zM103 0v665h105v-665h-105zM26 802v66h262v-66h-262z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="273" 
d="M6 0zM86 0v482h101v-482h-101zM6 616v66h262v-66h-262z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="311" 
d="M8 0zM103 0v665h105v-665h-105zM84 901q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="273" 
d="M0 0zM86 0v482h101v-482h-101zM64 715q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="311" 
d="M252 -169q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 37 18 64.5t36 41.5h-28v665h105v-665q-16 -15 -33.5 -40t-17.5 -48q0 -16 10 -22t26 -6q9 0 23 2t25 7z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="273" 
d="M231 -169q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 37 18 64.5t36 41.5h-24v482h101v-482q-16 -15 -33.5 -40t-17.5 -48q0 -16 10 -22t26 -6q9 0 23 2t25 7zM89 620q-17 15 -17 42q0 28 17 43t47 15q64 0 64 -58q0 -27 -16.5 -42t-47.5 -15q-30 0 -47 15z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="311" 
d="M98 0zM113 798q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13zM103 0v665h105v-665h-105z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="273" 
d="M86 0v482h101v-482h-101z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="306" 
d="M0 0zM-28 -136q34 29 59 54.5t41 50t23.5 50t7.5 55.5v591h105v-589q0 -46 -13.5 -83.5t-38 -70t-59 -62t-75.5 -59.5zM5 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="273" 
d="M0 0zM-25 -144q35 31 57 55.5t34 48.5t16 49t4 57v416h101v-415q0 -45 -8 -79.5t-27 -64.5t-51 -59t-79 -63zM-12 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="626" 
d="M103 0zM103 0v665h105v-354l300 354h116l-258 -303q36 -11 64 -30t49.5 -44.5t39.5 -58t35 -70.5l71 -159h-117l-58 134q-26 60 -63 103t-93 50l-86 -95v-192h-105zM241 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="539" 
d="M86 0zM86 0v730h101v-472l214 224h111l-185 -194q57 -14 91.5 -55.5t54.5 -98.5l46 -134h-109l-43 124q-17 50 -46 72t-67 26l-67 -64v-158h-101zM183 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="515" 
d="M103 0zM103 0v665h105v-586h284v-79h-389zM113 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="273" 
d="M86 0zM86 0v730h101v-730h-101zM95 780l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="515" 
d="M103 0zM103 0v665h105v-586h284v-79h-389zM185 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="273" 
d="M9 0zM86 0v730h101v-730h-101zM9 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="515" 
d="M6 0zM103 0v665h105v-586h284v-79h-389zM108 755l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="370" 
d="M86 0v730h101v-730h-101zM244 452l61 213h98l-82 -213h-77z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="502" 
d="M111 0v259l-91 -36v81l91 36v325h105v-284l187 73v-81l-187 -73v-221h284v-79h-389z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="291" 
d="M95 0v312l-75 -29v74l75 30v343h101v-304l75 30v-75l-75 -29v-352h-101z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="755" 
d="M103 0zM103 0v665h84l363 -389v389h102v-665h-102v150l-346 366v-516h-101zM305 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="587" 
d="M85 0zM85 0v455q19 6 43.5 12.5t52.5 12t58.5 9t60.5 3.5q45 0 83.5 -8.5t67 -28t44.5 -52.5t16 -81v-322h-101v318q0 28 -8.5 47t-23.5 30t-36 15.5t-45 4.5q-28 0 -58 -4.5t-53 -10.5v-400h-101zM249 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="755" 
d="M103 0zM103 0v665h84l363 -389v389h102v-665h-102v150l-346 366v-516h-101zM267 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="587" 
d="M85 0zM85 0v455q19 6 43.5 12.5t52.5 12t58.5 9t60.5 3.5q45 0 83.5 -8.5t67 -28t44.5 -52.5t16 -81v-322h-101v318q0 28 -8.5 47t-23.5 30t-36 15.5t-45 4.5q-28 0 -58 -4.5t-53 -10.5v-400h-101zM183 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="755" 
d="M103 0zM103 0v665h84l363 -389v389h102v-665h-102v150l-346 366v-516h-101zM329 755l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="587" 
d="M85 0zM85 0v455q19 6 43.5 12.5t52.5 12t58.5 9t60.5 3.5q45 0 83.5 -8.5t67 -28t44.5 -52.5t16 -81v-322h-101v318q0 28 -8.5 47t-23.5 30t-36 15.5t-45 4.5q-28 0 -58 -4.5t-53 -10.5v-400h-101zM250 569l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="755" 
d="M103 0v665h84l363 -390v390h102v-589q0 -46 -13.5 -83.5t-38 -70t-59 -62t-75.5 -59.5l-49 63q34 29 59 54.5t41.5 50t24.5 50t8 55.5v75l-346 367v-516h-101z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="587" 
d="M85 0v455q19 6 43.5 12.5t52.5 12t58.5 9t60.5 3.5q45 0 83.5 -8.5t67 -28t44.5 -52.5t16 -81v-255q0 -45 -8 -79.5t-27.5 -64.5t-51.5 -59t-79 -63l-47 55q35 31 57 55.5t34 48.5t16.5 49t4.5 57v252q0 28 -8.5 47t-23.5 30t-36 15.5t-45 4.5q-28 0 -58 -4.5t-53 -10.5
v-400h-101z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="687" 
d="M58 0zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5
t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5zM212 802v66h262v-66h-262z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="557" 
d="M50 0zM278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM147 616v66
h262v-66h-262z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="687" 
d="M58 0zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5
t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5zM271 900q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5
t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="557" 
d="M50 0zM278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM205 715
q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="687" 
d="M58 0zM220 8q-53 19 -89 60.5t-54.5 106.5t-18.5 157t18.5 157t54.5 106.5t89.5 60.5t123.5 19q69 0 122.5 -19t89.5 -60.5t54.5 -106.5t18.5 -157t-18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-70 0 -124 19zM212 126q23 -31 55.5 -44.5t76.5 -13.5q43 0 76 13.5
t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5t11 -124.5t33 -81.5zM374 754l61 161h104l-91 -161h-74zM244 754l39 161h93l-60 -161h-72z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="557" 
d="M50 0zM278 -10q-110 0 -169 59t-59 192t59 192t169 59t169.5 -59t59.5 -192t-59.5 -192t-169.5 -59zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM304 569
l61 161h104l-91 -161h-74zM174 569l39 161h93l-60 -161h-72z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="991" 
d="M747 0q-39 0 -71.5 2.5t-57.5 11t-42.5 23.5t-27.5 41q-32 -46 -83.5 -67.5t-123.5 -21.5q-69 0 -121.5 19t-88.5 60t-54.5 106t-18.5 157t18.5 157.5t54.5 107t89.5 60.5t122.5 19q62 0 108 -17t77 -54v61h390v-79h-293v-206h264v-79h-264v-145q0 -44 26 -60.5t64 -16.5
h217v-79h-185zM212 126q22 -31 55 -44.5t77 -13.5q43 0 76 13.5t55 44.5t33 81.5t11 124.5t-11 124.5t-33 81.5t-55 44.5t-76 13.5q-44 0 -77 -13.5t-55 -44.5t-33.5 -81.5t-11.5 -124.5t11.5 -124.5t33.5 -81.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="871" 
d="M509 212q1 -36 8.5 -63.5t25.5 -45.5t48 -27t76 -9q16 0 35 1.5t38 4.5t35.5 6t26.5 6l9 -74q-29 -8 -70 -14.5t-85 -6.5q-71 0 -121 18t-79 66q-26 -44 -70.5 -64t-107.5 -20q-110 0 -169 59t-59 192t59 192t169 59q63 0 106.5 -20t69.5 -63q24 45 67.5 64t104.5 19
q51 0 88 -14.5t61.5 -40.5t36 -63t11.5 -83v-79h-314zM187 103q16 -20 39 -28t53 -8t52.5 8t38.5 28t24 53.5t8 84.5t-8 84.5t-24 53.5t-38.5 28t-52.5 8t-53 -8t-39 -28t-24 -53.5t-8 -84.5t8 -84.5t24 -53.5zM725 282q0 139 -103 139q-57 0 -82.5 -35t-30.5 -104h216z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="641" 
d="M103 0zM103 0v665h215q63 0 110.5 -10t79.5 -34t48 -63.5t16 -98.5q0 -40 -10 -69.5t-27 -50.5t-40.5 -35t-51.5 -23q37 -14 57 -42t37 -75l59 -164h-109l-52 141q-9 24 -19 43.5t-24 34t-34 22.5t-48 8q-24 0 -49.5 1.5t-52.5 6.5v-257h-105zM208 336q29 -5 55.5 -7
t50.5 -2q84 0 118 32t34 100q0 38 -8.5 62.5t-26.5 39t-46 20t-67 5.5h-110v-250zM268 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="365" 
d="M84 0zM84 0v455q62 19 124.5 27.5t137.5 8.5v-80q-11 1 -23 1h-24q-32 0 -62 -4t-52 -10v-398h-101zM165 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="641" 
d="M103 0zM103 0v665h215q63 0 110.5 -10t79.5 -34t48 -63.5t16 -98.5q0 -40 -10 -69.5t-27 -50.5t-40.5 -35t-51.5 -23q37 -14 57 -42t37 -75l59 -164h-109l-52 141q-9 24 -19 43.5t-24 34t-34 22.5t-48 8q-24 0 -49.5 1.5t-52.5 6.5v-257h-105zM208 336q29 -5 55.5 -7
t50.5 -2q84 0 118 32t34 100q0 38 -8.5 62.5t-26.5 39t-46 20t-67 5.5h-110v-250zM236 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="365" 
d="M6 0zM84 0v455q62 19 124.5 27.5t137.5 8.5v-80q-11 1 -23 1h-24q-32 0 -62 -4t-52 -10v-398h-101zM6 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="641" 
d="M103 0zM103 0v665h215q63 0 110.5 -10t79.5 -34t48 -63.5t16 -98.5q0 -40 -10 -69.5t-27 -50.5t-40.5 -35t-51.5 -23q37 -14 57 -42t37 -75l59 -164h-109l-52 141q-9 24 -19 43.5t-24 34t-34 22.5t-48 8q-24 0 -49.5 1.5t-52.5 6.5v-257h-105zM208 336q29 -5 55.5 -7
t50.5 -2q84 0 118 32t34 100q0 38 -8.5 62.5t-26.5 39t-46 20t-67 5.5h-110v-250zM269 755l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="365" 
d="M67 0zM84 0v455q62 19 124.5 27.5t137.5 8.5v-80q-11 1 -23 1h-24q-32 0 -62 -4t-52 -10v-398h-101zM169 569l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="550" 
d="M57 0zM75 102q38 -11 87.5 -22.5t106.5 -11.5q53 0 87.5 16t34.5 60q0 31 -24.5 59t-62 55t-80.5 55t-80.5 59t-62 66t-24.5 78q0 45 19.5 75.5t52.5 49t78 26.5t96 8q22 0 46 -1.5t46.5 -3.5t41.5 -5t33 -6l-9 -80q-15 3 -35 6t-41.5 5.5t-43 4t-38.5 1.5
q-30 0 -55 -2.5t-43 -10.5t-28 -22.5t-10 -37.5q0 -27 24.5 -52t61.5 -50t80 -52.5t80 -59.5t61.5 -70.5t24.5 -85.5q0 -45 -16.5 -76.5t-46.5 -51t-72.5 -28t-93.5 -8.5q-63 0 -116.5 11.5t-91.5 23.5zM196 754l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="459" 
d="M49 0zM66 91q14 -5 32.5 -9t39.5 -7.5t42 -5.5t41 -2q46 0 69 10.5t23 41.5q0 23 -19.5 39.5t-49 31.5t-63.5 30t-63.5 34t-49 45t-19.5 62q0 39 17 64.5t46.5 40t69 20.5t83.5 6q37 0 73 -3.5t59 -7.5l-9 -75q-24 5 -56 8.5t-68 3.5q-53 0 -83.5 -11t-30.5 -43
q0 -19 20 -34t49.5 -29.5t64.5 -30t64.5 -36.5t49.5 -48.5t20 -66.5q0 -35 -15 -59.5t-41 -40t-61.5 -22.5t-77.5 -7q-48 0 -92.5 7t-75.5 18zM176 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="550" 
d="M57 0zM75 102q38 -11 87.5 -22.5t106.5 -11.5q53 0 87.5 16t34.5 60q0 31 -24.5 59t-62 55t-80.5 55t-80.5 59t-62 66t-24.5 78q0 45 19.5 75.5t52.5 49t78 26.5t96 8q22 0 46 -1.5t46.5 -3.5t41.5 -5t33 -6l-9 -80q-15 3 -35 6t-41.5 5.5t-43 4t-38.5 1.5
q-30 0 -55 -2.5t-43 -10.5t-28 -22.5t-10 -37.5q0 -27 24.5 -52t61.5 -50t80 -52.5t80 -59.5t61.5 -70.5t24.5 -85.5q0 -45 -16.5 -76.5t-46.5 -51t-72.5 -28t-93.5 -8.5q-63 0 -116.5 11.5t-91.5 23.5zM110 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="459" 
d="M49 0zM66 91q14 -5 32.5 -9t39.5 -7.5t42 -5.5t41 -2q46 0 69 10.5t23 41.5q0 23 -19.5 39.5t-49 31.5t-63.5 30t-63.5 34t-49 45t-19.5 62q0 39 17 64.5t46.5 40t69 20.5t83.5 6q37 0 73 -3.5t59 -7.5l-9 -75q-24 5 -56 8.5t-68 3.5q-53 0 -83.5 -11t-30.5 -43
q0 -19 20 -34t49.5 -29.5t64.5 -30t64.5 -36.5t49.5 -48.5t20 -66.5q0 -35 -15 -59.5t-41 -40t-61.5 -22.5t-77.5 -7q-48 0 -92.5 7t-75.5 18zM86 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="550" 
d="M57 0zM75 102q38 -11 87.5 -22.5t106.5 -11.5q53 0 87.5 16t34.5 60q0 31 -24.5 59t-62 55t-80.5 55t-80.5 59t-62 66t-24.5 78q0 45 19.5 75.5t52.5 49t78 26.5t96 8q22 0 46 -1.5t46.5 -3.5t41.5 -5t33 -6l-9 -80q-15 3 -35 6t-41.5 5.5t-43 4t-38.5 1.5
q-30 0 -55 -2.5t-43 -10.5t-28 -22.5t-10 -37.5q0 -27 24.5 -52t61.5 -50t80 -52.5t80 -59.5t61.5 -70.5t24.5 -85.5q0 -45 -16.5 -76.5t-46.5 -51t-72.5 -28t-93.5 -8.5q-63 0 -116.5 11.5t-91.5 23.5zM188 -125q11 -4 25 -6.5t24 -2.5q19 0 25 6.5t6 20.5t-7 29.5
t-16 31.5h74q13 -18 22 -37.5t9 -38.5q0 -35 -21.5 -55.5t-72.5 -20.5q-20 0 -43.5 4t-37.5 9z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="459" 
d="M49 0zM66 91q14 -5 32.5 -9t39.5 -7.5t42 -5.5t41 -2q46 0 69 10.5t23 41.5q0 23 -19.5 39.5t-49 31.5t-63.5 30t-63.5 34t-49 45t-19.5 62q0 39 17 64.5t46.5 40t69 20.5t83.5 6q37 0 73 -3.5t59 -7.5l-9 -75q-24 5 -56 8.5t-68 3.5q-53 0 -83.5 -11t-30.5 -43
q0 -19 20 -34t49.5 -29.5t64.5 -30t64.5 -36.5t49.5 -48.5t20 -66.5q0 -35 -15 -59.5t-41 -40t-61.5 -22.5t-77.5 -7q-48 0 -92.5 7t-75.5 18zM158 -125q11 -4 25 -6.5t24 -2.5q19 0 25 6.5t6 20.5t-7 29.5t-16 31.5h74q13 -18 22 -37.5t9 -38.5q0 -35 -21.5 -55.5
t-72.5 -20.5q-20 0 -43.5 4t-37.5 9z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="550" 
d="M57 0zM75 102q38 -11 87.5 -22.5t106.5 -11.5q53 0 87.5 16t34.5 60q0 31 -24.5 59t-62 55t-80.5 55t-80.5 59t-62 66t-24.5 78q0 45 19.5 75.5t52.5 49t78 26.5t96 8q22 0 46 -1.5t46.5 -3.5t41.5 -5t33 -6l-9 -80q-15 3 -35 6t-41.5 5.5t-43 4t-38.5 1.5
q-30 0 -55 -2.5t-43 -10.5t-28 -22.5t-10 -37.5q0 -27 24.5 -52t61.5 -50t80 -52.5t80 -59.5t61.5 -70.5t24.5 -85.5q0 -45 -16.5 -76.5t-46.5 -51t-72.5 -28t-93.5 -8.5q-63 0 -116.5 11.5t-91.5 23.5zM207 755l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="459" 
d="M49 0zM66 91q14 -5 32.5 -9t39.5 -7.5t42 -5.5t41 -2q46 0 69 10.5t23 41.5q0 23 -19.5 39.5t-49 31.5t-63.5 30t-63.5 34t-49 45t-19.5 62q0 39 17 64.5t46.5 40t69 20.5t83.5 6q37 0 73 -3.5t59 -7.5l-9 -75q-24 5 -56 8.5t-68 3.5q-53 0 -83.5 -11t-30.5 -43
q0 -19 20 -34t49.5 -29.5t64.5 -30t64.5 -36.5t49.5 -48.5t20 -66.5q0 -35 -15 -59.5t-41 -40t-61.5 -22.5t-77.5 -7q-48 0 -92.5 7t-75.5 18zM179 569l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="575" 
d="M17 0zM235 0v586h-218v79h541v-79h-218v-586h-105zM157 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="382" 
d="M26 0zM206 120q0 -29 16 -40.5t42 -11.5q23 0 48 4.5t42 9.5l3 -76q-23 -8 -50 -12t-55 -4q-65 0 -106 31t-41 102v284h-79v75h79l23 139h78v-139h140v-75h-140v-287zM126 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="575" 
d="M17 0zM235 0v586h-218v79h541v-79h-218v-586h-105zM239 755l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="402" 
d="M265 525l46 140h89l-67 -140h-68zM206 120q0 -29 16 -40.5t42 -11.5q23 0 48 4.5t42 9.5l3 -76q-23 -8 -50 -12t-55 -4q-65 0 -106 31t-41 102v284h-79v75h79l23 139h78v-139h140v-75h-140v-287z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="569" 
d="M232 0v284h-176v79h176v223h-218v79h541v-79h-218v-223h175v-79h-175v-284h-105z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="393" 
d="M35 210v74h79v123h-79v75h79l23 139h78v-139h140v-75h-140v-123h119v-74h-119v-90q0 -29 16 -40.5t42 -11.5q23 0 48 4.5t42 9.5l3 -76q-23 -8 -50 -12t-55 -4q-65 0 -106 31t-41 102v87h-79z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM196 803q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5
l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM130 618q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5
q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM221 802v66h262v-66h-262z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM159 616v66h262v-66h-262z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM278 900q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41
t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM218 715q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76
q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM274 771q-28 22 -28 64q0 43 28.5 64.5t77.5 21.5q50 0 78.5 -21.5t28.5 -64.5q0 -42 -28.5 -63.5
t-78.5 -21.5q-49 0 -78 21zM311 835q0 -38 41 -38q23 0 32.5 9.5t9.5 28.5q0 20 -9.5 29t-32.5 9q-41 0 -41 -38z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM212 584q-28 22 -28 64q0 43 28.5 64.5t77.5 21.5q50 0 78.5 -21.5
t28.5 -64.5q0 -42 -28.5 -63.5t-78.5 -21.5q-49 0 -78 21zM249 648q0 -38 41 -38q23 0 32.5 9.5t9.5 28.5q0 20 -9.5 29t-32.5 9q-41 0 -41 -38z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="705" 
d="M91 0zM196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-635q-44 -13 -106.5 -27t-149.5 -14q-72 0 -122 17t-81.5 48.5t-45.5 75.5t-14 98v437h105v-441zM366 755l61 161h104l-91 -161h-74zM236 755l39 161h93l-60 -161h-72z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="577" 
d="M76 0zM177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319zM312 569l61 161h104l-91 -161h-74zM182 569l39 161h93l-60 -161h-72z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="705" 
d="M196 224q0 -70 37.5 -113t123.5 -43q46 0 84 6t64 13v578h105v-636q-16 -15 -33.5 -40t-17.5 -48q0 -16 10 -22t26 -6q9 0 23 2t25 7l11 -62q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 25 8.5 45.5t20.5 35.5q-33 -6 -71 -10.5t-83 -4.5q-72 0 -122 17t-81.5 48.5
t-45.5 75.5t-14 98v437h105v-441z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="577" 
d="M177 163q0 -27 8.5 -45.5t23.5 -30t34.5 -16t42.5 -4.5q28 0 57 4.5t48 9.5v401h101v-457q-16 -15 -33.5 -40t-17.5 -48q0 -16 10 -22t26 -6q9 0 23 2t25 7l11 -62q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 25 8.5 45.5t20.5 35.5q-26 -4 -53 -7t-53 -3
q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v318h101v-319z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="910" 
d="M304 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87zM193 0l-183 665h107l140 -548l152 419h98l149 -423q49 112 86.5 246.5t58.5 305.5h99q-26 -185 -74 -346t-123 -319h-98l-149 424l-159 -424h-104z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="837" 
d="M269 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87zM172 0l-158 482h104l123 -396l130 396h107l143 -401q37 76 67 176t44 225h91q-16 -135 -53 -252t-98 -230h-114l-134 378l-127 -378h-125z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="596" 
d="M157 755l102 161h94l102 -161h-86l-62 103l-63 -103h-87zM243 0v227l-253 438h125l195 -339q34 33 63 73.5t51.5 84t37.5 90t23 91.5h106q-8 -60 -30 -122t-54.5 -119t-73 -107.5t-85.5 -87.5v-229h-105z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="577" 
d="M76 0zM131 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87zM111 -99q33 -8 77 -15t81 -7q64 0 93 24t29 81v28q-26 -5 -53.5 -8.5t-53.5 -3.5q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v308h101v-309q0 -27 8.5 -46t23.5 -30t34.5 -16t42.5 -5q28 0 57 4.5
t48 10.5v391h101v-484q0 -55 -15 -92.5t-43 -60.5t-69 -33t-93 -10q-43 0 -90.5 7.5t-81.5 16.5z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="596" 
d="M178 799q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM362 799q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13zM243 0v227l-253 438h125l195 -339q34 33 63 73.5t51.5 84t37.5 90t23 91.5h106
q-8 -60 -30 -122t-54.5 -119t-73 -107.5t-85.5 -87.5v-229h-105z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="606" 
d="M50 0zM50 0v72l371 514h-359v79h489v-73l-368 -513h377v-79h-510zM256 755l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="492" 
d="M41 0zM41 0v66l281 341h-272v75h395v-65l-278 -342h288v-75h-414zM204 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="606" 
d="M50 0zM50 0v72l371 514h-359v79h489v-73l-368 -513h377v-79h-510zM259 798q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="492" 
d="M41 0zM41 0v66l281 341h-272v75h395v-65l-278 -342h288v-75h-414zM207 613q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="606" 
d="M50 0zM50 0v72l371 514h-359v79h489v-73l-368 -513h377v-79h-510zM255 755l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="492" 
d="M41 0zM202 569l-102 161h87l63 -104l62 104h86l-102 -161h-94zM41 0v66l281 341h-272v75h395v-65l-278 -342h288v-75h-414z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="356" 
d="M107 0v407h-78v75h78v88q0 88 52.5 129.5t140.5 41.5q23 0 49 -2.5t46 -5.5l-11 -76q-17 3 -37.5 5t-38.5 2q-43 0 -71.5 -19.5t-28.5 -74.5v-570h-101z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M194 0v313h-127v70h127v105q0 100 54.5 143.5t147.5 43.5q36 0 75.5 -3.5t72.5 -10.5l-11 -80q-29 5 -61.5 10t-61.5 5q-24 0 -44.5 -4.5t-35.5 -16t-23 -31t-8 -49.5v-112h223v-70h-223v-313h-105z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="677" 
d="M378 915q32 -8 49.5 -28t17.5 -53q0 -42 -28.5 -63.5t-78.5 -21.5q-49 0 -77.5 21.5t-28.5 63.5q0 35 19 55.5t52 27.5l57 100h97zM297 834q0 -38 41 -38q23 0 32.5 9.5t9.5 28.5q0 20 -9.5 29t-32.5 9q-41 0 -41 -38zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5
t121 -17.5t78.5 -50t42 -77t12.5 -98.5v-432h-105v246h-299v-246h-103zM188 325h299v110q0 35 -7 64.5t-24.5 51t-46 33.5t-71.5 12q-44 0 -72.5 -12t-46 -33.5t-24.5 -51t-7 -64.5v-110z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="517" 
d="M304 729q32 -8 49.5 -28t17.5 -53q0 -42 -28.5 -63.5t-78.5 -21.5q-49 0 -77.5 21.5t-28.5 63.5q0 35 19 55.5t52 27.5l57 100h97zM223 648q0 -38 41 -38q23 0 32.5 9.5t9.5 28.5q0 20 -9.5 29t-32.5 9q-41 0 -41 -38zM344 212q-21 4 -46 6.5t-51 2.5q-22 0 -41.5 -3
t-34 -11.5t-23 -24t-8.5 -40.5q0 -46 29.5 -63.5t71.5 -17.5q30 0 56.5 4t46.5 9v138zM445 24q-38 -11 -91.5 -22.5t-114.5 -11.5q-42 0 -78 8t-62.5 26.5t-41.5 48.5t-15 75q0 42 16.5 70t43.5 45t63 24t75 7q32 0 57.5 -2.5t46.5 -6.5v47q0 50 -32.5 67.5t-81.5 17.5
q-34 0 -70 -6t-64 -12l-7 76q32 7 70.5 12t75.5 5q42 0 80.5 -7.5t67 -26t45.5 -48.5t17 -75v-311z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="960" 
d="M85 0zM85 0v432q0 54 12.5 98.5t42 77t78 50t120.5 17.5q56 0 93.5 -14.5t59.5 -39.5v44h395v-79h-293v-206h264v-79h-264v-145q0 -44 26.5 -60.5t64.5 -16.5h216v-79h-220q-100 0 -146 41t-46 125v135h-299v-301h-104zM189 380h299v43q0 39 -6.5 71t-23.5 54.5t-46 35
t-74 12.5t-73.5 -12.5t-45.5 -35t-23.5 -54.5t-6.5 -71v-43zM407 754l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="806" 
d="M42 0zM89 474q32 7 70 12.5t75 5.5q52 0 98.5 -15t73.5 -48q25 33 63.5 47.5t91.5 14.5q102 0 149.5 -54.5t47.5 -145.5v-79h-314q1 -37 9 -64.5t26 -45.5t48 -27t75 -9q16 0 35.5 1.5t38 4.5t35 6.5t27.5 6.5l9 -74q-29 -8 -70.5 -14.5t-84.5 -6.5q-52 0 -95.5 10
t-75.5 33q-38 -19 -83 -31t-98 -12q-41 0 -77 8t-63 26t-42.5 48t-15.5 75q0 42 16 70.5t43 45t62 23.5t72 7q28 0 57 -3t52 -7v49q0 25 -9 41.5t-24.5 26t-36.5 13t-45 3.5q-33 0 -69 -5.5t-65 -11.5zM360 89q-18 51 -19 124q-21 2 -45.5 5t-48.5 3q-22 0 -41.5 -3
t-34.5 -11.5t-23.5 -24t-8.5 -40.5q0 -43 28 -62t67 -19q34 0 68 7t58 21zM661 282q0 139 -103 139q-57 0 -83 -35.5t-30 -103.5h216zM372 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="687" 
d="M58 0zM71 -26l61 94q-74 82 -74 264q0 92 18.5 157t54.5 106.5t89.5 60.5t123.5 19q48 0 88.5 -9t72.5 -29l57 89l52 -33l-61 -94q38 -42 57 -107.5t19 -159.5q0 -92 -18.5 -157t-54.5 -106.5t-89.5 -60.5t-122.5 -19q-50 0 -91 9.5t-73 29.5l-57 -87zM230 105
q22 -20 50.5 -28.5t63.5 -8.5q43 0 76 13.5t55 44.5t33 81.5t11 124.5q0 57 -7.5 99t-21.5 71zM454 561q-41 35 -110 35q-44 0 -76.5 -13.5t-55 -44.5t-33.5 -81.5t-11 -124.5q0 -111 28 -168z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="557" 
d="M50 0zM60 -56l60 95q-70 59 -70 202q0 133 59 192t169 59q67 0 114 -22l54 84l49 -29l-54 -86q66 -61 66 -198q0 -133 -59.5 -192t-169.5 -59q-65 0 -110 19l-58 -93zM214 81q25 -14 65 -14q30 0 52.5 8t38.5 28t24 53.5t8 84.5q0 73 -17 112zM348 399q-14 9 -31.5 12.5
t-37.5 3.5q-30 0 -53 -8t-39 -28t-24 -53.5t-8 -84.5q0 -76 18 -115zM235 569l86 161h112l-115 -161h-83z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="550" 
d="M57 0zM75 102q38 -11 87.5 -22.5t106.5 -11.5q53 0 87.5 16t34.5 60q0 31 -24.5 59t-62 55t-80.5 55t-80.5 59t-62 66t-24.5 78q0 45 19.5 75.5t52.5 49t78 26.5t96 8q22 0 46 -1.5t46.5 -3.5t41.5 -5t33 -6l-9 -80q-15 3 -35 6t-41.5 5.5t-43 4t-38.5 1.5
q-30 0 -55 -2.5t-43 -10.5t-28 -22.5t-10 -37.5q0 -27 24.5 -52t61.5 -50t80 -52.5t80 -59.5t61.5 -70.5t24.5 -85.5q0 -45 -16.5 -76.5t-46.5 -51t-72.5 -28t-93.5 -8.5q-63 0 -116.5 11.5t-91.5 23.5zM154 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="459" 
d="M49 0zM66 91q14 -5 32.5 -9t39.5 -7.5t42 -5.5t41 -2q46 0 69 10.5t23 41.5q0 23 -19.5 39.5t-49 31.5t-63.5 30t-63.5 34t-49 45t-19.5 62q0 39 17 64.5t46.5 40t69 20.5t83.5 6q37 0 73 -3.5t59 -7.5l-9 -75q-24 5 -56 8.5t-68 3.5q-53 0 -83.5 -11t-30.5 -43
q0 -19 20 -34t49.5 -29.5t64.5 -30t64.5 -36.5t49.5 -48.5t20 -66.5q0 -35 -15 -59.5t-41 -40t-61.5 -22.5t-77.5 -7q-48 0 -92.5 7t-75.5 18zM141 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="dotlessj" unicode="&#x237;" horiz-adv-x="273" 
d="M-25 -144q35 31 57 55.5t34 48.5t16 49t4 57v416h101v-415q0 -45 -8 -79.5t-27 -64.5t-51 -59t-79 -63z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="550" 
d="M125 569l102 161h94l102 -161h-86l-62 103l-63 -103h-87z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="550" 
d="M227 569l-102 161h87l63 -104l62 104h86l-102 -161h-94z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="550" 
d="M202 715q1 -31 18.5 -48.5t54.5 -17.5t54.5 17.5t18.5 48.5h76q-1 -35 -12.5 -59.5t-31.5 -41t-47 -24t-58 -7.5q-32 0 -58.5 7.5t-46.5 24t-31.5 41t-12.5 59.5h76z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="550" 
d="M231 613q-15 14 -15 37t15 37t44 14t44 -14t15 -37t-15 -36.5t-44 -13.5t-44 13z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="550" 
d="M196 584q-28 22 -28 64q0 43 28.5 64.5t77.5 21.5q50 0 78.5 -21.5t28.5 -64.5q0 -42 -28.5 -63.5t-78.5 -21.5q-49 0 -78 21zM233 648q0 -38 41 -38q23 0 32.5 9.5t9.5 28.5q0 20 -9.5 29t-32.5 9q-41 0 -41 -38z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="550" 
d="M361 -169q-33 -14 -76 -14q-40 0 -69.5 17.5t-29.5 59.5q0 37 18 64.5t36 41.5h77q-16 -15 -33.5 -40t-17.5 -48q0 -16 10 -22t26 -6q9 0 23 2t25 7z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="550" 
d="M119 618q8 31 31 55.5t63 24.5q19 0 35 -5t30.5 -11.5t27.5 -11.5t25 -5q18 0 24 11.5t12 25.5l64 -20q-12 -36 -34.5 -60t-60.5 -24q-16 0 -31.5 5t-30 11.5t-27.5 11.5t-24 5q-20 0 -27.5 -10t-12.5 -24z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="550" 
d="M279 569l61 161h104l-91 -161h-74zM149 569l39 161h93l-60 -161h-72z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" 
d="M30 0zM30 0l221 665h101l222 -665h-544zM140 65h325l-163 508z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="693" 
d="M46 0zM46 0v71h134q-56 38 -87 101.5t-31 165.5q0 89 19 153t55.5 105t89 60t120.5 19q67 0 120 -19t90 -60t56 -105t19 -153q0 -102 -31.5 -165.5t-86.5 -101.5h134v-71h-262v70q38 19 66.5 41.5t48 53.5t29 73t9.5 100q0 73 -12 123.5t-36 82t-60 45t-84 13.5
t-83.5 -13.5t-59.5 -45t-36 -82t-12 -123.5q0 -58 10 -100t29 -73t47.5 -53.5t66.5 -41.5v-70h-262z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="580" 
d="M79 0zM79 -188v670h101v-319q0 -27 8.5 -46t23.5 -30t34.5 -16t42.5 -5q28 0 57 5t48 10v401h101v-456q-40 -14 -95.5 -25t-112.5 -11q-32 0 -59 6t-48 21v-205h-101z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="624" 
d="M119 0v407h-95v75h576v-75h-94v-407h-104v407h-178v-407h-105z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="910" 
d="M412 755l-116 161h112l86 -161h-82zM193 0l-183 665h107l140 -548l152 419h98l149 -423q49 112 86.5 246.5t58.5 305.5h99q-26 -185 -74 -346t-123 -319h-98l-149 424l-159 -424h-104z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="837" 
d="M379 569l-116 161h112l86 -161h-82zM172 0l-158 482h104l123 -396l130 396h107l143 -401q37 76 67 176t44 225h91q-16 -135 -53 -252t-98 -230h-114l-134 378l-127 -378h-125z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="910" 
d="M419 755l86 161h112l-115 -161h-83zM193 0l-183 665h107l140 -548l152 419h98l149 -423q49 112 86.5 246.5t58.5 305.5h99q-26 -185 -74 -346t-123 -319h-98l-149 424l-159 -424h-104z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="837" 
d="M381 569l86 161h112l-115 -161h-83zM172 0l-158 482h104l123 -396l130 396h107l143 -401q37 76 67 176t44 225h91q-16 -135 -53 -252t-98 -230h-114l-134 378l-127 -378h-125z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="910" 
d="M318 799q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM502 799q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13zM193 0l-183 665h107l140 -548l152 419h98l149 -423q49 112 86.5 246.5t58.5 305.5h99
q-26 -185 -74 -346t-123 -319h-98l-149 424l-159 -424h-104z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="837" 
d="M283 614q-14 13 -14 36t14.5 36t43.5 13q28 0 42.5 -13t14.5 -36t-14.5 -36t-42.5 -13q-29 0 -44 13zM467 614q-14 13 -14 36t14.5 36t42.5 13t43 -13t15 -36t-15 -36t-43 -13t-43 13zM172 0l-158 482h104l123 -396l130 396h107l143 -401q37 76 67 176t44 225h91
q-16 -135 -53 -252t-98 -230h-114l-134 378l-127 -378h-125z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="591" 
d="M275 755l-116 161h112l86 -161h-82zM243 0v227l-253 438h125l195 -339q34 33 63 73.5t51.5 84t37.5 90t23 91.5h106q-8 -60 -30 -122t-54.5 -119t-73 -107.5t-85.5 -87.5v-229h-105z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="577" 
d="M76 0zM111 -99q33 -8 77 -15t81 -7q64 0 93 24t29 81v28q-26 -5 -53.5 -8.5t-53.5 -3.5q-42 0 -80 8.5t-66.5 28.5t-45 53.5t-16.5 83.5v308h101v-309q0 -27 8.5 -46t23.5 -30t34.5 -16t42.5 -5q28 0 57 4.5t48 10.5v391h101v-484q0 -55 -15 -92.5t-43 -60.5t-69 -33
t-93 -10q-43 0 -90.5 7.5t-81.5 16.5zM249 569l-116 161h112l86 -161h-82z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="590" 
d="M62 235v72h466v-72h-466z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="805" 
d="M62 235v72h681v-72h-681z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="278" 
d="M54 452l82 213h77l-61 -213h-98z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="278" 
d="M65 452l61 213h98l-82 -213h-77z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="278" 
d="M65 -160l61 213h98l-82 -213h-77z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="469" 
d="M263 452l64 213h77l-46 -213h-95zM54 452l88 213h77l-66 -213h-99z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="471" 
d="M65 452l46 213h95l-64 -213h-77zM252 452l66 213h99l-88 -213h-77z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="470" 
d="M65 -160l45 213h96l-65 -213h-76zM251 -160l66 213h99l-89 -213h-76z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="449" 
d="M179 148v271h-146v71h146v175h89v-175h148v-71h-148v-271h-89z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="449" 
d="M33 293v72h146v89h-146v71h146v140h89v-140h148v-71h-148v-89h148v-72h-148v-145h-89v145h-146z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="385" 
d="M114 196q-31 26 -31 75q0 50 31 75t79 25t78 -25t30 -75q0 -49 -30 -75t-78 -26t-79 26z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="842" 
d="M90 6q-18 16 -18 43t18 42t47 15q30 0 47.5 -15t17.5 -42t-17.5 -42.5t-47.5 -15.5q-29 0 -47 15zM373 6q-18 16 -18 43t18 42t47 15q30 0 48 -15t18 -42t-18 -42.5t-48 -15.5q-29 0 -47 15zM657 6q-18 16 -18 43t18 42t47 15q30 0 48 -15t18 -42t-18 -42.5t-48 -15.5
q-29 0 -47 15z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1241" 
d="M980 76q11 -13 25 -19t30 -6q15 0 29 5.5t24.5 19t17 36t6.5 55.5q0 34 -6.5 56.5t-17 36t-24.5 19t-29 5.5q-16 0 -30 -5.5t-25 -19t-17.5 -35.5t-6.5 -56q0 -33 6.5 -55.5t17.5 -36.5zM972 1q-29 10 -50 31.5t-33 55t-12 80.5q0 48 12 81.5t33 54t50 30t63 9.5t63 -9.5
t50 -30.5t33 -54.5t12 -81.5q0 -93 -44.5 -134.5t-113.5 -41.5q-34 0 -63 10zM151 408q11 -13 25 -19t30 -6t29.5 6t24 19.5t16.5 36t6 55.5q0 34 -6 56.5t-16.5 35.5t-24 18.5t-29.5 5.5t-30 -5.5t-25 -18.5t-17.5 -35t-6.5 -56t6.5 -56.5t17.5 -36.5zM142 334
q-29 10 -50 31.5t-33 55t-12 80.5q0 48 12 81t33 53.5t50 30t64 9.5q34 0 63 -9.5t50 -30t33 -54t12 -81.5q0 -47 -12 -80.5t-33 -54.5t-50 -31t-63 -10q-35 0 -64 10zM599 76q11 -13 25.5 -19t29.5 -6q16 0 30 5.5t24.5 19t16.5 36t6 55.5q0 34 -6 56.5t-16.5 36t-24.5 19
t-30 5.5q-15 0 -29.5 -5.5t-25 -19t-17 -35.5t-6.5 -56q0 -33 6.5 -55.5t16.5 -36.5zM591 1q-29 10 -50 31.5t-33 55t-12 80.5q0 48 12 81.5t33 54t50 30t63 9.5t63 -9.5t50 -30.5t33 -54.5t12 -81.5q0 -93 -44.5 -134.5t-113.5 -41.5q-34 0 -63 10zM169 0l429 665h85
l-430 -665h-84z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="355" 
d="M210 90l-156 181l156 181h91l-138 -181l138 -181h-91z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="355" 
d="M53 90l138 181l-138 181h91l156 -181l-156 -181h-91z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="533" 
d="M11 0l428 665h83l-429 -665h-82z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M35 210v62h86q-1 14 -1.5 27.5t-0.5 30.5v37h-84v62h91q13 126 75 186t176 60q49 0 93 -4.5t76 -11.5l-14 -81q-32 8 -74 13t-78 5q-66 0 -101.5 -39t-43.5 -128h263l-25 -62h-243q-1 -9 -1 -19v-20q0 -15 0.5 -29t1.5 -27h212l-24 -62h-179q9 -77 46 -109.5t100 -32.5
q35 0 75 5t75 15l11 -76q-35 -12 -80.5 -17.5t-90.5 -5.5q-107 0 -166.5 55t-79.5 166h-95z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="818" 
d="M130 366v244h-100v55h278v-55h-99v-244h-79zM366 366v299h85l100 -170l100 170h86v-299h-70v207l-86 -144h-60l-84 144v-207h-71z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="693" 
d="M46 0v71h134q-56 38 -87 101.5t-31 165.5q0 89 19 153t55.5 105t89 60t120.5 19q67 0 120 -19t90 -60t56 -105t19 -153q0 -102 -31.5 -165.5t-86.5 -101.5h134v-71h-262v70q38 19 66.5 41.5t48 53.5t29 73t9.5 100q0 73 -12 123.5t-36 82t-60 45t-84 13.5t-83.5 -13.5
t-59.5 -45t-36 -82t-12 -123.5q0 -58 10 -100t29 -73t47.5 -53.5t66.5 -41.5v-70h-262z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="609" 
d="M164 638q38 18 80.5 27.5t99.5 9.5q97 0 147 -49.5t50 -158.5q0 -68 -15 -146q-16 -80 -41 -141.5t-61.5 -103.5t-85.5 -63.5t-112 -21.5q-86 0 -134.5 47t-48.5 127q0 108 61.5 170.5t180.5 62.5q54 0 91 -11.5t62 -25.5q5 31 7.5 59t2.5 48q0 74 -32.5 104t-90.5 30
q-46 0 -81 -10.5t-71 -24.5zM162 93q28 -28 76 -28q75 0 117 55.5t66 168.5q-26 14 -58 24.5t-77 10.5q-33 0 -60.5 -8t-48 -26t-31.5 -48t-11 -73q0 -48 27 -76z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" 
d="M30 0l221 665h101l222 -665h-544zM140 65h325l-163 508z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="715" 
d="M120 0v592h-78v73h639v-73h-78v-592h-101v592h-282v-592h-100z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M70 0v61l230 285l-230 259v60h460v-74h-332l220 -244l-224 -273h362v-74h-486z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M67 284v70h470v-70h-470z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M264 0l-140 414h-97v68h164l119 -382l184 565h83l-219 -665h-94z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="682" 
d="M115 320q0 -35 20.5 -49t49.5 -14t54.5 14.5t58.5 46.5q-33 32 -58.5 48.5t-54.5 16.5t-49.5 -14.5t-20.5 -48.5zM384 321q33 -32 58.5 -48t54.5 -16t49.5 14t20.5 49q0 34 -20.5 48.5t-49.5 14.5t-54.5 -15t-58.5 -47zM416 212q-37 20 -75 56q-38 -36 -75 -55.5
t-82 -19.5q-64 0 -105 32.5t-41 94.5q0 61 41 93.5t105 32.5q45 0 82 -19.5t75 -55.5q38 36 75 55.5t82 19.5q64 0 105 -32.5t41 -93.5q0 -62 -41 -94.5t-105 -32.5q-45 0 -82 19z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="301" 
d="M-19 -120q4 0 12.5 -0.5t13.5 -0.5q51 0 73.5 22.5t22.5 71.5v598q0 80 44.5 125t138.5 45q10 0 19.5 -0.5t19.5 -1.5l-4 -75q-5 0 -14 0.5t-13 0.5q-51 0 -73.5 -23t-22.5 -71v-598q0 -80 -44.5 -125t-138.5 -45h-19.5t-19.5 2z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M550 452q-15 -42 -48 -68t-87 -26q-26 0 -57.5 8t-63 18t-59 18t-45.5 8q-29 0 -44.5 -13.5t-27.5 -38.5q-16 7 -32 13t-33 13q15 40 46 66.5t85 26.5q26 0 58 -8t64.5 -17t61 -17t46.5 -8q30 0 45 12.5t27 38.5zM550 252q-15 -42 -48 -68t-87 -26q-26 0 -57.5 8t-63 18
t-59 18t-45.5 8q-29 0 -44.5 -13.5t-27.5 -38.5q-16 7 -32 13t-33 13q15 40 46 66.5t85 26.5q26 0 58 -8t64.5 -17t61 -17t46.5 -8q30 0 45 12.5t27 38.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M146 58l59 127h-138v69h170l60 130h-230v69h262l60 127h68l-59 -127h139v-69h-171l-61 -130h232v-69h-264l-59 -127h-68z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M67 0v70h470v-70h-470zM67 285v68l470 178v-75l-370 -137l370 -138v-75z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M67 0v70h470v-70h-470zM67 106v75l370 138l-370 137v75l470 -178v-68z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M269 0l-208 333l208 332h66l208 -332l-208 -333h-66zM302 88l150 245l-150 243l-150 -243z" />
    <glyph glyph-name="commaaccentcomb" horiz-adv-x="550" 
d="M171 -188l74 142h110l-102 -142h-82z" />
    <glyph glyph-name="apple" horiz-adv-x="1100" 
d="M137 216q-13 1 -26 4.5t-23.5 10t-17 16t-6.5 22.5q0 5 1 12t3 14l153 410q7 16 29 26.5t48 12.5l523 40h5q30 0 42 -38l47 -156l-499 59l-56 -72l217 -33l-123 -351zM169 290q-1 -5 -1.5 -9t-0.5 -7q0 -16 12 -25t31 -11l226 -21l97 265l-188 27l-8 68l71 89l481 -54
l-34 112q-5 14 -17.5 18.5t-24.5 5.5l-452 -34q-16 -1 -33.5 -8.5t-23.5 -26.5zM733 171v348l211 -33l80 -279q1 -2 1 -9q0 -19 -16 -29.5t-42 -10.5zM815 190l151 -12h3q14 0 22.5 8.5t8.5 18.5q0 6 -1 7l-65 211l-119 22v-255zM61 9h25q19 -1 32 10t13 38q0 29 -13 40
t-32 10h-25v-98zM43 -6v128h45q27 0 44.5 -15.5t18.5 -49.5q-1 -33 -18.5 -48t-44.5 -15h-45zM233 52q-3 21 -23 21q-12 0 -19 -8t-7 -21zM185 32q1 -13 8.5 -19t18.5 -6q9 -1 15.5 3t11.5 11l12 -6q-6 -10 -15.5 -16t-23.5 -6q-21 0 -34 12.5t-14 35.5q1 19 13 32t33 14
q22 -1 32 -13t11 -32zM280 21q8 -14 25 -14q22 0 19 13q1 7 -7.5 9.5t-19 5.5t-19.5 8t-9 17t10 19t27 8q25 -1 36 -18l-12 -8q-6 12 -25 12q-18 0 -18 -13q0 -7 8.5 -9.5t19 -5t19 -8t8.5 -17.5q0 -13 -9.5 -20t-26.5 -7q-15 0 -25 6t-15 16zM374 105q-10 0 -10 8q0 9 10 9
q9 0 11 -9q-2 -8 -11 -8zM365 -6v92h18v-92h-18zM474 69q-7 4 -21 4q-12 0 -20 -8t-8 -24q0 -32 26 -32q17 0 23 13v47zM421 -17q5 -8 11.5 -12t17.5 -4q26 0 24 24v16q-8 -13 -27 -13q-20 0 -30 12.5t-11 34.5q1 23 14.5 34.5t33.5 11.5q26 -2 39 -11v-84q0 -39 -43 -39
q-28 0 -41 21zM525 -6v92h16v-17q10 16 32 18q31 -1 32 -31v-62h-19v62q0 17 -18 17q-10 0 -15.5 -4.5t-10.5 -11.5v-63h-17zM694 52q-3 21 -23 21q-13 0 -20.5 -8t-7.5 -21zM645 32q3 -25 28 -25q18 -2 25 14l14 -6q-5 -10 -15 -16t-24 -6q-47 0 -47 48q0 19 12 32t33 14
q22 -1 32.5 -13t10.5 -32zM796 63q-8 10 -23 10q-13 0 -20 -9t-7 -24t7 -24t20 -9q15 0 23 13v43zM802 -7q0 3 -1 6.5t-1 7.5q-5 -6 -12.5 -10t-18.5 -4q-19 0 -30.5 13t-11.5 35q0 21 13 33t30 13q20 -1 26 -10v49h19v-108q0 -5 0.5 -12t2.5 -12zM914 20q10 -13 24 -13
t20.5 9.5t6.5 24.5q0 32 -27 32q-8 0 -13.5 -4.5t-10.5 -10.5v-38zM892 -6q2 6 3.5 12.5t1.5 11.5v108h17v-54q9 13 31 15q19 -1 28 -13t9 -33q0 -22 -10.5 -35t-28.5 -13q-23 0 -32 14q0 -8 -3 -14zM992 -35q23 1 31 21l3 7l-35 93h18l27 -75l27 75h18l-40 -100
q-6 -17 -16 -25t-29 -9zM139 -189v113h-39v14h97l-1 -14h-39v-113h-18zM305 -163q6 -13 24 -13q13 0 19 9t6 24q0 32 -24 32q-16 0 -25 -14v-38zM287 -230v132l15 1v-15q12 14 33 16q20 -1 29 -13.5t9 -33.5q1 -21 -9 -34.5t-32 -13.5q-10 0 -16.5 4t-10.5 8v-50zM467 -177
q-12 -13 -34 -14q-21 1 -33 14t-13 34q1 20 13 33t33 14q22 -1 34 -14t13 -33q-1 -21 -13 -34zM413 -167q7 -9 20 -9t20 9t7 24q0 14 -7 23t-20 9t-20 -9t-7 -23q0 -15 7 -24zM520 -163q8 -13 23 -13q14 0 20 9t6 24t-5.5 23.5t-19.5 8.5q-15 0 -24 -14v-38zM502 -230v132
l16 1v-15q10 14 33 16q18 -1 27.5 -13.5t9.5 -33.5q2 -21 -9 -34.5t-31 -13.5q-9 0 -16 4t-12 8v-50zM612 -189v131l18 1v-54q10 13 31 15q31 -1 32 -31v-62h-19v62q0 16 -18 16t-26 -16v-62h-18zM777 -191q-1 4 -1 7.5t-2 7.5q-4 -6 -11 -10.5t-18 -4.5q-32 0 -32 26
q-1 17 19 24q5 2 17 4t22 3v7q0 7 -4 11.5t-16 4.5q-10 0 -15 -3.5t-9 -8.5l-13 8q6 7 15.5 12.5t24.5 6.5q20 -2 27.5 -11t7.5 -21v-37q0 -5 1 -11.5t3 -11.5zM771 -146q-8 -1 -17.5 -3t-14.5 -3q-9 -4 -9 -11q0 -13 18 -13q17 0 23 14v16zM878 -115q-8 6 -21 4
q-13 0 -21 -7.5t-8 -24.5q0 -31 27 -32q16 2 23 14v46zM825 -200q5 -8 11.5 -12t17.5 -4q26 0 24 24v16q-8 -13 -28 -13q-19 0 -29 12.5t-10 33.5q0 23 13.5 35t33.5 12q11 -1 21 -3.5t16 -7.5v-84q0 -39 -41 -39q-31 0 -41 21zM987 -131q-1 20 -23 20q-12 0 -19 -8t-7 -20z
M938 -151q2 -13 9.5 -19t18.5 -6q18 -1 25 13l13 -5q-5 -10 -14 -16.5t-24 -6.5q-21 0 -33.5 12.5t-13.5 35.5q1 20 13 33t32 14q21 -1 31.5 -13t12.5 -32zM180 -219q23 1 31 21l4 7l-36 93h18l27 -75l27 75h18l-40 -100q-6 -17 -16 -25t-29 -9z" />
    <glyph glyph-name=".ttfautohint" horiz-adv-x="1100" 
d="M0 0z" />
    <hkern u1="&#x28;" u2="&#x134;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-40" />
    <hkern u1="&#x28;" u2="J" k="-50" />
    <hkern u1="&#x2a;" u2="&#x2026;" k="175" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="175" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="175" />
    <hkern u1="&#x2c;" g2="fl" k="10" />
    <hkern u1="&#x2c;" g2="fi" k="10" />
    <hkern u1="&#x2c;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2c;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2c;" u2="&#x1ef2;" k="125" />
    <hkern u1="&#x2c;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2c;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2c;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2c;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2c;" u2="&#x1fe;" k="45" />
    <hkern u1="&#x2c;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1fc;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1fa;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2c;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x178;" k="125" />
    <hkern u1="&#x2c;" u2="&#x177;" k="20" />
    <hkern u1="&#x2c;" u2="&#x176;" k="125" />
    <hkern u1="&#x2c;" u2="&#x175;" k="65" />
    <hkern u1="&#x2c;" u2="&#x174;" k="55" />
    <hkern u1="&#x2c;" u2="&#x173;" k="20" />
    <hkern u1="&#x2c;" u2="&#x172;" k="25" />
    <hkern u1="&#x2c;" u2="&#x171;" k="20" />
    <hkern u1="&#x2c;" u2="&#x170;" k="25" />
    <hkern u1="&#x2c;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16e;" k="25" />
    <hkern u1="&#x2c;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16c;" k="25" />
    <hkern u1="&#x2c;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16a;" k="25" />
    <hkern u1="&#x2c;" u2="&#x169;" k="20" />
    <hkern u1="&#x2c;" u2="&#x168;" k="25" />
    <hkern u1="&#x2c;" u2="&#x167;" k="20" />
    <hkern u1="&#x2c;" u2="&#x165;" k="20" />
    <hkern u1="&#x2c;" u2="&#x164;" k="110" />
    <hkern u1="&#x2c;" u2="&#x163;" k="20" />
    <hkern u1="&#x2c;" u2="&#x162;" k="110" />
    <hkern u1="&#x2c;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x153;" k="20" />
    <hkern u1="&#x2c;" u2="&#x152;" k="45" />
    <hkern u1="&#x2c;" u2="&#x151;" k="20" />
    <hkern u1="&#x2c;" u2="&#x150;" k="45" />
    <hkern u1="&#x2c;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x14e;" k="45" />
    <hkern u1="&#x2c;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x14c;" k="45" />
    <hkern u1="&#x2c;" u2="&#x123;" k="20" />
    <hkern u1="&#x2c;" u2="&#x122;" k="45" />
    <hkern u1="&#x2c;" u2="&#x121;" k="20" />
    <hkern u1="&#x2c;" u2="&#x120;" k="45" />
    <hkern u1="&#x2c;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11e;" k="45" />
    <hkern u1="&#x2c;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11c;" k="45" />
    <hkern u1="&#x2c;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2c;" u2="&#x119;" k="20" />
    <hkern u1="&#x2c;" u2="&#x118;" k="20" />
    <hkern u1="&#x2c;" u2="&#x117;" k="20" />
    <hkern u1="&#x2c;" u2="&#x116;" k="20" />
    <hkern u1="&#x2c;" u2="&#x115;" k="20" />
    <hkern u1="&#x2c;" u2="&#x114;" k="20" />
    <hkern u1="&#x2c;" u2="&#x113;" k="20" />
    <hkern u1="&#x2c;" u2="&#x112;" k="20" />
    <hkern u1="&#x2c;" u2="&#x111;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10c;" k="45" />
    <hkern u1="&#x2c;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10a;" k="45" />
    <hkern u1="&#x2c;" u2="&#x109;" k="20" />
    <hkern u1="&#x2c;" u2="&#x108;" k="45" />
    <hkern u1="&#x2c;" u2="&#x107;" k="20" />
    <hkern u1="&#x2c;" u2="&#x106;" k="45" />
    <hkern u1="&#x2c;" u2="&#x105;" k="10" />
    <hkern u1="&#x2c;" u2="&#x104;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x103;" k="10" />
    <hkern u1="&#x2c;" u2="&#x102;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x101;" k="10" />
    <hkern u1="&#x2c;" u2="&#x100;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xff;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2c;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xea;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe5;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe4;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe3;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe1;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe0;" k="10" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="125" />
    <hkern u1="&#x2c;" u2="&#xdc;" k="25" />
    <hkern u1="&#x2c;" u2="&#xdb;" k="25" />
    <hkern u1="&#x2c;" u2="&#xda;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd9;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="45" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="45" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="45" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="45" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="45" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="45" />
    <hkern u1="&#x2c;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xca;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="45" />
    <hkern u1="&#x2c;" u2="&#xc6;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc5;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc4;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc3;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc2;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc1;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc0;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xab;" k="50" />
    <hkern u1="&#x2c;" u2="&#xa9;" k="45" />
    <hkern u1="&#x2c;" u2="y" k="20" />
    <hkern u1="&#x2c;" u2="x" k="-20" />
    <hkern u1="&#x2c;" u2="v" k="65" />
    <hkern u1="&#x2c;" u2="q" k="20" />
    <hkern u1="&#x2c;" u2="o" k="20" />
    <hkern u1="&#x2c;" u2="g" k="20" />
    <hkern u1="&#x2c;" u2="f" k="10" />
    <hkern u1="&#x2c;" u2="e" k="20" />
    <hkern u1="&#x2c;" u2="d" k="20" />
    <hkern u1="&#x2c;" u2="X" k="-30" />
    <hkern u1="&#x2c;" u2="V" k="100" />
    <hkern u1="&#x2c;" u2="Q" k="45" />
    <hkern u1="&#x2c;" u2="O" k="45" />
    <hkern u1="&#x2c;" u2="G" k="45" />
    <hkern u1="&#x2d;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2d;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2d;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2d;" u2="&#x218;" k="85" />
    <hkern u1="&#x2d;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x17d;" k="75" />
    <hkern u1="&#x2d;" u2="&#x17b;" k="75" />
    <hkern u1="&#x2d;" u2="&#x179;" k="75" />
    <hkern u1="&#x2d;" u2="&#x178;" k="10" />
    <hkern u1="&#x2d;" u2="&#x176;" k="10" />
    <hkern u1="&#x2d;" u2="&#x175;" k="10" />
    <hkern u1="&#x2d;" u2="&#x174;" k="20" />
    <hkern u1="&#x2d;" u2="&#x164;" k="110" />
    <hkern u1="&#x2d;" u2="&#x162;" k="110" />
    <hkern u1="&#x2d;" u2="&#x160;" k="85" />
    <hkern u1="&#x2d;" u2="&#x15e;" k="85" />
    <hkern u1="&#x2d;" u2="&#x15c;" k="85" />
    <hkern u1="&#x2d;" u2="&#x15a;" k="85" />
    <hkern u1="&#x2d;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2d;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2d;" u2="z" k="35" />
    <hkern u1="&#x2d;" u2="x" k="20" />
    <hkern u1="&#x2d;" u2="X" k="65" />
    <hkern u1="&#x2d;" u2="V" k="35" />
    <hkern u1="&#x2d;" u2="Q" k="-15" />
    <hkern u1="&#x2d;" u2="O" k="-15" />
    <hkern u1="&#x2d;" u2="G" k="-15" />
    <hkern u1="&#x2e;" g2="fl" k="10" />
    <hkern u1="&#x2e;" g2="fi" k="10" />
    <hkern u1="&#x2e;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2e;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="60" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2e;" u2="&#x1ef2;" k="125" />
    <hkern u1="&#x2e;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2e;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2e;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2e;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2e;" u2="&#x1fe;" k="45" />
    <hkern u1="&#x2e;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1fc;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1fa;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2e;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x178;" k="125" />
    <hkern u1="&#x2e;" u2="&#x177;" k="20" />
    <hkern u1="&#x2e;" u2="&#x176;" k="125" />
    <hkern u1="&#x2e;" u2="&#x175;" k="65" />
    <hkern u1="&#x2e;" u2="&#x174;" k="55" />
    <hkern u1="&#x2e;" u2="&#x173;" k="20" />
    <hkern u1="&#x2e;" u2="&#x172;" k="25" />
    <hkern u1="&#x2e;" u2="&#x171;" k="20" />
    <hkern u1="&#x2e;" u2="&#x170;" k="25" />
    <hkern u1="&#x2e;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16e;" k="25" />
    <hkern u1="&#x2e;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16c;" k="25" />
    <hkern u1="&#x2e;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16a;" k="25" />
    <hkern u1="&#x2e;" u2="&#x169;" k="20" />
    <hkern u1="&#x2e;" u2="&#x168;" k="25" />
    <hkern u1="&#x2e;" u2="&#x167;" k="20" />
    <hkern u1="&#x2e;" u2="&#x165;" k="20" />
    <hkern u1="&#x2e;" u2="&#x164;" k="110" />
    <hkern u1="&#x2e;" u2="&#x163;" k="20" />
    <hkern u1="&#x2e;" u2="&#x162;" k="110" />
    <hkern u1="&#x2e;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x153;" k="20" />
    <hkern u1="&#x2e;" u2="&#x152;" k="45" />
    <hkern u1="&#x2e;" u2="&#x151;" k="20" />
    <hkern u1="&#x2e;" u2="&#x150;" k="45" />
    <hkern u1="&#x2e;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x14e;" k="45" />
    <hkern u1="&#x2e;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x14c;" k="45" />
    <hkern u1="&#x2e;" u2="&#x123;" k="20" />
    <hkern u1="&#x2e;" u2="&#x122;" k="45" />
    <hkern u1="&#x2e;" u2="&#x121;" k="20" />
    <hkern u1="&#x2e;" u2="&#x120;" k="45" />
    <hkern u1="&#x2e;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11e;" k="45" />
    <hkern u1="&#x2e;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11c;" k="45" />
    <hkern u1="&#x2e;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2e;" u2="&#x119;" k="20" />
    <hkern u1="&#x2e;" u2="&#x118;" k="20" />
    <hkern u1="&#x2e;" u2="&#x117;" k="20" />
    <hkern u1="&#x2e;" u2="&#x116;" k="20" />
    <hkern u1="&#x2e;" u2="&#x115;" k="20" />
    <hkern u1="&#x2e;" u2="&#x114;" k="20" />
    <hkern u1="&#x2e;" u2="&#x113;" k="20" />
    <hkern u1="&#x2e;" u2="&#x112;" k="20" />
    <hkern u1="&#x2e;" u2="&#x111;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10c;" k="45" />
    <hkern u1="&#x2e;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10a;" k="45" />
    <hkern u1="&#x2e;" u2="&#x109;" k="20" />
    <hkern u1="&#x2e;" u2="&#x108;" k="45" />
    <hkern u1="&#x2e;" u2="&#x107;" k="20" />
    <hkern u1="&#x2e;" u2="&#x106;" k="45" />
    <hkern u1="&#x2e;" u2="&#x105;" k="10" />
    <hkern u1="&#x2e;" u2="&#x104;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x103;" k="10" />
    <hkern u1="&#x2e;" u2="&#x102;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x101;" k="10" />
    <hkern u1="&#x2e;" u2="&#x100;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xff;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2e;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xea;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe5;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe4;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe3;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe1;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe0;" k="10" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="125" />
    <hkern u1="&#x2e;" u2="&#xdc;" k="25" />
    <hkern u1="&#x2e;" u2="&#xdb;" k="25" />
    <hkern u1="&#x2e;" u2="&#xda;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd9;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="45" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="45" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="45" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="45" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="45" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="45" />
    <hkern u1="&#x2e;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xca;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="45" />
    <hkern u1="&#x2e;" u2="&#xc6;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc5;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc4;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc3;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc2;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc1;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc0;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xab;" k="50" />
    <hkern u1="&#x2e;" u2="&#xa9;" k="45" />
    <hkern u1="&#x2e;" u2="y" k="20" />
    <hkern u1="&#x2e;" u2="x" k="-20" />
    <hkern u1="&#x2e;" u2="w" k="65" />
    <hkern u1="&#x2e;" u2="v" k="65" />
    <hkern u1="&#x2e;" u2="u" k="20" />
    <hkern u1="&#x2e;" u2="t" k="20" />
    <hkern u1="&#x2e;" u2="q" k="20" />
    <hkern u1="&#x2e;" u2="o" k="20" />
    <hkern u1="&#x2e;" u2="g" k="20" />
    <hkern u1="&#x2e;" u2="f" k="10" />
    <hkern u1="&#x2e;" u2="e" k="20" />
    <hkern u1="&#x2e;" u2="d" k="20" />
    <hkern u1="&#x2e;" u2="c" k="20" />
    <hkern u1="&#x2e;" u2="a" k="10" />
    <hkern u1="&#x2e;" u2="Z" k="-30" />
    <hkern u1="&#x2e;" u2="Y" k="125" />
    <hkern u1="&#x2e;" u2="X" k="-30" />
    <hkern u1="&#x2e;" u2="W" k="55" />
    <hkern u1="&#x2e;" u2="V" k="100" />
    <hkern u1="&#x2e;" u2="U" k="25" />
    <hkern u1="&#x2e;" u2="T" k="110" />
    <hkern u1="&#x2e;" u2="S" k="-20" />
    <hkern u1="&#x2e;" u2="Q" k="45" />
    <hkern u1="&#x2e;" u2="O" k="45" />
    <hkern u1="&#x2e;" u2="G" k="45" />
    <hkern u1="&#x2e;" u2="E" k="20" />
    <hkern u1="&#x2e;" u2="C" k="45" />
    <hkern u1="&#x2e;" u2="A" k="-10" />
    <hkern u1="&#x2f;" u2="&#x1ff;" k="65" />
    <hkern u1="&#x2f;" u2="&#x153;" k="65" />
    <hkern u1="&#x2f;" u2="&#x151;" k="65" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x123;" k="65" />
    <hkern u1="&#x2f;" u2="&#x121;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="65" />
    <hkern u1="&#x2f;" u2="&#x119;" k="65" />
    <hkern u1="&#x2f;" u2="&#x117;" k="65" />
    <hkern u1="&#x2f;" u2="&#x115;" k="65" />
    <hkern u1="&#x2f;" u2="&#x113;" k="65" />
    <hkern u1="&#x2f;" u2="&#x111;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="65" />
    <hkern u1="&#x2f;" u2="&#x109;" k="65" />
    <hkern u1="&#x2f;" u2="&#x107;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="65" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="65" />
    <hkern u1="&#x2f;" u2="&#xea;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="65" />
    <hkern u1="&#x2f;" u2="q" k="65" />
    <hkern u1="&#x2f;" u2="o" k="65" />
    <hkern u1="&#x2f;" u2="g" k="65" />
    <hkern u1="&#x2f;" u2="e" k="65" />
    <hkern u1="&#x2f;" u2="d" k="65" />
    <hkern u1="&#x2f;" u2="c" k="65" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="180" />
    <hkern u1="&#x40;" u2="&#x2026;" k="35" />
    <hkern u1="&#x40;" u2="&#x2e;" k="35" />
    <hkern u1="&#x40;" u2="&#x2c;" k="35" />
    <hkern u1="A" u2="&#x2026;" k="-10" />
    <hkern u1="A" u2="&#x164;" k="30" />
    <hkern u1="A" u2="&#x162;" k="30" />
    <hkern u1="A" u2="V" k="20" />
    <hkern u1="A" u2="&#x2e;" k="-10" />
    <hkern u1="B" u2="&#x1ef2;" k="15" />
    <hkern u1="B" u2="&#x178;" k="15" />
    <hkern u1="B" u2="&#x176;" k="15" />
    <hkern u1="B" u2="&#xdd;" k="15" />
    <hkern u1="B" u2="Y" k="15" />
    <hkern u1="B" u2="X" k="10" />
    <hkern u1="C" u2="&#x2026;" k="-15" />
    <hkern u1="C" u2="&#x2014;" k="75" />
    <hkern u1="C" u2="&#x2013;" k="75" />
    <hkern u1="C" u2="&#x1e85;" k="10" />
    <hkern u1="C" u2="&#x1e83;" k="10" />
    <hkern u1="C" u2="&#x1e81;" k="10" />
    <hkern u1="C" u2="&#x1ff;" k="10" />
    <hkern u1="C" u2="&#x1fe;" k="30" />
    <hkern u1="C" u2="&#x1fc;" k="7" />
    <hkern u1="C" u2="&#x1fa;" k="7" />
    <hkern u1="C" u2="&#x175;" k="10" />
    <hkern u1="C" u2="&#x153;" k="10" />
    <hkern u1="C" u2="&#x152;" k="30" />
    <hkern u1="C" u2="&#x151;" k="10" />
    <hkern u1="C" u2="&#x150;" k="30" />
    <hkern u1="C" u2="&#x14f;" k="10" />
    <hkern u1="C" u2="&#x14e;" k="30" />
    <hkern u1="C" u2="&#x14d;" k="10" />
    <hkern u1="C" u2="&#x14c;" k="30" />
    <hkern u1="C" u2="&#x123;" k="10" />
    <hkern u1="C" u2="&#x122;" k="30" />
    <hkern u1="C" u2="&#x121;" k="10" />
    <hkern u1="C" u2="&#x120;" k="30" />
    <hkern u1="C" u2="&#x11f;" k="10" />
    <hkern u1="C" u2="&#x11e;" k="30" />
    <hkern u1="C" u2="&#x11d;" k="10" />
    <hkern u1="C" u2="&#x11c;" k="30" />
    <hkern u1="C" u2="&#x11b;" k="10" />
    <hkern u1="C" u2="&#x119;" k="10" />
    <hkern u1="C" u2="&#x117;" k="10" />
    <hkern u1="C" u2="&#x115;" k="10" />
    <hkern u1="C" u2="&#x113;" k="10" />
    <hkern u1="C" u2="&#x111;" k="10" />
    <hkern u1="C" u2="&#x10f;" k="10" />
    <hkern u1="C" u2="&#x10d;" k="10" />
    <hkern u1="C" u2="&#x10c;" k="30" />
    <hkern u1="C" u2="&#x10b;" k="10" />
    <hkern u1="C" u2="&#x10a;" k="30" />
    <hkern u1="C" u2="&#x109;" k="10" />
    <hkern u1="C" u2="&#x108;" k="30" />
    <hkern u1="C" u2="&#x107;" k="10" />
    <hkern u1="C" u2="&#x106;" k="30" />
    <hkern u1="C" u2="&#x104;" k="7" />
    <hkern u1="C" u2="&#x102;" k="7" />
    <hkern u1="C" u2="&#x100;" k="7" />
    <hkern u1="C" u2="&#xf8;" k="10" />
    <hkern u1="C" u2="&#xf6;" k="10" />
    <hkern u1="C" u2="&#xf5;" k="10" />
    <hkern u1="C" u2="&#xf4;" k="10" />
    <hkern u1="C" u2="&#xf3;" k="10" />
    <hkern u1="C" u2="&#xf2;" k="10" />
    <hkern u1="C" u2="&#xf0;" k="10" />
    <hkern u1="C" u2="&#xeb;" k="10" />
    <hkern u1="C" u2="&#xea;" k="10" />
    <hkern u1="C" u2="&#xe9;" k="10" />
    <hkern u1="C" u2="&#xe8;" k="10" />
    <hkern u1="C" u2="&#xe7;" k="10" />
    <hkern u1="C" u2="&#xd8;" k="30" />
    <hkern u1="C" u2="&#xd6;" k="30" />
    <hkern u1="C" u2="&#xd5;" k="30" />
    <hkern u1="C" u2="&#xd4;" k="30" />
    <hkern u1="C" u2="&#xd3;" k="30" />
    <hkern u1="C" u2="&#xd2;" k="30" />
    <hkern u1="C" u2="&#xc7;" k="30" />
    <hkern u1="C" u2="&#xc6;" k="7" />
    <hkern u1="C" u2="&#xc5;" k="7" />
    <hkern u1="C" u2="&#xc4;" k="7" />
    <hkern u1="C" u2="&#xc3;" k="7" />
    <hkern u1="C" u2="&#xc2;" k="7" />
    <hkern u1="C" u2="&#xc1;" k="7" />
    <hkern u1="C" u2="&#xc0;" k="7" />
    <hkern u1="C" u2="&#xa9;" k="30" />
    <hkern u1="C" u2="v" k="30" />
    <hkern u1="C" u2="q" k="10" />
    <hkern u1="C" u2="o" k="10" />
    <hkern u1="C" u2="g" k="10" />
    <hkern u1="C" u2="e" k="10" />
    <hkern u1="C" u2="d" k="10" />
    <hkern u1="C" u2="_" k="-20" />
    <hkern u1="C" u2="Q" k="30" />
    <hkern u1="C" u2="O" k="30" />
    <hkern u1="C" u2="G" k="30" />
    <hkern u1="C" u2="&#x2e;" k="-15" />
    <hkern u1="D" u2="&#x2026;" k="45" />
    <hkern u1="D" u2="&#x2014;" k="-15" />
    <hkern u1="D" u2="&#x2013;" k="-15" />
    <hkern u1="D" u2="&#x218;" k="5" />
    <hkern u1="D" u2="&#x1fe;" k="-5" />
    <hkern u1="D" u2="&#x164;" k="15" />
    <hkern u1="D" u2="&#x162;" k="15" />
    <hkern u1="D" u2="&#x160;" k="5" />
    <hkern u1="D" u2="&#x15e;" k="5" />
    <hkern u1="D" u2="&#x15c;" k="5" />
    <hkern u1="D" u2="&#x15a;" k="5" />
    <hkern u1="D" u2="&#x152;" k="-5" />
    <hkern u1="D" u2="&#x150;" k="-5" />
    <hkern u1="D" u2="&#x14e;" k="-5" />
    <hkern u1="D" u2="&#x14c;" k="-5" />
    <hkern u1="D" u2="&#x134;" k="-20" />
    <hkern u1="D" u2="&#x122;" k="-5" />
    <hkern u1="D" u2="&#x120;" k="-5" />
    <hkern u1="D" u2="&#x11e;" k="-5" />
    <hkern u1="D" u2="&#x11c;" k="-5" />
    <hkern u1="D" u2="&#x10c;" k="-5" />
    <hkern u1="D" u2="&#x10a;" k="-5" />
    <hkern u1="D" u2="&#x108;" k="-5" />
    <hkern u1="D" u2="&#x106;" k="-5" />
    <hkern u1="D" u2="&#xd8;" k="-5" />
    <hkern u1="D" u2="&#xd6;" k="-5" />
    <hkern u1="D" u2="&#xd5;" k="-5" />
    <hkern u1="D" u2="&#xd4;" k="-5" />
    <hkern u1="D" u2="&#xd3;" k="-5" />
    <hkern u1="D" u2="&#xd2;" k="-5" />
    <hkern u1="D" u2="&#xc7;" k="-5" />
    <hkern u1="D" u2="&#xa9;" k="-5" />
    <hkern u1="D" u2="X" k="10" />
    <hkern u1="D" u2="V" k="15" />
    <hkern u1="D" u2="Q" k="-5" />
    <hkern u1="D" u2="O" k="-5" />
    <hkern u1="D" u2="J" k="-20" />
    <hkern u1="D" u2="G" k="-5" />
    <hkern u1="D" u2="&#x2e;" k="45" />
    <hkern u1="E" u2="&#x2026;" k="-20" />
    <hkern u1="E" u2="&#x1fe;" k="7" />
    <hkern u1="E" u2="&#x152;" k="7" />
    <hkern u1="E" u2="&#x150;" k="7" />
    <hkern u1="E" u2="&#x14e;" k="7" />
    <hkern u1="E" u2="&#x14c;" k="7" />
    <hkern u1="E" u2="&#x122;" k="7" />
    <hkern u1="E" u2="&#x120;" k="7" />
    <hkern u1="E" u2="&#x11e;" k="7" />
    <hkern u1="E" u2="&#x11c;" k="7" />
    <hkern u1="E" u2="&#x10c;" k="7" />
    <hkern u1="E" u2="&#x10a;" k="7" />
    <hkern u1="E" u2="&#x108;" k="7" />
    <hkern u1="E" u2="&#x106;" k="7" />
    <hkern u1="E" u2="&#xd8;" k="7" />
    <hkern u1="E" u2="&#xd6;" k="7" />
    <hkern u1="E" u2="&#xd5;" k="7" />
    <hkern u1="E" u2="&#xd4;" k="7" />
    <hkern u1="E" u2="&#xd3;" k="7" />
    <hkern u1="E" u2="&#xd2;" k="7" />
    <hkern u1="E" u2="&#xc7;" k="7" />
    <hkern u1="E" u2="&#xa9;" k="7" />
    <hkern u1="E" u2="Q" k="7" />
    <hkern u1="E" u2="O" k="7" />
    <hkern u1="E" u2="G" k="7" />
    <hkern u1="E" u2="&#x2e;" k="-20" />
    <hkern u1="F" u2="&#x2026;" k="120" />
    <hkern u1="F" u2="&#x1ff;" k="35" />
    <hkern u1="F" u2="&#x1fd;" k="30" />
    <hkern u1="F" u2="&#x1fc;" k="15" />
    <hkern u1="F" u2="&#x1fb;" k="30" />
    <hkern u1="F" u2="&#x1fa;" k="15" />
    <hkern u1="F" u2="&#x159;" k="10" />
    <hkern u1="F" u2="&#x157;" k="10" />
    <hkern u1="F" u2="&#x155;" k="10" />
    <hkern u1="F" u2="&#x153;" k="35" />
    <hkern u1="F" u2="&#x151;" k="35" />
    <hkern u1="F" u2="&#x14f;" k="35" />
    <hkern u1="F" u2="&#x14d;" k="35" />
    <hkern u1="F" u2="&#x14b;" k="10" />
    <hkern u1="F" u2="&#x148;" k="10" />
    <hkern u1="F" u2="&#x146;" k="10" />
    <hkern u1="F" u2="&#x144;" k="10" />
    <hkern u1="F" u2="&#x123;" k="35" />
    <hkern u1="F" u2="&#x121;" k="35" />
    <hkern u1="F" u2="&#x11f;" k="35" />
    <hkern u1="F" u2="&#x11d;" k="35" />
    <hkern u1="F" u2="&#x11b;" k="35" />
    <hkern u1="F" u2="&#x119;" k="35" />
    <hkern u1="F" u2="&#x117;" k="35" />
    <hkern u1="F" u2="&#x115;" k="35" />
    <hkern u1="F" u2="&#x113;" k="35" />
    <hkern u1="F" u2="&#x111;" k="35" />
    <hkern u1="F" u2="&#x10f;" k="35" />
    <hkern u1="F" u2="&#x10d;" k="35" />
    <hkern u1="F" u2="&#x10b;" k="35" />
    <hkern u1="F" u2="&#x109;" k="35" />
    <hkern u1="F" u2="&#x107;" k="35" />
    <hkern u1="F" u2="&#x105;" k="30" />
    <hkern u1="F" u2="&#x104;" k="15" />
    <hkern u1="F" u2="&#x103;" k="30" />
    <hkern u1="F" u2="&#x102;" k="15" />
    <hkern u1="F" u2="&#x101;" k="30" />
    <hkern u1="F" u2="&#x100;" k="15" />
    <hkern u1="F" u2="&#xf8;" k="35" />
    <hkern u1="F" u2="&#xf6;" k="35" />
    <hkern u1="F" u2="&#xf5;" k="35" />
    <hkern u1="F" u2="&#xf4;" k="35" />
    <hkern u1="F" u2="&#xf3;" k="35" />
    <hkern u1="F" u2="&#xf2;" k="35" />
    <hkern u1="F" u2="&#xf1;" k="10" />
    <hkern u1="F" u2="&#xf0;" k="35" />
    <hkern u1="F" u2="&#xeb;" k="35" />
    <hkern u1="F" u2="&#xea;" k="35" />
    <hkern u1="F" u2="&#xe9;" k="35" />
    <hkern u1="F" u2="&#xe8;" k="35" />
    <hkern u1="F" u2="&#xe7;" k="35" />
    <hkern u1="F" u2="&#xe6;" k="30" />
    <hkern u1="F" u2="&#xe5;" k="30" />
    <hkern u1="F" u2="&#xe4;" k="30" />
    <hkern u1="F" u2="&#xe3;" k="30" />
    <hkern u1="F" u2="&#xe2;" k="30" />
    <hkern u1="F" u2="&#xe1;" k="30" />
    <hkern u1="F" u2="&#xe0;" k="30" />
    <hkern u1="F" u2="&#xc6;" k="15" />
    <hkern u1="F" u2="&#xc5;" k="15" />
    <hkern u1="F" u2="&#xc4;" k="15" />
    <hkern u1="F" u2="&#xc3;" k="15" />
    <hkern u1="F" u2="&#xc2;" k="15" />
    <hkern u1="F" u2="&#xc1;" k="15" />
    <hkern u1="F" u2="&#xc0;" k="15" />
    <hkern u1="F" u2="r" k="10" />
    <hkern u1="F" u2="q" k="35" />
    <hkern u1="F" u2="p" k="10" />
    <hkern u1="F" u2="o" k="35" />
    <hkern u1="F" u2="n" k="10" />
    <hkern u1="F" u2="m" k="10" />
    <hkern u1="F" u2="g" k="35" />
    <hkern u1="F" u2="e" k="35" />
    <hkern u1="F" u2="d" k="35" />
    <hkern u1="F" u2="c" k="35" />
    <hkern u1="F" u2="a" k="30" />
    <hkern u1="F" u2="_" k="85" />
    <hkern u1="F" u2="A" k="15" />
    <hkern u1="F" u2="&#x2f;" k="75" />
    <hkern u1="F" u2="&#x2e;" k="120" />
    <hkern u1="F" u2="&#x2c;" k="120" />
    <hkern u1="G" u2="&#x1ef2;" k="30" />
    <hkern u1="G" u2="&#x178;" k="30" />
    <hkern u1="G" u2="&#x176;" k="30" />
    <hkern u1="G" u2="&#x164;" k="20" />
    <hkern u1="G" u2="&#x162;" k="20" />
    <hkern u1="G" u2="&#xdd;" k="30" />
    <hkern u1="G" u2="Y" k="30" />
    <hkern u1="G" u2="T" k="20" />
    <hkern u1="K" u2="&#x2026;" k="-60" />
    <hkern u1="K" u2="&#x2014;" k="10" />
    <hkern u1="K" u2="&#x2013;" k="10" />
    <hkern u1="K" u2="&#xef;" k="-20" />
    <hkern u1="K" u2="_" k="-70" />
    <hkern u1="K" u2="&#x2e;" k="-60" />
    <hkern u1="L" u2="&#x2122;" k="65" />
    <hkern u1="L" u2="&#x2026;" k="-60" />
    <hkern u1="L" u2="&#x201d;" k="110" />
    <hkern u1="L" u2="&#x201c;" k="110" />
    <hkern u1="L" u2="&#x2014;" k="100" />
    <hkern u1="L" u2="&#x2013;" k="100" />
    <hkern u1="L" u2="&#x1ef2;" k="110" />
    <hkern u1="L" u2="&#x1e85;" k="30" />
    <hkern u1="L" u2="&#x1e84;" k="45" />
    <hkern u1="L" u2="&#x1e83;" k="30" />
    <hkern u1="L" u2="&#x1e82;" k="45" />
    <hkern u1="L" u2="&#x1e81;" k="30" />
    <hkern u1="L" u2="&#x1e80;" k="45" />
    <hkern u1="L" u2="&#x1fe;" k="20" />
    <hkern u1="L" u2="&#x1fd;" k="-30" />
    <hkern u1="L" u2="&#x1fb;" k="-30" />
    <hkern u1="L" u2="&#x178;" k="110" />
    <hkern u1="L" u2="&#x176;" k="110" />
    <hkern u1="L" u2="&#x175;" k="30" />
    <hkern u1="L" u2="&#x174;" k="45" />
    <hkern u1="L" u2="&#x164;" k="75" />
    <hkern u1="L" u2="&#x162;" k="75" />
    <hkern u1="L" u2="&#x152;" k="20" />
    <hkern u1="L" u2="&#x150;" k="20" />
    <hkern u1="L" u2="&#x14e;" k="20" />
    <hkern u1="L" u2="&#x14c;" k="20" />
    <hkern u1="L" u2="&#x122;" k="20" />
    <hkern u1="L" u2="&#x120;" k="20" />
    <hkern u1="L" u2="&#x11e;" k="20" />
    <hkern u1="L" u2="&#x11c;" k="20" />
    <hkern u1="L" u2="&#x10c;" k="20" />
    <hkern u1="L" u2="&#x10a;" k="20" />
    <hkern u1="L" u2="&#x108;" k="20" />
    <hkern u1="L" u2="&#x106;" k="20" />
    <hkern u1="L" u2="&#x105;" k="-30" />
    <hkern u1="L" u2="&#x103;" k="-30" />
    <hkern u1="L" u2="&#x101;" k="-30" />
    <hkern u1="L" u2="&#xe6;" k="-30" />
    <hkern u1="L" u2="&#xe5;" k="-30" />
    <hkern u1="L" u2="&#xe4;" k="-30" />
    <hkern u1="L" u2="&#xe3;" k="-30" />
    <hkern u1="L" u2="&#xe2;" k="-30" />
    <hkern u1="L" u2="&#xe1;" k="-30" />
    <hkern u1="L" u2="&#xe0;" k="-30" />
    <hkern u1="L" u2="&#xdd;" k="110" />
    <hkern u1="L" u2="&#xd8;" k="20" />
    <hkern u1="L" u2="&#xd6;" k="20" />
    <hkern u1="L" u2="&#xd5;" k="20" />
    <hkern u1="L" u2="&#xd4;" k="20" />
    <hkern u1="L" u2="&#xd3;" k="20" />
    <hkern u1="L" u2="&#xd2;" k="20" />
    <hkern u1="L" u2="&#xc7;" k="20" />
    <hkern u1="L" u2="&#xa9;" k="20" />
    <hkern u1="L" u2="v" k="40" />
    <hkern u1="L" u2="_" k="-70" />
    <hkern u1="L" u2="V" k="90" />
    <hkern u1="L" u2="Q" k="20" />
    <hkern u1="L" u2="O" k="20" />
    <hkern u1="L" u2="G" k="20" />
    <hkern u1="L" u2="&#x2e;" k="-60" />
    <hkern u1="L" u2="&#x2a;" k="80" />
    <hkern u1="L" u2="&#x27;" k="125" />
    <hkern u1="L" u2="&#x22;" k="125" />
    <hkern u1="O" u2="&#x2026;" k="45" />
    <hkern u1="O" u2="&#x2014;" k="-15" />
    <hkern u1="O" u2="&#x2013;" k="-15" />
    <hkern u1="O" u2="&#x218;" k="5" />
    <hkern u1="O" u2="&#x1fe;" k="-5" />
    <hkern u1="O" u2="&#x164;" k="15" />
    <hkern u1="O" u2="&#x162;" k="15" />
    <hkern u1="O" u2="&#x160;" k="5" />
    <hkern u1="O" u2="&#x15e;" k="5" />
    <hkern u1="O" u2="&#x15c;" k="5" />
    <hkern u1="O" u2="&#x15a;" k="5" />
    <hkern u1="O" u2="&#x152;" k="-5" />
    <hkern u1="O" u2="&#x150;" k="-5" />
    <hkern u1="O" u2="&#x14e;" k="-5" />
    <hkern u1="O" u2="&#x14c;" k="-5" />
    <hkern u1="O" u2="&#x134;" k="-20" />
    <hkern u1="O" u2="&#x122;" k="-5" />
    <hkern u1="O" u2="&#x120;" k="-5" />
    <hkern u1="O" u2="&#x11e;" k="-5" />
    <hkern u1="O" u2="&#x11c;" k="-5" />
    <hkern u1="O" u2="&#x10c;" k="-5" />
    <hkern u1="O" u2="&#x10a;" k="-5" />
    <hkern u1="O" u2="&#x108;" k="-5" />
    <hkern u1="O" u2="&#x106;" k="-5" />
    <hkern u1="O" u2="&#xd8;" k="-5" />
    <hkern u1="O" u2="&#xd6;" k="-5" />
    <hkern u1="O" u2="&#xd5;" k="-5" />
    <hkern u1="O" u2="&#xd4;" k="-5" />
    <hkern u1="O" u2="&#xd3;" k="-5" />
    <hkern u1="O" u2="&#xd2;" k="-5" />
    <hkern u1="O" u2="&#xc7;" k="-5" />
    <hkern u1="O" u2="&#xa9;" k="-5" />
    <hkern u1="O" u2="X" k="10" />
    <hkern u1="O" u2="V" k="15" />
    <hkern u1="O" u2="T" k="15" />
    <hkern u1="O" u2="S" k="5" />
    <hkern u1="O" u2="Q" k="-5" />
    <hkern u1="O" u2="O" k="-5" />
    <hkern u1="O" u2="J" k="-20" />
    <hkern u1="O" u2="G" k="-5" />
    <hkern u1="O" u2="C" k="-5" />
    <hkern u1="O" u2="&#x2e;" k="45" />
    <hkern u1="O" u2="&#x2d;" k="-15" />
    <hkern u1="O" u2="&#x2c;" k="45" />
    <hkern u1="P" u2="&#x2026;" k="140" />
    <hkern u1="P" u2="&#x2014;" k="10" />
    <hkern u1="P" u2="&#x2013;" k="10" />
    <hkern u1="P" u2="&#x1ef2;" k="5" />
    <hkern u1="P" u2="&#x1ff;" k="15" />
    <hkern u1="P" u2="&#x1fd;" k="10" />
    <hkern u1="P" u2="&#x1fc;" k="10" />
    <hkern u1="P" u2="&#x1fb;" k="10" />
    <hkern u1="P" u2="&#x1fa;" k="10" />
    <hkern u1="P" u2="&#x178;" k="5" />
    <hkern u1="P" u2="&#x176;" k="5" />
    <hkern u1="P" u2="&#x153;" k="15" />
    <hkern u1="P" u2="&#x151;" k="15" />
    <hkern u1="P" u2="&#x14f;" k="15" />
    <hkern u1="P" u2="&#x14d;" k="15" />
    <hkern u1="P" u2="&#x123;" k="15" />
    <hkern u1="P" u2="&#x121;" k="15" />
    <hkern u1="P" u2="&#x11f;" k="15" />
    <hkern u1="P" u2="&#x11d;" k="15" />
    <hkern u1="P" u2="&#x11b;" k="15" />
    <hkern u1="P" u2="&#x119;" k="15" />
    <hkern u1="P" u2="&#x117;" k="15" />
    <hkern u1="P" u2="&#x115;" k="15" />
    <hkern u1="P" u2="&#x113;" k="15" />
    <hkern u1="P" u2="&#x111;" k="15" />
    <hkern u1="P" u2="&#x10f;" k="15" />
    <hkern u1="P" u2="&#x10d;" k="15" />
    <hkern u1="P" u2="&#x10b;" k="15" />
    <hkern u1="P" u2="&#x109;" k="15" />
    <hkern u1="P" u2="&#x107;" k="15" />
    <hkern u1="P" u2="&#x105;" k="10" />
    <hkern u1="P" u2="&#x104;" k="10" />
    <hkern u1="P" u2="&#x103;" k="10" />
    <hkern u1="P" u2="&#x102;" k="10" />
    <hkern u1="P" u2="&#x101;" k="10" />
    <hkern u1="P" u2="&#x100;" k="10" />
    <hkern u1="P" u2="&#xf8;" k="15" />
    <hkern u1="P" u2="&#xf6;" k="15" />
    <hkern u1="P" u2="&#xf5;" k="15" />
    <hkern u1="P" u2="&#xf4;" k="15" />
    <hkern u1="P" u2="&#xf3;" k="15" />
    <hkern u1="P" u2="&#xf2;" k="15" />
    <hkern u1="P" u2="&#xf0;" k="15" />
    <hkern u1="P" u2="&#xeb;" k="15" />
    <hkern u1="P" u2="&#xea;" k="15" />
    <hkern u1="P" u2="&#xe9;" k="15" />
    <hkern u1="P" u2="&#xe8;" k="15" />
    <hkern u1="P" u2="&#xe7;" k="15" />
    <hkern u1="P" u2="&#xe6;" k="10" />
    <hkern u1="P" u2="&#xe5;" k="10" />
    <hkern u1="P" u2="&#xe4;" k="10" />
    <hkern u1="P" u2="&#xe3;" k="10" />
    <hkern u1="P" u2="&#xe2;" k="10" />
    <hkern u1="P" u2="&#xe1;" k="10" />
    <hkern u1="P" u2="&#xe0;" k="10" />
    <hkern u1="P" u2="&#xdd;" k="5" />
    <hkern u1="P" u2="&#xc6;" k="10" />
    <hkern u1="P" u2="&#xc5;" k="10" />
    <hkern u1="P" u2="&#xc4;" k="10" />
    <hkern u1="P" u2="&#xc3;" k="10" />
    <hkern u1="P" u2="&#xc2;" k="10" />
    <hkern u1="P" u2="&#xc1;" k="10" />
    <hkern u1="P" u2="&#xc0;" k="10" />
    <hkern u1="P" u2="q" k="15" />
    <hkern u1="P" u2="o" k="15" />
    <hkern u1="P" u2="g" k="15" />
    <hkern u1="P" u2="e" k="15" />
    <hkern u1="P" u2="d" k="15" />
    <hkern u1="P" u2="c" k="15" />
    <hkern u1="P" u2="a" k="10" />
    <hkern u1="P" u2="_" k="100" />
    <hkern u1="P" u2="Y" k="5" />
    <hkern u1="P" u2="A" k="10" />
    <hkern u1="P" u2="&#x2f;" k="45" />
    <hkern u1="P" u2="&#x2e;" k="140" />
    <hkern u1="P" u2="&#x2d;" k="10" />
    <hkern u1="P" u2="&#x2c;" k="140" />
    <hkern u1="Q" u2="&#x2026;" k="45" />
    <hkern u1="Q" u2="&#x2014;" k="-15" />
    <hkern u1="Q" u2="&#x2013;" k="-15" />
    <hkern u1="Q" u2="&#x218;" k="5" />
    <hkern u1="Q" u2="&#x1fe;" k="-5" />
    <hkern u1="Q" u2="&#x164;" k="15" />
    <hkern u1="Q" u2="&#x162;" k="15" />
    <hkern u1="Q" u2="&#x160;" k="5" />
    <hkern u1="Q" u2="&#x15e;" k="5" />
    <hkern u1="Q" u2="&#x15c;" k="5" />
    <hkern u1="Q" u2="&#x15a;" k="5" />
    <hkern u1="Q" u2="&#x152;" k="-5" />
    <hkern u1="Q" u2="&#x150;" k="-5" />
    <hkern u1="Q" u2="&#x14e;" k="-5" />
    <hkern u1="Q" u2="&#x14c;" k="-5" />
    <hkern u1="Q" u2="&#x134;" k="-20" />
    <hkern u1="Q" u2="&#x122;" k="-5" />
    <hkern u1="Q" u2="&#x120;" k="-5" />
    <hkern u1="Q" u2="&#x11e;" k="-5" />
    <hkern u1="Q" u2="&#x11c;" k="-5" />
    <hkern u1="Q" u2="&#x10c;" k="-5" />
    <hkern u1="Q" u2="&#x10a;" k="-5" />
    <hkern u1="Q" u2="&#x108;" k="-5" />
    <hkern u1="Q" u2="&#x106;" k="-5" />
    <hkern u1="Q" u2="&#xd8;" k="-5" />
    <hkern u1="Q" u2="&#xd6;" k="-5" />
    <hkern u1="Q" u2="&#xd5;" k="-5" />
    <hkern u1="Q" u2="&#xd4;" k="-5" />
    <hkern u1="Q" u2="&#xd3;" k="-5" />
    <hkern u1="Q" u2="&#xd2;" k="-5" />
    <hkern u1="Q" u2="&#xc7;" k="-5" />
    <hkern u1="Q" u2="&#xa9;" k="-5" />
    <hkern u1="Q" u2="X" k="10" />
    <hkern u1="Q" u2="V" k="15" />
    <hkern u1="Q" u2="T" k="15" />
    <hkern u1="Q" u2="S" k="5" />
    <hkern u1="Q" u2="Q" k="-5" />
    <hkern u1="Q" u2="O" k="-5" />
    <hkern u1="Q" u2="J" k="-20" />
    <hkern u1="Q" u2="G" k="-5" />
    <hkern u1="Q" u2="C" k="-5" />
    <hkern u1="Q" u2="&#x2e;" k="45" />
    <hkern u1="Q" u2="&#x2d;" k="-15" />
    <hkern u1="Q" u2="&#x2c;" k="45" />
    <hkern u1="R" u2="&#x2026;" k="-20" />
    <hkern u1="R" u2="&#x164;" k="20" />
    <hkern u1="R" u2="&#x162;" k="20" />
    <hkern u1="R" u2="&#x2e;" k="-20" />
    <hkern u1="S" u2="&#x2026;" k="-10" />
    <hkern u1="S" u2="&#x1ef2;" k="40" />
    <hkern u1="S" u2="&#x218;" k="15" />
    <hkern u1="S" u2="&#x1fe;" k="5" />
    <hkern u1="S" u2="&#x178;" k="40" />
    <hkern u1="S" u2="&#x176;" k="40" />
    <hkern u1="S" u2="&#x164;" k="20" />
    <hkern u1="S" u2="&#x162;" k="20" />
    <hkern u1="S" u2="&#x160;" k="15" />
    <hkern u1="S" u2="&#x15e;" k="15" />
    <hkern u1="S" u2="&#x15c;" k="15" />
    <hkern u1="S" u2="&#x15a;" k="15" />
    <hkern u1="S" u2="&#x152;" k="5" />
    <hkern u1="S" u2="&#x150;" k="5" />
    <hkern u1="S" u2="&#x14e;" k="5" />
    <hkern u1="S" u2="&#x14c;" k="5" />
    <hkern u1="S" u2="&#x122;" k="5" />
    <hkern u1="S" u2="&#x120;" k="5" />
    <hkern u1="S" u2="&#x11e;" k="5" />
    <hkern u1="S" u2="&#x11c;" k="5" />
    <hkern u1="S" u2="&#x10c;" k="5" />
    <hkern u1="S" u2="&#x10a;" k="5" />
    <hkern u1="S" u2="&#x108;" k="5" />
    <hkern u1="S" u2="&#x106;" k="5" />
    <hkern u1="S" u2="&#xdd;" k="40" />
    <hkern u1="S" u2="&#xd8;" k="5" />
    <hkern u1="S" u2="&#xd6;" k="5" />
    <hkern u1="S" u2="&#xd5;" k="5" />
    <hkern u1="S" u2="&#xd4;" k="5" />
    <hkern u1="S" u2="&#xd3;" k="5" />
    <hkern u1="S" u2="&#xd2;" k="5" />
    <hkern u1="S" u2="&#xc7;" k="5" />
    <hkern u1="S" u2="&#xa9;" k="5" />
    <hkern u1="S" u2="V" k="15" />
    <hkern u1="S" u2="Q" k="5" />
    <hkern u1="S" u2="O" k="5" />
    <hkern u1="S" u2="G" k="5" />
    <hkern u1="S" u2="&#x2e;" k="-10" />
    <hkern u1="T" u2="&#x2026;" k="110" />
    <hkern u1="T" u2="&#x2014;" k="110" />
    <hkern u1="T" u2="&#x2013;" k="110" />
    <hkern u1="T" u2="&#x1ef3;" k="80" />
    <hkern u1="T" u2="&#x1e85;" k="55" />
    <hkern u1="T" u2="&#x1e83;" k="55" />
    <hkern u1="T" u2="&#x1e81;" k="55" />
    <hkern u1="T" u2="&#x219;" k="85" />
    <hkern u1="T" u2="&#x1ff;" k="85" />
    <hkern u1="T" u2="&#x1fe;" k="15" />
    <hkern u1="T" u2="&#x1fd;" k="75" />
    <hkern u1="T" u2="&#x1fc;" k="30" />
    <hkern u1="T" u2="&#x1fb;" k="75" />
    <hkern u1="T" u2="&#x1fa;" k="30" />
    <hkern u1="T" u2="&#x177;" k="80" />
    <hkern u1="T" u2="&#x175;" k="55" />
    <hkern u1="T" u2="&#x173;" k="80" />
    <hkern u1="T" u2="&#x171;" k="80" />
    <hkern u1="T" u2="&#x16f;" k="80" />
    <hkern u1="T" u2="&#x16d;" k="80" />
    <hkern u1="T" u2="&#x16b;" k="80" />
    <hkern u1="T" u2="&#x169;" k="80" />
    <hkern u1="T" u2="&#x161;" k="85" />
    <hkern u1="T" u2="&#x15f;" k="85" />
    <hkern u1="T" u2="&#x15d;" k="85" />
    <hkern u1="T" u2="&#x15b;" k="85" />
    <hkern u1="T" u2="&#x159;" k="100" />
    <hkern u1="T" u2="&#x157;" k="100" />
    <hkern u1="T" u2="&#x155;" k="100" />
    <hkern u1="T" u2="&#x153;" k="85" />
    <hkern u1="T" u2="&#x152;" k="15" />
    <hkern u1="T" u2="&#x151;" k="85" />
    <hkern u1="T" u2="&#x150;" k="15" />
    <hkern u1="T" u2="&#x14f;" k="85" />
    <hkern u1="T" u2="&#x14e;" k="15" />
    <hkern u1="T" u2="&#x14d;" k="85" />
    <hkern u1="T" u2="&#x14c;" k="15" />
    <hkern u1="T" u2="&#x14b;" k="100" />
    <hkern u1="T" u2="&#x148;" k="100" />
    <hkern u1="T" u2="&#x146;" k="100" />
    <hkern u1="T" u2="&#x144;" k="100" />
    <hkern u1="T" u2="&#x123;" k="85" />
    <hkern u1="T" u2="&#x122;" k="15" />
    <hkern u1="T" u2="&#x121;" k="85" />
    <hkern u1="T" u2="&#x120;" k="15" />
    <hkern u1="T" u2="&#x11f;" k="85" />
    <hkern u1="T" u2="&#x11e;" k="15" />
    <hkern u1="T" u2="&#x11d;" k="85" />
    <hkern u1="T" u2="&#x11c;" k="15" />
    <hkern u1="T" u2="&#x11b;" k="85" />
    <hkern u1="T" u2="&#x119;" k="85" />
    <hkern u1="T" u2="&#x117;" k="85" />
    <hkern u1="T" u2="&#x115;" k="85" />
    <hkern u1="T" u2="&#x113;" k="85" />
    <hkern u1="T" u2="&#x111;" k="85" />
    <hkern u1="T" u2="&#x10f;" k="85" />
    <hkern u1="T" u2="&#x10d;" k="85" />
    <hkern u1="T" u2="&#x10c;" k="15" />
    <hkern u1="T" u2="&#x10b;" k="85" />
    <hkern u1="T" u2="&#x10a;" k="15" />
    <hkern u1="T" u2="&#x109;" k="85" />
    <hkern u1="T" u2="&#x108;" k="15" />
    <hkern u1="T" u2="&#x107;" k="85" />
    <hkern u1="T" u2="&#x106;" k="15" />
    <hkern u1="T" u2="&#x105;" k="75" />
    <hkern u1="T" u2="&#x104;" k="30" />
    <hkern u1="T" u2="&#x103;" k="75" />
    <hkern u1="T" u2="&#x102;" k="30" />
    <hkern u1="T" u2="&#x101;" k="75" />
    <hkern u1="T" u2="&#x100;" k="30" />
    <hkern u1="T" u2="&#xff;" k="80" />
    <hkern u1="T" u2="&#xfd;" k="80" />
    <hkern u1="T" u2="&#xfc;" k="80" />
    <hkern u1="T" u2="&#xfb;" k="80" />
    <hkern u1="T" u2="&#xfa;" k="80" />
    <hkern u1="T" u2="&#xf9;" k="80" />
    <hkern u1="T" u2="&#xf8;" k="85" />
    <hkern u1="T" u2="&#xf6;" k="85" />
    <hkern u1="T" u2="&#xf5;" k="85" />
    <hkern u1="T" u2="&#xf4;" k="85" />
    <hkern u1="T" u2="&#xf3;" k="85" />
    <hkern u1="T" u2="&#xf2;" k="85" />
    <hkern u1="T" u2="&#xf1;" k="100" />
    <hkern u1="T" u2="&#xf0;" k="85" />
    <hkern u1="T" u2="&#xef;" k="-20" />
    <hkern u1="T" u2="&#xee;" k="-10" />
    <hkern u1="T" u2="&#xeb;" k="85" />
    <hkern u1="T" u2="&#xea;" k="85" />
    <hkern u1="T" u2="&#xe9;" k="85" />
    <hkern u1="T" u2="&#xe8;" k="85" />
    <hkern u1="T" u2="&#xe7;" k="85" />
    <hkern u1="T" u2="&#xe6;" k="75" />
    <hkern u1="T" u2="&#xe5;" k="75" />
    <hkern u1="T" u2="&#xe4;" k="75" />
    <hkern u1="T" u2="&#xe3;" k="75" />
    <hkern u1="T" u2="&#xe2;" k="75" />
    <hkern u1="T" u2="&#xe1;" k="75" />
    <hkern u1="T" u2="&#xe0;" k="75" />
    <hkern u1="T" u2="&#xd8;" k="15" />
    <hkern u1="T" u2="&#xd6;" k="15" />
    <hkern u1="T" u2="&#xd5;" k="15" />
    <hkern u1="T" u2="&#xd4;" k="15" />
    <hkern u1="T" u2="&#xd3;" k="15" />
    <hkern u1="T" u2="&#xd2;" k="15" />
    <hkern u1="T" u2="&#xc7;" k="15" />
    <hkern u1="T" u2="&#xc6;" k="30" />
    <hkern u1="T" u2="&#xc5;" k="30" />
    <hkern u1="T" u2="&#xc4;" k="30" />
    <hkern u1="T" u2="&#xc3;" k="30" />
    <hkern u1="T" u2="&#xc2;" k="30" />
    <hkern u1="T" u2="&#xc1;" k="30" />
    <hkern u1="T" u2="&#xc0;" k="30" />
    <hkern u1="T" u2="&#xa9;" k="15" />
    <hkern u1="T" u2="z" k="80" />
    <hkern u1="T" u2="y" k="80" />
    <hkern u1="T" u2="x" k="55" />
    <hkern u1="T" u2="v" k="55" />
    <hkern u1="T" u2="r" k="100" />
    <hkern u1="T" u2="q" k="85" />
    <hkern u1="T" u2="p" k="100" />
    <hkern u1="T" u2="o" k="85" />
    <hkern u1="T" u2="n" k="100" />
    <hkern u1="T" u2="g" k="85" />
    <hkern u1="T" u2="e" k="85" />
    <hkern u1="T" u2="d" k="85" />
    <hkern u1="T" u2="_" k="45" />
    <hkern u1="T" u2="V" k="-15" />
    <hkern u1="T" u2="Q" k="15" />
    <hkern u1="T" u2="O" k="15" />
    <hkern u1="T" u2="G" k="15" />
    <hkern u1="T" u2="&#x2f;" k="85" />
    <hkern u1="T" u2="&#x2e;" k="110" />
    <hkern u1="U" u2="&#x1ef2;" k="10" />
    <hkern u1="U" u2="&#x178;" k="10" />
    <hkern u1="U" u2="&#x176;" k="10" />
    <hkern u1="U" u2="&#xdd;" k="10" />
    <hkern u1="U" u2="Y" k="10" />
    <hkern u1="V" u2="&#x2026;" k="100" />
    <hkern u1="V" u2="&#x2014;" k="35" />
    <hkern u1="V" u2="&#x2013;" k="35" />
    <hkern u1="V" u2="&#x1ef2;" k="-20" />
    <hkern u1="V" u2="&#x1e85;" k="-20" />
    <hkern u1="V" u2="&#x1e83;" k="-20" />
    <hkern u1="V" u2="&#x1e81;" k="-20" />
    <hkern u1="V" u2="&#x219;" k="10" />
    <hkern u1="V" u2="&#x1ff;" k="20" />
    <hkern u1="V" u2="&#x1fe;" k="-10" />
    <hkern u1="V" u2="&#x1fd;" k="16" />
    <hkern u1="V" u2="&#x1fc;" k="-10" />
    <hkern u1="V" u2="&#x1fb;" k="16" />
    <hkern u1="V" u2="&#x1fa;" k="-10" />
    <hkern u1="V" u2="&#x178;" k="-20" />
    <hkern u1="V" u2="&#x176;" k="-20" />
    <hkern u1="V" u2="&#x175;" k="-20" />
    <hkern u1="V" u2="&#x164;" k="-15" />
    <hkern u1="V" u2="&#x162;" k="-15" />
    <hkern u1="V" u2="&#x161;" k="10" />
    <hkern u1="V" u2="&#x15f;" k="10" />
    <hkern u1="V" u2="&#x15d;" k="10" />
    <hkern u1="V" u2="&#x15b;" k="10" />
    <hkern u1="V" u2="&#x159;" k="10" />
    <hkern u1="V" u2="&#x157;" k="10" />
    <hkern u1="V" u2="&#x155;" k="10" />
    <hkern u1="V" u2="&#x153;" k="20" />
    <hkern u1="V" u2="&#x152;" k="-10" />
    <hkern u1="V" u2="&#x151;" k="20" />
    <hkern u1="V" u2="&#x150;" k="-10" />
    <hkern u1="V" u2="&#x14f;" k="20" />
    <hkern u1="V" u2="&#x14e;" k="-10" />
    <hkern u1="V" u2="&#x14d;" k="20" />
    <hkern u1="V" u2="&#x14c;" k="-10" />
    <hkern u1="V" u2="&#x14b;" k="10" />
    <hkern u1="V" u2="&#x148;" k="10" />
    <hkern u1="V" u2="&#x146;" k="10" />
    <hkern u1="V" u2="&#x144;" k="10" />
    <hkern u1="V" u2="&#x123;" k="20" />
    <hkern u1="V" u2="&#x122;" k="-10" />
    <hkern u1="V" u2="&#x121;" k="20" />
    <hkern u1="V" u2="&#x120;" k="-10" />
    <hkern u1="V" u2="&#x11f;" k="20" />
    <hkern u1="V" u2="&#x11e;" k="-10" />
    <hkern u1="V" u2="&#x11d;" k="20" />
    <hkern u1="V" u2="&#x11c;" k="-10" />
    <hkern u1="V" u2="&#x11b;" k="20" />
    <hkern u1="V" u2="&#x119;" k="20" />
    <hkern u1="V" u2="&#x117;" k="20" />
    <hkern u1="V" u2="&#x115;" k="20" />
    <hkern u1="V" u2="&#x113;" k="20" />
    <hkern u1="V" u2="&#x111;" k="20" />
    <hkern u1="V" u2="&#x10f;" k="20" />
    <hkern u1="V" u2="&#x10d;" k="20" />
    <hkern u1="V" u2="&#x10c;" k="-10" />
    <hkern u1="V" u2="&#x10b;" k="20" />
    <hkern u1="V" u2="&#x10a;" k="-10" />
    <hkern u1="V" u2="&#x109;" k="20" />
    <hkern u1="V" u2="&#x108;" k="-10" />
    <hkern u1="V" u2="&#x107;" k="20" />
    <hkern u1="V" u2="&#x106;" k="-10" />
    <hkern u1="V" u2="&#x105;" k="16" />
    <hkern u1="V" u2="&#x104;" k="-10" />
    <hkern u1="V" u2="&#x103;" k="16" />
    <hkern u1="V" u2="&#x102;" k="-10" />
    <hkern u1="V" u2="&#x101;" k="16" />
    <hkern u1="V" u2="&#x100;" k="-10" />
    <hkern u1="V" u2="&#xf8;" k="20" />
    <hkern u1="V" u2="&#xf6;" k="20" />
    <hkern u1="V" u2="&#xf5;" k="20" />
    <hkern u1="V" u2="&#xf4;" k="20" />
    <hkern u1="V" u2="&#xf3;" k="20" />
    <hkern u1="V" u2="&#xf2;" k="20" />
    <hkern u1="V" u2="&#xf1;" k="10" />
    <hkern u1="V" u2="&#xf0;" k="20" />
    <hkern u1="V" u2="&#xef;" k="-30" />
    <hkern u1="V" u2="&#xee;" k="-10" />
    <hkern u1="V" u2="&#xeb;" k="20" />
    <hkern u1="V" u2="&#xea;" k="20" />
    <hkern u1="V" u2="&#xe9;" k="20" />
    <hkern u1="V" u2="&#xe8;" k="20" />
    <hkern u1="V" u2="&#xe7;" k="20" />
    <hkern u1="V" u2="&#xe6;" k="16" />
    <hkern u1="V" u2="&#xe5;" k="16" />
    <hkern u1="V" u2="&#xe4;" k="16" />
    <hkern u1="V" u2="&#xe3;" k="16" />
    <hkern u1="V" u2="&#xe2;" k="16" />
    <hkern u1="V" u2="&#xe1;" k="16" />
    <hkern u1="V" u2="&#xe0;" k="16" />
    <hkern u1="V" u2="&#xdd;" k="-20" />
    <hkern u1="V" u2="&#xd8;" k="-10" />
    <hkern u1="V" u2="&#xd6;" k="-10" />
    <hkern u1="V" u2="&#xd5;" k="-10" />
    <hkern u1="V" u2="&#xd4;" k="-10" />
    <hkern u1="V" u2="&#xd3;" k="-10" />
    <hkern u1="V" u2="&#xd2;" k="-10" />
    <hkern u1="V" u2="&#xc7;" k="-10" />
    <hkern u1="V" u2="&#xc6;" k="-10" />
    <hkern u1="V" u2="&#xc5;" k="-10" />
    <hkern u1="V" u2="&#xc4;" k="-10" />
    <hkern u1="V" u2="&#xc3;" k="-10" />
    <hkern u1="V" u2="&#xc2;" k="-10" />
    <hkern u1="V" u2="&#xc1;" k="-10" />
    <hkern u1="V" u2="&#xc0;" k="-10" />
    <hkern u1="V" u2="&#xa9;" k="-10" />
    <hkern u1="V" u2="w" k="-20" />
    <hkern u1="V" u2="s" k="10" />
    <hkern u1="V" u2="r" k="10" />
    <hkern u1="V" u2="q" k="20" />
    <hkern u1="V" u2="p" k="10" />
    <hkern u1="V" u2="o" k="20" />
    <hkern u1="V" u2="n" k="10" />
    <hkern u1="V" u2="m" k="10" />
    <hkern u1="V" u2="g" k="20" />
    <hkern u1="V" u2="e" k="20" />
    <hkern u1="V" u2="d" k="20" />
    <hkern u1="V" u2="c" k="20" />
    <hkern u1="V" u2="a" k="16" />
    <hkern u1="V" u2="_" k="65" />
    <hkern u1="V" u2="Y" k="-20" />
    <hkern u1="V" u2="V" k="-15" />
    <hkern u1="V" u2="T" k="-15" />
    <hkern u1="V" u2="Q" k="-10" />
    <hkern u1="V" u2="O" k="-10" />
    <hkern u1="V" u2="G" k="-10" />
    <hkern u1="V" u2="C" k="-10" />
    <hkern u1="V" u2="A" k="-10" />
    <hkern u1="V" u2="&#x2f;" k="55" />
    <hkern u1="V" u2="&#x2e;" k="100" />
    <hkern u1="V" u2="&#x2d;" k="35" />
    <hkern u1="V" u2="&#x2c;" k="100" />
    <hkern u1="W" u2="&#x2026;" k="55" />
    <hkern u1="W" u2="&#x2014;" k="20" />
    <hkern u1="W" u2="&#x2013;" k="20" />
    <hkern u1="W" u2="&#x1ff;" k="10" />
    <hkern u1="W" u2="&#x1fd;" k="10" />
    <hkern u1="W" u2="&#x1fb;" k="10" />
    <hkern u1="W" u2="&#x153;" k="10" />
    <hkern u1="W" u2="&#x151;" k="10" />
    <hkern u1="W" u2="&#x14f;" k="10" />
    <hkern u1="W" u2="&#x14d;" k="10" />
    <hkern u1="W" u2="&#x123;" k="10" />
    <hkern u1="W" u2="&#x121;" k="10" />
    <hkern u1="W" u2="&#x11f;" k="10" />
    <hkern u1="W" u2="&#x11d;" k="10" />
    <hkern u1="W" u2="&#x11b;" k="10" />
    <hkern u1="W" u2="&#x119;" k="10" />
    <hkern u1="W" u2="&#x117;" k="10" />
    <hkern u1="W" u2="&#x115;" k="10" />
    <hkern u1="W" u2="&#x113;" k="10" />
    <hkern u1="W" u2="&#x111;" k="10" />
    <hkern u1="W" u2="&#x10f;" k="10" />
    <hkern u1="W" u2="&#x10d;" k="10" />
    <hkern u1="W" u2="&#x10b;" k="10" />
    <hkern u1="W" u2="&#x109;" k="10" />
    <hkern u1="W" u2="&#x107;" k="10" />
    <hkern u1="W" u2="&#x105;" k="10" />
    <hkern u1="W" u2="&#x103;" k="10" />
    <hkern u1="W" u2="&#x101;" k="10" />
    <hkern u1="W" u2="&#xf8;" k="10" />
    <hkern u1="W" u2="&#xf6;" k="10" />
    <hkern u1="W" u2="&#xf5;" k="10" />
    <hkern u1="W" u2="&#xf4;" k="10" />
    <hkern u1="W" u2="&#xf3;" k="10" />
    <hkern u1="W" u2="&#xf2;" k="10" />
    <hkern u1="W" u2="&#xf0;" k="10" />
    <hkern u1="W" u2="&#xef;" k="-20" />
    <hkern u1="W" u2="&#xee;" k="-10" />
    <hkern u1="W" u2="&#xeb;" k="10" />
    <hkern u1="W" u2="&#xea;" k="10" />
    <hkern u1="W" u2="&#xe9;" k="10" />
    <hkern u1="W" u2="&#xe8;" k="10" />
    <hkern u1="W" u2="&#xe7;" k="10" />
    <hkern u1="W" u2="&#xe6;" k="10" />
    <hkern u1="W" u2="&#xe5;" k="10" />
    <hkern u1="W" u2="&#xe4;" k="10" />
    <hkern u1="W" u2="&#xe3;" k="10" />
    <hkern u1="W" u2="&#xe2;" k="10" />
    <hkern u1="W" u2="&#xe1;" k="10" />
    <hkern u1="W" u2="&#xe0;" k="10" />
    <hkern u1="W" u2="q" k="10" />
    <hkern u1="W" u2="o" k="10" />
    <hkern u1="W" u2="g" k="10" />
    <hkern u1="W" u2="f" k="-25" />
    <hkern u1="W" u2="e" k="10" />
    <hkern u1="W" u2="d" k="10" />
    <hkern u1="W" u2="_" k="45" />
    <hkern u1="W" u2="&#x2f;" k="55" />
    <hkern u1="W" u2="&#x2e;" k="55" />
    <hkern u1="X" u2="&#x2026;" k="-30" />
    <hkern u1="X" u2="&#x2014;" k="65" />
    <hkern u1="X" u2="&#x2013;" k="65" />
    <hkern u1="X" u2="&#x1ef2;" k="10" />
    <hkern u1="X" u2="&#x1fe;" k="10" />
    <hkern u1="X" u2="&#x178;" k="10" />
    <hkern u1="X" u2="&#x176;" k="10" />
    <hkern u1="X" u2="&#x152;" k="10" />
    <hkern u1="X" u2="&#x150;" k="10" />
    <hkern u1="X" u2="&#x14e;" k="10" />
    <hkern u1="X" u2="&#x14c;" k="10" />
    <hkern u1="X" u2="&#x122;" k="10" />
    <hkern u1="X" u2="&#x120;" k="10" />
    <hkern u1="X" u2="&#x11e;" k="10" />
    <hkern u1="X" u2="&#x11c;" k="10" />
    <hkern u1="X" u2="&#x11a;" k="7" />
    <hkern u1="X" u2="&#x118;" k="7" />
    <hkern u1="X" u2="&#x116;" k="7" />
    <hkern u1="X" u2="&#x114;" k="7" />
    <hkern u1="X" u2="&#x112;" k="7" />
    <hkern u1="X" u2="&#x10c;" k="10" />
    <hkern u1="X" u2="&#x10a;" k="10" />
    <hkern u1="X" u2="&#x108;" k="10" />
    <hkern u1="X" u2="&#x106;" k="10" />
    <hkern u1="X" u2="&#xef;" k="-20" />
    <hkern u1="X" u2="&#xdd;" k="10" />
    <hkern u1="X" u2="&#xd8;" k="10" />
    <hkern u1="X" u2="&#xd6;" k="10" />
    <hkern u1="X" u2="&#xd5;" k="10" />
    <hkern u1="X" u2="&#xd4;" k="10" />
    <hkern u1="X" u2="&#xd3;" k="10" />
    <hkern u1="X" u2="&#xd2;" k="10" />
    <hkern u1="X" u2="&#xcb;" k="7" />
    <hkern u1="X" u2="&#xca;" k="7" />
    <hkern u1="X" u2="&#xc9;" k="7" />
    <hkern u1="X" u2="&#xc8;" k="7" />
    <hkern u1="X" u2="&#xc7;" k="10" />
    <hkern u1="X" u2="&#xa9;" k="10" />
    <hkern u1="X" u2="_" k="-50" />
    <hkern u1="X" u2="Y" k="10" />
    <hkern u1="X" u2="Q" k="10" />
    <hkern u1="X" u2="O" k="10" />
    <hkern u1="X" u2="G" k="10" />
    <hkern u1="X" u2="E" k="7" />
    <hkern u1="X" u2="C" k="10" />
    <hkern u1="X" u2="&#x2e;" k="-30" />
    <hkern u1="X" u2="&#x2d;" k="65" />
    <hkern u1="X" u2="&#x2c;" k="-30" />
    <hkern u1="Y" u2="V" k="-15" />
    <hkern u1="Z" u2="&#x2026;" k="-30" />
    <hkern u1="Z" u2="&#x2014;" k="120" />
    <hkern u1="Z" u2="&#x2013;" k="120" />
    <hkern u1="Z" u2="&#x1fe;" k="15" />
    <hkern u1="Z" u2="&#x152;" k="15" />
    <hkern u1="Z" u2="&#x150;" k="15" />
    <hkern u1="Z" u2="&#x14e;" k="15" />
    <hkern u1="Z" u2="&#x14c;" k="15" />
    <hkern u1="Z" u2="&#x122;" k="15" />
    <hkern u1="Z" u2="&#x120;" k="15" />
    <hkern u1="Z" u2="&#x11e;" k="15" />
    <hkern u1="Z" u2="&#x11c;" k="15" />
    <hkern u1="Z" u2="&#x10c;" k="15" />
    <hkern u1="Z" u2="&#x10a;" k="15" />
    <hkern u1="Z" u2="&#x108;" k="15" />
    <hkern u1="Z" u2="&#x106;" k="15" />
    <hkern u1="Z" u2="&#xd8;" k="15" />
    <hkern u1="Z" u2="&#xd6;" k="15" />
    <hkern u1="Z" u2="&#xd5;" k="15" />
    <hkern u1="Z" u2="&#xd4;" k="15" />
    <hkern u1="Z" u2="&#xd3;" k="15" />
    <hkern u1="Z" u2="&#xd2;" k="15" />
    <hkern u1="Z" u2="&#xc7;" k="15" />
    <hkern u1="Z" u2="&#xa9;" k="15" />
    <hkern u1="Z" u2="Q" k="15" />
    <hkern u1="Z" u2="O" k="15" />
    <hkern u1="Z" u2="G" k="15" />
    <hkern u1="Z" u2="&#x2e;" k="-30" />
    <hkern u1="[" u2="&#x134;" k="-50" />
    <hkern u1="[" u2="j" k="-60" />
    <hkern u1="[" u2="J" k="-50" />
    <hkern u1="_" u2="&#x1ef2;" k="50" />
    <hkern u1="_" u2="&#x1e85;" k="35" />
    <hkern u1="_" u2="&#x1e84;" k="45" />
    <hkern u1="_" u2="&#x1e83;" k="35" />
    <hkern u1="_" u2="&#x1e82;" k="45" />
    <hkern u1="_" u2="&#x1e81;" k="35" />
    <hkern u1="_" u2="&#x1e80;" k="45" />
    <hkern u1="_" u2="&#x178;" k="50" />
    <hkern u1="_" u2="&#x176;" k="50" />
    <hkern u1="_" u2="&#x175;" k="35" />
    <hkern u1="_" u2="&#x174;" k="45" />
    <hkern u1="_" u2="&#x164;" k="45" />
    <hkern u1="_" u2="&#x162;" k="45" />
    <hkern u1="_" u2="&#x135;" k="-65" />
    <hkern u1="_" u2="&#xdd;" k="50" />
    <hkern u1="_" u2="x" k="-40" />
    <hkern u1="_" u2="w" k="35" />
    <hkern u1="_" u2="v" k="35" />
    <hkern u1="_" u2="j" k="-65" />
    <hkern u1="_" u2="Y" k="50" />
    <hkern u1="_" u2="X" k="-50" />
    <hkern u1="_" u2="W" k="45" />
    <hkern u1="_" u2="V" k="65" />
    <hkern u1="_" u2="T" k="45" />
    <hkern u1="a" u2="&#x1ef2;" k="30" />
    <hkern u1="a" u2="&#x1e85;" k="10" />
    <hkern u1="a" u2="&#x1e83;" k="10" />
    <hkern u1="a" u2="&#x1e81;" k="10" />
    <hkern u1="a" u2="&#x178;" k="30" />
    <hkern u1="a" u2="&#x176;" k="30" />
    <hkern u1="a" u2="&#x175;" k="10" />
    <hkern u1="a" u2="&#x164;" k="70" />
    <hkern u1="a" u2="&#x162;" k="70" />
    <hkern u1="a" u2="&#xdd;" k="30" />
    <hkern u1="a" u2="w" k="10" />
    <hkern u1="a" u2="Y" k="30" />
    <hkern u1="a" u2="V" k="35" />
    <hkern u1="a" u2="T" k="70" />
    <hkern u1="b" u2="&#x2026;" k="20" />
    <hkern u1="b" u2="&#x1e84;" k="10" />
    <hkern u1="b" u2="&#x1e82;" k="10" />
    <hkern u1="b" u2="&#x1e80;" k="10" />
    <hkern u1="b" u2="&#x174;" k="10" />
    <hkern u1="b" u2="&#x164;" k="85" />
    <hkern u1="b" u2="&#x162;" k="85" />
    <hkern u1="b" u2="z" k="20" />
    <hkern u1="b" u2="x" k="10" />
    <hkern u1="b" u2="V" k="35" />
    <hkern u1="b" u2="&#x2e;" k="20" />
    <hkern u1="c" u2="&#x2026;" k="-20" />
    <hkern u1="c" u2="&#x1ef3;" k="5" />
    <hkern u1="c" u2="&#x1ff;" k="15" />
    <hkern u1="c" u2="&#x177;" k="5" />
    <hkern u1="c" u2="&#x173;" k="5" />
    <hkern u1="c" u2="&#x171;" k="5" />
    <hkern u1="c" u2="&#x16f;" k="5" />
    <hkern u1="c" u2="&#x16d;" k="5" />
    <hkern u1="c" u2="&#x16b;" k="5" />
    <hkern u1="c" u2="&#x169;" k="5" />
    <hkern u1="c" u2="&#x164;" k="60" />
    <hkern u1="c" u2="&#x162;" k="60" />
    <hkern u1="c" u2="&#x153;" k="15" />
    <hkern u1="c" u2="&#x151;" k="15" />
    <hkern u1="c" u2="&#x14f;" k="15" />
    <hkern u1="c" u2="&#x14d;" k="15" />
    <hkern u1="c" u2="&#x123;" k="15" />
    <hkern u1="c" u2="&#x121;" k="15" />
    <hkern u1="c" u2="&#x11f;" k="15" />
    <hkern u1="c" u2="&#x11d;" k="15" />
    <hkern u1="c" u2="&#x11b;" k="15" />
    <hkern u1="c" u2="&#x119;" k="15" />
    <hkern u1="c" u2="&#x117;" k="15" />
    <hkern u1="c" u2="&#x115;" k="15" />
    <hkern u1="c" u2="&#x113;" k="15" />
    <hkern u1="c" u2="&#x111;" k="15" />
    <hkern u1="c" u2="&#x10f;" k="15" />
    <hkern u1="c" u2="&#x10d;" k="15" />
    <hkern u1="c" u2="&#x10b;" k="15" />
    <hkern u1="c" u2="&#x109;" k="15" />
    <hkern u1="c" u2="&#x107;" k="15" />
    <hkern u1="c" u2="&#xff;" k="5" />
    <hkern u1="c" u2="&#xfd;" k="5" />
    <hkern u1="c" u2="&#xfc;" k="5" />
    <hkern u1="c" u2="&#xfb;" k="5" />
    <hkern u1="c" u2="&#xfa;" k="5" />
    <hkern u1="c" u2="&#xf9;" k="5" />
    <hkern u1="c" u2="&#xf8;" k="15" />
    <hkern u1="c" u2="&#xf6;" k="15" />
    <hkern u1="c" u2="&#xf5;" k="15" />
    <hkern u1="c" u2="&#xf4;" k="15" />
    <hkern u1="c" u2="&#xf3;" k="15" />
    <hkern u1="c" u2="&#xf2;" k="15" />
    <hkern u1="c" u2="&#xf0;" k="15" />
    <hkern u1="c" u2="&#xeb;" k="15" />
    <hkern u1="c" u2="&#xea;" k="15" />
    <hkern u1="c" u2="&#xe9;" k="15" />
    <hkern u1="c" u2="&#xe8;" k="15" />
    <hkern u1="c" u2="&#xe7;" k="15" />
    <hkern u1="c" u2="y" k="5" />
    <hkern u1="c" u2="x" k="-13" />
    <hkern u1="c" u2="q" k="15" />
    <hkern u1="c" u2="o" k="15" />
    <hkern u1="c" u2="g" k="15" />
    <hkern u1="c" u2="e" k="15" />
    <hkern u1="c" u2="d" k="15" />
    <hkern u1="c" u2="_" k="-40" />
    <hkern u1="c" u2="&#x2e;" k="-20" />
    <hkern u1="e" u2="&#x1e84;" k="10" />
    <hkern u1="e" u2="&#x1e82;" k="10" />
    <hkern u1="e" u2="&#x1e80;" k="10" />
    <hkern u1="e" u2="&#x219;" k="-5" />
    <hkern u1="e" u2="&#x174;" k="10" />
    <hkern u1="e" u2="&#x164;" k="70" />
    <hkern u1="e" u2="&#x162;" k="70" />
    <hkern u1="e" u2="&#x161;" k="-5" />
    <hkern u1="e" u2="&#x15f;" k="-5" />
    <hkern u1="e" u2="&#x15d;" k="-5" />
    <hkern u1="e" u2="&#x15b;" k="-5" />
    <hkern u1="e" u2="V" k="20" />
    <hkern u1="f" u2="&#x2122;" k="-75" />
    <hkern u1="f" u2="&#x2026;" k="75" />
    <hkern u1="f" u2="&#x201d;" k="-45" />
    <hkern u1="f" u2="&#x201c;" k="-40" />
    <hkern u1="f" u2="&#x2019;" k="-45" />
    <hkern u1="f" u2="&#x2018;" k="-40" />
    <hkern u1="f" u2="&#x2014;" k="20" />
    <hkern u1="f" u2="&#x2013;" k="20" />
    <hkern u1="f" u2="&#x1ef2;" k="-65" />
    <hkern u1="f" u2="&#x1e84;" k="-85" />
    <hkern u1="f" u2="&#x1e82;" k="-85" />
    <hkern u1="f" u2="&#x1e80;" k="-85" />
    <hkern u1="f" u2="&#x1ff;" k="13" />
    <hkern u1="f" u2="&#x1fd;" k="10" />
    <hkern u1="f" u2="&#x1fb;" k="10" />
    <hkern u1="f" u2="&#x17d;" k="-55" />
    <hkern u1="f" u2="&#x17b;" k="-55" />
    <hkern u1="f" u2="&#x179;" k="-55" />
    <hkern u1="f" u2="&#x178;" k="-65" />
    <hkern u1="f" u2="&#x176;" k="-65" />
    <hkern u1="f" u2="&#x174;" k="-85" />
    <hkern u1="f" u2="&#x164;" k="-85" />
    <hkern u1="f" u2="&#x162;" k="-85" />
    <hkern u1="f" u2="&#x153;" k="13" />
    <hkern u1="f" u2="&#x151;" k="13" />
    <hkern u1="f" u2="&#x14f;" k="13" />
    <hkern u1="f" u2="&#x14d;" k="13" />
    <hkern u1="f" u2="&#x123;" k="13" />
    <hkern u1="f" u2="&#x121;" k="13" />
    <hkern u1="f" u2="&#x11f;" k="13" />
    <hkern u1="f" u2="&#x11d;" k="13" />
    <hkern u1="f" u2="&#x11b;" k="13" />
    <hkern u1="f" u2="&#x119;" k="13" />
    <hkern u1="f" u2="&#x117;" k="13" />
    <hkern u1="f" u2="&#x115;" k="13" />
    <hkern u1="f" u2="&#x113;" k="13" />
    <hkern u1="f" u2="&#x111;" k="13" />
    <hkern u1="f" u2="&#x10f;" k="13" />
    <hkern u1="f" u2="&#x10d;" k="13" />
    <hkern u1="f" u2="&#x10b;" k="13" />
    <hkern u1="f" u2="&#x109;" k="13" />
    <hkern u1="f" u2="&#x107;" k="13" />
    <hkern u1="f" u2="&#x105;" k="10" />
    <hkern u1="f" u2="&#x103;" k="10" />
    <hkern u1="f" u2="&#x101;" k="10" />
    <hkern u1="f" u2="&#xf8;" k="13" />
    <hkern u1="f" u2="&#xf6;" k="13" />
    <hkern u1="f" u2="&#xf5;" k="13" />
    <hkern u1="f" u2="&#xf4;" k="13" />
    <hkern u1="f" u2="&#xf3;" k="13" />
    <hkern u1="f" u2="&#xf2;" k="13" />
    <hkern u1="f" u2="&#xf0;" k="13" />
    <hkern u1="f" u2="&#xeb;" k="13" />
    <hkern u1="f" u2="&#xea;" k="13" />
    <hkern u1="f" u2="&#xe9;" k="13" />
    <hkern u1="f" u2="&#xe8;" k="13" />
    <hkern u1="f" u2="&#xe7;" k="13" />
    <hkern u1="f" u2="&#xe6;" k="10" />
    <hkern u1="f" u2="&#xe5;" k="10" />
    <hkern u1="f" u2="&#xe4;" k="10" />
    <hkern u1="f" u2="&#xe3;" k="10" />
    <hkern u1="f" u2="&#xe2;" k="10" />
    <hkern u1="f" u2="&#xe1;" k="10" />
    <hkern u1="f" u2="&#xe0;" k="10" />
    <hkern u1="f" u2="&#xdd;" k="-65" />
    <hkern u1="f" u2="&#xba;" k="-20" />
    <hkern u1="f" u2="&#xaa;" k="-20" />
    <hkern u1="f" u2="&#x7d;" k="-100" />
    <hkern u1="f" u2="q" k="13" />
    <hkern u1="f" u2="o" k="13" />
    <hkern u1="f" u2="g" k="13" />
    <hkern u1="f" u2="e" k="13" />
    <hkern u1="f" u2="d" k="13" />
    <hkern u1="f" u2="c" k="13" />
    <hkern u1="f" u2="a" k="10" />
    <hkern u1="f" u2="]" k="-100" />
    <hkern u1="f" u2="\" k="-50" />
    <hkern u1="f" u2="Z" k="-55" />
    <hkern u1="f" u2="Y" k="-65" />
    <hkern u1="f" u2="X" k="-75" />
    <hkern u1="f" u2="W" k="-85" />
    <hkern u1="f" u2="V" k="-95" />
    <hkern u1="f" u2="T" k="-85" />
    <hkern u1="f" u2="&#x3f;" k="-50" />
    <hkern u1="f" u2="&#x2f;" k="10" />
    <hkern u1="f" u2="&#x2e;" k="75" />
    <hkern u1="f" u2="&#x2d;" k="20" />
    <hkern u1="f" u2="&#x2c;" k="75" />
    <hkern u1="f" u2="&#x2a;" k="-50" />
    <hkern u1="f" u2="&#x29;" k="-80" />
    <hkern u1="f" u2="&#x21;" k="-45" />
    <hkern u1="g" u2="&#x1e84;" k="10" />
    <hkern u1="g" u2="&#x1e82;" k="10" />
    <hkern u1="g" u2="&#x1e80;" k="10" />
    <hkern u1="g" u2="&#x174;" k="10" />
    <hkern u1="g" u2="&#x164;" k="80" />
    <hkern u1="g" u2="&#x162;" k="80" />
    <hkern u1="g" u2="V" k="30" />
    <hkern u1="h" u2="&#x164;" k="90" />
    <hkern u1="h" u2="&#x162;" k="90" />
    <hkern u1="h" u2="V" k="20" />
    <hkern u1="k" u2="&#x2026;" k="-30" />
    <hkern u1="k" u2="&#x2014;" k="45" />
    <hkern u1="k" u2="&#x2013;" k="45" />
    <hkern u1="k" u2="&#x1ff;" k="10" />
    <hkern u1="k" u2="&#x1fd;" k="-7" />
    <hkern u1="k" u2="&#x1fb;" k="-7" />
    <hkern u1="k" u2="&#x153;" k="10" />
    <hkern u1="k" u2="&#x151;" k="10" />
    <hkern u1="k" u2="&#x14f;" k="10" />
    <hkern u1="k" u2="&#x14d;" k="10" />
    <hkern u1="k" u2="&#x123;" k="10" />
    <hkern u1="k" u2="&#x121;" k="10" />
    <hkern u1="k" u2="&#x11f;" k="10" />
    <hkern u1="k" u2="&#x11d;" k="10" />
    <hkern u1="k" u2="&#x11b;" k="10" />
    <hkern u1="k" u2="&#x119;" k="10" />
    <hkern u1="k" u2="&#x117;" k="10" />
    <hkern u1="k" u2="&#x115;" k="10" />
    <hkern u1="k" u2="&#x113;" k="10" />
    <hkern u1="k" u2="&#x111;" k="10" />
    <hkern u1="k" u2="&#x10f;" k="10" />
    <hkern u1="k" u2="&#x10d;" k="10" />
    <hkern u1="k" u2="&#x10b;" k="10" />
    <hkern u1="k" u2="&#x109;" k="10" />
    <hkern u1="k" u2="&#x107;" k="10" />
    <hkern u1="k" u2="&#x105;" k="-7" />
    <hkern u1="k" u2="&#x103;" k="-7" />
    <hkern u1="k" u2="&#x101;" k="-7" />
    <hkern u1="k" u2="&#xf8;" k="10" />
    <hkern u1="k" u2="&#xf6;" k="10" />
    <hkern u1="k" u2="&#xf5;" k="10" />
    <hkern u1="k" u2="&#xf4;" k="10" />
    <hkern u1="k" u2="&#xf3;" k="10" />
    <hkern u1="k" u2="&#xf2;" k="10" />
    <hkern u1="k" u2="&#xf0;" k="10" />
    <hkern u1="k" u2="&#xeb;" k="10" />
    <hkern u1="k" u2="&#xea;" k="10" />
    <hkern u1="k" u2="&#xe9;" k="10" />
    <hkern u1="k" u2="&#xe8;" k="10" />
    <hkern u1="k" u2="&#xe7;" k="10" />
    <hkern u1="k" u2="&#xe6;" k="-17" />
    <hkern u1="k" u2="&#xe5;" k="-7" />
    <hkern u1="k" u2="&#xe4;" k="-7" />
    <hkern u1="k" u2="&#xe3;" k="-7" />
    <hkern u1="k" u2="&#xe2;" k="-7" />
    <hkern u1="k" u2="&#xe1;" k="-7" />
    <hkern u1="k" u2="&#xe0;" k="-7" />
    <hkern u1="k" u2="q" k="10" />
    <hkern u1="k" u2="o" k="10" />
    <hkern u1="k" u2="g" k="10" />
    <hkern u1="k" u2="e" k="10" />
    <hkern u1="k" u2="d" k="10" />
    <hkern u1="k" u2="_" k="-30" />
    <hkern u1="k" u2="&#x2e;" k="-30" />
    <hkern u1="m" u2="&#x164;" k="90" />
    <hkern u1="m" u2="&#x162;" k="90" />
    <hkern u1="m" u2="V" k="20" />
    <hkern u1="m" u2="T" k="90" />
    <hkern u1="n" u2="&#x164;" k="90" />
    <hkern u1="n" u2="&#x162;" k="90" />
    <hkern u1="n" u2="V" k="20" />
    <hkern u1="n" u2="T" k="90" />
    <hkern u1="o" u2="&#x2026;" k="20" />
    <hkern u1="o" u2="&#x1e84;" k="10" />
    <hkern u1="o" u2="&#x1e82;" k="10" />
    <hkern u1="o" u2="&#x1e80;" k="10" />
    <hkern u1="o" u2="&#x174;" k="10" />
    <hkern u1="o" u2="&#x164;" k="85" />
    <hkern u1="o" u2="&#x162;" k="85" />
    <hkern u1="o" u2="z" k="20" />
    <hkern u1="o" u2="x" k="10" />
    <hkern u1="o" u2="W" k="10" />
    <hkern u1="o" u2="V" k="20" />
    <hkern u1="o" u2="T" k="85" />
    <hkern u1="o" u2="&#x2e;" k="20" />
    <hkern u1="o" u2="&#x2c;" k="20" />
    <hkern u1="p" u2="&#x2026;" k="20" />
    <hkern u1="p" u2="&#x1e84;" k="10" />
    <hkern u1="p" u2="&#x1e82;" k="10" />
    <hkern u1="p" u2="&#x1e80;" k="10" />
    <hkern u1="p" u2="&#x174;" k="10" />
    <hkern u1="p" u2="&#x164;" k="85" />
    <hkern u1="p" u2="&#x162;" k="85" />
    <hkern u1="p" u2="z" k="20" />
    <hkern u1="p" u2="x" k="10" />
    <hkern u1="p" u2="W" k="10" />
    <hkern u1="p" u2="V" k="20" />
    <hkern u1="p" u2="T" k="85" />
    <hkern u1="p" u2="&#x2e;" k="20" />
    <hkern u1="p" u2="&#x2c;" k="20" />
    <hkern u1="q" u2="&#x1e84;" k="10" />
    <hkern u1="q" u2="&#x1e82;" k="10" />
    <hkern u1="q" u2="&#x1e80;" k="10" />
    <hkern u1="q" u2="&#x174;" k="10" />
    <hkern u1="q" u2="&#x164;" k="80" />
    <hkern u1="q" u2="&#x162;" k="80" />
    <hkern u1="q" u2="W" k="10" />
    <hkern u1="q" u2="V" k="20" />
    <hkern u1="q" u2="T" k="80" />
    <hkern u1="r" u2="&#x2026;" k="115" />
    <hkern u1="r" u2="&#x201c;" k="-40" />
    <hkern u1="r" u2="&#x2014;" k="40" />
    <hkern u1="r" u2="&#x2013;" k="40" />
    <hkern u1="r" u2="&#x1ff;" k="20" />
    <hkern u1="r" u2="&#x1fd;" k="10" />
    <hkern u1="r" u2="&#x1fb;" k="10" />
    <hkern u1="r" u2="&#x167;" k="-20" />
    <hkern u1="r" u2="&#x165;" k="-20" />
    <hkern u1="r" u2="&#x164;" k="30" />
    <hkern u1="r" u2="&#x163;" k="-20" />
    <hkern u1="r" u2="&#x162;" k="30" />
    <hkern u1="r" u2="&#x153;" k="20" />
    <hkern u1="r" u2="&#x151;" k="20" />
    <hkern u1="r" u2="&#x14f;" k="20" />
    <hkern u1="r" u2="&#x14d;" k="20" />
    <hkern u1="r" u2="&#x123;" k="20" />
    <hkern u1="r" u2="&#x121;" k="20" />
    <hkern u1="r" u2="&#x11f;" k="20" />
    <hkern u1="r" u2="&#x11d;" k="20" />
    <hkern u1="r" u2="&#x11b;" k="20" />
    <hkern u1="r" u2="&#x119;" k="20" />
    <hkern u1="r" u2="&#x117;" k="20" />
    <hkern u1="r" u2="&#x115;" k="20" />
    <hkern u1="r" u2="&#x113;" k="20" />
    <hkern u1="r" u2="&#x111;" k="20" />
    <hkern u1="r" u2="&#x10f;" k="20" />
    <hkern u1="r" u2="&#x10d;" k="20" />
    <hkern u1="r" u2="&#x10b;" k="20" />
    <hkern u1="r" u2="&#x109;" k="20" />
    <hkern u1="r" u2="&#x107;" k="20" />
    <hkern u1="r" u2="&#x105;" k="10" />
    <hkern u1="r" u2="&#x103;" k="10" />
    <hkern u1="r" u2="&#x101;" k="10" />
    <hkern u1="r" u2="&#xf8;" k="20" />
    <hkern u1="r" u2="&#xf6;" k="20" />
    <hkern u1="r" u2="&#xf5;" k="20" />
    <hkern u1="r" u2="&#xf4;" k="20" />
    <hkern u1="r" u2="&#xf3;" k="20" />
    <hkern u1="r" u2="&#xf2;" k="20" />
    <hkern u1="r" u2="&#xf0;" k="20" />
    <hkern u1="r" u2="&#xeb;" k="20" />
    <hkern u1="r" u2="&#xea;" k="20" />
    <hkern u1="r" u2="&#xe9;" k="20" />
    <hkern u1="r" u2="&#xe8;" k="20" />
    <hkern u1="r" u2="&#xe7;" k="20" />
    <hkern u1="r" u2="&#xe6;" k="10" />
    <hkern u1="r" u2="&#xe5;" k="10" />
    <hkern u1="r" u2="&#xe4;" k="10" />
    <hkern u1="r" u2="&#xe3;" k="10" />
    <hkern u1="r" u2="&#xe2;" k="10" />
    <hkern u1="r" u2="&#xe1;" k="10" />
    <hkern u1="r" u2="&#xe0;" k="10" />
    <hkern u1="r" u2="q" k="20" />
    <hkern u1="r" u2="o" k="20" />
    <hkern u1="r" u2="g" k="20" />
    <hkern u1="r" u2="e" k="20" />
    <hkern u1="r" u2="d" k="20" />
    <hkern u1="r" u2="&#x2f;" k="50" />
    <hkern u1="r" u2="&#x2e;" k="115" />
    <hkern u1="s" u2="&#x201c;" k="-20" />
    <hkern u1="s" u2="&#x219;" k="15" />
    <hkern u1="s" u2="&#x164;" k="70" />
    <hkern u1="s" u2="&#x162;" k="70" />
    <hkern u1="s" u2="&#x161;" k="15" />
    <hkern u1="s" u2="&#x15f;" k="15" />
    <hkern u1="s" u2="&#x15d;" k="15" />
    <hkern u1="s" u2="&#x15b;" k="15" />
    <hkern u1="s" u2="V" k="20" />
    <hkern u1="t" u2="&#x2026;" k="-50" />
    <hkern u1="t" u2="&#x201c;" k="-30" />
    <hkern u1="t" u2="&#x2018;" k="-30" />
    <hkern u1="t" u2="&#x2014;" k="65" />
    <hkern u1="t" u2="&#x2013;" k="65" />
    <hkern u1="t" u2="&#x1ff;" k="10" />
    <hkern u1="t" u2="&#x153;" k="10" />
    <hkern u1="t" u2="&#x151;" k="10" />
    <hkern u1="t" u2="&#x14f;" k="10" />
    <hkern u1="t" u2="&#x14d;" k="10" />
    <hkern u1="t" u2="&#x123;" k="10" />
    <hkern u1="t" u2="&#x121;" k="10" />
    <hkern u1="t" u2="&#x11f;" k="10" />
    <hkern u1="t" u2="&#x11d;" k="10" />
    <hkern u1="t" u2="&#x11b;" k="10" />
    <hkern u1="t" u2="&#x119;" k="10" />
    <hkern u1="t" u2="&#x117;" k="10" />
    <hkern u1="t" u2="&#x115;" k="10" />
    <hkern u1="t" u2="&#x113;" k="10" />
    <hkern u1="t" u2="&#x111;" k="10" />
    <hkern u1="t" u2="&#x10f;" k="10" />
    <hkern u1="t" u2="&#x10d;" k="10" />
    <hkern u1="t" u2="&#x10b;" k="10" />
    <hkern u1="t" u2="&#x109;" k="10" />
    <hkern u1="t" u2="&#x107;" k="10" />
    <hkern u1="t" u2="&#xf8;" k="10" />
    <hkern u1="t" u2="&#xf6;" k="10" />
    <hkern u1="t" u2="&#xf5;" k="10" />
    <hkern u1="t" u2="&#xf4;" k="10" />
    <hkern u1="t" u2="&#xf3;" k="10" />
    <hkern u1="t" u2="&#xf2;" k="10" />
    <hkern u1="t" u2="&#xf0;" k="10" />
    <hkern u1="t" u2="&#xeb;" k="10" />
    <hkern u1="t" u2="&#xea;" k="10" />
    <hkern u1="t" u2="&#xe9;" k="10" />
    <hkern u1="t" u2="&#xe8;" k="10" />
    <hkern u1="t" u2="&#xe7;" k="10" />
    <hkern u1="t" u2="x" k="-15" />
    <hkern u1="t" u2="q" k="10" />
    <hkern u1="t" u2="o" k="10" />
    <hkern u1="t" u2="g" k="10" />
    <hkern u1="t" u2="e" k="10" />
    <hkern u1="t" u2="d" k="10" />
    <hkern u1="t" u2="c" k="10" />
    <hkern u1="t" u2="_" k="-40" />
    <hkern u1="t" u2="&#x2e;" k="-50" />
    <hkern u1="t" u2="&#x2d;" k="65" />
    <hkern u1="t" u2="&#x2c;" k="-50" />
    <hkern u1="u" u2="&#x1ef2;" k="20" />
    <hkern u1="u" u2="&#x178;" k="20" />
    <hkern u1="u" u2="&#x176;" k="20" />
    <hkern u1="u" u2="&#x164;" k="70" />
    <hkern u1="u" u2="&#x162;" k="70" />
    <hkern u1="u" u2="&#xdd;" k="20" />
    <hkern u1="u" u2="Y" k="20" />
    <hkern u1="u" u2="T" k="70" />
    <hkern u1="v" u2="&#x2026;" k="65" />
    <hkern u1="v" u2="&#x201c;" k="-40" />
    <hkern u1="v" u2="&#x2018;" k="-40" />
    <hkern u1="v" u2="&#x1e84;" k="-25" />
    <hkern u1="v" u2="&#x1e82;" k="-25" />
    <hkern u1="v" u2="&#x1e80;" k="-25" />
    <hkern u1="v" u2="&#x174;" k="-25" />
    <hkern u1="v" u2="&#x164;" k="55" />
    <hkern u1="v" u2="&#x162;" k="55" />
    <hkern u1="v" u2="_" k="35" />
    <hkern u1="v" u2="W" k="-25" />
    <hkern u1="v" u2="V" k="-20" />
    <hkern u1="v" u2="T" k="35" />
    <hkern u1="v" u2="&#x2f;" k="35" />
    <hkern u1="v" u2="&#x2e;" k="65" />
    <hkern u1="v" u2="&#x2c;" k="65" />
    <hkern u1="w" u2="&#x2026;" k="65" />
    <hkern u1="w" u2="&#x201c;" k="-40" />
    <hkern u1="w" u2="&#x2014;" k="10" />
    <hkern u1="w" u2="&#x2013;" k="10" />
    <hkern u1="w" u2="&#x164;" k="55" />
    <hkern u1="w" u2="&#x162;" k="55" />
    <hkern u1="w" u2="_" k="35" />
    <hkern u1="w" u2="&#x2f;" k="20" />
    <hkern u1="w" u2="&#x2e;" k="65" />
    <hkern u1="x" u2="&#x2026;" k="-20" />
    <hkern u1="x" u2="&#x2014;" k="20" />
    <hkern u1="x" u2="&#x2013;" k="20" />
    <hkern u1="x" u2="&#x1ff;" k="10" />
    <hkern u1="x" u2="&#x164;" k="55" />
    <hkern u1="x" u2="&#x162;" k="55" />
    <hkern u1="x" u2="&#x153;" k="10" />
    <hkern u1="x" u2="&#x151;" k="10" />
    <hkern u1="x" u2="&#x14f;" k="10" />
    <hkern u1="x" u2="&#x14d;" k="10" />
    <hkern u1="x" u2="&#x123;" k="10" />
    <hkern u1="x" u2="&#x121;" k="10" />
    <hkern u1="x" u2="&#x11f;" k="10" />
    <hkern u1="x" u2="&#x11d;" k="10" />
    <hkern u1="x" u2="&#x11b;" k="10" />
    <hkern u1="x" u2="&#x119;" k="10" />
    <hkern u1="x" u2="&#x117;" k="10" />
    <hkern u1="x" u2="&#x115;" k="10" />
    <hkern u1="x" u2="&#x113;" k="10" />
    <hkern u1="x" u2="&#x111;" k="10" />
    <hkern u1="x" u2="&#x10f;" k="10" />
    <hkern u1="x" u2="&#x10d;" k="10" />
    <hkern u1="x" u2="&#x10b;" k="10" />
    <hkern u1="x" u2="&#x109;" k="10" />
    <hkern u1="x" u2="&#x107;" k="10" />
    <hkern u1="x" u2="&#xf8;" k="10" />
    <hkern u1="x" u2="&#xf6;" k="10" />
    <hkern u1="x" u2="&#xf5;" k="10" />
    <hkern u1="x" u2="&#xf4;" k="10" />
    <hkern u1="x" u2="&#xf3;" k="10" />
    <hkern u1="x" u2="&#xf2;" k="10" />
    <hkern u1="x" u2="&#xf0;" k="10" />
    <hkern u1="x" u2="&#xeb;" k="10" />
    <hkern u1="x" u2="&#xea;" k="10" />
    <hkern u1="x" u2="&#xe9;" k="10" />
    <hkern u1="x" u2="&#xe8;" k="10" />
    <hkern u1="x" u2="&#xe7;" k="10" />
    <hkern u1="x" u2="&#xe6;" k="-10" />
    <hkern u1="x" u2="q" k="10" />
    <hkern u1="x" u2="o" k="10" />
    <hkern u1="x" u2="g" k="10" />
    <hkern u1="x" u2="e" k="10" />
    <hkern u1="x" u2="d" k="10" />
    <hkern u1="x" u2="c" k="10" />
    <hkern u1="x" u2="_" k="-40" />
    <hkern u1="x" u2="T" k="55" />
    <hkern u1="x" u2="&#x2e;" k="-20" />
    <hkern u1="x" u2="&#x2d;" k="20" />
    <hkern u1="x" u2="&#x2c;" k="-20" />
    <hkern u1="y" u2="&#x1e84;" k="10" />
    <hkern u1="y" u2="&#x1e82;" k="10" />
    <hkern u1="y" u2="&#x1e80;" k="10" />
    <hkern u1="y" u2="&#x174;" k="10" />
    <hkern u1="y" u2="&#x164;" k="80" />
    <hkern u1="y" u2="&#x162;" k="80" />
    <hkern u1="y" u2="W" k="10" />
    <hkern u1="y" u2="V" k="30" />
    <hkern u1="y" u2="T" k="80" />
    <hkern u1="z" u2="&#x2014;" k="35" />
    <hkern u1="z" u2="&#x2013;" k="35" />
    <hkern u1="z" u2="&#x1ff;" k="15" />
    <hkern u1="z" u2="&#x164;" k="70" />
    <hkern u1="z" u2="&#x162;" k="70" />
    <hkern u1="z" u2="&#x153;" k="15" />
    <hkern u1="z" u2="&#x151;" k="15" />
    <hkern u1="z" u2="&#x14f;" k="15" />
    <hkern u1="z" u2="&#x14d;" k="15" />
    <hkern u1="z" u2="&#x123;" k="15" />
    <hkern u1="z" u2="&#x121;" k="15" />
    <hkern u1="z" u2="&#x11f;" k="15" />
    <hkern u1="z" u2="&#x11d;" k="15" />
    <hkern u1="z" u2="&#x11b;" k="15" />
    <hkern u1="z" u2="&#x119;" k="15" />
    <hkern u1="z" u2="&#x117;" k="15" />
    <hkern u1="z" u2="&#x115;" k="15" />
    <hkern u1="z" u2="&#x113;" k="15" />
    <hkern u1="z" u2="&#x111;" k="15" />
    <hkern u1="z" u2="&#x10f;" k="15" />
    <hkern u1="z" u2="&#x10d;" k="15" />
    <hkern u1="z" u2="&#x10b;" k="15" />
    <hkern u1="z" u2="&#x109;" k="15" />
    <hkern u1="z" u2="&#x107;" k="15" />
    <hkern u1="z" u2="&#xf8;" k="15" />
    <hkern u1="z" u2="&#xf6;" k="15" />
    <hkern u1="z" u2="&#xf5;" k="15" />
    <hkern u1="z" u2="&#xf4;" k="15" />
    <hkern u1="z" u2="&#xf3;" k="15" />
    <hkern u1="z" u2="&#xf2;" k="15" />
    <hkern u1="z" u2="&#xf0;" k="15" />
    <hkern u1="z" u2="&#xeb;" k="15" />
    <hkern u1="z" u2="&#xea;" k="15" />
    <hkern u1="z" u2="&#xe9;" k="15" />
    <hkern u1="z" u2="&#xe8;" k="15" />
    <hkern u1="z" u2="&#xe7;" k="15" />
    <hkern u1="z" u2="q" k="15" />
    <hkern u1="z" u2="o" k="15" />
    <hkern u1="z" u2="g" k="15" />
    <hkern u1="z" u2="e" k="15" />
    <hkern u1="z" u2="d" k="15" />
    <hkern u1="&#x7b;" u2="&#x134;" k="-50" />
    <hkern u1="&#x7b;" u2="j" k="-60" />
    <hkern u1="&#x7b;" u2="J" k="-50" />
    <hkern u1="&#xa9;" u2="&#x2026;" k="45" />
    <hkern u1="&#xa9;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x218;" k="5" />
    <hkern u1="&#xa9;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x164;" k="15" />
    <hkern u1="&#xa9;" u2="&#x162;" k="15" />
    <hkern u1="&#xa9;" u2="&#x160;" k="5" />
    <hkern u1="&#xa9;" u2="&#x15e;" k="5" />
    <hkern u1="&#xa9;" u2="&#x15c;" k="5" />
    <hkern u1="&#xa9;" u2="&#x15a;" k="5" />
    <hkern u1="&#xa9;" u2="&#x152;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x150;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x134;" k="-20" />
    <hkern u1="&#xa9;" u2="&#x122;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x120;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x108;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x106;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xa9;" u2="X" k="10" />
    <hkern u1="&#xa9;" u2="V" k="15" />
    <hkern u1="&#xa9;" u2="T" k="15" />
    <hkern u1="&#xa9;" u2="S" k="5" />
    <hkern u1="&#xa9;" u2="Q" k="-5" />
    <hkern u1="&#xa9;" u2="O" k="-5" />
    <hkern u1="&#xa9;" u2="J" k="-20" />
    <hkern u1="&#xa9;" u2="G" k="-5" />
    <hkern u1="&#xa9;" u2="C" k="-5" />
    <hkern u1="&#xa9;" u2="&#x2e;" k="45" />
    <hkern u1="&#xa9;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x2c;" k="45" />
    <hkern u1="&#xaa;" u2="&#x2026;" k="130" />
    <hkern u1="&#xaa;" u2="&#x2e;" k="130" />
    <hkern u1="&#xaa;" u2="&#x2c;" k="130" />
    <hkern u1="&#xae;" u2="&#x2026;" k="120" />
    <hkern u1="&#xae;" u2="&#x2e;" k="120" />
    <hkern u1="&#xae;" u2="&#x2c;" k="120" />
    <hkern u1="&#xb0;" u2="&#x2026;" k="185" />
    <hkern u1="&#xb0;" u2="&#x2e;" k="185" />
    <hkern u1="&#xb0;" u2="&#x2c;" k="185" />
    <hkern u1="&#xba;" u2="&#x2026;" k="130" />
    <hkern u1="&#xba;" u2="&#x2e;" k="130" />
    <hkern u1="&#xba;" u2="&#x2c;" k="130" />
    <hkern u1="&#xbb;" u2="&#x2026;" k="50" />
    <hkern u1="&#xbb;" u2="&#x2e;" k="50" />
    <hkern u1="&#xbb;" u2="&#x2c;" k="50" />
    <hkern u1="&#xbf;" u2="&#x2026;" k="50" />
    <hkern u1="&#xbf;" u2="j" k="-50" />
    <hkern u1="&#xbf;" u2="&#x2e;" k="50" />
    <hkern u1="&#xbf;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc0;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc0;" u2="&#x164;" k="30" />
    <hkern u1="&#xc0;" u2="&#x162;" k="30" />
    <hkern u1="&#xc0;" u2="V" k="20" />
    <hkern u1="&#xc0;" u2="T" k="30" />
    <hkern u1="&#xc0;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc0;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc1;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc1;" u2="&#x164;" k="30" />
    <hkern u1="&#xc1;" u2="&#x162;" k="30" />
    <hkern u1="&#xc1;" u2="V" k="20" />
    <hkern u1="&#xc1;" u2="T" k="30" />
    <hkern u1="&#xc1;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc1;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc2;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc2;" u2="&#x164;" k="30" />
    <hkern u1="&#xc2;" u2="&#x162;" k="30" />
    <hkern u1="&#xc2;" u2="V" k="20" />
    <hkern u1="&#xc2;" u2="T" k="30" />
    <hkern u1="&#xc2;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc2;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc3;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc3;" u2="&#x164;" k="30" />
    <hkern u1="&#xc3;" u2="&#x162;" k="30" />
    <hkern u1="&#xc3;" u2="V" k="20" />
    <hkern u1="&#xc3;" u2="T" k="30" />
    <hkern u1="&#xc3;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc3;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc4;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc4;" u2="&#x164;" k="30" />
    <hkern u1="&#xc4;" u2="&#x162;" k="30" />
    <hkern u1="&#xc4;" u2="V" k="20" />
    <hkern u1="&#xc4;" u2="T" k="30" />
    <hkern u1="&#xc4;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc4;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc5;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc5;" u2="&#x164;" k="30" />
    <hkern u1="&#xc5;" u2="&#x162;" k="30" />
    <hkern u1="&#xc5;" u2="V" k="20" />
    <hkern u1="&#xc5;" u2="T" k="30" />
    <hkern u1="&#xc5;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc5;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc6;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc6;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc6;" u2="&#x152;" k="7" />
    <hkern u1="&#xc6;" u2="&#x150;" k="7" />
    <hkern u1="&#xc6;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc6;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x122;" k="7" />
    <hkern u1="&#xc6;" u2="&#x120;" k="7" />
    <hkern u1="&#xc6;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc6;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc6;" u2="&#x108;" k="7" />
    <hkern u1="&#xc6;" u2="&#x106;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc6;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc6;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc6;" u2="Q" k="7" />
    <hkern u1="&#xc6;" u2="O" k="7" />
    <hkern u1="&#xc6;" u2="G" k="7" />
    <hkern u1="&#xc6;" u2="C" k="7" />
    <hkern u1="&#xc6;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc6;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xc7;" u2="&#x2026;" k="-15" />
    <hkern u1="&#xc7;" u2="&#x2014;" k="75" />
    <hkern u1="&#xc7;" u2="&#x2013;" k="75" />
    <hkern u1="&#xc7;" u2="&#x1e85;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1e83;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1e81;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1ff;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1fe;" k="30" />
    <hkern u1="&#xc7;" u2="&#x1fc;" k="7" />
    <hkern u1="&#xc7;" u2="&#x1fa;" k="7" />
    <hkern u1="&#xc7;" u2="&#x175;" k="10" />
    <hkern u1="&#xc7;" u2="&#x153;" k="10" />
    <hkern u1="&#xc7;" u2="&#x152;" k="30" />
    <hkern u1="&#xc7;" u2="&#x151;" k="10" />
    <hkern u1="&#xc7;" u2="&#x150;" k="30" />
    <hkern u1="&#xc7;" u2="&#x14f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x14e;" k="30" />
    <hkern u1="&#xc7;" u2="&#x14d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x14c;" k="30" />
    <hkern u1="&#xc7;" u2="&#x123;" k="10" />
    <hkern u1="&#xc7;" u2="&#x122;" k="30" />
    <hkern u1="&#xc7;" u2="&#x121;" k="10" />
    <hkern u1="&#xc7;" u2="&#x120;" k="30" />
    <hkern u1="&#xc7;" u2="&#x11f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x11e;" k="30" />
    <hkern u1="&#xc7;" u2="&#x11d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x11c;" k="30" />
    <hkern u1="&#xc7;" u2="&#x11b;" k="10" />
    <hkern u1="&#xc7;" u2="&#x119;" k="10" />
    <hkern u1="&#xc7;" u2="&#x117;" k="10" />
    <hkern u1="&#xc7;" u2="&#x115;" k="10" />
    <hkern u1="&#xc7;" u2="&#x113;" k="10" />
    <hkern u1="&#xc7;" u2="&#x111;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10c;" k="30" />
    <hkern u1="&#xc7;" u2="&#x10b;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10a;" k="30" />
    <hkern u1="&#xc7;" u2="&#x109;" k="10" />
    <hkern u1="&#xc7;" u2="&#x108;" k="30" />
    <hkern u1="&#xc7;" u2="&#x107;" k="10" />
    <hkern u1="&#xc7;" u2="&#x106;" k="30" />
    <hkern u1="&#xc7;" u2="&#x104;" k="7" />
    <hkern u1="&#xc7;" u2="&#x102;" k="7" />
    <hkern u1="&#xc7;" u2="&#x100;" k="7" />
    <hkern u1="&#xc7;" u2="&#xf8;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc7;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc7;" u2="&#xea;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc7;" u2="&#xd8;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd6;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd5;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd4;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd3;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd2;" k="30" />
    <hkern u1="&#xc7;" u2="&#xc7;" k="30" />
    <hkern u1="&#xc7;" u2="&#xc6;" k="7" />
    <hkern u1="&#xc7;" u2="&#xc5;" k="7" />
    <hkern u1="&#xc7;" u2="&#xc4;" k="7" />
    <hkern u1="&#xc7;" u2="&#xc3;" k="7" />
    <hkern u1="&#xc7;" u2="&#xc2;" k="7" />
    <hkern u1="&#xc7;" u2="&#xc1;" k="7" />
    <hkern u1="&#xc7;" u2="&#xc0;" k="7" />
    <hkern u1="&#xc7;" u2="&#xa9;" k="30" />
    <hkern u1="&#xc7;" u2="w" k="10" />
    <hkern u1="&#xc7;" u2="v" k="20" />
    <hkern u1="&#xc7;" u2="q" k="10" />
    <hkern u1="&#xc7;" u2="o" k="10" />
    <hkern u1="&#xc7;" u2="g" k="10" />
    <hkern u1="&#xc7;" u2="e" k="10" />
    <hkern u1="&#xc7;" u2="d" k="10" />
    <hkern u1="&#xc7;" u2="c" k="10" />
    <hkern u1="&#xc7;" u2="_" k="-20" />
    <hkern u1="&#xc7;" u2="Q" k="30" />
    <hkern u1="&#xc7;" u2="O" k="30" />
    <hkern u1="&#xc7;" u2="G" k="30" />
    <hkern u1="&#xc7;" u2="C" k="30" />
    <hkern u1="&#xc7;" u2="A" k="7" />
    <hkern u1="&#xc7;" u2="&#x2e;" k="-15" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="75" />
    <hkern u1="&#xc7;" u2="&#x2c;" k="-15" />
    <hkern u1="&#xc8;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc8;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc8;" u2="&#x152;" k="7" />
    <hkern u1="&#xc8;" u2="&#x150;" k="7" />
    <hkern u1="&#xc8;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc8;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x122;" k="7" />
    <hkern u1="&#xc8;" u2="&#x120;" k="7" />
    <hkern u1="&#xc8;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc8;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc8;" u2="&#x108;" k="7" />
    <hkern u1="&#xc8;" u2="&#x106;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc8;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc8;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc8;" u2="Q" k="7" />
    <hkern u1="&#xc8;" u2="O" k="7" />
    <hkern u1="&#xc8;" u2="G" k="7" />
    <hkern u1="&#xc8;" u2="C" k="7" />
    <hkern u1="&#xc8;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc8;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc9;" u2="&#x152;" k="7" />
    <hkern u1="&#xc9;" u2="&#x150;" k="7" />
    <hkern u1="&#xc9;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc9;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x122;" k="7" />
    <hkern u1="&#xc9;" u2="&#x120;" k="7" />
    <hkern u1="&#xc9;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc9;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc9;" u2="&#x108;" k="7" />
    <hkern u1="&#xc9;" u2="&#x106;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc9;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc9;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc9;" u2="Q" k="7" />
    <hkern u1="&#xc9;" u2="O" k="7" />
    <hkern u1="&#xc9;" u2="G" k="7" />
    <hkern u1="&#xc9;" u2="C" k="7" />
    <hkern u1="&#xc9;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xca;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xca;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xca;" u2="&#x152;" k="7" />
    <hkern u1="&#xca;" u2="&#x150;" k="7" />
    <hkern u1="&#xca;" u2="&#x14e;" k="7" />
    <hkern u1="&#xca;" u2="&#x14c;" k="7" />
    <hkern u1="&#xca;" u2="&#x122;" k="7" />
    <hkern u1="&#xca;" u2="&#x120;" k="7" />
    <hkern u1="&#xca;" u2="&#x11e;" k="7" />
    <hkern u1="&#xca;" u2="&#x11c;" k="7" />
    <hkern u1="&#xca;" u2="&#x10c;" k="7" />
    <hkern u1="&#xca;" u2="&#x10a;" k="7" />
    <hkern u1="&#xca;" u2="&#x108;" k="7" />
    <hkern u1="&#xca;" u2="&#x106;" k="7" />
    <hkern u1="&#xca;" u2="&#xd8;" k="7" />
    <hkern u1="&#xca;" u2="&#xd6;" k="7" />
    <hkern u1="&#xca;" u2="&#xd5;" k="7" />
    <hkern u1="&#xca;" u2="&#xd4;" k="7" />
    <hkern u1="&#xca;" u2="&#xd3;" k="7" />
    <hkern u1="&#xca;" u2="&#xd2;" k="7" />
    <hkern u1="&#xca;" u2="&#xc7;" k="7" />
    <hkern u1="&#xca;" u2="&#xa9;" k="7" />
    <hkern u1="&#xca;" u2="Q" k="7" />
    <hkern u1="&#xca;" u2="O" k="7" />
    <hkern u1="&#xca;" u2="G" k="7" />
    <hkern u1="&#xca;" u2="C" k="7" />
    <hkern u1="&#xca;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xca;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xcb;" u2="&#x152;" k="7" />
    <hkern u1="&#xcb;" u2="&#x150;" k="7" />
    <hkern u1="&#xcb;" u2="&#x14e;" k="7" />
    <hkern u1="&#xcb;" u2="&#x14c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x122;" k="7" />
    <hkern u1="&#xcb;" u2="&#x120;" k="7" />
    <hkern u1="&#xcb;" u2="&#x11e;" k="7" />
    <hkern u1="&#xcb;" u2="&#x11c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x10c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x10a;" k="7" />
    <hkern u1="&#xcb;" u2="&#x108;" k="7" />
    <hkern u1="&#xcb;" u2="&#x106;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd8;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd6;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd5;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd4;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd3;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd2;" k="7" />
    <hkern u1="&#xcb;" u2="&#xc7;" k="7" />
    <hkern u1="&#xcb;" u2="&#xa9;" k="7" />
    <hkern u1="&#xcb;" u2="Q" k="7" />
    <hkern u1="&#xcb;" u2="O" k="7" />
    <hkern u1="&#xcb;" u2="G" k="7" />
    <hkern u1="&#xcb;" u2="C" k="7" />
    <hkern u1="&#xcb;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xd0;" u2="&#x2026;" k="45" />
    <hkern u1="&#xd0;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x218;" k="5" />
    <hkern u1="&#xd0;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x164;" k="15" />
    <hkern u1="&#xd0;" u2="&#x162;" k="15" />
    <hkern u1="&#xd0;" u2="&#x160;" k="5" />
    <hkern u1="&#xd0;" u2="&#x15e;" k="5" />
    <hkern u1="&#xd0;" u2="&#x15c;" k="5" />
    <hkern u1="&#xd0;" u2="&#x15a;" k="5" />
    <hkern u1="&#xd0;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd0;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd0;" u2="X" k="10" />
    <hkern u1="&#xd0;" u2="V" k="15" />
    <hkern u1="&#xd0;" u2="T" k="15" />
    <hkern u1="&#xd0;" u2="S" k="5" />
    <hkern u1="&#xd0;" u2="Q" k="-5" />
    <hkern u1="&#xd0;" u2="O" k="-5" />
    <hkern u1="&#xd0;" u2="J" k="-20" />
    <hkern u1="&#xd0;" u2="G" k="-5" />
    <hkern u1="&#xd0;" u2="C" k="-5" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="45" />
    <hkern u1="&#xd0;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="45" />
    <hkern u1="&#xd2;" u2="&#x2026;" k="45" />
    <hkern u1="&#xd2;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x218;" k="5" />
    <hkern u1="&#xd2;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x164;" k="15" />
    <hkern u1="&#xd2;" u2="&#x162;" k="15" />
    <hkern u1="&#xd2;" u2="&#x160;" k="5" />
    <hkern u1="&#xd2;" u2="&#x15e;" k="5" />
    <hkern u1="&#xd2;" u2="&#x15c;" k="5" />
    <hkern u1="&#xd2;" u2="&#x15a;" k="5" />
    <hkern u1="&#xd2;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd2;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd2;" u2="X" k="10" />
    <hkern u1="&#xd2;" u2="V" k="15" />
    <hkern u1="&#xd2;" u2="T" k="15" />
    <hkern u1="&#xd2;" u2="S" k="5" />
    <hkern u1="&#xd2;" u2="Q" k="-5" />
    <hkern u1="&#xd2;" u2="O" k="-5" />
    <hkern u1="&#xd2;" u2="J" k="-20" />
    <hkern u1="&#xd2;" u2="G" k="-5" />
    <hkern u1="&#xd2;" u2="C" k="-5" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="45" />
    <hkern u1="&#xd2;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="45" />
    <hkern u1="&#xd3;" u2="&#x2026;" k="45" />
    <hkern u1="&#xd3;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x218;" k="5" />
    <hkern u1="&#xd3;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x164;" k="15" />
    <hkern u1="&#xd3;" u2="&#x162;" k="15" />
    <hkern u1="&#xd3;" u2="&#x160;" k="5" />
    <hkern u1="&#xd3;" u2="&#x15e;" k="5" />
    <hkern u1="&#xd3;" u2="&#x15c;" k="5" />
    <hkern u1="&#xd3;" u2="&#x15a;" k="5" />
    <hkern u1="&#xd3;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd3;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd3;" u2="X" k="10" />
    <hkern u1="&#xd3;" u2="V" k="15" />
    <hkern u1="&#xd3;" u2="T" k="15" />
    <hkern u1="&#xd3;" u2="S" k="5" />
    <hkern u1="&#xd3;" u2="Q" k="-5" />
    <hkern u1="&#xd3;" u2="O" k="-5" />
    <hkern u1="&#xd3;" u2="J" k="-20" />
    <hkern u1="&#xd3;" u2="G" k="-5" />
    <hkern u1="&#xd3;" u2="C" k="-5" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="45" />
    <hkern u1="&#xd3;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="45" />
    <hkern u1="&#xd4;" u2="&#x2026;" k="45" />
    <hkern u1="&#xd4;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x218;" k="5" />
    <hkern u1="&#xd4;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x164;" k="15" />
    <hkern u1="&#xd4;" u2="&#x162;" k="15" />
    <hkern u1="&#xd4;" u2="&#x160;" k="5" />
    <hkern u1="&#xd4;" u2="&#x15e;" k="5" />
    <hkern u1="&#xd4;" u2="&#x15c;" k="5" />
    <hkern u1="&#xd4;" u2="&#x15a;" k="5" />
    <hkern u1="&#xd4;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd4;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd4;" u2="X" k="10" />
    <hkern u1="&#xd4;" u2="V" k="15" />
    <hkern u1="&#xd4;" u2="T" k="15" />
    <hkern u1="&#xd4;" u2="S" k="5" />
    <hkern u1="&#xd4;" u2="Q" k="-5" />
    <hkern u1="&#xd4;" u2="O" k="-5" />
    <hkern u1="&#xd4;" u2="J" k="-20" />
    <hkern u1="&#xd4;" u2="G" k="-5" />
    <hkern u1="&#xd4;" u2="C" k="-5" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="45" />
    <hkern u1="&#xd4;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="45" />
    <hkern u1="&#xd5;" u2="&#x2026;" k="45" />
    <hkern u1="&#xd5;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x218;" k="5" />
    <hkern u1="&#xd5;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x164;" k="15" />
    <hkern u1="&#xd5;" u2="&#x162;" k="15" />
    <hkern u1="&#xd5;" u2="&#x160;" k="5" />
    <hkern u1="&#xd5;" u2="&#x15e;" k="5" />
    <hkern u1="&#xd5;" u2="&#x15c;" k="5" />
    <hkern u1="&#xd5;" u2="&#x15a;" k="5" />
    <hkern u1="&#xd5;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd5;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd5;" u2="X" k="10" />
    <hkern u1="&#xd5;" u2="V" k="15" />
    <hkern u1="&#xd5;" u2="T" k="15" />
    <hkern u1="&#xd5;" u2="S" k="5" />
    <hkern u1="&#xd5;" u2="Q" k="-5" />
    <hkern u1="&#xd5;" u2="O" k="-5" />
    <hkern u1="&#xd5;" u2="J" k="-20" />
    <hkern u1="&#xd5;" u2="G" k="-5" />
    <hkern u1="&#xd5;" u2="C" k="-5" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="45" />
    <hkern u1="&#xd5;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="45" />
    <hkern u1="&#xd6;" u2="&#x2026;" k="45" />
    <hkern u1="&#xd6;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x218;" k="5" />
    <hkern u1="&#xd6;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x164;" k="15" />
    <hkern u1="&#xd6;" u2="&#x162;" k="15" />
    <hkern u1="&#xd6;" u2="&#x160;" k="5" />
    <hkern u1="&#xd6;" u2="&#x15e;" k="5" />
    <hkern u1="&#xd6;" u2="&#x15c;" k="5" />
    <hkern u1="&#xd6;" u2="&#x15a;" k="5" />
    <hkern u1="&#xd6;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd6;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd6;" u2="X" k="10" />
    <hkern u1="&#xd6;" u2="V" k="15" />
    <hkern u1="&#xd6;" u2="T" k="15" />
    <hkern u1="&#xd6;" u2="S" k="5" />
    <hkern u1="&#xd6;" u2="Q" k="-5" />
    <hkern u1="&#xd6;" u2="O" k="-5" />
    <hkern u1="&#xd6;" u2="J" k="-20" />
    <hkern u1="&#xd6;" u2="G" k="-5" />
    <hkern u1="&#xd6;" u2="C" k="-5" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="45" />
    <hkern u1="&#xd6;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="45" />
    <hkern u1="&#xd8;" u2="&#x2026;" k="45" />
    <hkern u1="&#xd8;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x218;" k="5" />
    <hkern u1="&#xd8;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x164;" k="15" />
    <hkern u1="&#xd8;" u2="&#x162;" k="15" />
    <hkern u1="&#xd8;" u2="&#x160;" k="5" />
    <hkern u1="&#xd8;" u2="&#x15e;" k="5" />
    <hkern u1="&#xd8;" u2="&#x15c;" k="5" />
    <hkern u1="&#xd8;" u2="&#x15a;" k="5" />
    <hkern u1="&#xd8;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd8;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd8;" u2="X" k="10" />
    <hkern u1="&#xd8;" u2="V" k="15" />
    <hkern u1="&#xd8;" u2="T" k="15" />
    <hkern u1="&#xd8;" u2="S" k="5" />
    <hkern u1="&#xd8;" u2="Q" k="-5" />
    <hkern u1="&#xd8;" u2="O" k="-5" />
    <hkern u1="&#xd8;" u2="J" k="-20" />
    <hkern u1="&#xd8;" u2="G" k="-5" />
    <hkern u1="&#xd8;" u2="C" k="-5" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="45" />
    <hkern u1="&#xd8;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="45" />
    <hkern u1="&#xdd;" u2="V" k="-15" />
    <hkern u1="&#xe6;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe6;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe6;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe6;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x174;" k="10" />
    <hkern u1="&#xe6;" u2="&#x164;" k="70" />
    <hkern u1="&#xe6;" u2="&#x162;" k="70" />
    <hkern u1="&#xe6;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe6;" u2="s" k="-5" />
    <hkern u1="&#xe6;" u2="W" k="10" />
    <hkern u1="&#xe6;" u2="V" k="20" />
    <hkern u1="&#xe6;" u2="T" k="70" />
    <hkern u1="&#xe7;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xe7;" u2="&#x1ef3;" k="5" />
    <hkern u1="&#xe7;" u2="&#x1ff;" k="15" />
    <hkern u1="&#xe7;" u2="&#x177;" k="5" />
    <hkern u1="&#xe7;" u2="&#x173;" k="5" />
    <hkern u1="&#xe7;" u2="&#x171;" k="5" />
    <hkern u1="&#xe7;" u2="&#x16f;" k="5" />
    <hkern u1="&#xe7;" u2="&#x16d;" k="5" />
    <hkern u1="&#xe7;" u2="&#x16b;" k="5" />
    <hkern u1="&#xe7;" u2="&#x169;" k="5" />
    <hkern u1="&#xe7;" u2="&#x164;" k="60" />
    <hkern u1="&#xe7;" u2="&#x162;" k="60" />
    <hkern u1="&#xe7;" u2="&#x153;" k="15" />
    <hkern u1="&#xe7;" u2="&#x151;" k="15" />
    <hkern u1="&#xe7;" u2="&#x14f;" k="15" />
    <hkern u1="&#xe7;" u2="&#x14d;" k="15" />
    <hkern u1="&#xe7;" u2="&#x123;" k="15" />
    <hkern u1="&#xe7;" u2="&#x121;" k="15" />
    <hkern u1="&#xe7;" u2="&#x11f;" k="15" />
    <hkern u1="&#xe7;" u2="&#x11d;" k="15" />
    <hkern u1="&#xe7;" u2="&#x11b;" k="15" />
    <hkern u1="&#xe7;" u2="&#x119;" k="15" />
    <hkern u1="&#xe7;" u2="&#x117;" k="15" />
    <hkern u1="&#xe7;" u2="&#x115;" k="15" />
    <hkern u1="&#xe7;" u2="&#x113;" k="15" />
    <hkern u1="&#xe7;" u2="&#x111;" k="15" />
    <hkern u1="&#xe7;" u2="&#x10f;" k="15" />
    <hkern u1="&#xe7;" u2="&#x10d;" k="15" />
    <hkern u1="&#xe7;" u2="&#x10b;" k="15" />
    <hkern u1="&#xe7;" u2="&#x109;" k="15" />
    <hkern u1="&#xe7;" u2="&#x107;" k="15" />
    <hkern u1="&#xe7;" u2="&#xff;" k="5" />
    <hkern u1="&#xe7;" u2="&#xfd;" k="5" />
    <hkern u1="&#xe7;" u2="&#xfc;" k="5" />
    <hkern u1="&#xe7;" u2="&#xfb;" k="5" />
    <hkern u1="&#xe7;" u2="&#xfa;" k="5" />
    <hkern u1="&#xe7;" u2="&#xf9;" k="5" />
    <hkern u1="&#xe7;" u2="&#xf8;" k="15" />
    <hkern u1="&#xe7;" u2="&#xf6;" k="15" />
    <hkern u1="&#xe7;" u2="&#xf5;" k="15" />
    <hkern u1="&#xe7;" u2="&#xf4;" k="15" />
    <hkern u1="&#xe7;" u2="&#xf3;" k="15" />
    <hkern u1="&#xe7;" u2="&#xf2;" k="15" />
    <hkern u1="&#xe7;" u2="&#xf0;" k="15" />
    <hkern u1="&#xe7;" u2="&#xeb;" k="15" />
    <hkern u1="&#xe7;" u2="&#xea;" k="15" />
    <hkern u1="&#xe7;" u2="&#xe9;" k="15" />
    <hkern u1="&#xe7;" u2="&#xe8;" k="15" />
    <hkern u1="&#xe7;" u2="&#xe7;" k="15" />
    <hkern u1="&#xe7;" u2="y" k="5" />
    <hkern u1="&#xe7;" u2="x" k="-13" />
    <hkern u1="&#xe7;" u2="u" k="5" />
    <hkern u1="&#xe7;" u2="q" k="15" />
    <hkern u1="&#xe7;" u2="o" k="15" />
    <hkern u1="&#xe7;" u2="g" k="15" />
    <hkern u1="&#xe7;" u2="e" k="15" />
    <hkern u1="&#xe7;" u2="d" k="15" />
    <hkern u1="&#xe7;" u2="c" k="15" />
    <hkern u1="&#xe7;" u2="_" k="-40" />
    <hkern u1="&#xe7;" u2="T" k="60" />
    <hkern u1="&#xe7;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xe7;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xe8;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe8;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe8;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe8;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x174;" k="10" />
    <hkern u1="&#xe8;" u2="&#x164;" k="70" />
    <hkern u1="&#xe8;" u2="&#x162;" k="70" />
    <hkern u1="&#xe8;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe8;" u2="s" k="-5" />
    <hkern u1="&#xe8;" u2="W" k="10" />
    <hkern u1="&#xe8;" u2="V" k="20" />
    <hkern u1="&#xe8;" u2="T" k="70" />
    <hkern u1="&#xe9;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe9;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe9;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe9;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x174;" k="10" />
    <hkern u1="&#xe9;" u2="&#x164;" k="70" />
    <hkern u1="&#xe9;" u2="&#x162;" k="70" />
    <hkern u1="&#xe9;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe9;" u2="s" k="-5" />
    <hkern u1="&#xe9;" u2="W" k="10" />
    <hkern u1="&#xe9;" u2="V" k="20" />
    <hkern u1="&#xe9;" u2="T" k="70" />
    <hkern u1="&#xea;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xea;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xea;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xea;" u2="&#x219;" k="-5" />
    <hkern u1="&#xea;" u2="&#x174;" k="10" />
    <hkern u1="&#xea;" u2="&#x164;" k="70" />
    <hkern u1="&#xea;" u2="&#x162;" k="70" />
    <hkern u1="&#xea;" u2="&#x161;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xea;" u2="s" k="-5" />
    <hkern u1="&#xea;" u2="W" k="10" />
    <hkern u1="&#xea;" u2="V" k="20" />
    <hkern u1="&#xea;" u2="T" k="70" />
    <hkern u1="&#xeb;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xeb;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xeb;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xeb;" u2="&#x219;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x174;" k="10" />
    <hkern u1="&#xeb;" u2="&#x164;" k="70" />
    <hkern u1="&#xeb;" u2="&#x162;" k="70" />
    <hkern u1="&#xeb;" u2="&#x161;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xeb;" u2="s" k="-5" />
    <hkern u1="&#xeb;" u2="W" k="10" />
    <hkern u1="&#xeb;" u2="V" k="20" />
    <hkern u1="&#xeb;" u2="T" k="70" />
    <hkern u1="&#xf1;" u2="&#x164;" k="90" />
    <hkern u1="&#xf1;" u2="&#x162;" k="90" />
    <hkern u1="&#xf1;" u2="V" k="20" />
    <hkern u1="&#xf1;" u2="T" k="90" />
    <hkern u1="&#xf2;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf2;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf2;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf2;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf2;" u2="&#x174;" k="10" />
    <hkern u1="&#xf2;" u2="&#x164;" k="85" />
    <hkern u1="&#xf2;" u2="&#x162;" k="85" />
    <hkern u1="&#xf2;" u2="z" k="20" />
    <hkern u1="&#xf2;" u2="x" k="10" />
    <hkern u1="&#xf2;" u2="W" k="10" />
    <hkern u1="&#xf2;" u2="V" k="20" />
    <hkern u1="&#xf2;" u2="T" k="85" />
    <hkern u1="&#xf2;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf2;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf3;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf3;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf3;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf3;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf3;" u2="&#x174;" k="10" />
    <hkern u1="&#xf3;" u2="&#x164;" k="85" />
    <hkern u1="&#xf3;" u2="&#x162;" k="85" />
    <hkern u1="&#xf3;" u2="z" k="20" />
    <hkern u1="&#xf3;" u2="x" k="10" />
    <hkern u1="&#xf3;" u2="W" k="10" />
    <hkern u1="&#xf3;" u2="V" k="20" />
    <hkern u1="&#xf3;" u2="T" k="85" />
    <hkern u1="&#xf3;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf3;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf4;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf4;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf4;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf4;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf4;" u2="&#x174;" k="10" />
    <hkern u1="&#xf4;" u2="&#x164;" k="85" />
    <hkern u1="&#xf4;" u2="&#x162;" k="85" />
    <hkern u1="&#xf4;" u2="z" k="20" />
    <hkern u1="&#xf4;" u2="x" k="10" />
    <hkern u1="&#xf4;" u2="W" k="10" />
    <hkern u1="&#xf4;" u2="V" k="20" />
    <hkern u1="&#xf4;" u2="T" k="85" />
    <hkern u1="&#xf4;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf4;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf5;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf5;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf5;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf5;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf5;" u2="&#x174;" k="10" />
    <hkern u1="&#xf5;" u2="&#x164;" k="85" />
    <hkern u1="&#xf5;" u2="&#x162;" k="85" />
    <hkern u1="&#xf5;" u2="z" k="20" />
    <hkern u1="&#xf5;" u2="x" k="10" />
    <hkern u1="&#xf5;" u2="W" k="10" />
    <hkern u1="&#xf5;" u2="V" k="20" />
    <hkern u1="&#xf5;" u2="T" k="85" />
    <hkern u1="&#xf5;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf5;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf6;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf6;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf6;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf6;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf6;" u2="&#x174;" k="10" />
    <hkern u1="&#xf6;" u2="&#x164;" k="85" />
    <hkern u1="&#xf6;" u2="&#x162;" k="85" />
    <hkern u1="&#xf6;" u2="z" k="20" />
    <hkern u1="&#xf6;" u2="x" k="10" />
    <hkern u1="&#xf6;" u2="W" k="10" />
    <hkern u1="&#xf6;" u2="V" k="20" />
    <hkern u1="&#xf6;" u2="T" k="85" />
    <hkern u1="&#xf6;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf6;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf8;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf8;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf8;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf8;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf8;" u2="&#x174;" k="10" />
    <hkern u1="&#xf8;" u2="&#x164;" k="85" />
    <hkern u1="&#xf8;" u2="&#x162;" k="85" />
    <hkern u1="&#xf8;" u2="z" k="20" />
    <hkern u1="&#xf8;" u2="x" k="10" />
    <hkern u1="&#xf8;" u2="W" k="10" />
    <hkern u1="&#xf8;" u2="V" k="20" />
    <hkern u1="&#xf8;" u2="T" k="85" />
    <hkern u1="&#xf8;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf8;" u2="&#x2c;" k="20" />
    <hkern u1="&#xfd;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xfd;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xfd;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xfd;" u2="&#x174;" k="10" />
    <hkern u1="&#xfd;" u2="&#x164;" k="80" />
    <hkern u1="&#xfd;" u2="&#x162;" k="80" />
    <hkern u1="&#xfd;" u2="W" k="10" />
    <hkern u1="&#xfd;" u2="V" k="20" />
    <hkern u1="&#xfd;" u2="T" k="80" />
    <hkern u1="&#xfe;" u2="&#x2026;" k="20" />
    <hkern u1="&#xfe;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xfe;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xfe;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xfe;" u2="&#x174;" k="10" />
    <hkern u1="&#xfe;" u2="&#x164;" k="85" />
    <hkern u1="&#xfe;" u2="&#x162;" k="85" />
    <hkern u1="&#xfe;" u2="z" k="20" />
    <hkern u1="&#xfe;" u2="x" k="10" />
    <hkern u1="&#xfe;" u2="W" k="10" />
    <hkern u1="&#xfe;" u2="V" k="20" />
    <hkern u1="&#xfe;" u2="T" k="85" />
    <hkern u1="&#xfe;" u2="&#x2e;" k="20" />
    <hkern u1="&#xfe;" u2="&#x2c;" k="20" />
    <hkern u1="&#xff;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xff;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xff;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xff;" u2="&#x174;" k="10" />
    <hkern u1="&#xff;" u2="&#x164;" k="80" />
    <hkern u1="&#xff;" u2="&#x162;" k="80" />
    <hkern u1="&#xff;" u2="W" k="10" />
    <hkern u1="&#xff;" u2="V" k="20" />
    <hkern u1="&#xff;" u2="T" k="80" />
    <hkern u1="&#x100;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x100;" u2="&#x164;" k="30" />
    <hkern u1="&#x100;" u2="&#x162;" k="30" />
    <hkern u1="&#x100;" u2="V" k="20" />
    <hkern u1="&#x100;" u2="T" k="30" />
    <hkern u1="&#x100;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x100;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x102;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x102;" u2="&#x164;" k="30" />
    <hkern u1="&#x102;" u2="&#x162;" k="30" />
    <hkern u1="&#x102;" u2="V" k="20" />
    <hkern u1="&#x102;" u2="T" k="30" />
    <hkern u1="&#x102;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x102;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x104;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x104;" u2="&#x164;" k="30" />
    <hkern u1="&#x104;" u2="&#x162;" k="30" />
    <hkern u1="&#x104;" u2="V" k="20" />
    <hkern u1="&#x104;" u2="T" k="30" />
    <hkern u1="&#x104;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x106;" u2="&#x2026;" k="-15" />
    <hkern u1="&#x106;" u2="&#x2014;" k="75" />
    <hkern u1="&#x106;" u2="&#x2013;" k="75" />
    <hkern u1="&#x106;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x106;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x106;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x106;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x106;" u2="&#x1fe;" k="30" />
    <hkern u1="&#x106;" u2="&#x1fc;" k="7" />
    <hkern u1="&#x106;" u2="&#x1fa;" k="7" />
    <hkern u1="&#x106;" u2="&#x175;" k="10" />
    <hkern u1="&#x106;" u2="&#x153;" k="10" />
    <hkern u1="&#x106;" u2="&#x152;" k="30" />
    <hkern u1="&#x106;" u2="&#x151;" k="10" />
    <hkern u1="&#x106;" u2="&#x150;" k="30" />
    <hkern u1="&#x106;" u2="&#x14f;" k="10" />
    <hkern u1="&#x106;" u2="&#x14e;" k="30" />
    <hkern u1="&#x106;" u2="&#x14d;" k="10" />
    <hkern u1="&#x106;" u2="&#x14c;" k="30" />
    <hkern u1="&#x106;" u2="&#x123;" k="10" />
    <hkern u1="&#x106;" u2="&#x122;" k="30" />
    <hkern u1="&#x106;" u2="&#x121;" k="10" />
    <hkern u1="&#x106;" u2="&#x120;" k="30" />
    <hkern u1="&#x106;" u2="&#x11f;" k="10" />
    <hkern u1="&#x106;" u2="&#x11e;" k="30" />
    <hkern u1="&#x106;" u2="&#x11d;" k="10" />
    <hkern u1="&#x106;" u2="&#x11c;" k="30" />
    <hkern u1="&#x106;" u2="&#x11b;" k="10" />
    <hkern u1="&#x106;" u2="&#x119;" k="10" />
    <hkern u1="&#x106;" u2="&#x117;" k="10" />
    <hkern u1="&#x106;" u2="&#x115;" k="10" />
    <hkern u1="&#x106;" u2="&#x113;" k="10" />
    <hkern u1="&#x106;" u2="&#x111;" k="10" />
    <hkern u1="&#x106;" u2="&#x10f;" k="10" />
    <hkern u1="&#x106;" u2="&#x10d;" k="10" />
    <hkern u1="&#x106;" u2="&#x10c;" k="30" />
    <hkern u1="&#x106;" u2="&#x10b;" k="10" />
    <hkern u1="&#x106;" u2="&#x10a;" k="30" />
    <hkern u1="&#x106;" u2="&#x109;" k="10" />
    <hkern u1="&#x106;" u2="&#x108;" k="30" />
    <hkern u1="&#x106;" u2="&#x107;" k="10" />
    <hkern u1="&#x106;" u2="&#x106;" k="30" />
    <hkern u1="&#x106;" u2="&#x104;" k="7" />
    <hkern u1="&#x106;" u2="&#x102;" k="7" />
    <hkern u1="&#x106;" u2="&#x100;" k="7" />
    <hkern u1="&#x106;" u2="&#xf8;" k="10" />
    <hkern u1="&#x106;" u2="&#xf6;" k="10" />
    <hkern u1="&#x106;" u2="&#xf5;" k="10" />
    <hkern u1="&#x106;" u2="&#xf4;" k="10" />
    <hkern u1="&#x106;" u2="&#xf3;" k="10" />
    <hkern u1="&#x106;" u2="&#xf2;" k="10" />
    <hkern u1="&#x106;" u2="&#xf0;" k="10" />
    <hkern u1="&#x106;" u2="&#xeb;" k="10" />
    <hkern u1="&#x106;" u2="&#xea;" k="10" />
    <hkern u1="&#x106;" u2="&#xe9;" k="10" />
    <hkern u1="&#x106;" u2="&#xe8;" k="10" />
    <hkern u1="&#x106;" u2="&#xe7;" k="10" />
    <hkern u1="&#x106;" u2="&#xd8;" k="30" />
    <hkern u1="&#x106;" u2="&#xd6;" k="30" />
    <hkern u1="&#x106;" u2="&#xd5;" k="30" />
    <hkern u1="&#x106;" u2="&#xd4;" k="30" />
    <hkern u1="&#x106;" u2="&#xd3;" k="30" />
    <hkern u1="&#x106;" u2="&#xd2;" k="30" />
    <hkern u1="&#x106;" u2="&#xc7;" k="30" />
    <hkern u1="&#x106;" u2="&#xc6;" k="7" />
    <hkern u1="&#x106;" u2="&#xc5;" k="7" />
    <hkern u1="&#x106;" u2="&#xc4;" k="7" />
    <hkern u1="&#x106;" u2="&#xc3;" k="7" />
    <hkern u1="&#x106;" u2="&#xc2;" k="7" />
    <hkern u1="&#x106;" u2="&#xc1;" k="7" />
    <hkern u1="&#x106;" u2="&#xc0;" k="7" />
    <hkern u1="&#x106;" u2="&#xa9;" k="30" />
    <hkern u1="&#x106;" u2="w" k="10" />
    <hkern u1="&#x106;" u2="v" k="20" />
    <hkern u1="&#x106;" u2="q" k="10" />
    <hkern u1="&#x106;" u2="o" k="10" />
    <hkern u1="&#x106;" u2="g" k="10" />
    <hkern u1="&#x106;" u2="e" k="10" />
    <hkern u1="&#x106;" u2="d" k="10" />
    <hkern u1="&#x106;" u2="c" k="10" />
    <hkern u1="&#x106;" u2="_" k="-20" />
    <hkern u1="&#x106;" u2="Q" k="30" />
    <hkern u1="&#x106;" u2="O" k="30" />
    <hkern u1="&#x106;" u2="G" k="30" />
    <hkern u1="&#x106;" u2="C" k="30" />
    <hkern u1="&#x106;" u2="A" k="7" />
    <hkern u1="&#x106;" u2="&#x2e;" k="-15" />
    <hkern u1="&#x106;" u2="&#x2d;" k="75" />
    <hkern u1="&#x106;" u2="&#x2c;" k="-15" />
    <hkern u1="&#x107;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x107;" u2="&#x1ef3;" k="5" />
    <hkern u1="&#x107;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x107;" u2="&#x177;" k="5" />
    <hkern u1="&#x107;" u2="&#x173;" k="5" />
    <hkern u1="&#x107;" u2="&#x171;" k="5" />
    <hkern u1="&#x107;" u2="&#x16f;" k="5" />
    <hkern u1="&#x107;" u2="&#x16d;" k="5" />
    <hkern u1="&#x107;" u2="&#x16b;" k="5" />
    <hkern u1="&#x107;" u2="&#x169;" k="5" />
    <hkern u1="&#x107;" u2="&#x164;" k="60" />
    <hkern u1="&#x107;" u2="&#x162;" k="60" />
    <hkern u1="&#x107;" u2="&#x153;" k="15" />
    <hkern u1="&#x107;" u2="&#x151;" k="15" />
    <hkern u1="&#x107;" u2="&#x14f;" k="15" />
    <hkern u1="&#x107;" u2="&#x14d;" k="15" />
    <hkern u1="&#x107;" u2="&#x123;" k="15" />
    <hkern u1="&#x107;" u2="&#x121;" k="15" />
    <hkern u1="&#x107;" u2="&#x11f;" k="15" />
    <hkern u1="&#x107;" u2="&#x11d;" k="15" />
    <hkern u1="&#x107;" u2="&#x11b;" k="15" />
    <hkern u1="&#x107;" u2="&#x119;" k="15" />
    <hkern u1="&#x107;" u2="&#x117;" k="15" />
    <hkern u1="&#x107;" u2="&#x115;" k="15" />
    <hkern u1="&#x107;" u2="&#x113;" k="15" />
    <hkern u1="&#x107;" u2="&#x111;" k="15" />
    <hkern u1="&#x107;" u2="&#x10f;" k="15" />
    <hkern u1="&#x107;" u2="&#x10d;" k="15" />
    <hkern u1="&#x107;" u2="&#x10b;" k="15" />
    <hkern u1="&#x107;" u2="&#x109;" k="15" />
    <hkern u1="&#x107;" u2="&#x107;" k="15" />
    <hkern u1="&#x107;" u2="&#xff;" k="5" />
    <hkern u1="&#x107;" u2="&#xfd;" k="5" />
    <hkern u1="&#x107;" u2="&#xfc;" k="5" />
    <hkern u1="&#x107;" u2="&#xfb;" k="5" />
    <hkern u1="&#x107;" u2="&#xfa;" k="5" />
    <hkern u1="&#x107;" u2="&#xf9;" k="5" />
    <hkern u1="&#x107;" u2="&#xf8;" k="15" />
    <hkern u1="&#x107;" u2="&#xf6;" k="15" />
    <hkern u1="&#x107;" u2="&#xf5;" k="15" />
    <hkern u1="&#x107;" u2="&#xf4;" k="15" />
    <hkern u1="&#x107;" u2="&#xf3;" k="15" />
    <hkern u1="&#x107;" u2="&#xf2;" k="15" />
    <hkern u1="&#x107;" u2="&#xf0;" k="15" />
    <hkern u1="&#x107;" u2="&#xeb;" k="15" />
    <hkern u1="&#x107;" u2="&#xea;" k="15" />
    <hkern u1="&#x107;" u2="&#xe9;" k="15" />
    <hkern u1="&#x107;" u2="&#xe8;" k="15" />
    <hkern u1="&#x107;" u2="&#xe7;" k="15" />
    <hkern u1="&#x107;" u2="y" k="5" />
    <hkern u1="&#x107;" u2="x" k="-13" />
    <hkern u1="&#x107;" u2="u" k="5" />
    <hkern u1="&#x107;" u2="q" k="15" />
    <hkern u1="&#x107;" u2="o" k="15" />
    <hkern u1="&#x107;" u2="g" k="15" />
    <hkern u1="&#x107;" u2="e" k="15" />
    <hkern u1="&#x107;" u2="d" k="15" />
    <hkern u1="&#x107;" u2="c" k="15" />
    <hkern u1="&#x107;" u2="_" k="-40" />
    <hkern u1="&#x107;" u2="T" k="60" />
    <hkern u1="&#x107;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x107;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x108;" u2="&#x2026;" k="-15" />
    <hkern u1="&#x108;" u2="&#x2014;" k="75" />
    <hkern u1="&#x108;" u2="&#x2013;" k="75" />
    <hkern u1="&#x108;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x108;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x108;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x108;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x108;" u2="&#x1fe;" k="30" />
    <hkern u1="&#x108;" u2="&#x1fc;" k="7" />
    <hkern u1="&#x108;" u2="&#x1fa;" k="7" />
    <hkern u1="&#x108;" u2="&#x175;" k="10" />
    <hkern u1="&#x108;" u2="&#x153;" k="10" />
    <hkern u1="&#x108;" u2="&#x152;" k="30" />
    <hkern u1="&#x108;" u2="&#x151;" k="10" />
    <hkern u1="&#x108;" u2="&#x150;" k="30" />
    <hkern u1="&#x108;" u2="&#x14f;" k="10" />
    <hkern u1="&#x108;" u2="&#x14e;" k="30" />
    <hkern u1="&#x108;" u2="&#x14d;" k="10" />
    <hkern u1="&#x108;" u2="&#x14c;" k="30" />
    <hkern u1="&#x108;" u2="&#x123;" k="10" />
    <hkern u1="&#x108;" u2="&#x122;" k="30" />
    <hkern u1="&#x108;" u2="&#x121;" k="10" />
    <hkern u1="&#x108;" u2="&#x120;" k="30" />
    <hkern u1="&#x108;" u2="&#x11f;" k="10" />
    <hkern u1="&#x108;" u2="&#x11e;" k="30" />
    <hkern u1="&#x108;" u2="&#x11d;" k="10" />
    <hkern u1="&#x108;" u2="&#x11c;" k="30" />
    <hkern u1="&#x108;" u2="&#x11b;" k="10" />
    <hkern u1="&#x108;" u2="&#x119;" k="10" />
    <hkern u1="&#x108;" u2="&#x117;" k="10" />
    <hkern u1="&#x108;" u2="&#x115;" k="10" />
    <hkern u1="&#x108;" u2="&#x113;" k="10" />
    <hkern u1="&#x108;" u2="&#x111;" k="10" />
    <hkern u1="&#x108;" u2="&#x10f;" k="10" />
    <hkern u1="&#x108;" u2="&#x10d;" k="10" />
    <hkern u1="&#x108;" u2="&#x10c;" k="30" />
    <hkern u1="&#x108;" u2="&#x10b;" k="10" />
    <hkern u1="&#x108;" u2="&#x10a;" k="30" />
    <hkern u1="&#x108;" u2="&#x109;" k="10" />
    <hkern u1="&#x108;" u2="&#x108;" k="30" />
    <hkern u1="&#x108;" u2="&#x107;" k="10" />
    <hkern u1="&#x108;" u2="&#x106;" k="30" />
    <hkern u1="&#x108;" u2="&#x104;" k="7" />
    <hkern u1="&#x108;" u2="&#x102;" k="7" />
    <hkern u1="&#x108;" u2="&#x100;" k="7" />
    <hkern u1="&#x108;" u2="&#xf8;" k="10" />
    <hkern u1="&#x108;" u2="&#xf6;" k="10" />
    <hkern u1="&#x108;" u2="&#xf5;" k="10" />
    <hkern u1="&#x108;" u2="&#xf4;" k="10" />
    <hkern u1="&#x108;" u2="&#xf3;" k="10" />
    <hkern u1="&#x108;" u2="&#xf2;" k="10" />
    <hkern u1="&#x108;" u2="&#xf0;" k="10" />
    <hkern u1="&#x108;" u2="&#xeb;" k="10" />
    <hkern u1="&#x108;" u2="&#xea;" k="10" />
    <hkern u1="&#x108;" u2="&#xe9;" k="10" />
    <hkern u1="&#x108;" u2="&#xe8;" k="10" />
    <hkern u1="&#x108;" u2="&#xe7;" k="10" />
    <hkern u1="&#x108;" u2="&#xd8;" k="30" />
    <hkern u1="&#x108;" u2="&#xd6;" k="30" />
    <hkern u1="&#x108;" u2="&#xd5;" k="30" />
    <hkern u1="&#x108;" u2="&#xd4;" k="30" />
    <hkern u1="&#x108;" u2="&#xd3;" k="30" />
    <hkern u1="&#x108;" u2="&#xd2;" k="30" />
    <hkern u1="&#x108;" u2="&#xc7;" k="30" />
    <hkern u1="&#x108;" u2="&#xc6;" k="7" />
    <hkern u1="&#x108;" u2="&#xc5;" k="7" />
    <hkern u1="&#x108;" u2="&#xc4;" k="7" />
    <hkern u1="&#x108;" u2="&#xc3;" k="7" />
    <hkern u1="&#x108;" u2="&#xc2;" k="7" />
    <hkern u1="&#x108;" u2="&#xc1;" k="7" />
    <hkern u1="&#x108;" u2="&#xc0;" k="7" />
    <hkern u1="&#x108;" u2="&#xa9;" k="30" />
    <hkern u1="&#x108;" u2="w" k="10" />
    <hkern u1="&#x108;" u2="v" k="20" />
    <hkern u1="&#x108;" u2="q" k="10" />
    <hkern u1="&#x108;" u2="o" k="10" />
    <hkern u1="&#x108;" u2="g" k="10" />
    <hkern u1="&#x108;" u2="e" k="10" />
    <hkern u1="&#x108;" u2="d" k="10" />
    <hkern u1="&#x108;" u2="c" k="10" />
    <hkern u1="&#x108;" u2="_" k="-20" />
    <hkern u1="&#x108;" u2="Q" k="30" />
    <hkern u1="&#x108;" u2="O" k="30" />
    <hkern u1="&#x108;" u2="G" k="30" />
    <hkern u1="&#x108;" u2="C" k="30" />
    <hkern u1="&#x108;" u2="A" k="7" />
    <hkern u1="&#x108;" u2="&#x2e;" k="-15" />
    <hkern u1="&#x108;" u2="&#x2d;" k="75" />
    <hkern u1="&#x108;" u2="&#x2c;" k="-15" />
    <hkern u1="&#x109;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x109;" u2="&#x1ef3;" k="5" />
    <hkern u1="&#x109;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x109;" u2="&#x177;" k="5" />
    <hkern u1="&#x109;" u2="&#x173;" k="5" />
    <hkern u1="&#x109;" u2="&#x171;" k="5" />
    <hkern u1="&#x109;" u2="&#x16f;" k="5" />
    <hkern u1="&#x109;" u2="&#x16d;" k="5" />
    <hkern u1="&#x109;" u2="&#x16b;" k="5" />
    <hkern u1="&#x109;" u2="&#x169;" k="5" />
    <hkern u1="&#x109;" u2="&#x164;" k="60" />
    <hkern u1="&#x109;" u2="&#x162;" k="60" />
    <hkern u1="&#x109;" u2="&#x153;" k="15" />
    <hkern u1="&#x109;" u2="&#x151;" k="15" />
    <hkern u1="&#x109;" u2="&#x14f;" k="15" />
    <hkern u1="&#x109;" u2="&#x14d;" k="15" />
    <hkern u1="&#x109;" u2="&#x123;" k="15" />
    <hkern u1="&#x109;" u2="&#x121;" k="15" />
    <hkern u1="&#x109;" u2="&#x11f;" k="15" />
    <hkern u1="&#x109;" u2="&#x11d;" k="15" />
    <hkern u1="&#x109;" u2="&#x11b;" k="15" />
    <hkern u1="&#x109;" u2="&#x119;" k="15" />
    <hkern u1="&#x109;" u2="&#x117;" k="15" />
    <hkern u1="&#x109;" u2="&#x115;" k="15" />
    <hkern u1="&#x109;" u2="&#x113;" k="15" />
    <hkern u1="&#x109;" u2="&#x111;" k="15" />
    <hkern u1="&#x109;" u2="&#x10f;" k="15" />
    <hkern u1="&#x109;" u2="&#x10d;" k="15" />
    <hkern u1="&#x109;" u2="&#x10b;" k="15" />
    <hkern u1="&#x109;" u2="&#x109;" k="15" />
    <hkern u1="&#x109;" u2="&#x107;" k="15" />
    <hkern u1="&#x109;" u2="&#xff;" k="5" />
    <hkern u1="&#x109;" u2="&#xfd;" k="5" />
    <hkern u1="&#x109;" u2="&#xfc;" k="5" />
    <hkern u1="&#x109;" u2="&#xfb;" k="5" />
    <hkern u1="&#x109;" u2="&#xfa;" k="5" />
    <hkern u1="&#x109;" u2="&#xf9;" k="5" />
    <hkern u1="&#x109;" u2="&#xf8;" k="15" />
    <hkern u1="&#x109;" u2="&#xf6;" k="15" />
    <hkern u1="&#x109;" u2="&#xf5;" k="15" />
    <hkern u1="&#x109;" u2="&#xf4;" k="15" />
    <hkern u1="&#x109;" u2="&#xf3;" k="15" />
    <hkern u1="&#x109;" u2="&#xf2;" k="15" />
    <hkern u1="&#x109;" u2="&#xf0;" k="15" />
    <hkern u1="&#x109;" u2="&#xeb;" k="15" />
    <hkern u1="&#x109;" u2="&#xea;" k="15" />
    <hkern u1="&#x109;" u2="&#xe9;" k="15" />
    <hkern u1="&#x109;" u2="&#xe8;" k="15" />
    <hkern u1="&#x109;" u2="&#xe7;" k="15" />
    <hkern u1="&#x109;" u2="y" k="5" />
    <hkern u1="&#x109;" u2="x" k="-13" />
    <hkern u1="&#x109;" u2="u" k="5" />
    <hkern u1="&#x109;" u2="q" k="15" />
    <hkern u1="&#x109;" u2="o" k="15" />
    <hkern u1="&#x109;" u2="g" k="15" />
    <hkern u1="&#x109;" u2="e" k="15" />
    <hkern u1="&#x109;" u2="d" k="15" />
    <hkern u1="&#x109;" u2="c" k="15" />
    <hkern u1="&#x109;" u2="_" k="-40" />
    <hkern u1="&#x109;" u2="T" k="60" />
    <hkern u1="&#x109;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x109;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10a;" u2="&#x2026;" k="-15" />
    <hkern u1="&#x10a;" u2="&#x2014;" k="75" />
    <hkern u1="&#x10a;" u2="&#x2013;" k="75" />
    <hkern u1="&#x10a;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1fe;" k="30" />
    <hkern u1="&#x10a;" u2="&#x1fc;" k="7" />
    <hkern u1="&#x10a;" u2="&#x1fa;" k="7" />
    <hkern u1="&#x10a;" u2="&#x175;" k="10" />
    <hkern u1="&#x10a;" u2="&#x153;" k="10" />
    <hkern u1="&#x10a;" u2="&#x152;" k="30" />
    <hkern u1="&#x10a;" u2="&#x151;" k="10" />
    <hkern u1="&#x10a;" u2="&#x150;" k="30" />
    <hkern u1="&#x10a;" u2="&#x14f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x14e;" k="30" />
    <hkern u1="&#x10a;" u2="&#x14d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x14c;" k="30" />
    <hkern u1="&#x10a;" u2="&#x123;" k="10" />
    <hkern u1="&#x10a;" u2="&#x122;" k="30" />
    <hkern u1="&#x10a;" u2="&#x121;" k="10" />
    <hkern u1="&#x10a;" u2="&#x120;" k="30" />
    <hkern u1="&#x10a;" u2="&#x11f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x11e;" k="30" />
    <hkern u1="&#x10a;" u2="&#x11d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x11c;" k="30" />
    <hkern u1="&#x10a;" u2="&#x11b;" k="10" />
    <hkern u1="&#x10a;" u2="&#x119;" k="10" />
    <hkern u1="&#x10a;" u2="&#x117;" k="10" />
    <hkern u1="&#x10a;" u2="&#x115;" k="10" />
    <hkern u1="&#x10a;" u2="&#x113;" k="10" />
    <hkern u1="&#x10a;" u2="&#x111;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10c;" k="30" />
    <hkern u1="&#x10a;" u2="&#x10b;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10a;" k="30" />
    <hkern u1="&#x10a;" u2="&#x109;" k="10" />
    <hkern u1="&#x10a;" u2="&#x108;" k="30" />
    <hkern u1="&#x10a;" u2="&#x107;" k="10" />
    <hkern u1="&#x10a;" u2="&#x106;" k="30" />
    <hkern u1="&#x10a;" u2="&#x104;" k="7" />
    <hkern u1="&#x10a;" u2="&#x102;" k="7" />
    <hkern u1="&#x10a;" u2="&#x100;" k="7" />
    <hkern u1="&#x10a;" u2="&#xf8;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf6;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf5;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf4;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf3;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf2;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf0;" k="10" />
    <hkern u1="&#x10a;" u2="&#xeb;" k="10" />
    <hkern u1="&#x10a;" u2="&#xea;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe9;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe8;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe7;" k="10" />
    <hkern u1="&#x10a;" u2="&#xd8;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd6;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd5;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd4;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd3;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd2;" k="30" />
    <hkern u1="&#x10a;" u2="&#xc7;" k="30" />
    <hkern u1="&#x10a;" u2="&#xc6;" k="7" />
    <hkern u1="&#x10a;" u2="&#xc5;" k="7" />
    <hkern u1="&#x10a;" u2="&#xc4;" k="7" />
    <hkern u1="&#x10a;" u2="&#xc3;" k="7" />
    <hkern u1="&#x10a;" u2="&#xc2;" k="7" />
    <hkern u1="&#x10a;" u2="&#xc1;" k="7" />
    <hkern u1="&#x10a;" u2="&#xc0;" k="7" />
    <hkern u1="&#x10a;" u2="&#xa9;" k="30" />
    <hkern u1="&#x10a;" u2="w" k="10" />
    <hkern u1="&#x10a;" u2="v" k="20" />
    <hkern u1="&#x10a;" u2="q" k="10" />
    <hkern u1="&#x10a;" u2="o" k="10" />
    <hkern u1="&#x10a;" u2="g" k="10" />
    <hkern u1="&#x10a;" u2="e" k="10" />
    <hkern u1="&#x10a;" u2="d" k="10" />
    <hkern u1="&#x10a;" u2="c" k="10" />
    <hkern u1="&#x10a;" u2="_" k="-20" />
    <hkern u1="&#x10a;" u2="Q" k="30" />
    <hkern u1="&#x10a;" u2="O" k="30" />
    <hkern u1="&#x10a;" u2="G" k="30" />
    <hkern u1="&#x10a;" u2="C" k="30" />
    <hkern u1="&#x10a;" u2="A" k="7" />
    <hkern u1="&#x10a;" u2="&#x2e;" k="-15" />
    <hkern u1="&#x10a;" u2="&#x2d;" k="75" />
    <hkern u1="&#x10a;" u2="&#x2c;" k="-15" />
    <hkern u1="&#x10b;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x1ef3;" k="5" />
    <hkern u1="&#x10b;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x10b;" u2="&#x177;" k="5" />
    <hkern u1="&#x10b;" u2="&#x173;" k="5" />
    <hkern u1="&#x10b;" u2="&#x171;" k="5" />
    <hkern u1="&#x10b;" u2="&#x16f;" k="5" />
    <hkern u1="&#x10b;" u2="&#x16d;" k="5" />
    <hkern u1="&#x10b;" u2="&#x16b;" k="5" />
    <hkern u1="&#x10b;" u2="&#x169;" k="5" />
    <hkern u1="&#x10b;" u2="&#x164;" k="60" />
    <hkern u1="&#x10b;" u2="&#x162;" k="60" />
    <hkern u1="&#x10b;" u2="&#x153;" k="15" />
    <hkern u1="&#x10b;" u2="&#x151;" k="15" />
    <hkern u1="&#x10b;" u2="&#x14f;" k="15" />
    <hkern u1="&#x10b;" u2="&#x14d;" k="15" />
    <hkern u1="&#x10b;" u2="&#x123;" k="15" />
    <hkern u1="&#x10b;" u2="&#x121;" k="15" />
    <hkern u1="&#x10b;" u2="&#x11f;" k="15" />
    <hkern u1="&#x10b;" u2="&#x11d;" k="15" />
    <hkern u1="&#x10b;" u2="&#x11b;" k="15" />
    <hkern u1="&#x10b;" u2="&#x119;" k="15" />
    <hkern u1="&#x10b;" u2="&#x117;" k="15" />
    <hkern u1="&#x10b;" u2="&#x115;" k="15" />
    <hkern u1="&#x10b;" u2="&#x113;" k="15" />
    <hkern u1="&#x10b;" u2="&#x111;" k="15" />
    <hkern u1="&#x10b;" u2="&#x10f;" k="15" />
    <hkern u1="&#x10b;" u2="&#x10d;" k="15" />
    <hkern u1="&#x10b;" u2="&#x10b;" k="15" />
    <hkern u1="&#x10b;" u2="&#x109;" k="15" />
    <hkern u1="&#x10b;" u2="&#x107;" k="15" />
    <hkern u1="&#x10b;" u2="&#xff;" k="5" />
    <hkern u1="&#x10b;" u2="&#xfd;" k="5" />
    <hkern u1="&#x10b;" u2="&#xfc;" k="5" />
    <hkern u1="&#x10b;" u2="&#xfb;" k="5" />
    <hkern u1="&#x10b;" u2="&#xfa;" k="5" />
    <hkern u1="&#x10b;" u2="&#xf9;" k="5" />
    <hkern u1="&#x10b;" u2="&#xf8;" k="15" />
    <hkern u1="&#x10b;" u2="&#xf6;" k="15" />
    <hkern u1="&#x10b;" u2="&#xf5;" k="15" />
    <hkern u1="&#x10b;" u2="&#xf4;" k="15" />
    <hkern u1="&#x10b;" u2="&#xf3;" k="15" />
    <hkern u1="&#x10b;" u2="&#xf2;" k="15" />
    <hkern u1="&#x10b;" u2="&#xf0;" k="15" />
    <hkern u1="&#x10b;" u2="&#xeb;" k="15" />
    <hkern u1="&#x10b;" u2="&#xea;" k="15" />
    <hkern u1="&#x10b;" u2="&#xe9;" k="15" />
    <hkern u1="&#x10b;" u2="&#xe8;" k="15" />
    <hkern u1="&#x10b;" u2="&#xe7;" k="15" />
    <hkern u1="&#x10b;" u2="y" k="5" />
    <hkern u1="&#x10b;" u2="x" k="-13" />
    <hkern u1="&#x10b;" u2="u" k="5" />
    <hkern u1="&#x10b;" u2="q" k="15" />
    <hkern u1="&#x10b;" u2="o" k="15" />
    <hkern u1="&#x10b;" u2="g" k="15" />
    <hkern u1="&#x10b;" u2="e" k="15" />
    <hkern u1="&#x10b;" u2="d" k="15" />
    <hkern u1="&#x10b;" u2="c" k="15" />
    <hkern u1="&#x10b;" u2="_" k="-40" />
    <hkern u1="&#x10b;" u2="T" k="60" />
    <hkern u1="&#x10b;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10c;" u2="&#x2026;" k="-15" />
    <hkern u1="&#x10c;" u2="&#x2014;" k="75" />
    <hkern u1="&#x10c;" u2="&#x2013;" k="75" />
    <hkern u1="&#x10c;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1fe;" k="30" />
    <hkern u1="&#x10c;" u2="&#x1fc;" k="7" />
    <hkern u1="&#x10c;" u2="&#x1fa;" k="7" />
    <hkern u1="&#x10c;" u2="&#x175;" k="10" />
    <hkern u1="&#x10c;" u2="&#x153;" k="10" />
    <hkern u1="&#x10c;" u2="&#x152;" k="30" />
    <hkern u1="&#x10c;" u2="&#x151;" k="10" />
    <hkern u1="&#x10c;" u2="&#x150;" k="30" />
    <hkern u1="&#x10c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x14e;" k="30" />
    <hkern u1="&#x10c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x14c;" k="30" />
    <hkern u1="&#x10c;" u2="&#x123;" k="10" />
    <hkern u1="&#x10c;" u2="&#x122;" k="30" />
    <hkern u1="&#x10c;" u2="&#x121;" k="10" />
    <hkern u1="&#x10c;" u2="&#x120;" k="30" />
    <hkern u1="&#x10c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x11e;" k="30" />
    <hkern u1="&#x10c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x11c;" k="30" />
    <hkern u1="&#x10c;" u2="&#x11b;" k="10" />
    <hkern u1="&#x10c;" u2="&#x119;" k="10" />
    <hkern u1="&#x10c;" u2="&#x117;" k="10" />
    <hkern u1="&#x10c;" u2="&#x115;" k="10" />
    <hkern u1="&#x10c;" u2="&#x113;" k="10" />
    <hkern u1="&#x10c;" u2="&#x111;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10c;" k="30" />
    <hkern u1="&#x10c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10a;" k="30" />
    <hkern u1="&#x10c;" u2="&#x109;" k="10" />
    <hkern u1="&#x10c;" u2="&#x108;" k="30" />
    <hkern u1="&#x10c;" u2="&#x107;" k="10" />
    <hkern u1="&#x10c;" u2="&#x106;" k="30" />
    <hkern u1="&#x10c;" u2="&#x104;" k="7" />
    <hkern u1="&#x10c;" u2="&#x102;" k="7" />
    <hkern u1="&#x10c;" u2="&#x100;" k="7" />
    <hkern u1="&#x10c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf0;" k="10" />
    <hkern u1="&#x10c;" u2="&#xeb;" k="10" />
    <hkern u1="&#x10c;" u2="&#xea;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe9;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe8;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x10c;" u2="&#xd8;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd6;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd5;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd4;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd3;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd2;" k="30" />
    <hkern u1="&#x10c;" u2="&#xc7;" k="30" />
    <hkern u1="&#x10c;" u2="&#xc6;" k="7" />
    <hkern u1="&#x10c;" u2="&#xc5;" k="7" />
    <hkern u1="&#x10c;" u2="&#xc4;" k="7" />
    <hkern u1="&#x10c;" u2="&#xc3;" k="7" />
    <hkern u1="&#x10c;" u2="&#xc2;" k="7" />
    <hkern u1="&#x10c;" u2="&#xc1;" k="7" />
    <hkern u1="&#x10c;" u2="&#xc0;" k="7" />
    <hkern u1="&#x10c;" u2="&#xa9;" k="30" />
    <hkern u1="&#x10c;" u2="w" k="10" />
    <hkern u1="&#x10c;" u2="v" k="20" />
    <hkern u1="&#x10c;" u2="q" k="10" />
    <hkern u1="&#x10c;" u2="o" k="10" />
    <hkern u1="&#x10c;" u2="g" k="10" />
    <hkern u1="&#x10c;" u2="e" k="10" />
    <hkern u1="&#x10c;" u2="d" k="10" />
    <hkern u1="&#x10c;" u2="c" k="10" />
    <hkern u1="&#x10c;" u2="_" k="-20" />
    <hkern u1="&#x10c;" u2="Q" k="30" />
    <hkern u1="&#x10c;" u2="O" k="30" />
    <hkern u1="&#x10c;" u2="G" k="30" />
    <hkern u1="&#x10c;" u2="C" k="30" />
    <hkern u1="&#x10c;" u2="A" k="7" />
    <hkern u1="&#x10c;" u2="&#x2e;" k="-15" />
    <hkern u1="&#x10c;" u2="&#x2d;" k="75" />
    <hkern u1="&#x10c;" u2="&#x2c;" k="-15" />
    <hkern u1="&#x10d;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x1ef3;" k="5" />
    <hkern u1="&#x10d;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x10d;" u2="&#x177;" k="5" />
    <hkern u1="&#x10d;" u2="&#x173;" k="5" />
    <hkern u1="&#x10d;" u2="&#x171;" k="5" />
    <hkern u1="&#x10d;" u2="&#x16f;" k="5" />
    <hkern u1="&#x10d;" u2="&#x16d;" k="5" />
    <hkern u1="&#x10d;" u2="&#x16b;" k="5" />
    <hkern u1="&#x10d;" u2="&#x169;" k="5" />
    <hkern u1="&#x10d;" u2="&#x164;" k="60" />
    <hkern u1="&#x10d;" u2="&#x162;" k="60" />
    <hkern u1="&#x10d;" u2="&#x153;" k="15" />
    <hkern u1="&#x10d;" u2="&#x151;" k="15" />
    <hkern u1="&#x10d;" u2="&#x14f;" k="15" />
    <hkern u1="&#x10d;" u2="&#x14d;" k="15" />
    <hkern u1="&#x10d;" u2="&#x123;" k="15" />
    <hkern u1="&#x10d;" u2="&#x121;" k="15" />
    <hkern u1="&#x10d;" u2="&#x11f;" k="15" />
    <hkern u1="&#x10d;" u2="&#x11d;" k="15" />
    <hkern u1="&#x10d;" u2="&#x11b;" k="15" />
    <hkern u1="&#x10d;" u2="&#x119;" k="15" />
    <hkern u1="&#x10d;" u2="&#x117;" k="15" />
    <hkern u1="&#x10d;" u2="&#x115;" k="15" />
    <hkern u1="&#x10d;" u2="&#x113;" k="15" />
    <hkern u1="&#x10d;" u2="&#x111;" k="15" />
    <hkern u1="&#x10d;" u2="&#x10f;" k="15" />
    <hkern u1="&#x10d;" u2="&#x10d;" k="15" />
    <hkern u1="&#x10d;" u2="&#x10b;" k="15" />
    <hkern u1="&#x10d;" u2="&#x109;" k="15" />
    <hkern u1="&#x10d;" u2="&#x107;" k="15" />
    <hkern u1="&#x10d;" u2="&#xff;" k="5" />
    <hkern u1="&#x10d;" u2="&#xfd;" k="5" />
    <hkern u1="&#x10d;" u2="&#xfc;" k="5" />
    <hkern u1="&#x10d;" u2="&#xfb;" k="5" />
    <hkern u1="&#x10d;" u2="&#xfa;" k="5" />
    <hkern u1="&#x10d;" u2="&#xf9;" k="5" />
    <hkern u1="&#x10d;" u2="&#xf8;" k="15" />
    <hkern u1="&#x10d;" u2="&#xf6;" k="15" />
    <hkern u1="&#x10d;" u2="&#xf5;" k="15" />
    <hkern u1="&#x10d;" u2="&#xf4;" k="15" />
    <hkern u1="&#x10d;" u2="&#xf3;" k="15" />
    <hkern u1="&#x10d;" u2="&#xf2;" k="15" />
    <hkern u1="&#x10d;" u2="&#xf0;" k="15" />
    <hkern u1="&#x10d;" u2="&#xeb;" k="15" />
    <hkern u1="&#x10d;" u2="&#xea;" k="15" />
    <hkern u1="&#x10d;" u2="&#xe9;" k="15" />
    <hkern u1="&#x10d;" u2="&#xe8;" k="15" />
    <hkern u1="&#x10d;" u2="&#xe7;" k="15" />
    <hkern u1="&#x10d;" u2="y" k="5" />
    <hkern u1="&#x10d;" u2="x" k="-13" />
    <hkern u1="&#x10d;" u2="u" k="5" />
    <hkern u1="&#x10d;" u2="q" k="15" />
    <hkern u1="&#x10d;" u2="o" k="15" />
    <hkern u1="&#x10d;" u2="g" k="15" />
    <hkern u1="&#x10d;" u2="e" k="15" />
    <hkern u1="&#x10d;" u2="d" k="15" />
    <hkern u1="&#x10d;" u2="c" k="15" />
    <hkern u1="&#x10d;" u2="_" k="-40" />
    <hkern u1="&#x10d;" u2="T" k="60" />
    <hkern u1="&#x10d;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10e;" u2="&#x2026;" k="45" />
    <hkern u1="&#x10e;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x218;" k="5" />
    <hkern u1="&#x10e;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x164;" k="15" />
    <hkern u1="&#x10e;" u2="&#x162;" k="15" />
    <hkern u1="&#x10e;" u2="&#x160;" k="5" />
    <hkern u1="&#x10e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x10e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x10e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x10e;" u2="&#x152;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x150;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x134;" k="-20" />
    <hkern u1="&#x10e;" u2="&#x122;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x120;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x108;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x106;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x10e;" u2="X" k="10" />
    <hkern u1="&#x10e;" u2="V" k="15" />
    <hkern u1="&#x10e;" u2="T" k="15" />
    <hkern u1="&#x10e;" u2="S" k="5" />
    <hkern u1="&#x10e;" u2="Q" k="-5" />
    <hkern u1="&#x10e;" u2="O" k="-5" />
    <hkern u1="&#x10e;" u2="J" k="-20" />
    <hkern u1="&#x10e;" u2="G" k="-5" />
    <hkern u1="&#x10e;" u2="C" k="-5" />
    <hkern u1="&#x10e;" u2="&#x2e;" k="45" />
    <hkern u1="&#x10e;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x2c;" k="45" />
    <hkern u1="&#x110;" u2="&#x2026;" k="45" />
    <hkern u1="&#x110;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x110;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x110;" u2="&#x218;" k="5" />
    <hkern u1="&#x110;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x110;" u2="&#x164;" k="15" />
    <hkern u1="&#x110;" u2="&#x162;" k="15" />
    <hkern u1="&#x110;" u2="&#x160;" k="5" />
    <hkern u1="&#x110;" u2="&#x15e;" k="5" />
    <hkern u1="&#x110;" u2="&#x15c;" k="5" />
    <hkern u1="&#x110;" u2="&#x15a;" k="5" />
    <hkern u1="&#x110;" u2="&#x152;" k="-5" />
    <hkern u1="&#x110;" u2="&#x150;" k="-5" />
    <hkern u1="&#x110;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x110;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x134;" k="-20" />
    <hkern u1="&#x110;" u2="&#x122;" k="-5" />
    <hkern u1="&#x110;" u2="&#x120;" k="-5" />
    <hkern u1="&#x110;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x110;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x110;" u2="&#x108;" k="-5" />
    <hkern u1="&#x110;" u2="&#x106;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x110;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x110;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x110;" u2="X" k="10" />
    <hkern u1="&#x110;" u2="V" k="15" />
    <hkern u1="&#x110;" u2="T" k="15" />
    <hkern u1="&#x110;" u2="S" k="5" />
    <hkern u1="&#x110;" u2="Q" k="-5" />
    <hkern u1="&#x110;" u2="O" k="-5" />
    <hkern u1="&#x110;" u2="J" k="-20" />
    <hkern u1="&#x110;" u2="G" k="-5" />
    <hkern u1="&#x110;" u2="C" k="-5" />
    <hkern u1="&#x110;" u2="&#x2e;" k="45" />
    <hkern u1="&#x110;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x110;" u2="&#x2c;" k="45" />
    <hkern u1="&#x112;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x112;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x112;" u2="&#x152;" k="7" />
    <hkern u1="&#x112;" u2="&#x150;" k="7" />
    <hkern u1="&#x112;" u2="&#x14e;" k="7" />
    <hkern u1="&#x112;" u2="&#x14c;" k="7" />
    <hkern u1="&#x112;" u2="&#x122;" k="7" />
    <hkern u1="&#x112;" u2="&#x120;" k="7" />
    <hkern u1="&#x112;" u2="&#x11e;" k="7" />
    <hkern u1="&#x112;" u2="&#x11c;" k="7" />
    <hkern u1="&#x112;" u2="&#x10c;" k="7" />
    <hkern u1="&#x112;" u2="&#x10a;" k="7" />
    <hkern u1="&#x112;" u2="&#x108;" k="7" />
    <hkern u1="&#x112;" u2="&#x106;" k="7" />
    <hkern u1="&#x112;" u2="&#xd8;" k="7" />
    <hkern u1="&#x112;" u2="&#xd6;" k="7" />
    <hkern u1="&#x112;" u2="&#xd5;" k="7" />
    <hkern u1="&#x112;" u2="&#xd4;" k="7" />
    <hkern u1="&#x112;" u2="&#xd3;" k="7" />
    <hkern u1="&#x112;" u2="&#xd2;" k="7" />
    <hkern u1="&#x112;" u2="&#xc7;" k="7" />
    <hkern u1="&#x112;" u2="&#xa9;" k="7" />
    <hkern u1="&#x112;" u2="Q" k="7" />
    <hkern u1="&#x112;" u2="O" k="7" />
    <hkern u1="&#x112;" u2="G" k="7" />
    <hkern u1="&#x112;" u2="C" k="7" />
    <hkern u1="&#x112;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x112;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x113;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x113;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x113;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x113;" u2="&#x219;" k="-5" />
    <hkern u1="&#x113;" u2="&#x174;" k="10" />
    <hkern u1="&#x113;" u2="&#x164;" k="70" />
    <hkern u1="&#x113;" u2="&#x162;" k="70" />
    <hkern u1="&#x113;" u2="&#x161;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x113;" u2="s" k="-5" />
    <hkern u1="&#x113;" u2="W" k="10" />
    <hkern u1="&#x113;" u2="V" k="20" />
    <hkern u1="&#x113;" u2="T" k="70" />
    <hkern u1="&#x114;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x114;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x114;" u2="&#x152;" k="7" />
    <hkern u1="&#x114;" u2="&#x150;" k="7" />
    <hkern u1="&#x114;" u2="&#x14e;" k="7" />
    <hkern u1="&#x114;" u2="&#x14c;" k="7" />
    <hkern u1="&#x114;" u2="&#x122;" k="7" />
    <hkern u1="&#x114;" u2="&#x120;" k="7" />
    <hkern u1="&#x114;" u2="&#x11e;" k="7" />
    <hkern u1="&#x114;" u2="&#x11c;" k="7" />
    <hkern u1="&#x114;" u2="&#x10c;" k="7" />
    <hkern u1="&#x114;" u2="&#x10a;" k="7" />
    <hkern u1="&#x114;" u2="&#x108;" k="7" />
    <hkern u1="&#x114;" u2="&#x106;" k="7" />
    <hkern u1="&#x114;" u2="&#xd8;" k="7" />
    <hkern u1="&#x114;" u2="&#xd6;" k="7" />
    <hkern u1="&#x114;" u2="&#xd5;" k="7" />
    <hkern u1="&#x114;" u2="&#xd4;" k="7" />
    <hkern u1="&#x114;" u2="&#xd3;" k="7" />
    <hkern u1="&#x114;" u2="&#xd2;" k="7" />
    <hkern u1="&#x114;" u2="&#xc7;" k="7" />
    <hkern u1="&#x114;" u2="&#xa9;" k="7" />
    <hkern u1="&#x114;" u2="Q" k="7" />
    <hkern u1="&#x114;" u2="O" k="7" />
    <hkern u1="&#x114;" u2="G" k="7" />
    <hkern u1="&#x114;" u2="C" k="7" />
    <hkern u1="&#x114;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x114;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x115;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x115;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x115;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x115;" u2="&#x219;" k="-5" />
    <hkern u1="&#x115;" u2="&#x174;" k="10" />
    <hkern u1="&#x115;" u2="&#x164;" k="70" />
    <hkern u1="&#x115;" u2="&#x162;" k="70" />
    <hkern u1="&#x115;" u2="&#x161;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x115;" u2="s" k="-5" />
    <hkern u1="&#x115;" u2="W" k="10" />
    <hkern u1="&#x115;" u2="V" k="20" />
    <hkern u1="&#x115;" u2="T" k="70" />
    <hkern u1="&#x116;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x116;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x116;" u2="&#x152;" k="7" />
    <hkern u1="&#x116;" u2="&#x150;" k="7" />
    <hkern u1="&#x116;" u2="&#x14e;" k="7" />
    <hkern u1="&#x116;" u2="&#x14c;" k="7" />
    <hkern u1="&#x116;" u2="&#x122;" k="7" />
    <hkern u1="&#x116;" u2="&#x120;" k="7" />
    <hkern u1="&#x116;" u2="&#x11e;" k="7" />
    <hkern u1="&#x116;" u2="&#x11c;" k="7" />
    <hkern u1="&#x116;" u2="&#x10c;" k="7" />
    <hkern u1="&#x116;" u2="&#x10a;" k="7" />
    <hkern u1="&#x116;" u2="&#x108;" k="7" />
    <hkern u1="&#x116;" u2="&#x106;" k="7" />
    <hkern u1="&#x116;" u2="&#xd8;" k="7" />
    <hkern u1="&#x116;" u2="&#xd6;" k="7" />
    <hkern u1="&#x116;" u2="&#xd5;" k="7" />
    <hkern u1="&#x116;" u2="&#xd4;" k="7" />
    <hkern u1="&#x116;" u2="&#xd3;" k="7" />
    <hkern u1="&#x116;" u2="&#xd2;" k="7" />
    <hkern u1="&#x116;" u2="&#xc7;" k="7" />
    <hkern u1="&#x116;" u2="&#xa9;" k="7" />
    <hkern u1="&#x116;" u2="Q" k="7" />
    <hkern u1="&#x116;" u2="O" k="7" />
    <hkern u1="&#x116;" u2="G" k="7" />
    <hkern u1="&#x116;" u2="C" k="7" />
    <hkern u1="&#x116;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x116;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x117;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x117;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x117;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x117;" u2="&#x219;" k="-5" />
    <hkern u1="&#x117;" u2="&#x174;" k="10" />
    <hkern u1="&#x117;" u2="&#x164;" k="70" />
    <hkern u1="&#x117;" u2="&#x162;" k="70" />
    <hkern u1="&#x117;" u2="&#x161;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x117;" u2="s" k="-5" />
    <hkern u1="&#x117;" u2="W" k="10" />
    <hkern u1="&#x117;" u2="V" k="20" />
    <hkern u1="&#x117;" u2="T" k="70" />
    <hkern u1="&#x118;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x118;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x118;" u2="&#x152;" k="7" />
    <hkern u1="&#x118;" u2="&#x150;" k="7" />
    <hkern u1="&#x118;" u2="&#x14e;" k="7" />
    <hkern u1="&#x118;" u2="&#x14c;" k="7" />
    <hkern u1="&#x118;" u2="&#x122;" k="7" />
    <hkern u1="&#x118;" u2="&#x120;" k="7" />
    <hkern u1="&#x118;" u2="&#x11e;" k="7" />
    <hkern u1="&#x118;" u2="&#x11c;" k="7" />
    <hkern u1="&#x118;" u2="&#x10c;" k="7" />
    <hkern u1="&#x118;" u2="&#x10a;" k="7" />
    <hkern u1="&#x118;" u2="&#x108;" k="7" />
    <hkern u1="&#x118;" u2="&#x106;" k="7" />
    <hkern u1="&#x118;" u2="&#xd8;" k="7" />
    <hkern u1="&#x118;" u2="&#xd6;" k="7" />
    <hkern u1="&#x118;" u2="&#xd5;" k="7" />
    <hkern u1="&#x118;" u2="&#xd4;" k="7" />
    <hkern u1="&#x118;" u2="&#xd3;" k="7" />
    <hkern u1="&#x118;" u2="&#xd2;" k="7" />
    <hkern u1="&#x118;" u2="&#xc7;" k="7" />
    <hkern u1="&#x118;" u2="&#xa9;" k="7" />
    <hkern u1="&#x118;" u2="Q" k="7" />
    <hkern u1="&#x118;" u2="O" k="7" />
    <hkern u1="&#x118;" u2="G" k="7" />
    <hkern u1="&#x118;" u2="C" k="7" />
    <hkern u1="&#x118;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x119;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x119;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x119;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x119;" u2="&#x219;" k="-5" />
    <hkern u1="&#x119;" u2="&#x174;" k="10" />
    <hkern u1="&#x119;" u2="&#x164;" k="70" />
    <hkern u1="&#x119;" u2="&#x162;" k="70" />
    <hkern u1="&#x119;" u2="&#x161;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x119;" u2="s" k="-5" />
    <hkern u1="&#x119;" u2="W" k="10" />
    <hkern u1="&#x119;" u2="V" k="20" />
    <hkern u1="&#x119;" u2="T" k="70" />
    <hkern u1="&#x11a;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x11a;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x11a;" u2="&#x152;" k="7" />
    <hkern u1="&#x11a;" u2="&#x150;" k="7" />
    <hkern u1="&#x11a;" u2="&#x14e;" k="7" />
    <hkern u1="&#x11a;" u2="&#x14c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x122;" k="7" />
    <hkern u1="&#x11a;" u2="&#x120;" k="7" />
    <hkern u1="&#x11a;" u2="&#x11e;" k="7" />
    <hkern u1="&#x11a;" u2="&#x11c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x10c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x10a;" k="7" />
    <hkern u1="&#x11a;" u2="&#x108;" k="7" />
    <hkern u1="&#x11a;" u2="&#x106;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd8;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd6;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd5;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd4;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd3;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd2;" k="7" />
    <hkern u1="&#x11a;" u2="&#xc7;" k="7" />
    <hkern u1="&#x11a;" u2="&#xa9;" k="7" />
    <hkern u1="&#x11a;" u2="Q" k="7" />
    <hkern u1="&#x11a;" u2="O" k="7" />
    <hkern u1="&#x11a;" u2="G" k="7" />
    <hkern u1="&#x11a;" u2="C" k="7" />
    <hkern u1="&#x11a;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x11a;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x11b;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x11b;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x11b;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x11b;" u2="&#x219;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x174;" k="10" />
    <hkern u1="&#x11b;" u2="&#x164;" k="70" />
    <hkern u1="&#x11b;" u2="&#x162;" k="70" />
    <hkern u1="&#x11b;" u2="&#x161;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x11b;" u2="s" k="-5" />
    <hkern u1="&#x11b;" u2="W" k="10" />
    <hkern u1="&#x11b;" u2="V" k="20" />
    <hkern u1="&#x11b;" u2="T" k="70" />
    <hkern u1="&#x11d;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x11d;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x11d;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x11d;" u2="&#x174;" k="10" />
    <hkern u1="&#x11d;" u2="&#x164;" k="80" />
    <hkern u1="&#x11d;" u2="&#x162;" k="80" />
    <hkern u1="&#x11d;" u2="W" k="10" />
    <hkern u1="&#x11d;" u2="V" k="20" />
    <hkern u1="&#x11d;" u2="T" k="80" />
    <hkern u1="&#x11f;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x11f;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x11f;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x11f;" u2="&#x174;" k="10" />
    <hkern u1="&#x11f;" u2="&#x164;" k="80" />
    <hkern u1="&#x11f;" u2="&#x162;" k="80" />
    <hkern u1="&#x11f;" u2="W" k="10" />
    <hkern u1="&#x11f;" u2="V" k="20" />
    <hkern u1="&#x11f;" u2="T" k="80" />
    <hkern u1="&#x121;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x121;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x121;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x121;" u2="&#x174;" k="10" />
    <hkern u1="&#x121;" u2="&#x164;" k="80" />
    <hkern u1="&#x121;" u2="&#x162;" k="80" />
    <hkern u1="&#x121;" u2="W" k="10" />
    <hkern u1="&#x121;" u2="V" k="20" />
    <hkern u1="&#x121;" u2="T" k="80" />
    <hkern u1="&#x123;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x123;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x123;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x123;" u2="&#x174;" k="10" />
    <hkern u1="&#x123;" u2="&#x164;" k="80" />
    <hkern u1="&#x123;" u2="&#x162;" k="80" />
    <hkern u1="&#x123;" u2="W" k="10" />
    <hkern u1="&#x123;" u2="V" k="20" />
    <hkern u1="&#x123;" u2="T" k="80" />
    <hkern u1="&#x125;" u2="&#x164;" k="90" />
    <hkern u1="&#x125;" u2="&#x162;" k="90" />
    <hkern u1="&#x125;" u2="V" k="20" />
    <hkern u1="&#x125;" u2="T" k="90" />
    <hkern u1="&#x127;" u2="&#x164;" k="90" />
    <hkern u1="&#x127;" u2="&#x162;" k="90" />
    <hkern u1="&#x127;" u2="V" k="20" />
    <hkern u1="&#x127;" u2="T" k="90" />
    <hkern u1="&#x136;" u2="&#x2026;" k="-60" />
    <hkern u1="&#x136;" u2="&#x2014;" k="10" />
    <hkern u1="&#x136;" u2="&#x2013;" k="10" />
    <hkern u1="&#x136;" u2="&#xef;" k="-20" />
    <hkern u1="&#x136;" u2="_" k="-70" />
    <hkern u1="&#x136;" u2="&#x2e;" k="-60" />
    <hkern u1="&#x136;" u2="&#x2d;" k="10" />
    <hkern u1="&#x136;" u2="&#x2c;" k="-60" />
    <hkern u1="&#x137;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x137;" u2="&#x2014;" k="45" />
    <hkern u1="&#x137;" u2="&#x2013;" k="45" />
    <hkern u1="&#x137;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x137;" u2="&#x1fd;" k="-7" />
    <hkern u1="&#x137;" u2="&#x1fb;" k="-7" />
    <hkern u1="&#x137;" u2="&#x153;" k="10" />
    <hkern u1="&#x137;" u2="&#x151;" k="10" />
    <hkern u1="&#x137;" u2="&#x14f;" k="10" />
    <hkern u1="&#x137;" u2="&#x14d;" k="10" />
    <hkern u1="&#x137;" u2="&#x123;" k="10" />
    <hkern u1="&#x137;" u2="&#x121;" k="10" />
    <hkern u1="&#x137;" u2="&#x11f;" k="10" />
    <hkern u1="&#x137;" u2="&#x11d;" k="10" />
    <hkern u1="&#x137;" u2="&#x11b;" k="10" />
    <hkern u1="&#x137;" u2="&#x119;" k="10" />
    <hkern u1="&#x137;" u2="&#x117;" k="10" />
    <hkern u1="&#x137;" u2="&#x115;" k="10" />
    <hkern u1="&#x137;" u2="&#x113;" k="10" />
    <hkern u1="&#x137;" u2="&#x111;" k="10" />
    <hkern u1="&#x137;" u2="&#x10f;" k="10" />
    <hkern u1="&#x137;" u2="&#x10d;" k="10" />
    <hkern u1="&#x137;" u2="&#x10b;" k="10" />
    <hkern u1="&#x137;" u2="&#x109;" k="10" />
    <hkern u1="&#x137;" u2="&#x107;" k="10" />
    <hkern u1="&#x137;" u2="&#x105;" k="-7" />
    <hkern u1="&#x137;" u2="&#x103;" k="-7" />
    <hkern u1="&#x137;" u2="&#x101;" k="-7" />
    <hkern u1="&#x137;" u2="&#xf8;" k="10" />
    <hkern u1="&#x137;" u2="&#xf6;" k="10" />
    <hkern u1="&#x137;" u2="&#xf5;" k="10" />
    <hkern u1="&#x137;" u2="&#xf4;" k="10" />
    <hkern u1="&#x137;" u2="&#xf3;" k="10" />
    <hkern u1="&#x137;" u2="&#xf2;" k="10" />
    <hkern u1="&#x137;" u2="&#xf0;" k="10" />
    <hkern u1="&#x137;" u2="&#xeb;" k="10" />
    <hkern u1="&#x137;" u2="&#xea;" k="10" />
    <hkern u1="&#x137;" u2="&#xe9;" k="10" />
    <hkern u1="&#x137;" u2="&#xe8;" k="10" />
    <hkern u1="&#x137;" u2="&#xe7;" k="10" />
    <hkern u1="&#x137;" u2="&#xe6;" k="-7" />
    <hkern u1="&#x137;" u2="&#xe5;" k="-7" />
    <hkern u1="&#x137;" u2="&#xe4;" k="-7" />
    <hkern u1="&#x137;" u2="&#xe3;" k="-7" />
    <hkern u1="&#x137;" u2="&#xe2;" k="-7" />
    <hkern u1="&#x137;" u2="&#xe1;" k="-7" />
    <hkern u1="&#x137;" u2="&#xe0;" k="-7" />
    <hkern u1="&#x137;" u2="q" k="10" />
    <hkern u1="&#x137;" u2="o" k="10" />
    <hkern u1="&#x137;" u2="g" k="10" />
    <hkern u1="&#x137;" u2="e" k="10" />
    <hkern u1="&#x137;" u2="d" k="10" />
    <hkern u1="&#x137;" u2="c" k="10" />
    <hkern u1="&#x137;" u2="a" k="-7" />
    <hkern u1="&#x137;" u2="_" k="-30" />
    <hkern u1="&#x137;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x137;" u2="&#x2d;" k="45" />
    <hkern u1="&#x137;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x139;" u2="&#x2122;" k="65" />
    <hkern u1="&#x139;" u2="&#x2026;" k="-60" />
    <hkern u1="&#x139;" u2="&#x201d;" k="110" />
    <hkern u1="&#x139;" u2="&#x201c;" k="110" />
    <hkern u1="&#x139;" u2="&#x2019;" k="110" />
    <hkern u1="&#x139;" u2="&#x2018;" k="110" />
    <hkern u1="&#x139;" u2="&#x2014;" k="100" />
    <hkern u1="&#x139;" u2="&#x2013;" k="100" />
    <hkern u1="&#x139;" u2="&#x1ef2;" k="110" />
    <hkern u1="&#x139;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e84;" k="45" />
    <hkern u1="&#x139;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e82;" k="45" />
    <hkern u1="&#x139;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e80;" k="45" />
    <hkern u1="&#x139;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x139;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x139;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x139;" u2="&#x178;" k="110" />
    <hkern u1="&#x139;" u2="&#x176;" k="110" />
    <hkern u1="&#x139;" u2="&#x175;" k="30" />
    <hkern u1="&#x139;" u2="&#x174;" k="45" />
    <hkern u1="&#x139;" u2="&#x164;" k="75" />
    <hkern u1="&#x139;" u2="&#x162;" k="75" />
    <hkern u1="&#x139;" u2="&#x152;" k="20" />
    <hkern u1="&#x139;" u2="&#x150;" k="20" />
    <hkern u1="&#x139;" u2="&#x14e;" k="20" />
    <hkern u1="&#x139;" u2="&#x14c;" k="20" />
    <hkern u1="&#x139;" u2="&#x122;" k="20" />
    <hkern u1="&#x139;" u2="&#x120;" k="20" />
    <hkern u1="&#x139;" u2="&#x11e;" k="20" />
    <hkern u1="&#x139;" u2="&#x11c;" k="20" />
    <hkern u1="&#x139;" u2="&#x10c;" k="20" />
    <hkern u1="&#x139;" u2="&#x10a;" k="20" />
    <hkern u1="&#x139;" u2="&#x108;" k="20" />
    <hkern u1="&#x139;" u2="&#x106;" k="20" />
    <hkern u1="&#x139;" u2="&#x105;" k="-30" />
    <hkern u1="&#x139;" u2="&#x103;" k="-30" />
    <hkern u1="&#x139;" u2="&#x101;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x139;" u2="&#xdd;" k="110" />
    <hkern u1="&#x139;" u2="&#xd8;" k="20" />
    <hkern u1="&#x139;" u2="&#xd6;" k="20" />
    <hkern u1="&#x139;" u2="&#xd5;" k="20" />
    <hkern u1="&#x139;" u2="&#xd4;" k="20" />
    <hkern u1="&#x139;" u2="&#xd3;" k="20" />
    <hkern u1="&#x139;" u2="&#xd2;" k="20" />
    <hkern u1="&#x139;" u2="&#xc7;" k="20" />
    <hkern u1="&#x139;" u2="&#xa9;" k="20" />
    <hkern u1="&#x139;" u2="w" k="30" />
    <hkern u1="&#x139;" u2="v" k="40" />
    <hkern u1="&#x139;" u2="a" k="-30" />
    <hkern u1="&#x139;" u2="_" k="-70" />
    <hkern u1="&#x139;" u2="Y" k="110" />
    <hkern u1="&#x139;" u2="W" k="45" />
    <hkern u1="&#x139;" u2="V" k="90" />
    <hkern u1="&#x139;" u2="T" k="75" />
    <hkern u1="&#x139;" u2="Q" k="20" />
    <hkern u1="&#x139;" u2="O" k="20" />
    <hkern u1="&#x139;" u2="G" k="20" />
    <hkern u1="&#x139;" u2="C" k="20" />
    <hkern u1="&#x139;" u2="&#x2e;" k="-60" />
    <hkern u1="&#x139;" u2="&#x2d;" k="100" />
    <hkern u1="&#x139;" u2="&#x2c;" k="-60" />
    <hkern u1="&#x139;" u2="&#x2a;" k="80" />
    <hkern u1="&#x139;" u2="&#x27;" k="125" />
    <hkern u1="&#x139;" u2="&#x22;" k="125" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="65" />
    <hkern u1="&#x13b;" u2="&#x2026;" k="-60" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="110" />
    <hkern u1="&#x13b;" u2="&#x201c;" k="110" />
    <hkern u1="&#x13b;" u2="&#x2019;" k="110" />
    <hkern u1="&#x13b;" u2="&#x2018;" k="110" />
    <hkern u1="&#x13b;" u2="&#x2014;" k="100" />
    <hkern u1="&#x13b;" u2="&#x2013;" k="100" />
    <hkern u1="&#x13b;" u2="&#x1ef2;" k="110" />
    <hkern u1="&#x13b;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e84;" k="45" />
    <hkern u1="&#x13b;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e82;" k="45" />
    <hkern u1="&#x13b;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e80;" k="45" />
    <hkern u1="&#x13b;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x13b;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x178;" k="110" />
    <hkern u1="&#x13b;" u2="&#x176;" k="110" />
    <hkern u1="&#x13b;" u2="&#x175;" k="30" />
    <hkern u1="&#x13b;" u2="&#x174;" k="45" />
    <hkern u1="&#x13b;" u2="&#x164;" k="75" />
    <hkern u1="&#x13b;" u2="&#x162;" k="75" />
    <hkern u1="&#x13b;" u2="&#x152;" k="20" />
    <hkern u1="&#x13b;" u2="&#x150;" k="20" />
    <hkern u1="&#x13b;" u2="&#x14e;" k="20" />
    <hkern u1="&#x13b;" u2="&#x14c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x122;" k="20" />
    <hkern u1="&#x13b;" u2="&#x120;" k="20" />
    <hkern u1="&#x13b;" u2="&#x11e;" k="20" />
    <hkern u1="&#x13b;" u2="&#x11c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x10c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x10a;" k="20" />
    <hkern u1="&#x13b;" u2="&#x108;" k="20" />
    <hkern u1="&#x13b;" u2="&#x106;" k="20" />
    <hkern u1="&#x13b;" u2="&#x105;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x103;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x101;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xdd;" k="110" />
    <hkern u1="&#x13b;" u2="&#xd8;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd6;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd5;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd4;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd3;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd2;" k="20" />
    <hkern u1="&#x13b;" u2="&#xc7;" k="20" />
    <hkern u1="&#x13b;" u2="&#xa9;" k="20" />
    <hkern u1="&#x13b;" u2="w" k="30" />
    <hkern u1="&#x13b;" u2="v" k="40" />
    <hkern u1="&#x13b;" u2="a" k="-30" />
    <hkern u1="&#x13b;" u2="_" k="-70" />
    <hkern u1="&#x13b;" u2="Y" k="110" />
    <hkern u1="&#x13b;" u2="W" k="45" />
    <hkern u1="&#x13b;" u2="V" k="90" />
    <hkern u1="&#x13b;" u2="T" k="75" />
    <hkern u1="&#x13b;" u2="Q" k="20" />
    <hkern u1="&#x13b;" u2="O" k="20" />
    <hkern u1="&#x13b;" u2="G" k="20" />
    <hkern u1="&#x13b;" u2="C" k="20" />
    <hkern u1="&#x13b;" u2="&#x2e;" k="-60" />
    <hkern u1="&#x13b;" u2="&#x2d;" k="100" />
    <hkern u1="&#x13b;" u2="&#x2c;" k="-60" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="80" />
    <hkern u1="&#x13b;" u2="&#x27;" k="125" />
    <hkern u1="&#x13b;" u2="&#x22;" k="125" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="65" />
    <hkern u1="&#x13d;" u2="&#x2026;" k="-60" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="110" />
    <hkern u1="&#x13d;" u2="&#x201c;" k="110" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="110" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="110" />
    <hkern u1="&#x13d;" u2="&#x2014;" k="100" />
    <hkern u1="&#x13d;" u2="&#x2013;" k="100" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="110" />
    <hkern u1="&#x13d;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="45" />
    <hkern u1="&#x13d;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="45" />
    <hkern u1="&#x13d;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="45" />
    <hkern u1="&#x13d;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x13d;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x178;" k="110" />
    <hkern u1="&#x13d;" u2="&#x176;" k="110" />
    <hkern u1="&#x13d;" u2="&#x175;" k="30" />
    <hkern u1="&#x13d;" u2="&#x174;" k="45" />
    <hkern u1="&#x13d;" u2="&#x164;" k="75" />
    <hkern u1="&#x13d;" u2="&#x162;" k="75" />
    <hkern u1="&#x13d;" u2="&#x152;" k="20" />
    <hkern u1="&#x13d;" u2="&#x150;" k="20" />
    <hkern u1="&#x13d;" u2="&#x14e;" k="20" />
    <hkern u1="&#x13d;" u2="&#x14c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x122;" k="20" />
    <hkern u1="&#x13d;" u2="&#x120;" k="20" />
    <hkern u1="&#x13d;" u2="&#x11e;" k="20" />
    <hkern u1="&#x13d;" u2="&#x11c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x10c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x10a;" k="20" />
    <hkern u1="&#x13d;" u2="&#x108;" k="20" />
    <hkern u1="&#x13d;" u2="&#x106;" k="20" />
    <hkern u1="&#x13d;" u2="&#x105;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x103;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x101;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="110" />
    <hkern u1="&#x13d;" u2="&#xd8;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd6;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd5;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd4;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd3;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd2;" k="20" />
    <hkern u1="&#x13d;" u2="&#xc7;" k="20" />
    <hkern u1="&#x13d;" u2="&#xa9;" k="20" />
    <hkern u1="&#x13d;" u2="w" k="30" />
    <hkern u1="&#x13d;" u2="v" k="40" />
    <hkern u1="&#x13d;" u2="a" k="-30" />
    <hkern u1="&#x13d;" u2="_" k="-70" />
    <hkern u1="&#x13d;" u2="Y" k="110" />
    <hkern u1="&#x13d;" u2="W" k="45" />
    <hkern u1="&#x13d;" u2="V" k="90" />
    <hkern u1="&#x13d;" u2="T" k="75" />
    <hkern u1="&#x13d;" u2="Q" k="20" />
    <hkern u1="&#x13d;" u2="O" k="20" />
    <hkern u1="&#x13d;" u2="G" k="20" />
    <hkern u1="&#x13d;" u2="C" k="20" />
    <hkern u1="&#x13d;" u2="&#x2e;" k="-60" />
    <hkern u1="&#x13d;" u2="&#x2d;" k="100" />
    <hkern u1="&#x13d;" u2="&#x2c;" k="-60" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="80" />
    <hkern u1="&#x13d;" u2="&#x27;" k="125" />
    <hkern u1="&#x13d;" u2="&#x22;" k="125" />
    <hkern u1="&#x141;" u2="&#x2122;" k="65" />
    <hkern u1="&#x141;" u2="&#x2026;" k="-60" />
    <hkern u1="&#x141;" u2="&#x201d;" k="110" />
    <hkern u1="&#x141;" u2="&#x201c;" k="110" />
    <hkern u1="&#x141;" u2="&#x2019;" k="110" />
    <hkern u1="&#x141;" u2="&#x2018;" k="110" />
    <hkern u1="&#x141;" u2="&#x2014;" k="100" />
    <hkern u1="&#x141;" u2="&#x2013;" k="100" />
    <hkern u1="&#x141;" u2="&#x1ef2;" k="110" />
    <hkern u1="&#x141;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e84;" k="45" />
    <hkern u1="&#x141;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e82;" k="45" />
    <hkern u1="&#x141;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e80;" k="45" />
    <hkern u1="&#x141;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x141;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x141;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x141;" u2="&#x178;" k="110" />
    <hkern u1="&#x141;" u2="&#x176;" k="110" />
    <hkern u1="&#x141;" u2="&#x175;" k="30" />
    <hkern u1="&#x141;" u2="&#x174;" k="45" />
    <hkern u1="&#x141;" u2="&#x164;" k="75" />
    <hkern u1="&#x141;" u2="&#x162;" k="75" />
    <hkern u1="&#x141;" u2="&#x152;" k="20" />
    <hkern u1="&#x141;" u2="&#x150;" k="20" />
    <hkern u1="&#x141;" u2="&#x14e;" k="20" />
    <hkern u1="&#x141;" u2="&#x14c;" k="20" />
    <hkern u1="&#x141;" u2="&#x122;" k="20" />
    <hkern u1="&#x141;" u2="&#x120;" k="20" />
    <hkern u1="&#x141;" u2="&#x11e;" k="20" />
    <hkern u1="&#x141;" u2="&#x11c;" k="20" />
    <hkern u1="&#x141;" u2="&#x10c;" k="20" />
    <hkern u1="&#x141;" u2="&#x10a;" k="20" />
    <hkern u1="&#x141;" u2="&#x108;" k="20" />
    <hkern u1="&#x141;" u2="&#x106;" k="20" />
    <hkern u1="&#x141;" u2="&#x105;" k="-30" />
    <hkern u1="&#x141;" u2="&#x103;" k="-30" />
    <hkern u1="&#x141;" u2="&#x101;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x141;" u2="&#xdd;" k="110" />
    <hkern u1="&#x141;" u2="&#xd8;" k="20" />
    <hkern u1="&#x141;" u2="&#xd6;" k="20" />
    <hkern u1="&#x141;" u2="&#xd5;" k="20" />
    <hkern u1="&#x141;" u2="&#xd4;" k="20" />
    <hkern u1="&#x141;" u2="&#xd3;" k="20" />
    <hkern u1="&#x141;" u2="&#xd2;" k="20" />
    <hkern u1="&#x141;" u2="&#xc7;" k="20" />
    <hkern u1="&#x141;" u2="&#xa9;" k="20" />
    <hkern u1="&#x141;" u2="w" k="30" />
    <hkern u1="&#x141;" u2="v" k="40" />
    <hkern u1="&#x141;" u2="a" k="-30" />
    <hkern u1="&#x141;" u2="_" k="-70" />
    <hkern u1="&#x141;" u2="Y" k="110" />
    <hkern u1="&#x141;" u2="W" k="45" />
    <hkern u1="&#x141;" u2="V" k="90" />
    <hkern u1="&#x141;" u2="T" k="75" />
    <hkern u1="&#x141;" u2="Q" k="20" />
    <hkern u1="&#x141;" u2="O" k="20" />
    <hkern u1="&#x141;" u2="G" k="20" />
    <hkern u1="&#x141;" u2="C" k="20" />
    <hkern u1="&#x141;" u2="&#x2e;" k="-60" />
    <hkern u1="&#x141;" u2="&#x2d;" k="100" />
    <hkern u1="&#x141;" u2="&#x2c;" k="-60" />
    <hkern u1="&#x141;" u2="&#x2a;" k="80" />
    <hkern u1="&#x141;" u2="&#x27;" k="125" />
    <hkern u1="&#x141;" u2="&#x22;" k="125" />
    <hkern u1="&#x144;" u2="&#x164;" k="90" />
    <hkern u1="&#x144;" u2="&#x162;" k="90" />
    <hkern u1="&#x144;" u2="V" k="20" />
    <hkern u1="&#x144;" u2="T" k="90" />
    <hkern u1="&#x146;" u2="&#x164;" k="90" />
    <hkern u1="&#x146;" u2="&#x162;" k="90" />
    <hkern u1="&#x146;" u2="V" k="20" />
    <hkern u1="&#x146;" u2="T" k="90" />
    <hkern u1="&#x148;" u2="&#x164;" k="90" />
    <hkern u1="&#x148;" u2="&#x162;" k="90" />
    <hkern u1="&#x148;" u2="V" k="20" />
    <hkern u1="&#x148;" u2="T" k="90" />
    <hkern u1="&#x14b;" u2="&#x164;" k="90" />
    <hkern u1="&#x14b;" u2="&#x162;" k="90" />
    <hkern u1="&#x14b;" u2="V" k="20" />
    <hkern u1="&#x14b;" u2="T" k="90" />
    <hkern u1="&#x14c;" u2="&#x2026;" k="45" />
    <hkern u1="&#x14c;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x218;" k="5" />
    <hkern u1="&#x14c;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x164;" k="15" />
    <hkern u1="&#x14c;" u2="&#x162;" k="15" />
    <hkern u1="&#x14c;" u2="&#x160;" k="5" />
    <hkern u1="&#x14c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x14c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x14c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x14c;" u2="&#x152;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x150;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x134;" k="-20" />
    <hkern u1="&#x14c;" u2="&#x122;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x120;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x108;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x106;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x14c;" u2="X" k="10" />
    <hkern u1="&#x14c;" u2="V" k="15" />
    <hkern u1="&#x14c;" u2="T" k="15" />
    <hkern u1="&#x14c;" u2="S" k="5" />
    <hkern u1="&#x14c;" u2="Q" k="-5" />
    <hkern u1="&#x14c;" u2="O" k="-5" />
    <hkern u1="&#x14c;" u2="J" k="-20" />
    <hkern u1="&#x14c;" u2="G" k="-5" />
    <hkern u1="&#x14c;" u2="C" k="-5" />
    <hkern u1="&#x14c;" u2="&#x2e;" k="45" />
    <hkern u1="&#x14c;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x2c;" k="45" />
    <hkern u1="&#x14d;" u2="&#x2026;" k="20" />
    <hkern u1="&#x14d;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x14d;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x14d;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x14d;" u2="&#x174;" k="10" />
    <hkern u1="&#x14d;" u2="&#x164;" k="85" />
    <hkern u1="&#x14d;" u2="&#x162;" k="85" />
    <hkern u1="&#x14d;" u2="z" k="20" />
    <hkern u1="&#x14d;" u2="x" k="10" />
    <hkern u1="&#x14d;" u2="W" k="10" />
    <hkern u1="&#x14d;" u2="V" k="20" />
    <hkern u1="&#x14d;" u2="T" k="85" />
    <hkern u1="&#x14d;" u2="&#x2e;" k="20" />
    <hkern u1="&#x14d;" u2="&#x2c;" k="20" />
    <hkern u1="&#x14e;" u2="&#x2026;" k="45" />
    <hkern u1="&#x14e;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x218;" k="5" />
    <hkern u1="&#x14e;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x164;" k="15" />
    <hkern u1="&#x14e;" u2="&#x162;" k="15" />
    <hkern u1="&#x14e;" u2="&#x160;" k="5" />
    <hkern u1="&#x14e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x14e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x14e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x14e;" u2="&#x152;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x150;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x134;" k="-20" />
    <hkern u1="&#x14e;" u2="&#x122;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x120;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x108;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x106;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x14e;" u2="X" k="10" />
    <hkern u1="&#x14e;" u2="V" k="15" />
    <hkern u1="&#x14e;" u2="T" k="15" />
    <hkern u1="&#x14e;" u2="S" k="5" />
    <hkern u1="&#x14e;" u2="Q" k="-5" />
    <hkern u1="&#x14e;" u2="O" k="-5" />
    <hkern u1="&#x14e;" u2="J" k="-20" />
    <hkern u1="&#x14e;" u2="G" k="-5" />
    <hkern u1="&#x14e;" u2="C" k="-5" />
    <hkern u1="&#x14e;" u2="&#x2e;" k="45" />
    <hkern u1="&#x14e;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x2c;" k="45" />
    <hkern u1="&#x14f;" u2="&#x2026;" k="20" />
    <hkern u1="&#x14f;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x14f;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x14f;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x14f;" u2="&#x174;" k="10" />
    <hkern u1="&#x14f;" u2="&#x164;" k="85" />
    <hkern u1="&#x14f;" u2="&#x162;" k="85" />
    <hkern u1="&#x14f;" u2="z" k="20" />
    <hkern u1="&#x14f;" u2="x" k="10" />
    <hkern u1="&#x14f;" u2="W" k="10" />
    <hkern u1="&#x14f;" u2="V" k="20" />
    <hkern u1="&#x14f;" u2="T" k="85" />
    <hkern u1="&#x14f;" u2="&#x2e;" k="20" />
    <hkern u1="&#x14f;" u2="&#x2c;" k="20" />
    <hkern u1="&#x150;" u2="&#x2026;" k="45" />
    <hkern u1="&#x150;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x150;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x150;" u2="&#x218;" k="5" />
    <hkern u1="&#x150;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x150;" u2="&#x164;" k="15" />
    <hkern u1="&#x150;" u2="&#x162;" k="15" />
    <hkern u1="&#x150;" u2="&#x160;" k="5" />
    <hkern u1="&#x150;" u2="&#x15e;" k="5" />
    <hkern u1="&#x150;" u2="&#x15c;" k="5" />
    <hkern u1="&#x150;" u2="&#x15a;" k="5" />
    <hkern u1="&#x150;" u2="&#x152;" k="-5" />
    <hkern u1="&#x150;" u2="&#x150;" k="-5" />
    <hkern u1="&#x150;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x150;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x134;" k="-20" />
    <hkern u1="&#x150;" u2="&#x122;" k="-5" />
    <hkern u1="&#x150;" u2="&#x120;" k="-5" />
    <hkern u1="&#x150;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x150;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x150;" u2="&#x108;" k="-5" />
    <hkern u1="&#x150;" u2="&#x106;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x150;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x150;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x150;" u2="X" k="10" />
    <hkern u1="&#x150;" u2="V" k="15" />
    <hkern u1="&#x150;" u2="T" k="15" />
    <hkern u1="&#x150;" u2="S" k="5" />
    <hkern u1="&#x150;" u2="Q" k="-5" />
    <hkern u1="&#x150;" u2="O" k="-5" />
    <hkern u1="&#x150;" u2="J" k="-20" />
    <hkern u1="&#x150;" u2="G" k="-5" />
    <hkern u1="&#x150;" u2="C" k="-5" />
    <hkern u1="&#x150;" u2="&#x2e;" k="45" />
    <hkern u1="&#x150;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x150;" u2="&#x2c;" k="45" />
    <hkern u1="&#x151;" u2="&#x2026;" k="20" />
    <hkern u1="&#x151;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x151;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x151;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x151;" u2="&#x174;" k="10" />
    <hkern u1="&#x151;" u2="&#x164;" k="85" />
    <hkern u1="&#x151;" u2="&#x162;" k="85" />
    <hkern u1="&#x151;" u2="z" k="20" />
    <hkern u1="&#x151;" u2="x" k="10" />
    <hkern u1="&#x151;" u2="W" k="10" />
    <hkern u1="&#x151;" u2="V" k="20" />
    <hkern u1="&#x151;" u2="T" k="85" />
    <hkern u1="&#x151;" u2="&#x2e;" k="20" />
    <hkern u1="&#x151;" u2="&#x2c;" k="20" />
    <hkern u1="&#x152;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x152;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x152;" u2="&#x152;" k="7" />
    <hkern u1="&#x152;" u2="&#x150;" k="7" />
    <hkern u1="&#x152;" u2="&#x14e;" k="7" />
    <hkern u1="&#x152;" u2="&#x14c;" k="7" />
    <hkern u1="&#x152;" u2="&#x122;" k="7" />
    <hkern u1="&#x152;" u2="&#x120;" k="7" />
    <hkern u1="&#x152;" u2="&#x11e;" k="7" />
    <hkern u1="&#x152;" u2="&#x11c;" k="7" />
    <hkern u1="&#x152;" u2="&#x10c;" k="7" />
    <hkern u1="&#x152;" u2="&#x10a;" k="7" />
    <hkern u1="&#x152;" u2="&#x108;" k="7" />
    <hkern u1="&#x152;" u2="&#x106;" k="7" />
    <hkern u1="&#x152;" u2="&#xd8;" k="7" />
    <hkern u1="&#x152;" u2="&#xd6;" k="7" />
    <hkern u1="&#x152;" u2="&#xd5;" k="7" />
    <hkern u1="&#x152;" u2="&#xd4;" k="7" />
    <hkern u1="&#x152;" u2="&#xd3;" k="7" />
    <hkern u1="&#x152;" u2="&#xd2;" k="7" />
    <hkern u1="&#x152;" u2="&#xc7;" k="7" />
    <hkern u1="&#x152;" u2="&#xa9;" k="7" />
    <hkern u1="&#x152;" u2="Q" k="7" />
    <hkern u1="&#x152;" u2="O" k="7" />
    <hkern u1="&#x152;" u2="G" k="7" />
    <hkern u1="&#x152;" u2="C" k="7" />
    <hkern u1="&#x152;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x152;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x153;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x153;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x153;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x153;" u2="&#x219;" k="-5" />
    <hkern u1="&#x153;" u2="&#x174;" k="10" />
    <hkern u1="&#x153;" u2="&#x164;" k="70" />
    <hkern u1="&#x153;" u2="&#x162;" k="70" />
    <hkern u1="&#x153;" u2="&#x161;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x153;" u2="s" k="-5" />
    <hkern u1="&#x153;" u2="W" k="10" />
    <hkern u1="&#x153;" u2="V" k="20" />
    <hkern u1="&#x153;" u2="T" k="70" />
    <hkern u1="&#x154;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x154;" u2="&#x164;" k="20" />
    <hkern u1="&#x154;" u2="&#x162;" k="20" />
    <hkern u1="&#x154;" u2="T" k="20" />
    <hkern u1="&#x154;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x154;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x155;" u2="&#x2026;" k="115" />
    <hkern u1="&#x155;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x155;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x155;" u2="&#x2014;" k="40" />
    <hkern u1="&#x155;" u2="&#x2013;" k="40" />
    <hkern u1="&#x155;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x155;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x155;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x155;" u2="&#x167;" k="-20" />
    <hkern u1="&#x155;" u2="&#x165;" k="-20" />
    <hkern u1="&#x155;" u2="&#x164;" k="30" />
    <hkern u1="&#x155;" u2="&#x163;" k="-20" />
    <hkern u1="&#x155;" u2="&#x162;" k="30" />
    <hkern u1="&#x155;" u2="&#x153;" k="20" />
    <hkern u1="&#x155;" u2="&#x151;" k="20" />
    <hkern u1="&#x155;" u2="&#x14f;" k="20" />
    <hkern u1="&#x155;" u2="&#x14d;" k="20" />
    <hkern u1="&#x155;" u2="&#x123;" k="20" />
    <hkern u1="&#x155;" u2="&#x121;" k="20" />
    <hkern u1="&#x155;" u2="&#x11f;" k="20" />
    <hkern u1="&#x155;" u2="&#x11d;" k="20" />
    <hkern u1="&#x155;" u2="&#x11b;" k="20" />
    <hkern u1="&#x155;" u2="&#x119;" k="20" />
    <hkern u1="&#x155;" u2="&#x117;" k="20" />
    <hkern u1="&#x155;" u2="&#x115;" k="20" />
    <hkern u1="&#x155;" u2="&#x113;" k="20" />
    <hkern u1="&#x155;" u2="&#x111;" k="20" />
    <hkern u1="&#x155;" u2="&#x10f;" k="20" />
    <hkern u1="&#x155;" u2="&#x10d;" k="20" />
    <hkern u1="&#x155;" u2="&#x10b;" k="20" />
    <hkern u1="&#x155;" u2="&#x109;" k="20" />
    <hkern u1="&#x155;" u2="&#x107;" k="20" />
    <hkern u1="&#x155;" u2="&#x105;" k="10" />
    <hkern u1="&#x155;" u2="&#x103;" k="10" />
    <hkern u1="&#x155;" u2="&#x101;" k="10" />
    <hkern u1="&#x155;" u2="&#xf8;" k="20" />
    <hkern u1="&#x155;" u2="&#xf6;" k="20" />
    <hkern u1="&#x155;" u2="&#xf5;" k="20" />
    <hkern u1="&#x155;" u2="&#xf4;" k="20" />
    <hkern u1="&#x155;" u2="&#xf3;" k="20" />
    <hkern u1="&#x155;" u2="&#xf2;" k="20" />
    <hkern u1="&#x155;" u2="&#xf0;" k="20" />
    <hkern u1="&#x155;" u2="&#xeb;" k="20" />
    <hkern u1="&#x155;" u2="&#xea;" k="20" />
    <hkern u1="&#x155;" u2="&#xe9;" k="20" />
    <hkern u1="&#x155;" u2="&#xe8;" k="20" />
    <hkern u1="&#x155;" u2="&#xe7;" k="20" />
    <hkern u1="&#x155;" u2="&#xe6;" k="10" />
    <hkern u1="&#x155;" u2="&#xe5;" k="10" />
    <hkern u1="&#x155;" u2="&#xe4;" k="10" />
    <hkern u1="&#x155;" u2="&#xe3;" k="10" />
    <hkern u1="&#x155;" u2="&#xe2;" k="10" />
    <hkern u1="&#x155;" u2="&#xe1;" k="10" />
    <hkern u1="&#x155;" u2="&#xe0;" k="10" />
    <hkern u1="&#x155;" u2="t" k="-20" />
    <hkern u1="&#x155;" u2="q" k="20" />
    <hkern u1="&#x155;" u2="o" k="20" />
    <hkern u1="&#x155;" u2="g" k="20" />
    <hkern u1="&#x155;" u2="e" k="20" />
    <hkern u1="&#x155;" u2="d" k="20" />
    <hkern u1="&#x155;" u2="c" k="20" />
    <hkern u1="&#x155;" u2="a" k="10" />
    <hkern u1="&#x155;" u2="T" k="30" />
    <hkern u1="&#x155;" u2="&#x2f;" k="50" />
    <hkern u1="&#x155;" u2="&#x2e;" k="115" />
    <hkern u1="&#x155;" u2="&#x2d;" k="40" />
    <hkern u1="&#x155;" u2="&#x2c;" k="115" />
    <hkern u1="&#x156;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x156;" u2="&#x164;" k="20" />
    <hkern u1="&#x156;" u2="&#x162;" k="20" />
    <hkern u1="&#x156;" u2="T" k="20" />
    <hkern u1="&#x156;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x156;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x157;" u2="&#x2026;" k="115" />
    <hkern u1="&#x157;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x157;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x157;" u2="&#x2014;" k="40" />
    <hkern u1="&#x157;" u2="&#x2013;" k="40" />
    <hkern u1="&#x157;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x157;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x157;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x157;" u2="&#x167;" k="-20" />
    <hkern u1="&#x157;" u2="&#x165;" k="-20" />
    <hkern u1="&#x157;" u2="&#x164;" k="30" />
    <hkern u1="&#x157;" u2="&#x163;" k="-20" />
    <hkern u1="&#x157;" u2="&#x162;" k="30" />
    <hkern u1="&#x157;" u2="&#x153;" k="20" />
    <hkern u1="&#x157;" u2="&#x151;" k="20" />
    <hkern u1="&#x157;" u2="&#x14f;" k="20" />
    <hkern u1="&#x157;" u2="&#x14d;" k="20" />
    <hkern u1="&#x157;" u2="&#x123;" k="20" />
    <hkern u1="&#x157;" u2="&#x121;" k="20" />
    <hkern u1="&#x157;" u2="&#x11f;" k="20" />
    <hkern u1="&#x157;" u2="&#x11d;" k="20" />
    <hkern u1="&#x157;" u2="&#x11b;" k="20" />
    <hkern u1="&#x157;" u2="&#x119;" k="20" />
    <hkern u1="&#x157;" u2="&#x117;" k="20" />
    <hkern u1="&#x157;" u2="&#x115;" k="20" />
    <hkern u1="&#x157;" u2="&#x113;" k="20" />
    <hkern u1="&#x157;" u2="&#x111;" k="20" />
    <hkern u1="&#x157;" u2="&#x10f;" k="20" />
    <hkern u1="&#x157;" u2="&#x10d;" k="20" />
    <hkern u1="&#x157;" u2="&#x10b;" k="20" />
    <hkern u1="&#x157;" u2="&#x109;" k="20" />
    <hkern u1="&#x157;" u2="&#x107;" k="20" />
    <hkern u1="&#x157;" u2="&#x105;" k="10" />
    <hkern u1="&#x157;" u2="&#x103;" k="10" />
    <hkern u1="&#x157;" u2="&#x101;" k="10" />
    <hkern u1="&#x157;" u2="&#xf8;" k="20" />
    <hkern u1="&#x157;" u2="&#xf6;" k="20" />
    <hkern u1="&#x157;" u2="&#xf5;" k="20" />
    <hkern u1="&#x157;" u2="&#xf4;" k="20" />
    <hkern u1="&#x157;" u2="&#xf3;" k="20" />
    <hkern u1="&#x157;" u2="&#xf2;" k="20" />
    <hkern u1="&#x157;" u2="&#xf0;" k="20" />
    <hkern u1="&#x157;" u2="&#xeb;" k="20" />
    <hkern u1="&#x157;" u2="&#xea;" k="20" />
    <hkern u1="&#x157;" u2="&#xe9;" k="20" />
    <hkern u1="&#x157;" u2="&#xe8;" k="20" />
    <hkern u1="&#x157;" u2="&#xe7;" k="20" />
    <hkern u1="&#x157;" u2="&#xe6;" k="10" />
    <hkern u1="&#x157;" u2="&#xe5;" k="10" />
    <hkern u1="&#x157;" u2="&#xe4;" k="10" />
    <hkern u1="&#x157;" u2="&#xe3;" k="10" />
    <hkern u1="&#x157;" u2="&#xe2;" k="10" />
    <hkern u1="&#x157;" u2="&#xe1;" k="10" />
    <hkern u1="&#x157;" u2="&#xe0;" k="10" />
    <hkern u1="&#x157;" u2="t" k="-20" />
    <hkern u1="&#x157;" u2="q" k="20" />
    <hkern u1="&#x157;" u2="o" k="20" />
    <hkern u1="&#x157;" u2="g" k="20" />
    <hkern u1="&#x157;" u2="e" k="20" />
    <hkern u1="&#x157;" u2="d" k="20" />
    <hkern u1="&#x157;" u2="c" k="20" />
    <hkern u1="&#x157;" u2="a" k="10" />
    <hkern u1="&#x157;" u2="T" k="30" />
    <hkern u1="&#x157;" u2="&#x2f;" k="50" />
    <hkern u1="&#x157;" u2="&#x2e;" k="115" />
    <hkern u1="&#x157;" u2="&#x2d;" k="40" />
    <hkern u1="&#x157;" u2="&#x2c;" k="115" />
    <hkern u1="&#x158;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x158;" u2="&#x164;" k="20" />
    <hkern u1="&#x158;" u2="&#x162;" k="20" />
    <hkern u1="&#x158;" u2="T" k="20" />
    <hkern u1="&#x158;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x158;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x159;" u2="&#x2026;" k="115" />
    <hkern u1="&#x159;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x159;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x159;" u2="&#x2014;" k="40" />
    <hkern u1="&#x159;" u2="&#x2013;" k="40" />
    <hkern u1="&#x159;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x159;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x159;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x159;" u2="&#x167;" k="-20" />
    <hkern u1="&#x159;" u2="&#x165;" k="-20" />
    <hkern u1="&#x159;" u2="&#x164;" k="30" />
    <hkern u1="&#x159;" u2="&#x163;" k="-20" />
    <hkern u1="&#x159;" u2="&#x162;" k="30" />
    <hkern u1="&#x159;" u2="&#x153;" k="20" />
    <hkern u1="&#x159;" u2="&#x151;" k="20" />
    <hkern u1="&#x159;" u2="&#x14f;" k="20" />
    <hkern u1="&#x159;" u2="&#x14d;" k="20" />
    <hkern u1="&#x159;" u2="&#x123;" k="20" />
    <hkern u1="&#x159;" u2="&#x121;" k="20" />
    <hkern u1="&#x159;" u2="&#x11f;" k="20" />
    <hkern u1="&#x159;" u2="&#x11d;" k="20" />
    <hkern u1="&#x159;" u2="&#x11b;" k="20" />
    <hkern u1="&#x159;" u2="&#x119;" k="20" />
    <hkern u1="&#x159;" u2="&#x117;" k="20" />
    <hkern u1="&#x159;" u2="&#x115;" k="20" />
    <hkern u1="&#x159;" u2="&#x113;" k="20" />
    <hkern u1="&#x159;" u2="&#x111;" k="20" />
    <hkern u1="&#x159;" u2="&#x10f;" k="20" />
    <hkern u1="&#x159;" u2="&#x10d;" k="20" />
    <hkern u1="&#x159;" u2="&#x10b;" k="20" />
    <hkern u1="&#x159;" u2="&#x109;" k="20" />
    <hkern u1="&#x159;" u2="&#x107;" k="20" />
    <hkern u1="&#x159;" u2="&#x105;" k="10" />
    <hkern u1="&#x159;" u2="&#x103;" k="10" />
    <hkern u1="&#x159;" u2="&#x101;" k="10" />
    <hkern u1="&#x159;" u2="&#xf8;" k="20" />
    <hkern u1="&#x159;" u2="&#xf6;" k="20" />
    <hkern u1="&#x159;" u2="&#xf5;" k="20" />
    <hkern u1="&#x159;" u2="&#xf4;" k="20" />
    <hkern u1="&#x159;" u2="&#xf3;" k="20" />
    <hkern u1="&#x159;" u2="&#xf2;" k="20" />
    <hkern u1="&#x159;" u2="&#xf0;" k="20" />
    <hkern u1="&#x159;" u2="&#xeb;" k="20" />
    <hkern u1="&#x159;" u2="&#xea;" k="20" />
    <hkern u1="&#x159;" u2="&#xe9;" k="20" />
    <hkern u1="&#x159;" u2="&#xe8;" k="20" />
    <hkern u1="&#x159;" u2="&#xe7;" k="20" />
    <hkern u1="&#x159;" u2="&#xe6;" k="10" />
    <hkern u1="&#x159;" u2="&#xe5;" k="10" />
    <hkern u1="&#x159;" u2="&#xe4;" k="10" />
    <hkern u1="&#x159;" u2="&#xe3;" k="10" />
    <hkern u1="&#x159;" u2="&#xe2;" k="10" />
    <hkern u1="&#x159;" u2="&#xe1;" k="10" />
    <hkern u1="&#x159;" u2="&#xe0;" k="10" />
    <hkern u1="&#x159;" u2="t" k="-20" />
    <hkern u1="&#x159;" u2="q" k="20" />
    <hkern u1="&#x159;" u2="o" k="20" />
    <hkern u1="&#x159;" u2="g" k="20" />
    <hkern u1="&#x159;" u2="e" k="20" />
    <hkern u1="&#x159;" u2="d" k="20" />
    <hkern u1="&#x159;" u2="c" k="20" />
    <hkern u1="&#x159;" u2="a" k="10" />
    <hkern u1="&#x159;" u2="T" k="30" />
    <hkern u1="&#x159;" u2="&#x2f;" k="50" />
    <hkern u1="&#x159;" u2="&#x2e;" k="115" />
    <hkern u1="&#x159;" u2="&#x2d;" k="40" />
    <hkern u1="&#x159;" u2="&#x2c;" k="115" />
    <hkern u1="&#x15a;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x15a;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x15a;" u2="&#x218;" k="15" />
    <hkern u1="&#x15a;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x15a;" u2="&#x178;" k="40" />
    <hkern u1="&#x15a;" u2="&#x176;" k="40" />
    <hkern u1="&#x15a;" u2="&#x164;" k="20" />
    <hkern u1="&#x15a;" u2="&#x162;" k="20" />
    <hkern u1="&#x15a;" u2="&#x160;" k="15" />
    <hkern u1="&#x15a;" u2="&#x15e;" k="15" />
    <hkern u1="&#x15a;" u2="&#x15c;" k="15" />
    <hkern u1="&#x15a;" u2="&#x15a;" k="15" />
    <hkern u1="&#x15a;" u2="&#x152;" k="5" />
    <hkern u1="&#x15a;" u2="&#x150;" k="5" />
    <hkern u1="&#x15a;" u2="&#x14e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x14c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x122;" k="5" />
    <hkern u1="&#x15a;" u2="&#x120;" k="5" />
    <hkern u1="&#x15a;" u2="&#x11e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x11c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x10c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x10a;" k="5" />
    <hkern u1="&#x15a;" u2="&#x108;" k="5" />
    <hkern u1="&#x15a;" u2="&#x106;" k="5" />
    <hkern u1="&#x15a;" u2="&#xdd;" k="40" />
    <hkern u1="&#x15a;" u2="&#xd8;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd6;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd5;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd4;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd3;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd2;" k="5" />
    <hkern u1="&#x15a;" u2="&#xc7;" k="5" />
    <hkern u1="&#x15a;" u2="&#xa9;" k="5" />
    <hkern u1="&#x15a;" u2="Y" k="40" />
    <hkern u1="&#x15a;" u2="V" k="15" />
    <hkern u1="&#x15a;" u2="T" k="20" />
    <hkern u1="&#x15a;" u2="S" k="15" />
    <hkern u1="&#x15a;" u2="Q" k="5" />
    <hkern u1="&#x15a;" u2="O" k="5" />
    <hkern u1="&#x15a;" u2="G" k="5" />
    <hkern u1="&#x15a;" u2="C" k="5" />
    <hkern u1="&#x15a;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x15a;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x15b;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x15b;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x15b;" u2="&#x219;" k="15" />
    <hkern u1="&#x15b;" u2="&#x164;" k="70" />
    <hkern u1="&#x15b;" u2="&#x162;" k="70" />
    <hkern u1="&#x15b;" u2="&#x161;" k="15" />
    <hkern u1="&#x15b;" u2="&#x15f;" k="15" />
    <hkern u1="&#x15b;" u2="&#x15d;" k="15" />
    <hkern u1="&#x15b;" u2="&#x15b;" k="15" />
    <hkern u1="&#x15b;" u2="s" k="15" />
    <hkern u1="&#x15b;" u2="V" k="10" />
    <hkern u1="&#x15b;" u2="T" k="70" />
    <hkern u1="&#x15c;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x15c;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x15c;" u2="&#x218;" k="15" />
    <hkern u1="&#x15c;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x15c;" u2="&#x178;" k="40" />
    <hkern u1="&#x15c;" u2="&#x176;" k="40" />
    <hkern u1="&#x15c;" u2="&#x164;" k="20" />
    <hkern u1="&#x15c;" u2="&#x162;" k="20" />
    <hkern u1="&#x15c;" u2="&#x160;" k="15" />
    <hkern u1="&#x15c;" u2="&#x15e;" k="15" />
    <hkern u1="&#x15c;" u2="&#x15c;" k="15" />
    <hkern u1="&#x15c;" u2="&#x15a;" k="15" />
    <hkern u1="&#x15c;" u2="&#x152;" k="5" />
    <hkern u1="&#x15c;" u2="&#x150;" k="5" />
    <hkern u1="&#x15c;" u2="&#x14e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x14c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x122;" k="5" />
    <hkern u1="&#x15c;" u2="&#x120;" k="5" />
    <hkern u1="&#x15c;" u2="&#x11e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x11c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x10c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x10a;" k="5" />
    <hkern u1="&#x15c;" u2="&#x108;" k="5" />
    <hkern u1="&#x15c;" u2="&#x106;" k="5" />
    <hkern u1="&#x15c;" u2="&#xdd;" k="40" />
    <hkern u1="&#x15c;" u2="&#xd8;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd6;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd5;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd4;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd3;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd2;" k="5" />
    <hkern u1="&#x15c;" u2="&#xc7;" k="5" />
    <hkern u1="&#x15c;" u2="&#xa9;" k="5" />
    <hkern u1="&#x15c;" u2="Y" k="40" />
    <hkern u1="&#x15c;" u2="V" k="15" />
    <hkern u1="&#x15c;" u2="T" k="20" />
    <hkern u1="&#x15c;" u2="S" k="15" />
    <hkern u1="&#x15c;" u2="Q" k="5" />
    <hkern u1="&#x15c;" u2="O" k="5" />
    <hkern u1="&#x15c;" u2="G" k="5" />
    <hkern u1="&#x15c;" u2="C" k="5" />
    <hkern u1="&#x15c;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x15c;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x15d;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x15d;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x15d;" u2="&#x219;" k="15" />
    <hkern u1="&#x15d;" u2="&#x164;" k="70" />
    <hkern u1="&#x15d;" u2="&#x162;" k="70" />
    <hkern u1="&#x15d;" u2="&#x161;" k="15" />
    <hkern u1="&#x15d;" u2="&#x15f;" k="15" />
    <hkern u1="&#x15d;" u2="&#x15d;" k="15" />
    <hkern u1="&#x15d;" u2="&#x15b;" k="15" />
    <hkern u1="&#x15d;" u2="s" k="15" />
    <hkern u1="&#x15d;" u2="V" k="10" />
    <hkern u1="&#x15d;" u2="T" k="70" />
    <hkern u1="&#x15e;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x15e;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x15e;" u2="&#x218;" k="15" />
    <hkern u1="&#x15e;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x15e;" u2="&#x178;" k="40" />
    <hkern u1="&#x15e;" u2="&#x176;" k="40" />
    <hkern u1="&#x15e;" u2="&#x164;" k="20" />
    <hkern u1="&#x15e;" u2="&#x162;" k="20" />
    <hkern u1="&#x15e;" u2="&#x160;" k="15" />
    <hkern u1="&#x15e;" u2="&#x15e;" k="15" />
    <hkern u1="&#x15e;" u2="&#x15c;" k="15" />
    <hkern u1="&#x15e;" u2="&#x15a;" k="15" />
    <hkern u1="&#x15e;" u2="&#x152;" k="5" />
    <hkern u1="&#x15e;" u2="&#x150;" k="5" />
    <hkern u1="&#x15e;" u2="&#x14e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x14c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x122;" k="5" />
    <hkern u1="&#x15e;" u2="&#x120;" k="5" />
    <hkern u1="&#x15e;" u2="&#x11e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x11c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x10c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x10a;" k="5" />
    <hkern u1="&#x15e;" u2="&#x108;" k="5" />
    <hkern u1="&#x15e;" u2="&#x106;" k="5" />
    <hkern u1="&#x15e;" u2="&#xdd;" k="40" />
    <hkern u1="&#x15e;" u2="&#xd8;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd6;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd5;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd4;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd3;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd2;" k="5" />
    <hkern u1="&#x15e;" u2="&#xc7;" k="5" />
    <hkern u1="&#x15e;" u2="&#xa9;" k="5" />
    <hkern u1="&#x15e;" u2="Y" k="40" />
    <hkern u1="&#x15e;" u2="V" k="15" />
    <hkern u1="&#x15e;" u2="T" k="20" />
    <hkern u1="&#x15e;" u2="S" k="15" />
    <hkern u1="&#x15e;" u2="Q" k="5" />
    <hkern u1="&#x15e;" u2="O" k="5" />
    <hkern u1="&#x15e;" u2="G" k="5" />
    <hkern u1="&#x15e;" u2="C" k="5" />
    <hkern u1="&#x15e;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x15e;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x15f;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x15f;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x15f;" u2="&#x219;" k="15" />
    <hkern u1="&#x15f;" u2="&#x164;" k="70" />
    <hkern u1="&#x15f;" u2="&#x162;" k="70" />
    <hkern u1="&#x15f;" u2="&#x161;" k="15" />
    <hkern u1="&#x15f;" u2="&#x15f;" k="15" />
    <hkern u1="&#x15f;" u2="&#x15d;" k="15" />
    <hkern u1="&#x15f;" u2="&#x15b;" k="15" />
    <hkern u1="&#x15f;" u2="s" k="15" />
    <hkern u1="&#x15f;" u2="V" k="10" />
    <hkern u1="&#x15f;" u2="T" k="70" />
    <hkern u1="&#x160;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x160;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x160;" u2="&#x218;" k="15" />
    <hkern u1="&#x160;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x160;" u2="&#x178;" k="40" />
    <hkern u1="&#x160;" u2="&#x176;" k="40" />
    <hkern u1="&#x160;" u2="&#x164;" k="20" />
    <hkern u1="&#x160;" u2="&#x162;" k="20" />
    <hkern u1="&#x160;" u2="&#x160;" k="15" />
    <hkern u1="&#x160;" u2="&#x15e;" k="15" />
    <hkern u1="&#x160;" u2="&#x15c;" k="15" />
    <hkern u1="&#x160;" u2="&#x15a;" k="15" />
    <hkern u1="&#x160;" u2="&#x152;" k="5" />
    <hkern u1="&#x160;" u2="&#x150;" k="5" />
    <hkern u1="&#x160;" u2="&#x14e;" k="5" />
    <hkern u1="&#x160;" u2="&#x14c;" k="5" />
    <hkern u1="&#x160;" u2="&#x122;" k="5" />
    <hkern u1="&#x160;" u2="&#x120;" k="5" />
    <hkern u1="&#x160;" u2="&#x11e;" k="5" />
    <hkern u1="&#x160;" u2="&#x11c;" k="5" />
    <hkern u1="&#x160;" u2="&#x10c;" k="5" />
    <hkern u1="&#x160;" u2="&#x10a;" k="5" />
    <hkern u1="&#x160;" u2="&#x108;" k="5" />
    <hkern u1="&#x160;" u2="&#x106;" k="5" />
    <hkern u1="&#x160;" u2="&#xdd;" k="40" />
    <hkern u1="&#x160;" u2="&#xd8;" k="5" />
    <hkern u1="&#x160;" u2="&#xd6;" k="5" />
    <hkern u1="&#x160;" u2="&#xd5;" k="5" />
    <hkern u1="&#x160;" u2="&#xd4;" k="5" />
    <hkern u1="&#x160;" u2="&#xd3;" k="5" />
    <hkern u1="&#x160;" u2="&#xd2;" k="5" />
    <hkern u1="&#x160;" u2="&#xc7;" k="5" />
    <hkern u1="&#x160;" u2="&#xa9;" k="5" />
    <hkern u1="&#x160;" u2="Y" k="40" />
    <hkern u1="&#x160;" u2="V" k="15" />
    <hkern u1="&#x160;" u2="T" k="20" />
    <hkern u1="&#x160;" u2="S" k="15" />
    <hkern u1="&#x160;" u2="Q" k="5" />
    <hkern u1="&#x160;" u2="O" k="5" />
    <hkern u1="&#x160;" u2="G" k="5" />
    <hkern u1="&#x160;" u2="C" k="5" />
    <hkern u1="&#x160;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x160;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x161;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x161;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x161;" u2="&#x219;" k="15" />
    <hkern u1="&#x161;" u2="&#x164;" k="70" />
    <hkern u1="&#x161;" u2="&#x162;" k="70" />
    <hkern u1="&#x161;" u2="&#x161;" k="15" />
    <hkern u1="&#x161;" u2="&#x15f;" k="15" />
    <hkern u1="&#x161;" u2="&#x15d;" k="15" />
    <hkern u1="&#x161;" u2="&#x15b;" k="15" />
    <hkern u1="&#x161;" u2="s" k="15" />
    <hkern u1="&#x161;" u2="V" k="10" />
    <hkern u1="&#x161;" u2="T" k="70" />
    <hkern u1="&#x162;" u2="&#x2026;" k="110" />
    <hkern u1="&#x162;" u2="&#x2014;" k="110" />
    <hkern u1="&#x162;" u2="&#x2013;" k="110" />
    <hkern u1="&#x162;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x162;" u2="&#x1e85;" k="55" />
    <hkern u1="&#x162;" u2="&#x1e83;" k="55" />
    <hkern u1="&#x162;" u2="&#x1e81;" k="55" />
    <hkern u1="&#x162;" u2="&#x219;" k="85" />
    <hkern u1="&#x162;" u2="&#x1ff;" k="85" />
    <hkern u1="&#x162;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x162;" u2="&#x1fd;" k="75" />
    <hkern u1="&#x162;" u2="&#x1fc;" k="30" />
    <hkern u1="&#x162;" u2="&#x1fb;" k="75" />
    <hkern u1="&#x162;" u2="&#x1fa;" k="30" />
    <hkern u1="&#x162;" u2="&#x177;" k="80" />
    <hkern u1="&#x162;" u2="&#x175;" k="55" />
    <hkern u1="&#x162;" u2="&#x173;" k="80" />
    <hkern u1="&#x162;" u2="&#x171;" k="80" />
    <hkern u1="&#x162;" u2="&#x16f;" k="80" />
    <hkern u1="&#x162;" u2="&#x16d;" k="80" />
    <hkern u1="&#x162;" u2="&#x16b;" k="80" />
    <hkern u1="&#x162;" u2="&#x169;" k="80" />
    <hkern u1="&#x162;" u2="&#x161;" k="85" />
    <hkern u1="&#x162;" u2="&#x15f;" k="85" />
    <hkern u1="&#x162;" u2="&#x15d;" k="85" />
    <hkern u1="&#x162;" u2="&#x15b;" k="85" />
    <hkern u1="&#x162;" u2="&#x159;" k="100" />
    <hkern u1="&#x162;" u2="&#x157;" k="100" />
    <hkern u1="&#x162;" u2="&#x155;" k="100" />
    <hkern u1="&#x162;" u2="&#x153;" k="85" />
    <hkern u1="&#x162;" u2="&#x152;" k="15" />
    <hkern u1="&#x162;" u2="&#x151;" k="85" />
    <hkern u1="&#x162;" u2="&#x150;" k="15" />
    <hkern u1="&#x162;" u2="&#x14f;" k="85" />
    <hkern u1="&#x162;" u2="&#x14e;" k="15" />
    <hkern u1="&#x162;" u2="&#x14d;" k="85" />
    <hkern u1="&#x162;" u2="&#x14c;" k="15" />
    <hkern u1="&#x162;" u2="&#x14b;" k="100" />
    <hkern u1="&#x162;" u2="&#x148;" k="100" />
    <hkern u1="&#x162;" u2="&#x146;" k="100" />
    <hkern u1="&#x162;" u2="&#x144;" k="100" />
    <hkern u1="&#x162;" u2="&#x123;" k="85" />
    <hkern u1="&#x162;" u2="&#x122;" k="15" />
    <hkern u1="&#x162;" u2="&#x121;" k="85" />
    <hkern u1="&#x162;" u2="&#x120;" k="15" />
    <hkern u1="&#x162;" u2="&#x11f;" k="85" />
    <hkern u1="&#x162;" u2="&#x11e;" k="15" />
    <hkern u1="&#x162;" u2="&#x11d;" k="85" />
    <hkern u1="&#x162;" u2="&#x11c;" k="15" />
    <hkern u1="&#x162;" u2="&#x11b;" k="85" />
    <hkern u1="&#x162;" u2="&#x119;" k="85" />
    <hkern u1="&#x162;" u2="&#x117;" k="85" />
    <hkern u1="&#x162;" u2="&#x115;" k="85" />
    <hkern u1="&#x162;" u2="&#x113;" k="85" />
    <hkern u1="&#x162;" u2="&#x111;" k="85" />
    <hkern u1="&#x162;" u2="&#x10f;" k="85" />
    <hkern u1="&#x162;" u2="&#x10d;" k="85" />
    <hkern u1="&#x162;" u2="&#x10c;" k="15" />
    <hkern u1="&#x162;" u2="&#x10b;" k="85" />
    <hkern u1="&#x162;" u2="&#x10a;" k="15" />
    <hkern u1="&#x162;" u2="&#x109;" k="85" />
    <hkern u1="&#x162;" u2="&#x108;" k="15" />
    <hkern u1="&#x162;" u2="&#x107;" k="85" />
    <hkern u1="&#x162;" u2="&#x106;" k="15" />
    <hkern u1="&#x162;" u2="&#x105;" k="75" />
    <hkern u1="&#x162;" u2="&#x104;" k="30" />
    <hkern u1="&#x162;" u2="&#x103;" k="75" />
    <hkern u1="&#x162;" u2="&#x102;" k="30" />
    <hkern u1="&#x162;" u2="&#x101;" k="75" />
    <hkern u1="&#x162;" u2="&#x100;" k="30" />
    <hkern u1="&#x162;" u2="&#xff;" k="80" />
    <hkern u1="&#x162;" u2="&#xfd;" k="80" />
    <hkern u1="&#x162;" u2="&#xfc;" k="80" />
    <hkern u1="&#x162;" u2="&#xfb;" k="80" />
    <hkern u1="&#x162;" u2="&#xfa;" k="80" />
    <hkern u1="&#x162;" u2="&#xf9;" k="80" />
    <hkern u1="&#x162;" u2="&#xf8;" k="85" />
    <hkern u1="&#x162;" u2="&#xf6;" k="85" />
    <hkern u1="&#x162;" u2="&#xf5;" k="85" />
    <hkern u1="&#x162;" u2="&#xf4;" k="85" />
    <hkern u1="&#x162;" u2="&#xf3;" k="85" />
    <hkern u1="&#x162;" u2="&#xf2;" k="85" />
    <hkern u1="&#x162;" u2="&#xf1;" k="100" />
    <hkern u1="&#x162;" u2="&#xf0;" k="85" />
    <hkern u1="&#x162;" u2="&#xef;" k="-20" />
    <hkern u1="&#x162;" u2="&#xee;" k="-10" />
    <hkern u1="&#x162;" u2="&#xeb;" k="85" />
    <hkern u1="&#x162;" u2="&#xea;" k="85" />
    <hkern u1="&#x162;" u2="&#xe9;" k="85" />
    <hkern u1="&#x162;" u2="&#xe8;" k="85" />
    <hkern u1="&#x162;" u2="&#xe7;" k="85" />
    <hkern u1="&#x162;" u2="&#xe6;" k="75" />
    <hkern u1="&#x162;" u2="&#xe5;" k="75" />
    <hkern u1="&#x162;" u2="&#xe4;" k="75" />
    <hkern u1="&#x162;" u2="&#xe3;" k="75" />
    <hkern u1="&#x162;" u2="&#xe2;" k="75" />
    <hkern u1="&#x162;" u2="&#xe1;" k="75" />
    <hkern u1="&#x162;" u2="&#xe0;" k="75" />
    <hkern u1="&#x162;" u2="&#xd8;" k="15" />
    <hkern u1="&#x162;" u2="&#xd6;" k="15" />
    <hkern u1="&#x162;" u2="&#xd5;" k="15" />
    <hkern u1="&#x162;" u2="&#xd4;" k="15" />
    <hkern u1="&#x162;" u2="&#xd3;" k="15" />
    <hkern u1="&#x162;" u2="&#xd2;" k="15" />
    <hkern u1="&#x162;" u2="&#xc7;" k="15" />
    <hkern u1="&#x162;" u2="&#xc6;" k="30" />
    <hkern u1="&#x162;" u2="&#xc5;" k="30" />
    <hkern u1="&#x162;" u2="&#xc4;" k="30" />
    <hkern u1="&#x162;" u2="&#xc3;" k="30" />
    <hkern u1="&#x162;" u2="&#xc2;" k="30" />
    <hkern u1="&#x162;" u2="&#xc1;" k="30" />
    <hkern u1="&#x162;" u2="&#xc0;" k="30" />
    <hkern u1="&#x162;" u2="&#xa9;" k="15" />
    <hkern u1="&#x162;" u2="z" k="80" />
    <hkern u1="&#x162;" u2="y" k="80" />
    <hkern u1="&#x162;" u2="x" k="55" />
    <hkern u1="&#x162;" u2="w" k="55" />
    <hkern u1="&#x162;" u2="v" k="55" />
    <hkern u1="&#x162;" u2="u" k="80" />
    <hkern u1="&#x162;" u2="s" k="85" />
    <hkern u1="&#x162;" u2="r" k="100" />
    <hkern u1="&#x162;" u2="q" k="85" />
    <hkern u1="&#x162;" u2="p" k="100" />
    <hkern u1="&#x162;" u2="o" k="85" />
    <hkern u1="&#x162;" u2="n" k="100" />
    <hkern u1="&#x162;" u2="m" k="100" />
    <hkern u1="&#x162;" u2="g" k="85" />
    <hkern u1="&#x162;" u2="e" k="85" />
    <hkern u1="&#x162;" u2="d" k="85" />
    <hkern u1="&#x162;" u2="c" k="85" />
    <hkern u1="&#x162;" u2="a" k="75" />
    <hkern u1="&#x162;" u2="_" k="45" />
    <hkern u1="&#x162;" u2="V" k="-15" />
    <hkern u1="&#x162;" u2="Q" k="15" />
    <hkern u1="&#x162;" u2="O" k="15" />
    <hkern u1="&#x162;" u2="G" k="15" />
    <hkern u1="&#x162;" u2="C" k="15" />
    <hkern u1="&#x162;" u2="A" k="30" />
    <hkern u1="&#x162;" u2="&#x2f;" k="85" />
    <hkern u1="&#x162;" u2="&#x2e;" k="110" />
    <hkern u1="&#x162;" u2="&#x2d;" k="110" />
    <hkern u1="&#x162;" u2="&#x2c;" k="110" />
    <hkern u1="&#x164;" u2="&#x2026;" k="110" />
    <hkern u1="&#x164;" u2="&#x2014;" k="110" />
    <hkern u1="&#x164;" u2="&#x2013;" k="110" />
    <hkern u1="&#x164;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x164;" u2="&#x1e85;" k="55" />
    <hkern u1="&#x164;" u2="&#x1e83;" k="55" />
    <hkern u1="&#x164;" u2="&#x1e81;" k="55" />
    <hkern u1="&#x164;" u2="&#x219;" k="85" />
    <hkern u1="&#x164;" u2="&#x1ff;" k="85" />
    <hkern u1="&#x164;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x164;" u2="&#x1fd;" k="75" />
    <hkern u1="&#x164;" u2="&#x1fc;" k="30" />
    <hkern u1="&#x164;" u2="&#x1fb;" k="75" />
    <hkern u1="&#x164;" u2="&#x1fa;" k="30" />
    <hkern u1="&#x164;" u2="&#x177;" k="80" />
    <hkern u1="&#x164;" u2="&#x175;" k="55" />
    <hkern u1="&#x164;" u2="&#x173;" k="80" />
    <hkern u1="&#x164;" u2="&#x171;" k="80" />
    <hkern u1="&#x164;" u2="&#x16f;" k="80" />
    <hkern u1="&#x164;" u2="&#x16d;" k="80" />
    <hkern u1="&#x164;" u2="&#x16b;" k="80" />
    <hkern u1="&#x164;" u2="&#x169;" k="80" />
    <hkern u1="&#x164;" u2="&#x161;" k="85" />
    <hkern u1="&#x164;" u2="&#x15f;" k="85" />
    <hkern u1="&#x164;" u2="&#x15d;" k="85" />
    <hkern u1="&#x164;" u2="&#x15b;" k="85" />
    <hkern u1="&#x164;" u2="&#x159;" k="100" />
    <hkern u1="&#x164;" u2="&#x157;" k="100" />
    <hkern u1="&#x164;" u2="&#x155;" k="100" />
    <hkern u1="&#x164;" u2="&#x153;" k="85" />
    <hkern u1="&#x164;" u2="&#x152;" k="15" />
    <hkern u1="&#x164;" u2="&#x151;" k="85" />
    <hkern u1="&#x164;" u2="&#x150;" k="15" />
    <hkern u1="&#x164;" u2="&#x14f;" k="85" />
    <hkern u1="&#x164;" u2="&#x14e;" k="15" />
    <hkern u1="&#x164;" u2="&#x14d;" k="85" />
    <hkern u1="&#x164;" u2="&#x14c;" k="15" />
    <hkern u1="&#x164;" u2="&#x14b;" k="100" />
    <hkern u1="&#x164;" u2="&#x148;" k="100" />
    <hkern u1="&#x164;" u2="&#x146;" k="100" />
    <hkern u1="&#x164;" u2="&#x144;" k="100" />
    <hkern u1="&#x164;" u2="&#x123;" k="85" />
    <hkern u1="&#x164;" u2="&#x122;" k="15" />
    <hkern u1="&#x164;" u2="&#x121;" k="85" />
    <hkern u1="&#x164;" u2="&#x120;" k="15" />
    <hkern u1="&#x164;" u2="&#x11f;" k="85" />
    <hkern u1="&#x164;" u2="&#x11e;" k="15" />
    <hkern u1="&#x164;" u2="&#x11d;" k="85" />
    <hkern u1="&#x164;" u2="&#x11c;" k="15" />
    <hkern u1="&#x164;" u2="&#x11b;" k="85" />
    <hkern u1="&#x164;" u2="&#x119;" k="85" />
    <hkern u1="&#x164;" u2="&#x117;" k="85" />
    <hkern u1="&#x164;" u2="&#x115;" k="85" />
    <hkern u1="&#x164;" u2="&#x113;" k="85" />
    <hkern u1="&#x164;" u2="&#x111;" k="85" />
    <hkern u1="&#x164;" u2="&#x10f;" k="85" />
    <hkern u1="&#x164;" u2="&#x10d;" k="85" />
    <hkern u1="&#x164;" u2="&#x10c;" k="15" />
    <hkern u1="&#x164;" u2="&#x10b;" k="85" />
    <hkern u1="&#x164;" u2="&#x10a;" k="15" />
    <hkern u1="&#x164;" u2="&#x109;" k="85" />
    <hkern u1="&#x164;" u2="&#x108;" k="15" />
    <hkern u1="&#x164;" u2="&#x107;" k="85" />
    <hkern u1="&#x164;" u2="&#x106;" k="15" />
    <hkern u1="&#x164;" u2="&#x105;" k="75" />
    <hkern u1="&#x164;" u2="&#x104;" k="30" />
    <hkern u1="&#x164;" u2="&#x103;" k="75" />
    <hkern u1="&#x164;" u2="&#x102;" k="30" />
    <hkern u1="&#x164;" u2="&#x101;" k="75" />
    <hkern u1="&#x164;" u2="&#x100;" k="30" />
    <hkern u1="&#x164;" u2="&#xff;" k="80" />
    <hkern u1="&#x164;" u2="&#xfd;" k="80" />
    <hkern u1="&#x164;" u2="&#xfc;" k="80" />
    <hkern u1="&#x164;" u2="&#xfb;" k="80" />
    <hkern u1="&#x164;" u2="&#xfa;" k="80" />
    <hkern u1="&#x164;" u2="&#xf9;" k="80" />
    <hkern u1="&#x164;" u2="&#xf8;" k="85" />
    <hkern u1="&#x164;" u2="&#xf6;" k="85" />
    <hkern u1="&#x164;" u2="&#xf5;" k="85" />
    <hkern u1="&#x164;" u2="&#xf4;" k="85" />
    <hkern u1="&#x164;" u2="&#xf3;" k="85" />
    <hkern u1="&#x164;" u2="&#xf2;" k="85" />
    <hkern u1="&#x164;" u2="&#xf1;" k="100" />
    <hkern u1="&#x164;" u2="&#xf0;" k="85" />
    <hkern u1="&#x164;" u2="&#xef;" k="-20" />
    <hkern u1="&#x164;" u2="&#xee;" k="-10" />
    <hkern u1="&#x164;" u2="&#xeb;" k="85" />
    <hkern u1="&#x164;" u2="&#xea;" k="85" />
    <hkern u1="&#x164;" u2="&#xe9;" k="85" />
    <hkern u1="&#x164;" u2="&#xe8;" k="85" />
    <hkern u1="&#x164;" u2="&#xe7;" k="85" />
    <hkern u1="&#x164;" u2="&#xe6;" k="75" />
    <hkern u1="&#x164;" u2="&#xe5;" k="75" />
    <hkern u1="&#x164;" u2="&#xe4;" k="75" />
    <hkern u1="&#x164;" u2="&#xe3;" k="75" />
    <hkern u1="&#x164;" u2="&#xe2;" k="75" />
    <hkern u1="&#x164;" u2="&#xe1;" k="75" />
    <hkern u1="&#x164;" u2="&#xe0;" k="75" />
    <hkern u1="&#x164;" u2="&#xd8;" k="15" />
    <hkern u1="&#x164;" u2="&#xd6;" k="15" />
    <hkern u1="&#x164;" u2="&#xd5;" k="15" />
    <hkern u1="&#x164;" u2="&#xd4;" k="15" />
    <hkern u1="&#x164;" u2="&#xd3;" k="15" />
    <hkern u1="&#x164;" u2="&#xd2;" k="15" />
    <hkern u1="&#x164;" u2="&#xc7;" k="15" />
    <hkern u1="&#x164;" u2="&#xc6;" k="30" />
    <hkern u1="&#x164;" u2="&#xc5;" k="30" />
    <hkern u1="&#x164;" u2="&#xc4;" k="30" />
    <hkern u1="&#x164;" u2="&#xc3;" k="30" />
    <hkern u1="&#x164;" u2="&#xc2;" k="30" />
    <hkern u1="&#x164;" u2="&#xc1;" k="30" />
    <hkern u1="&#x164;" u2="&#xc0;" k="30" />
    <hkern u1="&#x164;" u2="&#xa9;" k="15" />
    <hkern u1="&#x164;" u2="z" k="80" />
    <hkern u1="&#x164;" u2="y" k="80" />
    <hkern u1="&#x164;" u2="x" k="55" />
    <hkern u1="&#x164;" u2="w" k="55" />
    <hkern u1="&#x164;" u2="v" k="55" />
    <hkern u1="&#x164;" u2="u" k="80" />
    <hkern u1="&#x164;" u2="s" k="85" />
    <hkern u1="&#x164;" u2="r" k="100" />
    <hkern u1="&#x164;" u2="q" k="85" />
    <hkern u1="&#x164;" u2="p" k="100" />
    <hkern u1="&#x164;" u2="o" k="85" />
    <hkern u1="&#x164;" u2="n" k="100" />
    <hkern u1="&#x164;" u2="m" k="100" />
    <hkern u1="&#x164;" u2="g" k="85" />
    <hkern u1="&#x164;" u2="e" k="85" />
    <hkern u1="&#x164;" u2="d" k="85" />
    <hkern u1="&#x164;" u2="c" k="85" />
    <hkern u1="&#x164;" u2="a" k="75" />
    <hkern u1="&#x164;" u2="_" k="45" />
    <hkern u1="&#x164;" u2="V" k="-15" />
    <hkern u1="&#x164;" u2="Q" k="15" />
    <hkern u1="&#x164;" u2="O" k="15" />
    <hkern u1="&#x164;" u2="G" k="15" />
    <hkern u1="&#x164;" u2="C" k="15" />
    <hkern u1="&#x164;" u2="A" k="30" />
    <hkern u1="&#x164;" u2="&#x2f;" k="85" />
    <hkern u1="&#x164;" u2="&#x2e;" k="110" />
    <hkern u1="&#x164;" u2="&#x2d;" k="110" />
    <hkern u1="&#x164;" u2="&#x2c;" k="110" />
    <hkern u1="&#x174;" u2="&#x2026;" k="55" />
    <hkern u1="&#x174;" u2="&#x2014;" k="20" />
    <hkern u1="&#x174;" u2="&#x2013;" k="20" />
    <hkern u1="&#x174;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x174;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x174;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x174;" u2="&#x153;" k="10" />
    <hkern u1="&#x174;" u2="&#x151;" k="10" />
    <hkern u1="&#x174;" u2="&#x14f;" k="10" />
    <hkern u1="&#x174;" u2="&#x14d;" k="10" />
    <hkern u1="&#x174;" u2="&#x123;" k="10" />
    <hkern u1="&#x174;" u2="&#x121;" k="10" />
    <hkern u1="&#x174;" u2="&#x11f;" k="10" />
    <hkern u1="&#x174;" u2="&#x11d;" k="10" />
    <hkern u1="&#x174;" u2="&#x11b;" k="10" />
    <hkern u1="&#x174;" u2="&#x119;" k="10" />
    <hkern u1="&#x174;" u2="&#x117;" k="10" />
    <hkern u1="&#x174;" u2="&#x115;" k="10" />
    <hkern u1="&#x174;" u2="&#x113;" k="10" />
    <hkern u1="&#x174;" u2="&#x111;" k="10" />
    <hkern u1="&#x174;" u2="&#x10f;" k="10" />
    <hkern u1="&#x174;" u2="&#x10d;" k="10" />
    <hkern u1="&#x174;" u2="&#x10b;" k="10" />
    <hkern u1="&#x174;" u2="&#x109;" k="10" />
    <hkern u1="&#x174;" u2="&#x107;" k="10" />
    <hkern u1="&#x174;" u2="&#x105;" k="10" />
    <hkern u1="&#x174;" u2="&#x103;" k="10" />
    <hkern u1="&#x174;" u2="&#x101;" k="10" />
    <hkern u1="&#x174;" u2="&#xf8;" k="10" />
    <hkern u1="&#x174;" u2="&#xf6;" k="10" />
    <hkern u1="&#x174;" u2="&#xf5;" k="10" />
    <hkern u1="&#x174;" u2="&#xf4;" k="10" />
    <hkern u1="&#x174;" u2="&#xf3;" k="10" />
    <hkern u1="&#x174;" u2="&#xf2;" k="10" />
    <hkern u1="&#x174;" u2="&#xf0;" k="10" />
    <hkern u1="&#x174;" u2="&#xef;" k="-20" />
    <hkern u1="&#x174;" u2="&#xee;" k="-10" />
    <hkern u1="&#x174;" u2="&#xeb;" k="10" />
    <hkern u1="&#x174;" u2="&#xea;" k="10" />
    <hkern u1="&#x174;" u2="&#xe9;" k="10" />
    <hkern u1="&#x174;" u2="&#xe8;" k="10" />
    <hkern u1="&#x174;" u2="&#xe7;" k="10" />
    <hkern u1="&#x174;" u2="&#xe6;" k="10" />
    <hkern u1="&#x174;" u2="&#xe5;" k="10" />
    <hkern u1="&#x174;" u2="&#xe4;" k="10" />
    <hkern u1="&#x174;" u2="&#xe3;" k="10" />
    <hkern u1="&#x174;" u2="&#xe2;" k="10" />
    <hkern u1="&#x174;" u2="&#xe1;" k="10" />
    <hkern u1="&#x174;" u2="&#xe0;" k="10" />
    <hkern u1="&#x174;" u2="q" k="10" />
    <hkern u1="&#x174;" u2="o" k="10" />
    <hkern u1="&#x174;" u2="g" k="10" />
    <hkern u1="&#x174;" u2="f" k="-25" />
    <hkern u1="&#x174;" u2="e" k="10" />
    <hkern u1="&#x174;" u2="d" k="10" />
    <hkern u1="&#x174;" u2="c" k="10" />
    <hkern u1="&#x174;" u2="a" k="10" />
    <hkern u1="&#x174;" u2="_" k="45" />
    <hkern u1="&#x174;" u2="&#x2f;" k="55" />
    <hkern u1="&#x174;" u2="&#x2e;" k="55" />
    <hkern u1="&#x174;" u2="&#x2d;" k="20" />
    <hkern u1="&#x174;" u2="&#x2c;" k="55" />
    <hkern u1="&#x175;" u2="&#x2026;" k="65" />
    <hkern u1="&#x175;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x175;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x175;" u2="&#x2014;" k="10" />
    <hkern u1="&#x175;" u2="&#x2013;" k="10" />
    <hkern u1="&#x175;" u2="&#x164;" k="55" />
    <hkern u1="&#x175;" u2="&#x162;" k="55" />
    <hkern u1="&#x175;" u2="_" k="35" />
    <hkern u1="&#x175;" u2="T" k="55" />
    <hkern u1="&#x175;" u2="&#x2f;" k="20" />
    <hkern u1="&#x175;" u2="&#x2e;" k="65" />
    <hkern u1="&#x175;" u2="&#x2d;" k="10" />
    <hkern u1="&#x175;" u2="&#x2c;" k="65" />
    <hkern u1="&#x176;" u2="V" k="-15" />
    <hkern u1="&#x177;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x177;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x177;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x177;" u2="&#x174;" k="10" />
    <hkern u1="&#x177;" u2="&#x164;" k="80" />
    <hkern u1="&#x177;" u2="&#x162;" k="80" />
    <hkern u1="&#x177;" u2="W" k="10" />
    <hkern u1="&#x177;" u2="V" k="20" />
    <hkern u1="&#x177;" u2="T" k="80" />
    <hkern u1="&#x178;" u2="V" k="-15" />
    <hkern u1="&#x179;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x179;" u2="&#x2014;" k="120" />
    <hkern u1="&#x179;" u2="&#x2013;" k="120" />
    <hkern u1="&#x179;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x179;" u2="&#x152;" k="15" />
    <hkern u1="&#x179;" u2="&#x150;" k="15" />
    <hkern u1="&#x179;" u2="&#x14e;" k="15" />
    <hkern u1="&#x179;" u2="&#x14c;" k="15" />
    <hkern u1="&#x179;" u2="&#x122;" k="15" />
    <hkern u1="&#x179;" u2="&#x120;" k="15" />
    <hkern u1="&#x179;" u2="&#x11e;" k="15" />
    <hkern u1="&#x179;" u2="&#x11c;" k="15" />
    <hkern u1="&#x179;" u2="&#x10c;" k="15" />
    <hkern u1="&#x179;" u2="&#x10a;" k="15" />
    <hkern u1="&#x179;" u2="&#x108;" k="15" />
    <hkern u1="&#x179;" u2="&#x106;" k="15" />
    <hkern u1="&#x179;" u2="&#xd8;" k="15" />
    <hkern u1="&#x179;" u2="&#xd6;" k="15" />
    <hkern u1="&#x179;" u2="&#xd5;" k="15" />
    <hkern u1="&#x179;" u2="&#xd4;" k="15" />
    <hkern u1="&#x179;" u2="&#xd3;" k="15" />
    <hkern u1="&#x179;" u2="&#xd2;" k="15" />
    <hkern u1="&#x179;" u2="&#xc7;" k="15" />
    <hkern u1="&#x179;" u2="&#xa9;" k="15" />
    <hkern u1="&#x179;" u2="Q" k="15" />
    <hkern u1="&#x179;" u2="O" k="15" />
    <hkern u1="&#x179;" u2="G" k="15" />
    <hkern u1="&#x179;" u2="C" k="15" />
    <hkern u1="&#x179;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x179;" u2="&#x2d;" k="120" />
    <hkern u1="&#x179;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17a;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17a;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17a;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x17a;" u2="&#x164;" k="70" />
    <hkern u1="&#x17a;" u2="&#x162;" k="70" />
    <hkern u1="&#x17a;" u2="&#x153;" k="15" />
    <hkern u1="&#x17a;" u2="&#x151;" k="15" />
    <hkern u1="&#x17a;" u2="&#x14f;" k="15" />
    <hkern u1="&#x17a;" u2="&#x14d;" k="15" />
    <hkern u1="&#x17a;" u2="&#x123;" k="15" />
    <hkern u1="&#x17a;" u2="&#x121;" k="15" />
    <hkern u1="&#x17a;" u2="&#x11f;" k="15" />
    <hkern u1="&#x17a;" u2="&#x11d;" k="15" />
    <hkern u1="&#x17a;" u2="&#x11b;" k="15" />
    <hkern u1="&#x17a;" u2="&#x119;" k="15" />
    <hkern u1="&#x17a;" u2="&#x117;" k="15" />
    <hkern u1="&#x17a;" u2="&#x115;" k="15" />
    <hkern u1="&#x17a;" u2="&#x113;" k="15" />
    <hkern u1="&#x17a;" u2="&#x111;" k="15" />
    <hkern u1="&#x17a;" u2="&#x10f;" k="15" />
    <hkern u1="&#x17a;" u2="&#x10d;" k="15" />
    <hkern u1="&#x17a;" u2="&#x10b;" k="15" />
    <hkern u1="&#x17a;" u2="&#x109;" k="15" />
    <hkern u1="&#x17a;" u2="&#x107;" k="15" />
    <hkern u1="&#x17a;" u2="&#xf8;" k="15" />
    <hkern u1="&#x17a;" u2="&#xf6;" k="15" />
    <hkern u1="&#x17a;" u2="&#xf5;" k="15" />
    <hkern u1="&#x17a;" u2="&#xf4;" k="15" />
    <hkern u1="&#x17a;" u2="&#xf3;" k="15" />
    <hkern u1="&#x17a;" u2="&#xf2;" k="15" />
    <hkern u1="&#x17a;" u2="&#xf0;" k="15" />
    <hkern u1="&#x17a;" u2="&#xeb;" k="15" />
    <hkern u1="&#x17a;" u2="&#xea;" k="15" />
    <hkern u1="&#x17a;" u2="&#xe9;" k="15" />
    <hkern u1="&#x17a;" u2="&#xe8;" k="15" />
    <hkern u1="&#x17a;" u2="&#xe7;" k="15" />
    <hkern u1="&#x17a;" u2="q" k="15" />
    <hkern u1="&#x17a;" u2="o" k="15" />
    <hkern u1="&#x17a;" u2="g" k="15" />
    <hkern u1="&#x17a;" u2="e" k="15" />
    <hkern u1="&#x17a;" u2="d" k="15" />
    <hkern u1="&#x17a;" u2="c" k="15" />
    <hkern u1="&#x17a;" u2="T" k="70" />
    <hkern u1="&#x17a;" u2="&#x2d;" k="35" />
    <hkern u1="&#x17b;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x17b;" u2="&#x2014;" k="120" />
    <hkern u1="&#x17b;" u2="&#x2013;" k="120" />
    <hkern u1="&#x17b;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x17b;" u2="&#x152;" k="15" />
    <hkern u1="&#x17b;" u2="&#x150;" k="15" />
    <hkern u1="&#x17b;" u2="&#x14e;" k="15" />
    <hkern u1="&#x17b;" u2="&#x14c;" k="15" />
    <hkern u1="&#x17b;" u2="&#x122;" k="15" />
    <hkern u1="&#x17b;" u2="&#x120;" k="15" />
    <hkern u1="&#x17b;" u2="&#x11e;" k="15" />
    <hkern u1="&#x17b;" u2="&#x11c;" k="15" />
    <hkern u1="&#x17b;" u2="&#x10c;" k="15" />
    <hkern u1="&#x17b;" u2="&#x10a;" k="15" />
    <hkern u1="&#x17b;" u2="&#x108;" k="15" />
    <hkern u1="&#x17b;" u2="&#x106;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd8;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd6;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd5;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd4;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd3;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd2;" k="15" />
    <hkern u1="&#x17b;" u2="&#xc7;" k="15" />
    <hkern u1="&#x17b;" u2="&#xa9;" k="15" />
    <hkern u1="&#x17b;" u2="Q" k="15" />
    <hkern u1="&#x17b;" u2="O" k="15" />
    <hkern u1="&#x17b;" u2="G" k="15" />
    <hkern u1="&#x17b;" u2="C" k="15" />
    <hkern u1="&#x17b;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x17b;" u2="&#x2d;" k="120" />
    <hkern u1="&#x17b;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17c;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17c;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17c;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x17c;" u2="&#x164;" k="70" />
    <hkern u1="&#x17c;" u2="&#x162;" k="70" />
    <hkern u1="&#x17c;" u2="&#x153;" k="15" />
    <hkern u1="&#x17c;" u2="&#x151;" k="15" />
    <hkern u1="&#x17c;" u2="&#x14f;" k="15" />
    <hkern u1="&#x17c;" u2="&#x14d;" k="15" />
    <hkern u1="&#x17c;" u2="&#x123;" k="15" />
    <hkern u1="&#x17c;" u2="&#x121;" k="15" />
    <hkern u1="&#x17c;" u2="&#x11f;" k="15" />
    <hkern u1="&#x17c;" u2="&#x11d;" k="15" />
    <hkern u1="&#x17c;" u2="&#x11b;" k="15" />
    <hkern u1="&#x17c;" u2="&#x119;" k="15" />
    <hkern u1="&#x17c;" u2="&#x117;" k="15" />
    <hkern u1="&#x17c;" u2="&#x115;" k="15" />
    <hkern u1="&#x17c;" u2="&#x113;" k="15" />
    <hkern u1="&#x17c;" u2="&#x111;" k="15" />
    <hkern u1="&#x17c;" u2="&#x10f;" k="15" />
    <hkern u1="&#x17c;" u2="&#x10d;" k="15" />
    <hkern u1="&#x17c;" u2="&#x10b;" k="15" />
    <hkern u1="&#x17c;" u2="&#x109;" k="15" />
    <hkern u1="&#x17c;" u2="&#x107;" k="15" />
    <hkern u1="&#x17c;" u2="&#xf8;" k="15" />
    <hkern u1="&#x17c;" u2="&#xf6;" k="15" />
    <hkern u1="&#x17c;" u2="&#xf5;" k="15" />
    <hkern u1="&#x17c;" u2="&#xf4;" k="15" />
    <hkern u1="&#x17c;" u2="&#xf3;" k="15" />
    <hkern u1="&#x17c;" u2="&#xf2;" k="15" />
    <hkern u1="&#x17c;" u2="&#xf0;" k="15" />
    <hkern u1="&#x17c;" u2="&#xeb;" k="15" />
    <hkern u1="&#x17c;" u2="&#xea;" k="15" />
    <hkern u1="&#x17c;" u2="&#xe9;" k="15" />
    <hkern u1="&#x17c;" u2="&#xe8;" k="15" />
    <hkern u1="&#x17c;" u2="&#xe7;" k="15" />
    <hkern u1="&#x17c;" u2="q" k="15" />
    <hkern u1="&#x17c;" u2="o" k="15" />
    <hkern u1="&#x17c;" u2="g" k="15" />
    <hkern u1="&#x17c;" u2="e" k="15" />
    <hkern u1="&#x17c;" u2="d" k="15" />
    <hkern u1="&#x17c;" u2="c" k="15" />
    <hkern u1="&#x17c;" u2="T" k="70" />
    <hkern u1="&#x17c;" u2="&#x2d;" k="35" />
    <hkern u1="&#x17d;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x17d;" u2="&#x2014;" k="120" />
    <hkern u1="&#x17d;" u2="&#x2013;" k="120" />
    <hkern u1="&#x17d;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x17d;" u2="&#x152;" k="15" />
    <hkern u1="&#x17d;" u2="&#x150;" k="15" />
    <hkern u1="&#x17d;" u2="&#x14e;" k="15" />
    <hkern u1="&#x17d;" u2="&#x14c;" k="15" />
    <hkern u1="&#x17d;" u2="&#x122;" k="15" />
    <hkern u1="&#x17d;" u2="&#x120;" k="15" />
    <hkern u1="&#x17d;" u2="&#x11e;" k="15" />
    <hkern u1="&#x17d;" u2="&#x11c;" k="15" />
    <hkern u1="&#x17d;" u2="&#x10c;" k="15" />
    <hkern u1="&#x17d;" u2="&#x10a;" k="15" />
    <hkern u1="&#x17d;" u2="&#x108;" k="15" />
    <hkern u1="&#x17d;" u2="&#x106;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="15" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="15" />
    <hkern u1="&#x17d;" u2="&#xa9;" k="15" />
    <hkern u1="&#x17d;" u2="Q" k="15" />
    <hkern u1="&#x17d;" u2="O" k="15" />
    <hkern u1="&#x17d;" u2="G" k="15" />
    <hkern u1="&#x17d;" u2="C" k="15" />
    <hkern u1="&#x17d;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x17d;" u2="&#x2d;" k="120" />
    <hkern u1="&#x17d;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17e;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17e;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17e;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x17e;" u2="&#x164;" k="70" />
    <hkern u1="&#x17e;" u2="&#x162;" k="70" />
    <hkern u1="&#x17e;" u2="&#x153;" k="15" />
    <hkern u1="&#x17e;" u2="&#x151;" k="15" />
    <hkern u1="&#x17e;" u2="&#x14f;" k="15" />
    <hkern u1="&#x17e;" u2="&#x14d;" k="15" />
    <hkern u1="&#x17e;" u2="&#x123;" k="15" />
    <hkern u1="&#x17e;" u2="&#x121;" k="15" />
    <hkern u1="&#x17e;" u2="&#x11f;" k="15" />
    <hkern u1="&#x17e;" u2="&#x11d;" k="15" />
    <hkern u1="&#x17e;" u2="&#x11b;" k="15" />
    <hkern u1="&#x17e;" u2="&#x119;" k="15" />
    <hkern u1="&#x17e;" u2="&#x117;" k="15" />
    <hkern u1="&#x17e;" u2="&#x115;" k="15" />
    <hkern u1="&#x17e;" u2="&#x113;" k="15" />
    <hkern u1="&#x17e;" u2="&#x111;" k="15" />
    <hkern u1="&#x17e;" u2="&#x10f;" k="15" />
    <hkern u1="&#x17e;" u2="&#x10d;" k="15" />
    <hkern u1="&#x17e;" u2="&#x10b;" k="15" />
    <hkern u1="&#x17e;" u2="&#x109;" k="15" />
    <hkern u1="&#x17e;" u2="&#x107;" k="15" />
    <hkern u1="&#x17e;" u2="&#xf8;" k="15" />
    <hkern u1="&#x17e;" u2="&#xf6;" k="15" />
    <hkern u1="&#x17e;" u2="&#xf5;" k="15" />
    <hkern u1="&#x17e;" u2="&#xf4;" k="15" />
    <hkern u1="&#x17e;" u2="&#xf3;" k="15" />
    <hkern u1="&#x17e;" u2="&#xf2;" k="15" />
    <hkern u1="&#x17e;" u2="&#xf0;" k="15" />
    <hkern u1="&#x17e;" u2="&#xeb;" k="15" />
    <hkern u1="&#x17e;" u2="&#xea;" k="15" />
    <hkern u1="&#x17e;" u2="&#xe9;" k="15" />
    <hkern u1="&#x17e;" u2="&#xe8;" k="15" />
    <hkern u1="&#x17e;" u2="&#xe7;" k="15" />
    <hkern u1="&#x17e;" u2="q" k="15" />
    <hkern u1="&#x17e;" u2="o" k="15" />
    <hkern u1="&#x17e;" u2="g" k="15" />
    <hkern u1="&#x17e;" u2="e" k="15" />
    <hkern u1="&#x17e;" u2="d" k="15" />
    <hkern u1="&#x17e;" u2="c" k="15" />
    <hkern u1="&#x17e;" u2="T" k="70" />
    <hkern u1="&#x17e;" u2="&#x2d;" k="35" />
    <hkern u1="&#x1fa;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x1fa;" u2="&#x164;" k="30" />
    <hkern u1="&#x1fa;" u2="&#x162;" k="30" />
    <hkern u1="&#x1fa;" u2="V" k="20" />
    <hkern u1="&#x1fa;" u2="T" k="30" />
    <hkern u1="&#x1fa;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x1fa;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x1fc;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x1fc;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x152;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x150;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x14e;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x14c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x122;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x120;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x11e;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x11c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x10c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x10a;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x108;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x106;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd8;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd6;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd5;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd4;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd3;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd2;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xc7;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xa9;" k="7" />
    <hkern u1="&#x1fc;" u2="Q" k="7" />
    <hkern u1="&#x1fc;" u2="O" k="7" />
    <hkern u1="&#x1fc;" u2="G" k="7" />
    <hkern u1="&#x1fc;" u2="C" k="7" />
    <hkern u1="&#x1fc;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x1fc;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x1fd;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x219;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x174;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x164;" k="70" />
    <hkern u1="&#x1fd;" u2="&#x162;" k="70" />
    <hkern u1="&#x1fd;" u2="&#x161;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x1fd;" u2="s" k="-5" />
    <hkern u1="&#x1fd;" u2="W" k="10" />
    <hkern u1="&#x1fd;" u2="V" k="20" />
    <hkern u1="&#x1fd;" u2="T" k="70" />
    <hkern u1="&#x1fe;" u2="&#x2026;" k="45" />
    <hkern u1="&#x1fe;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x218;" k="5" />
    <hkern u1="&#x1fe;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x164;" k="15" />
    <hkern u1="&#x1fe;" u2="&#x162;" k="15" />
    <hkern u1="&#x1fe;" u2="&#x160;" k="5" />
    <hkern u1="&#x1fe;" u2="&#x15e;" k="5" />
    <hkern u1="&#x1fe;" u2="&#x15c;" k="5" />
    <hkern u1="&#x1fe;" u2="&#x15a;" k="5" />
    <hkern u1="&#x1fe;" u2="&#x152;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x150;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x134;" k="-20" />
    <hkern u1="&#x1fe;" u2="&#x122;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x120;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x108;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x106;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x1fe;" u2="X" k="10" />
    <hkern u1="&#x1fe;" u2="V" k="15" />
    <hkern u1="&#x1fe;" u2="T" k="15" />
    <hkern u1="&#x1fe;" u2="S" k="5" />
    <hkern u1="&#x1fe;" u2="Q" k="-5" />
    <hkern u1="&#x1fe;" u2="O" k="-5" />
    <hkern u1="&#x1fe;" u2="J" k="-20" />
    <hkern u1="&#x1fe;" u2="G" k="-5" />
    <hkern u1="&#x1fe;" u2="C" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x2e;" k="45" />
    <hkern u1="&#x1fe;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x2c;" k="45" />
    <hkern u1="&#x1ff;" u2="&#x2026;" k="20" />
    <hkern u1="&#x1ff;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x174;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x164;" k="85" />
    <hkern u1="&#x1ff;" u2="&#x162;" k="85" />
    <hkern u1="&#x1ff;" u2="z" k="20" />
    <hkern u1="&#x1ff;" u2="x" k="10" />
    <hkern u1="&#x1ff;" u2="W" k="10" />
    <hkern u1="&#x1ff;" u2="V" k="20" />
    <hkern u1="&#x1ff;" u2="T" k="85" />
    <hkern u1="&#x1ff;" u2="&#x2e;" k="20" />
    <hkern u1="&#x1ff;" u2="&#x2c;" k="20" />
    <hkern u1="&#x218;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x218;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x218;" u2="&#x218;" k="15" />
    <hkern u1="&#x218;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x218;" u2="&#x178;" k="40" />
    <hkern u1="&#x218;" u2="&#x176;" k="40" />
    <hkern u1="&#x218;" u2="&#x164;" k="20" />
    <hkern u1="&#x218;" u2="&#x162;" k="20" />
    <hkern u1="&#x218;" u2="&#x160;" k="15" />
    <hkern u1="&#x218;" u2="&#x15e;" k="15" />
    <hkern u1="&#x218;" u2="&#x15c;" k="15" />
    <hkern u1="&#x218;" u2="&#x15a;" k="15" />
    <hkern u1="&#x218;" u2="&#x152;" k="5" />
    <hkern u1="&#x218;" u2="&#x150;" k="5" />
    <hkern u1="&#x218;" u2="&#x14e;" k="5" />
    <hkern u1="&#x218;" u2="&#x14c;" k="5" />
    <hkern u1="&#x218;" u2="&#x122;" k="5" />
    <hkern u1="&#x218;" u2="&#x120;" k="5" />
    <hkern u1="&#x218;" u2="&#x11e;" k="5" />
    <hkern u1="&#x218;" u2="&#x11c;" k="5" />
    <hkern u1="&#x218;" u2="&#x10c;" k="5" />
    <hkern u1="&#x218;" u2="&#x10a;" k="5" />
    <hkern u1="&#x218;" u2="&#x108;" k="5" />
    <hkern u1="&#x218;" u2="&#x106;" k="5" />
    <hkern u1="&#x218;" u2="&#xdd;" k="40" />
    <hkern u1="&#x218;" u2="&#xd8;" k="5" />
    <hkern u1="&#x218;" u2="&#xd6;" k="5" />
    <hkern u1="&#x218;" u2="&#xd5;" k="5" />
    <hkern u1="&#x218;" u2="&#xd4;" k="5" />
    <hkern u1="&#x218;" u2="&#xd3;" k="5" />
    <hkern u1="&#x218;" u2="&#xd2;" k="5" />
    <hkern u1="&#x218;" u2="&#xc7;" k="5" />
    <hkern u1="&#x218;" u2="&#xa9;" k="5" />
    <hkern u1="&#x218;" u2="Y" k="40" />
    <hkern u1="&#x218;" u2="V" k="15" />
    <hkern u1="&#x218;" u2="T" k="20" />
    <hkern u1="&#x218;" u2="S" k="15" />
    <hkern u1="&#x218;" u2="Q" k="5" />
    <hkern u1="&#x218;" u2="O" k="5" />
    <hkern u1="&#x218;" u2="G" k="5" />
    <hkern u1="&#x218;" u2="C" k="5" />
    <hkern u1="&#x218;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x218;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x219;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x219;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x219;" u2="&#x219;" k="15" />
    <hkern u1="&#x219;" u2="&#x164;" k="70" />
    <hkern u1="&#x219;" u2="&#x162;" k="70" />
    <hkern u1="&#x219;" u2="&#x161;" k="15" />
    <hkern u1="&#x219;" u2="&#x15f;" k="15" />
    <hkern u1="&#x219;" u2="&#x15d;" k="15" />
    <hkern u1="&#x219;" u2="&#x15b;" k="15" />
    <hkern u1="&#x219;" u2="s" k="15" />
    <hkern u1="&#x219;" u2="V" k="10" />
    <hkern u1="&#x219;" u2="T" k="70" />
    <hkern u1="&#x1e80;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xef;" k="-20" />
    <hkern u1="&#x1e80;" u2="&#xee;" k="-10" />
    <hkern u1="&#x1e80;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e80;" u2="q" k="10" />
    <hkern u1="&#x1e80;" u2="o" k="10" />
    <hkern u1="&#x1e80;" u2="g" k="10" />
    <hkern u1="&#x1e80;" u2="f" k="-25" />
    <hkern u1="&#x1e80;" u2="e" k="10" />
    <hkern u1="&#x1e80;" u2="d" k="10" />
    <hkern u1="&#x1e80;" u2="c" k="10" />
    <hkern u1="&#x1e80;" u2="a" k="10" />
    <hkern u1="&#x1e80;" u2="_" k="45" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e81;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e81;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e81;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e81;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e81;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e81;" u2="_" k="35" />
    <hkern u1="&#x1e81;" u2="T" k="55" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e81;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e81;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1e82;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xef;" k="-20" />
    <hkern u1="&#x1e82;" u2="&#xee;" k="-10" />
    <hkern u1="&#x1e82;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e82;" u2="q" k="10" />
    <hkern u1="&#x1e82;" u2="o" k="10" />
    <hkern u1="&#x1e82;" u2="g" k="10" />
    <hkern u1="&#x1e82;" u2="f" k="-25" />
    <hkern u1="&#x1e82;" u2="e" k="10" />
    <hkern u1="&#x1e82;" u2="d" k="10" />
    <hkern u1="&#x1e82;" u2="c" k="10" />
    <hkern u1="&#x1e82;" u2="a" k="10" />
    <hkern u1="&#x1e82;" u2="_" k="45" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e83;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e83;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e83;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e83;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e83;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e83;" u2="_" k="35" />
    <hkern u1="&#x1e83;" u2="T" k="55" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e83;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e83;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1e84;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xef;" k="-20" />
    <hkern u1="&#x1e84;" u2="&#xee;" k="-10" />
    <hkern u1="&#x1e84;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e84;" u2="q" k="10" />
    <hkern u1="&#x1e84;" u2="o" k="10" />
    <hkern u1="&#x1e84;" u2="g" k="10" />
    <hkern u1="&#x1e84;" u2="f" k="-25" />
    <hkern u1="&#x1e84;" u2="e" k="10" />
    <hkern u1="&#x1e84;" u2="d" k="10" />
    <hkern u1="&#x1e84;" u2="c" k="10" />
    <hkern u1="&#x1e84;" u2="a" k="10" />
    <hkern u1="&#x1e84;" u2="_" k="45" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e85;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e85;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e85;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e85;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e85;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e85;" u2="_" k="35" />
    <hkern u1="&#x1e85;" u2="T" k="55" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e85;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e85;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1ef2;" u2="V" k="-15" />
    <hkern u1="&#x1ef3;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x174;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x164;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x162;" k="80" />
    <hkern u1="&#x1ef3;" u2="W" k="10" />
    <hkern u1="&#x1ef3;" u2="V" k="20" />
    <hkern u1="&#x1ef3;" u2="T" k="80" />
    <hkern u1="&#x2013;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2013;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2013;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2013;" u2="&#x218;" k="85" />
    <hkern u1="&#x2013;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x17d;" k="75" />
    <hkern u1="&#x2013;" u2="&#x17b;" k="75" />
    <hkern u1="&#x2013;" u2="&#x179;" k="75" />
    <hkern u1="&#x2013;" u2="&#x178;" k="10" />
    <hkern u1="&#x2013;" u2="&#x176;" k="10" />
    <hkern u1="&#x2013;" u2="&#x175;" k="10" />
    <hkern u1="&#x2013;" u2="&#x174;" k="20" />
    <hkern u1="&#x2013;" u2="&#x164;" k="110" />
    <hkern u1="&#x2013;" u2="&#x162;" k="110" />
    <hkern u1="&#x2013;" u2="&#x160;" k="85" />
    <hkern u1="&#x2013;" u2="&#x15e;" k="85" />
    <hkern u1="&#x2013;" u2="&#x15c;" k="85" />
    <hkern u1="&#x2013;" u2="&#x15a;" k="85" />
    <hkern u1="&#x2013;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2013;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2013;" u2="z" k="35" />
    <hkern u1="&#x2013;" u2="x" k="20" />
    <hkern u1="&#x2013;" u2="w" k="10" />
    <hkern u1="&#x2013;" u2="Z" k="75" />
    <hkern u1="&#x2013;" u2="Y" k="10" />
    <hkern u1="&#x2013;" u2="X" k="65" />
    <hkern u1="&#x2013;" u2="W" k="20" />
    <hkern u1="&#x2013;" u2="V" k="35" />
    <hkern u1="&#x2013;" u2="T" k="110" />
    <hkern u1="&#x2013;" u2="S" k="85" />
    <hkern u1="&#x2013;" u2="Q" k="-15" />
    <hkern u1="&#x2013;" u2="O" k="-15" />
    <hkern u1="&#x2013;" u2="G" k="-15" />
    <hkern u1="&#x2013;" u2="C" k="-15" />
    <hkern u1="&#x2014;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2014;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2014;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2014;" u2="&#x218;" k="85" />
    <hkern u1="&#x2014;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x17d;" k="75" />
    <hkern u1="&#x2014;" u2="&#x17b;" k="75" />
    <hkern u1="&#x2014;" u2="&#x179;" k="75" />
    <hkern u1="&#x2014;" u2="&#x178;" k="10" />
    <hkern u1="&#x2014;" u2="&#x176;" k="10" />
    <hkern u1="&#x2014;" u2="&#x175;" k="10" />
    <hkern u1="&#x2014;" u2="&#x174;" k="20" />
    <hkern u1="&#x2014;" u2="&#x164;" k="110" />
    <hkern u1="&#x2014;" u2="&#x162;" k="110" />
    <hkern u1="&#x2014;" u2="&#x160;" k="85" />
    <hkern u1="&#x2014;" u2="&#x15e;" k="85" />
    <hkern u1="&#x2014;" u2="&#x15c;" k="85" />
    <hkern u1="&#x2014;" u2="&#x15a;" k="85" />
    <hkern u1="&#x2014;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2014;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2014;" u2="z" k="35" />
    <hkern u1="&#x2014;" u2="x" k="20" />
    <hkern u1="&#x2014;" u2="w" k="10" />
    <hkern u1="&#x2014;" u2="Z" k="75" />
    <hkern u1="&#x2014;" u2="Y" k="10" />
    <hkern u1="&#x2014;" u2="X" k="65" />
    <hkern u1="&#x2014;" u2="W" k="20" />
    <hkern u1="&#x2014;" u2="V" k="35" />
    <hkern u1="&#x2014;" u2="T" k="110" />
    <hkern u1="&#x2014;" u2="S" k="85" />
    <hkern u1="&#x2014;" u2="Q" k="-15" />
    <hkern u1="&#x2014;" u2="O" k="-15" />
    <hkern u1="&#x2014;" u2="G" k="-15" />
    <hkern u1="&#x2014;" u2="C" k="-15" />
    <hkern u1="&#x2018;" u2="&#x2026;" k="140" />
    <hkern u1="&#x2018;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x2018;" u2="&#x153;" k="40" />
    <hkern u1="&#x2018;" u2="&#x151;" k="40" />
    <hkern u1="&#x2018;" u2="&#x14f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x14d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x123;" k="40" />
    <hkern u1="&#x2018;" u2="&#x121;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11b;" k="40" />
    <hkern u1="&#x2018;" u2="&#x119;" k="40" />
    <hkern u1="&#x2018;" u2="&#x117;" k="40" />
    <hkern u1="&#x2018;" u2="&#x115;" k="40" />
    <hkern u1="&#x2018;" u2="&#x113;" k="40" />
    <hkern u1="&#x2018;" u2="&#x111;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10b;" k="40" />
    <hkern u1="&#x2018;" u2="&#x109;" k="40" />
    <hkern u1="&#x2018;" u2="&#x107;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="40" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="40" />
    <hkern u1="&#x2018;" u2="&#xea;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="40" />
    <hkern u1="&#x2018;" u2="q" k="40" />
    <hkern u1="&#x2018;" u2="o" k="40" />
    <hkern u1="&#x2018;" u2="g" k="40" />
    <hkern u1="&#x2018;" u2="e" k="40" />
    <hkern u1="&#x2018;" u2="d" k="40" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="140" />
    <hkern u1="&#x2019;" u2="&#x2026;" k="140" />
    <hkern u1="&#x2019;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x2019;" u2="&#x153;" k="40" />
    <hkern u1="&#x2019;" u2="&#x151;" k="40" />
    <hkern u1="&#x2019;" u2="&#x14f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x14d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x123;" k="40" />
    <hkern u1="&#x2019;" u2="&#x121;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11b;" k="40" />
    <hkern u1="&#x2019;" u2="&#x119;" k="40" />
    <hkern u1="&#x2019;" u2="&#x117;" k="40" />
    <hkern u1="&#x2019;" u2="&#x115;" k="40" />
    <hkern u1="&#x2019;" u2="&#x113;" k="40" />
    <hkern u1="&#x2019;" u2="&#x111;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10b;" k="40" />
    <hkern u1="&#x2019;" u2="&#x109;" k="40" />
    <hkern u1="&#x2019;" u2="&#x107;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="40" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="40" />
    <hkern u1="&#x2019;" u2="&#xea;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="40" />
    <hkern u1="&#x2019;" u2="q" k="40" />
    <hkern u1="&#x2019;" u2="o" k="40" />
    <hkern u1="&#x2019;" u2="g" k="40" />
    <hkern u1="&#x2019;" u2="e" k="40" />
    <hkern u1="&#x2019;" u2="d" k="40" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201c;" u2="&#x2026;" k="140" />
    <hkern u1="&#x201c;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x201c;" u2="&#x153;" k="40" />
    <hkern u1="&#x201c;" u2="&#x151;" k="40" />
    <hkern u1="&#x201c;" u2="&#x14f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x14d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x123;" k="40" />
    <hkern u1="&#x201c;" u2="&#x121;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11b;" k="40" />
    <hkern u1="&#x201c;" u2="&#x119;" k="40" />
    <hkern u1="&#x201c;" u2="&#x117;" k="40" />
    <hkern u1="&#x201c;" u2="&#x115;" k="40" />
    <hkern u1="&#x201c;" u2="&#x113;" k="40" />
    <hkern u1="&#x201c;" u2="&#x111;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10b;" k="40" />
    <hkern u1="&#x201c;" u2="&#x109;" k="40" />
    <hkern u1="&#x201c;" u2="&#x107;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="40" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="40" />
    <hkern u1="&#x201c;" u2="&#xea;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="40" />
    <hkern u1="&#x201c;" u2="q" k="40" />
    <hkern u1="&#x201c;" u2="o" k="40" />
    <hkern u1="&#x201c;" u2="g" k="40" />
    <hkern u1="&#x201c;" u2="e" k="40" />
    <hkern u1="&#x201c;" u2="d" k="40" />
    <hkern u1="&#x201c;" u2="c" k="40" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201c;" u2="&#x2c;" k="140" />
    <hkern u1="&#x201d;" u2="&#x2026;" k="140" />
    <hkern u1="&#x201d;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x201d;" u2="&#x153;" k="40" />
    <hkern u1="&#x201d;" u2="&#x151;" k="40" />
    <hkern u1="&#x201d;" u2="&#x14f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x14d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x123;" k="40" />
    <hkern u1="&#x201d;" u2="&#x121;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11b;" k="40" />
    <hkern u1="&#x201d;" u2="&#x119;" k="40" />
    <hkern u1="&#x201d;" u2="&#x117;" k="40" />
    <hkern u1="&#x201d;" u2="&#x115;" k="40" />
    <hkern u1="&#x201d;" u2="&#x113;" k="40" />
    <hkern u1="&#x201d;" u2="&#x111;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10b;" k="40" />
    <hkern u1="&#x201d;" u2="&#x109;" k="40" />
    <hkern u1="&#x201d;" u2="&#x107;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="40" />
    <hkern u1="&#x201d;" u2="&#xeb;" k="40" />
    <hkern u1="&#x201d;" u2="&#xea;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe9;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe8;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="40" />
    <hkern u1="&#x201d;" u2="q" k="40" />
    <hkern u1="&#x201d;" u2="o" k="40" />
    <hkern u1="&#x201d;" u2="g" k="40" />
    <hkern u1="&#x201d;" u2="e" k="40" />
    <hkern u1="&#x201d;" u2="d" k="40" />
    <hkern u1="&#x201d;" u2="c" k="40" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201d;" u2="&#x2c;" k="140" />
    <hkern u1="&#x2026;" g2="fl" k="10" />
    <hkern u1="&#x2026;" g2="fi" k="10" />
    <hkern u1="&#x2026;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2026;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2026;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2026;" u2="&#x2019;" k="60" />
    <hkern u1="&#x2026;" u2="&#x2018;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2026;" u2="&#x1ef2;" k="125" />
    <hkern u1="&#x2026;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2026;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2026;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2026;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2026;" u2="&#x1fe;" k="45" />
    <hkern u1="&#x2026;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1fc;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1fa;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2026;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x178;" k="125" />
    <hkern u1="&#x2026;" u2="&#x177;" k="20" />
    <hkern u1="&#x2026;" u2="&#x176;" k="125" />
    <hkern u1="&#x2026;" u2="&#x175;" k="65" />
    <hkern u1="&#x2026;" u2="&#x174;" k="55" />
    <hkern u1="&#x2026;" u2="&#x173;" k="20" />
    <hkern u1="&#x2026;" u2="&#x172;" k="25" />
    <hkern u1="&#x2026;" u2="&#x171;" k="20" />
    <hkern u1="&#x2026;" u2="&#x170;" k="25" />
    <hkern u1="&#x2026;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16e;" k="25" />
    <hkern u1="&#x2026;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16c;" k="25" />
    <hkern u1="&#x2026;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16a;" k="25" />
    <hkern u1="&#x2026;" u2="&#x169;" k="20" />
    <hkern u1="&#x2026;" u2="&#x168;" k="25" />
    <hkern u1="&#x2026;" u2="&#x167;" k="20" />
    <hkern u1="&#x2026;" u2="&#x165;" k="20" />
    <hkern u1="&#x2026;" u2="&#x164;" k="110" />
    <hkern u1="&#x2026;" u2="&#x163;" k="20" />
    <hkern u1="&#x2026;" u2="&#x162;" k="110" />
    <hkern u1="&#x2026;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x153;" k="20" />
    <hkern u1="&#x2026;" u2="&#x152;" k="45" />
    <hkern u1="&#x2026;" u2="&#x151;" k="20" />
    <hkern u1="&#x2026;" u2="&#x150;" k="45" />
    <hkern u1="&#x2026;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x14e;" k="45" />
    <hkern u1="&#x2026;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x14c;" k="45" />
    <hkern u1="&#x2026;" u2="&#x123;" k="20" />
    <hkern u1="&#x2026;" u2="&#x122;" k="45" />
    <hkern u1="&#x2026;" u2="&#x121;" k="20" />
    <hkern u1="&#x2026;" u2="&#x120;" k="45" />
    <hkern u1="&#x2026;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11e;" k="45" />
    <hkern u1="&#x2026;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11c;" k="45" />
    <hkern u1="&#x2026;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2026;" u2="&#x119;" k="20" />
    <hkern u1="&#x2026;" u2="&#x118;" k="20" />
    <hkern u1="&#x2026;" u2="&#x117;" k="20" />
    <hkern u1="&#x2026;" u2="&#x116;" k="20" />
    <hkern u1="&#x2026;" u2="&#x115;" k="20" />
    <hkern u1="&#x2026;" u2="&#x114;" k="20" />
    <hkern u1="&#x2026;" u2="&#x113;" k="20" />
    <hkern u1="&#x2026;" u2="&#x112;" k="20" />
    <hkern u1="&#x2026;" u2="&#x111;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10c;" k="45" />
    <hkern u1="&#x2026;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10a;" k="45" />
    <hkern u1="&#x2026;" u2="&#x109;" k="20" />
    <hkern u1="&#x2026;" u2="&#x108;" k="45" />
    <hkern u1="&#x2026;" u2="&#x107;" k="20" />
    <hkern u1="&#x2026;" u2="&#x106;" k="45" />
    <hkern u1="&#x2026;" u2="&#x105;" k="10" />
    <hkern u1="&#x2026;" u2="&#x104;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x103;" k="10" />
    <hkern u1="&#x2026;" u2="&#x102;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x101;" k="10" />
    <hkern u1="&#x2026;" u2="&#x100;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xff;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2026;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xea;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe5;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe4;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe3;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe1;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe0;" k="10" />
    <hkern u1="&#x2026;" u2="&#xdd;" k="125" />
    <hkern u1="&#x2026;" u2="&#xdc;" k="25" />
    <hkern u1="&#x2026;" u2="&#xdb;" k="25" />
    <hkern u1="&#x2026;" u2="&#xda;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd9;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd8;" k="45" />
    <hkern u1="&#x2026;" u2="&#xd6;" k="45" />
    <hkern u1="&#x2026;" u2="&#xd5;" k="45" />
    <hkern u1="&#x2026;" u2="&#xd4;" k="45" />
    <hkern u1="&#x2026;" u2="&#xd3;" k="45" />
    <hkern u1="&#x2026;" u2="&#xd2;" k="45" />
    <hkern u1="&#x2026;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xca;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc7;" k="45" />
    <hkern u1="&#x2026;" u2="&#xc6;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc5;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc4;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc3;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc2;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc1;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc0;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xab;" k="50" />
    <hkern u1="&#x2026;" u2="&#xa9;" k="45" />
    <hkern u1="&#x2026;" u2="y" k="20" />
    <hkern u1="&#x2026;" u2="x" k="-20" />
    <hkern u1="&#x2026;" u2="w" k="65" />
    <hkern u1="&#x2026;" u2="v" k="65" />
    <hkern u1="&#x2026;" u2="u" k="20" />
    <hkern u1="&#x2026;" u2="t" k="20" />
    <hkern u1="&#x2026;" u2="q" k="20" />
    <hkern u1="&#x2026;" u2="o" k="20" />
    <hkern u1="&#x2026;" u2="g" k="20" />
    <hkern u1="&#x2026;" u2="f" k="10" />
    <hkern u1="&#x2026;" u2="e" k="20" />
    <hkern u1="&#x2026;" u2="d" k="20" />
    <hkern u1="&#x2026;" u2="c" k="20" />
    <hkern u1="&#x2026;" u2="a" k="10" />
    <hkern u1="&#x2026;" u2="Z" k="-30" />
    <hkern u1="&#x2026;" u2="Y" k="125" />
    <hkern u1="&#x2026;" u2="X" k="-30" />
    <hkern u1="&#x2026;" u2="W" k="55" />
    <hkern u1="&#x2026;" u2="V" k="100" />
    <hkern u1="&#x2026;" u2="U" k="25" />
    <hkern u1="&#x2026;" u2="T" k="110" />
    <hkern u1="&#x2026;" u2="S" k="-20" />
    <hkern u1="&#x2026;" u2="Q" k="45" />
    <hkern u1="&#x2026;" u2="O" k="45" />
    <hkern u1="&#x2026;" u2="G" k="45" />
    <hkern u1="&#x2026;" u2="E" k="20" />
    <hkern u1="&#x2026;" u2="C" k="45" />
    <hkern u1="&#x2026;" u2="A" k="-10" />
    <hkern u1="&#x203a;" u2="&#x2026;" k="50" />
    <hkern u1="&#x203a;" u2="&#x2e;" k="50" />
    <hkern u1="&#x203a;" u2="&#x2c;" k="50" />
    <hkern u1="&#x2122;" u2="&#x2026;" k="120" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="120" />
    <hkern u1="&#x2122;" u2="&#x2c;" k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,Tcommaaccent,Tcaron"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="comma,period,ellipsis"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="comma,period,ellipsis"
	k="-15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute"
	k="7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="hyphen,endash,emdash"
	k="75" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="15" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="28" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,period,ellipsis"
	k="45" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-5" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,endash,emdash"
	k="-15" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="7" />
    <hkern g1="K,Kcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-15" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,period,ellipsis"
	k="-60" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="T,Tcommaaccent,Tcaron"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="85" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="comma,period,ellipsis"
	k="-60" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen,endash,emdash"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="-30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteleft,quotedblleft"
	k="110" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteright,quotedblright"
	k="110" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="24" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="32" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="comma,period,ellipsis"
	k="-10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="15" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-15" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="comma,period,ellipsis"
	k="110" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="15" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="hyphen,endash,emdash"
	k="110" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="100" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,ellipsis"
	k="55" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="-18" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,ellipsis"
	k="-30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="120" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="85" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="T,Tcommaaccent,Tcaron"
	k="60" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="-5" />
    <hkern g1="g,q,y,yacute,ydieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,ycircumflex,ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="80" />
    <hkern g1="g,q,y,yacute,ydieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="g,q,y,yacute,ydieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,ycircumflex,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="T,Tcommaaccent,Tcaron"
	k="90" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="k,kcommaaccent"
	g2="comma,period,ellipsis"
	k="-30" />
    <hkern g1="k,kcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen,endash,emdash"
	k="45" />
    <hkern g1="k,kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="-7" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,ellipsis"
	k="115" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="40" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron"
	k="35" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,ellipsis"
	k="65" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="comma,period,ellipsis"
	g2="T,Tcommaaccent,Tcaron"
	k="110" />
    <hkern g1="comma,period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="125" />
    <hkern g1="comma,period,ellipsis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute"
	k="-10" />
    <hkern g1="comma,period,ellipsis"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="45" />
    <hkern g1="comma,period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="65" />
    <hkern g1="comma,period,ellipsis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-20" />
    <hkern g1="comma,period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="comma,period,ellipsis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteleft,quotedblleft"
	k="60" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteright,quotedblright"
	k="60" />
    <hkern g1="comma,period,ellipsis"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="E,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="25" />
    <hkern g1="comma,period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-30" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron"
	k="110" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-15" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="85" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="75" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,ellipsis"
	k="140" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,ellipsis"
	k="140" />
    <hkern g1="quoteright,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
  </font>
</defs></svg>
