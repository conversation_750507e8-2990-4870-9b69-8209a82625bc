<?xml version="1.0" encoding="UTF-8" standalone="no"?><ApplicationsConfig>
	<application>
		<name>project</name>
		<properties>
			<property>
				<name>enovia_link</name>
				<value>https://vdevpril4214dsy.dsone.3ds.com/3DSpace/common/emxNavigator.jsp?objectId=</value>
			</property>
			<property>
				<name>multiselection_limit</name>
				<value>10</value>
			</property>
			<property>
				<name>base_facet_list</name>
				<value>hierarchical_types,ts_state,ts_transition_state,type,current,owner,project_related_organization,related_program,related_project,project_task_tree,task_mgmt_assignee,task_mgmt_critical_task,project_percent_complete,collaborative_space</value>
			</property>
			<property>
				<name>refine_panel_suggest_facet_list</name>
				<value>current,owner,type,vault,project_originator,project_policy,project_related_organization,issue_to_reporting_organization,related_program_related_project,task_mgmt_assignee,task_mgmt_constraint_type,taks_mgmt_critical_task,task_mgmt,task_tree</value>
			</property>
			<property>
				<name>refine_panel_facet_list</name>
				<value>type, current, owner, task_mgmt_assignee, project_related_organization, issue_to_reporting_organization, related_program, related_project, collaborative_space</value>
			</property>
			<property>
				<name>refine_panel_project_facet_list</name>
				<value>type, current, owner, task_mgmt_assignee, project_related_organization, issue_to_reporting_organization, related_program, related_project, collaborative_space, project_space_classification</value>
			</property>
			<property>
				<name>enrich3d_defaults</name>
				<value>{ logicPathMetaName: 'issue_physicalproductpath', pathMetaName: 'physicalproductpath', maxHitsToFetch:100, indexHasPaths: false, transpLayerStyle: { color: 'white', opacity: 0.1 } }</value>
			</property>
		</properties>
		<menu>
			<mainItem id="index">
				<label>${i18n['plma.project.menu.home.page.index']}</label>
				<iconCss>fonticon fonticon-home</iconCss>
				<target>/</target>
				<description>${i18n['plma.project.home.page.description']}</description>
			</mainItem>
			<mainItem id="health_risk">
				<label>${i18n['plma.project.menu.risk']}</label>
				<section>${i18n['plma.project.menu.health']}</section>
				<iconCss>fonticon fonticon-bell</iconCss>
				<target>health_risk</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.risk.page.description']}</description>
			</mainItem>
			<mainItem id="health_issue">
				<label>${i18n['plma.project.menu.issue']}</label>
				<section>${i18n['plma.project.menu.health']}</section>
				<iconCss>fonticon fonticon-record</iconCss>
				<target>health_issue</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.issue.page.description']}</description>
			</mainItem>
			<mainItem id="health_assessment">
				<label>${i18n['plma.project.menu.assessment']}</label>
				<section>${i18n['plma.project.menu.health']}</section>
				<iconCss>fonticon fonticon-doc</iconCss>
				<target>health_assessment</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.assessment.page.description']}</description>
			</mainItem>
			<mainItem id="schedule_task">
				<label>${i18n['plma.project.menu.task']}</label>
				<section>${i18n['plma.project.menu.schedule_performance']}</section>
				<iconCss>fonticon fonticon-files-check</iconCss>
				<target>schedule_task</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.task.page.description']}</description>
			</mainItem>
			<mainItem id="schedule_my_schedule">
				<label>${i18n['plma.project.menu.my_schedule']}</label>
				<section>${i18n['plma.project.menu.schedule_performance']}</section>
				<iconCss>fonticon fonticon-calendar</iconCss>
				<target>schedule_my_schedule</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.schedule.page.description']}</description>
			</mainItem>
			<mainItem id="schedule_project">
				<label>${i18n['plma.project.menu.project']}</label>
				<section>${i18n['plma.project.menu.schedule_performance']}</section>
				<iconCss>fonticon fonticon-folder</iconCss>
				<target>schedule_project</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.project.page.description']}</description>
			</mainItem>
			<mainItem id="schedule_gate">
				<label>${i18n['plma.project.menu.gate_phase']}</label>
				<section>${i18n['plma.project.menu.schedule_performance']}</section>
				<iconCss>fonticon fonticon-publish</iconCss>
				<target>schedule_gate</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.gate.page.description']}</description>
			</mainItem>
			<mainItem id="schedule_deliverable">
				<label>${i18n['plma.project.menu.deliverable']}</label>
				<section>${i18n['plma.project.menu.schedule_performance']}</section>
				<iconCss>fonticon fonticon-box</iconCss>
				<target>schedule_deliverable</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.deliverable.page.description']}</description>
			</mainItem>
			<mainItem id="resource_workload">
				<label>${i18n['plma.project.resource_workload.title']}</label>
				<section>${i18n['plma.project.menu.resources']}</section>
				<iconCss>fonticon fonticon-calendar</iconCss>
				<target>resource_workload</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.workload.page.description']}</description>
			</mainItem>
			<mainItem id="resource_management">
				<label>${i18n['plma.project.page.resource_management.title']}</label>
				<section>${i18n['plma.project.menu.resources']}</section>
				<iconCss>fonticon fonticon-hourglass</iconCss>
				<target>resource_management</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.management.page.description']}</description>
			</mainItem>
			<mainItem id="search">
				<label>${i18n['plma.project.menu.detail']}</label>
				<iconCss>fonticon fonticon-list</iconCss>
				<target>search</target>
				<keepQueryString>true</keepQueryString>
				<description>${i18n['plma.project.detail.page.description']}</description>
			</mainItem>
			<secondaryItem>
				<iconCss>fonticon fonticon-cog</iconCss>
				<onClick>$('.wuid.plma-light-box-pref').trigger('plma:lightbox');</onClick>
				<description>${i18n['plma.navigation.preference.description']}</description>
			</secondaryItem>
			<secondaryItem>
				<iconCss>fonticon fonticon-help</iconCss>
				<onClick>$('.wuid.plma-light-box-doc').trigger('plma:lightbox');</onClick>
				<description>${i18n['plma.project.help.page.description']}</description>
			</secondaryItem>
			<secondaryItem>
				<iconCss>fonticon fonticon-3ds</iconCss>
				<onClick>$('.wuid.plma-light-box-about').trigger('plma:lightbox');</onClick>
				<description>${i18n['plma.navigation.about.description']}</description>
			</secondaryItem>
		</menu>
		<facetDisplays>
			<facetDisplay id="change_originator">
				<icon>fonticon fonticon-user-alt</icon>
			</facetDisplay>
			<facetDisplay id="risk_prob_impact_matrix">
				<categoryDisplay id="1_1">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="1_2">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="1_3">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="1_4">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="1_5">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="2_1">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="2_2">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="2_3">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="2_4">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="2_5">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="3_1">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="3_2">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="3_3">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="3_4">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="3_5">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="4_1">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="4_2">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="4_3">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="4_4">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="4_5">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="5_1">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="5_2">
					<color>#FEE000</color>
				</categoryDisplay>
				<categoryDisplay id="5_3">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="5_4">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="5_5">
					<color>#EA4F37</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_assess_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_finance_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_resource_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_risk_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_schedule_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="overall_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_quality_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_procurement_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_stakeholder_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_communication_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="assessment_scope_status">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="GreenUp">
					<color>#A2E88E</color>
				</categoryDisplay>
				<categoryDisplay id="GreenDown">
					<color>#477738</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FFCE00</color>
				</categoryDisplay>
				<categoryDisplay id="YellowUp">
					<color>#FFE164</color>
				</categoryDisplay>
				<categoryDisplay id="YellowDown">
					<color>#CE9509</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="RedUp">
					<color>#FF8A8A</color>
				</categoryDisplay>
				<categoryDisplay id="RedDown">
					<color>#844138</color>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="current">
				<icon>fonticon-cog</icon>
				<categoryDisplay id="Create">
					<color>#FEE000</color>
					<icon>fonticon fonticon-plus</icon>
				</categoryDisplay>
				<categoryDisplay id="Exists">
					<color>#FEE000</color>
					<icon>fonticon fonticon-asterisk-alt</icon>
				</categoryDisplay>
				<categoryDisplay id="Identified">
					<color>#F564A3</color>
					<icon>fonticon fonticon-mouse-pointer-square</icon>
				</categoryDisplay>
				<categoryDisplay id="Prepare">
					<color>#F564A3</color>
					<icon>fonticon fonticon-flight</icon>
				</categoryDisplay>
				<categoryDisplay id="Assign">
					<color>#FF8A2E</color>
					<icon>fonticon fonticon-user-check</icon>
				</categoryDisplay>
				<categoryDisplay id="Active">
					<color>#009DDB</color>
					<icon>fonticon fonticon-mouse-pointer-check</icon>
				</categoryDisplay>
				<categoryDisplay id="Private">
					<color>#7F7F7F</color>
					<icon>fonticon fonticon-eye-slash</icon>
				</categoryDisplay>
				<categoryDisplay id="IN_WORK">
					<color>#009DDB</color>
					<icon>fonticon fonticon-play</icon>
				</categoryDisplay>
				<categoryDisplay id="In Work">
					<color>#009DDB</color>
					<icon>fonticon fonticon-play</icon>
				</categoryDisplay>
				<categoryDisplay id="FROZEN">
					<color>#8DDEE4</color>
					<icon>fonticon fonticon-stop</icon>
				</categoryDisplay>
				<categoryDisplay id="Implemented">
					<color>#009DDB</color>
					<icon>fonticon fonticon-code</icon>
				</categoryDisplay>
				<categoryDisplay id="Review">
					<color>#9B2C98</color>
					<icon>fonticon fonticon-eye</icon>
				</categoryDisplay>
				<categoryDisplay id="Complete">
					<color>#00AA86</color>
					<icon>fonticon fonticon-check</icon>
				</categoryDisplay>
				<categoryDisplay id="RELEASED">
					<color>#00AA86</color>
					<icon>fonticon fonticon-newspaper</icon>
				</categoryDisplay>
				<categoryDisplay id="Archive">
					<color>#00AA86</color>
					<icon>fonticon fonticon-archive</icon>
				</categoryDisplay>
				<categoryDisplay id="Closed">
					<color>#00AA86</color>
					<icon>fonticon fonticon-archive</icon>
				</categoryDisplay>
				<categoryDisplay id="OBSOLETE">
					<color>#E2D5CC</color>
					<icon>fonticon fonticon-back-in-time</icon>
				</categoryDisplay>
				<categoryDisplay id="Hold">
					<color>#ADADAD</color>
					<icon>fonticon fonticon-pause </icon>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="issue_priority">
				<icon>fonticon-medium</icon>
				<categoryDisplay id="Pre-assigned">
					<color>#00B8DE</color>
					<icon>fonticon-check</icon>
				</categoryDisplay>
				<categoryDisplay id="Low">
					<color>#57B847</color>
					<icon>fonticon-low</icon>
				</categoryDisplay>
				<categoryDisplay id="Medium">
					<color>#E87B00</color>
					<icon>fonticon-medium</icon>
				</categoryDisplay>
				<categoryDisplay id="High">
					<color>#EA4F37</color>
					<icon>fonticon-high</icon>
				</categoryDisplay>
				<categoryDisplay id="Urgent">
					<color>#844138</color>
					<icon>fonticon-bell</icon>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="priority">
				<icon>fonticon-medium</icon>
				<categoryDisplay id="Pre-assigned">
					<color>#00B8DE</color>
					<icon>fonticon-check</icon>
				</categoryDisplay>
				<categoryDisplay id="Low">
					<color>#57B847</color>
					<icon>fonticon-low</icon>
				</categoryDisplay>
				<categoryDisplay id="Medium">
					<color>#E87B00</color>
					<icon>fonticon-medium</icon>
				</categoryDisplay>
				<categoryDisplay id="High">
					<color>#EA4F37</color>
					<icon>fonticon-high</icon>
				</categoryDisplay>
				<categoryDisplay id="Urgent">
					<color>#844138</color>
					<icon>fonticon-bell</icon>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="project_related_organization">
				<icon>fonticon fonticon-network</icon>
			</facetDisplay>
			<facetDisplay id="owner">
				<icon>fonticon fonticon-user</icon>
			</facetDisplay>
			<facetDisplay id="project_originator">
				<icon>fonticon fonticon-user-alt</icon>
			</facetDisplay>
			<facetDisplay id="percent_complete">
				<icon>fonticon fonticon-progress-2</icon>
			</facetDisplay>
			<facetDisplay id="age_days">
				<icon>fonticon fonticon-clock</icon>
			</facetDisplay>
			<facetDisplay id="collaborative_space">
				<icon>fonticon fonticon-drive</icon>
			</facetDisplay>
			<facetDisplay id="type">
				<categoryDisplay id="Program">
					<icon>fonticon fonticon-box</icon>
				</categoryDisplay>
				<categoryDisplay id="Project Space">
					<icon>fonticon fonticon-folder</icon>
				</categoryDisplay>
				<categoryDisplay id="Task">
					<icon>fonticon fonticon-files-check</icon>
				</categoryDisplay>
				<categoryDisplay id="Phase">
					<icon>fonticon fonticon-publish</icon>
				</categoryDisplay>
				<categoryDisplay id="Risk">
					<icon>fonticon fonticon-bell</icon>
					<color>#E87B00</color>
				</categoryDisplay>
				<categoryDisplay id="Issue">
					<icon>fonticon fonticon-issue</icon>
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="Gate">
					<icon>fonticon fonticon-survey</icon>
				</categoryDisplay>
				<categoryDisplay id="Assessment">
					<icon>fonticon fonticon-folder-share</icon>
				</categoryDisplay>
				<categoryDisplay id="Deliverable">
					<icon>fonticon fonticon-doc-log</icon>
				</categoryDisplay>
				<categoryDisplay id="Document">
					<icon>fonticon fonticon-doc-log</icon>
				</categoryDisplay>
				<categoryDisplay id="Milestone">
					<icon>fonticon fonticon-calendar</icon>
				</categoryDisplay>
				<categoryDisplay id="Effort">
					<icon>fonticon fonticon-users-group</icon>
				</categoryDisplay>
			</facetDisplay>
			<facetDisplay id="rpn">
				<categoryDisplay id="Green">
					<color>#57B847</color>
				</categoryDisplay>
				<categoryDisplay id="Red">
					<color>#EA4F37</color>
				</categoryDisplay>
				<categoryDisplay id="Yellow">
					<color>#FEE000</color>
				</categoryDisplay>
			</facetDisplay>
		</facetDisplays>
		<facetRanges>
			<setFacetRange id="weeks">
				<facetRange id="first_week">
        		<label>One week</label>
        		<start>0</start>
        		<end>7</end>
        		<subRanges>
        			<subRange id="1">
        				<label>1 day</label>
		        		<start>0</start>
		        		<end>1</end>
        			</subRange>
        			<subRange id="2">
        				<label>2 days</label>
		        		<start>2</start>
		        		<end>2</end>
        			</subRange>
        			<subRange id="3">
        				<label>3 days</label>
		        		<start>3</start>
		        		<end>3</end>
        			</subRange>
        			<subRange id="4">
        				<label>4 days</label>
		        		<start>4</start>
		        		<end>4</end>
        			</subRange>
        			<subRange id="5">
        				<label>5 days</label>
		        		<start>5</start>
		        		<end>5</end>
        			</subRange>
        			<subRange id="6">
        				<label>6 days</label>
		        		<start>6</start>
		        		<end>6</end>
        			</subRange>
        			<subRange id="7">
        				<label>7 days</label>
		        		<start>7</start>
		        		<end>7</end>
        			</subRange>
        		</subRanges>
        	</facetRange>
        	<facetRange id="second_week">
        		<label>Two weeks</label>
        		<start>8</start>
        		<end>14</end>
        		<subRanges>
        			<subRange id="8">
        				<label>8 days</label>
		        		<start>8</start>
		        		<end>8</end>
        			</subRange>
        			<subRange id="9">
        				<label>9 days</label>
		        		<start>9</start>
		        		<end>9</end>
        			</subRange>
        			<subRange id="10">
        				<label>10 days</label>
		        		<start>10</start>
		        		<end>10</end>
        			</subRange>
        			<subRange id="11">
        				<label>11 days</label>
		        		<start>11</start>
		        		<end>11</end>
        			</subRange>
        			<subRange id="12">
        				<label>12 days</label>
		        		<start>12</start>
		        		<end>12</end>
        			</subRange>
        			<subRange id="13">
        				<label>13 days</label>
		        		<start>13</start>
		        		<end>13</end>
        			</subRange>
        			<subRange id="14">
        				<label>14 days</label>
		        		<start>14</start>
		        		<end>14</end>
        			</subRange>
        		</subRanges>
        	</facetRange>
        	<facetRange id="third_week">
        		<label>Three weeks</label>
        		<start>15</start>
        		<end>21</end>
        		<subRanges>
        			<subRange id="15">
        				<label>15 days</label>
		        		<start>15</start>
		        		<end>15</end>
        			</subRange>
        			<subRange id="16">
        				<label>16 days</label>
		        		<start>16</start>
		        		<end>16</end>
        			</subRange>
        			<subRange id="17">
        				<label>17 days</label>
		        		<start>17</start>
		        		<end>17</end>
        			</subRange>
        			<subRange id="18">
        				<label>18 days</label>
		        		<start>18</start>
		        		<end>18</end>
        			</subRange>
        			<subRange id="19">
        				<label>19 days</label>
		        		<start>19</start>
		        		<end>19</end>
        			</subRange>
        			<subRange id="20">
        				<label>20 days</label>
		        		<start>20</start>
		        		<end>20</end>
        			</subRange>
        			<subRange id="21">
        				<label>21 days</label>
		        		<start>21</start>
		        		<end>21</end>
        			</subRange>
        		</subRanges>
        	</facetRange>
        	<facetRange id="fourth_week">
        		<label>Four weeks</label>
        		<start>22</start>
        		<end>28</end>
        		<subRanges>
        			<subRange id="22">
        				<label>22 days</label>
		        		<start>22</start>
		        		<end>22</end>
        			</subRange>
        			<subRange id="23">
        				<label>23 days</label>
		        		<start>23</start>
		        		<end>23</end>
        			</subRange>
        			<subRange id="24">
        				<label>24 days</label>
		        		<start>24</start>
		        		<end>24</end>
        			</subRange>
        			<subRange id="25">
        				<label>25 days</label>
		        		<start>25</start>
		        		<end>25</end>
        			</subRange>
        			<subRange id="26">
        				<label>26 days</label>
		        		<start>26</start>
		        		<end>26</end>
        			</subRange>
        			<subRange id="27">
        				<label>27 days</label>
		        		<start>27</start>
		        		<end>27</end>
        			</subRange>
        			<subRange id="28">
        				<label>28 days</label>
		        		<start>28</start>
		        		<end>28</end>
        			</subRange>
        		</subRanges>
        	</facetRange>
        	<facetRange id="fifth_week">
        		<label>Five weeks</label>
        		<start>29</start>
        		<end>35</end>
        		<subRanges>
        			<subRange id="29">
        				<label>29 days</label>
		        		<start>29</start>
		        		<end>29</end>
        			</subRange>
        			<subRange id="30">
        				<label>30 days</label>
		        		<start>30</start>
		        		<end>30</end>
        			</subRange>
        			<subRange id="31">
        				<label>31 days</label>
		        		<start>31</start>
		        		<end>31</end>
        			</subRange>
        			<subRange id="32">
        				<label>32 days</label>
		        		<start>32</start>
		        		<end>32</end>
        			</subRange>
        			<subRange id="33">
        				<label>33 days</label>
		        		<start>33</start>
		        		<end>33</end>
        			</subRange>
        			<subRange id="34">
        				<label>34 days</label>
		        		<start>34</start>
		        		<end>34</end>
        			</subRange>
        			<subRange id="35">
        				<label>35 days</label>
		        		<start>35</start>
		        		<end>35</end>
        			</subRange>
        		</subRanges>
        	</facetRange>
        	<facetRange id="sixth_week">
        		<label>Six weeks</label>
        		<start>36</start>
        		<end>42</end>
        		<subRanges>
        			<subRange id="36">
        				<label>36 days</label>
		        		<start>36</start>
		        		<end>36</end>
        			</subRange>
        			<subRange id="37">
        				<label>37 days</label>
		        		<start>37</start>
		        		<end>37</end>
        			</subRange>
        			<subRange id="38">
        				<label>38 days</label>
		        		<start>38</start>
		        		<end>38</end>
        			</subRange>
        			<subRange id="39">
        				<label>39 days</label>
		        		<start>39</start>
		        		<end>39</end>
        			</subRange>
        			<subRange id="40">
        				<label>40 days</label>
		        		<start>40</start>
		        		<end>40</end>
        			</subRange>
        			<subRange id="41">
        				<label>41 days</label>
		        		<start>41</start>
		        		<end>41</end>
        			</subRange>
        			<subRange id="42">
        				<label>42 days</label>
		        		<start>42</start>
		        		<end>42</end>
        			</subRange>
        		</subRanges>
        	</facetRange>
        	<facetRange id="more_than_six_week">
        		<label>More than six weeks</label>
        		<start>43</start>
        		<end>40000</end>
        		<subRanges>
        			<subRange id="43">
        				<label>43 days</label>
		        		<start>43</start>
		        		<end>43</end>
        			</subRange>
        			<subRange id="44">
        				<label>44 days</label>
		        		<start>44</start>
		        		<end>44</end>
        			</subRange>
        			<subRange id="45">
        				<label>45 days</label>
		        		<start>45</start>
		        		<end>45</end>
        			</subRange>
        			<subRange id="46">
        				<label>46 days</label>
		        		<start>46</start>
		        		<end>46</end>
        			</subRange>
        			<subRange id="47">
        				<label>47 days</label>
		        		<start>47</start>
		        		<end>47</end>
        			</subRange>
        			<subRange id="48">
        				<label>48 days</label>
		        		<start>48</start>
		        		<end>48</end>
        			</subRange>
        			<subRange id="more">
        				<label>More</label>
		        		<start>49</start>
		        		<end>40000</end>
        			</subRange>
        		</subRanges>
        	</facetRange>
			</setFacetRange>
			<setFacetRange id="late">
				<facetRange id="late">
					<label>Late</label>
					<start>1</start>
					<end>1000000</end>
				</facetRange>
				<facetRange id="early">
					<label>Early</label>
					<start>-1000000</start>
					<end>0</end>
				</facetRange>
			</setFacetRange>
			<setFacetRange id="lateness">
				<facetRange id="late_more">
					<label>Late by more than 2 weeks</label>
					<start>15</start>
					<end>10000</end>
				</facetRange>
				<facetRange id="late_2">
					<label>Late by 2 Weeks</label>
					<start>8</start>
					<end>14</end>
				</facetRange>
				<facetRange id="late_1">
					<label>Late by 1 Week</label>
					<start>1</start>
					<end>7</end>
				</facetRange>
				<facetRange id="time">
					<label>On Time</label>
					<start>0</start>
					<end>0</end>
				</facetRange>
				<facetRange id="early_1">
					<label>Early by 1 Week</label>
					<start>-7</start>
					<end>-1</end>
				</facetRange>
				<facetRange id="early_2">
					<label>Early by 2 Weeks</label>
					<start>-14</start>
					<end>-8</end>
				</facetRange>
				<facetRange id="early_more">
					<label>Early by more than 2 weeks</label>
					<start>-10000</start>
					<end>-15</end>
				</facetRange>
			</setFacetRange>
		</facetRanges>
		<refineRanges>
			<refineRange id="day">
				<label>plma.date.range.day</label>
				<fromNb>1</fromNb>
				<fromUnit>day</fromUnit>
				<toNb>0</toNb>
				<toUnit>day</toUnit>
			</refineRange>
			<refineRange id="week">
				<label>plma.date.range.week</label>
				<fromNb>1</fromNb>
				<fromUnit>week</fromUnit>
				<toNb>0</toNb>
				<toUnit>day</toUnit>
			</refineRange>
			<refineRange id="month">
				<label>plma.date.range.month</label>
				<fromNb>1</fromNb>
				<fromUnit>month</fromUnit>
				<toNb>0</toNb>
				<toUnit>day</toUnit>
			</refineRange>
			<refineRange id="year">
				<label>plma.date.range.year</label>
				<fromNb>1</fromNb>
				<fromUnit>year</fromUnit>
				<toNb>0</toNb>
				<toUnit>day</toUnit>
			</refineRange>
		</refineRanges>
		<channels>
			<channel id="DS/PADUtils/PADCommandProxy/select">
				<topic>DS/PADUtils/PADCommandProxy/select</topic>
				<data>
					{
						'${entry.metas['url']}': {
							physicalId: '${entry.metas['physicalid']}',
							rawPaths: [
								${foreach path in entry.metas['physicalproductpath']}
									'${path}',
								${/foreach}
							]
						}
					}
				</data>
				<normalizer>
					function (selectedHits, data) {
						return SelectChannelHelper.normalizer({
							selectedHits: selectedHits,
							data: data,
							reframe: true
						});
					}
				</normalizer>
				<denormalizer>
					function (message, data) {
						var matches = SelectChannelHelper.denormalizer({
							message: message,
							data: data
						});
						SelectChannelHelper.searchIfNotLoaded(matches, false, 'issue_physicalproductpath');
						return matches.hitIds;
					}
				</denormalizer>
			</channel>
			<channel id="DS/Object/select">
				<topic>DS/Object/select</topic>
				<data>
					{
						'${entry.metas['url']}': {
							physicalId: '${entry.metas['physicalid']}'
						}
					}
				</data>
				<normalizer>
					function (selectedHits, data) {
						return ObjectChannelHelper.normalizer({
							selectedHits: selectedHits,
							data: data
						});
					}
				</normalizer>
				<denormalizer>
					function (message, data) {
						var matches = ObjectChannelHelper.denormalizer({
							message: message,
							data: data
						});
						ObjectChannelHelper.searchIfNotLoaded(matches, false, message);
						return matches.hitIds;
					}
				</denormalizer>
			</channel>
		</channels>

		<!-- Reusable Constant information of the fields(Meta/Facet). It will be reused by HitDetails/ResultList configs -->
		<fieldDefList>
			<fieldDef id="title" isFacet="false">
				<label>${i18n['meta_title']}</label>
				<meta>title</meta>
				<type>String</type>
				<sortField>title</sortField>
				<sortLabel>${i18n['plma.sort.name']}</sortLabel>
				<sortIcon>fonticon fonticon-text,fonticon-sort-alpha-asc,fonticon-sort-alpha-desc</sortIcon>
			</fieldDef>
			<fieldDef id="type" isFacet="true">
				<label>${i18n['facet_Top/classproperties/type']}</label>
				<meta>type</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="name" isFacet="false" isShown="true" order="3">
				<label>${i18n['meta_name']}</label>
				<meta>name</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="age_days" isFacet="false">
				<label>${i18n['meta_age_days']}</label>
				<meta>age_days</meta>
				<type>Number</type>
				<sortField>opened_days</sortField>
				<sortLabel>${i18n['plma.sort.age']}</sortLabel>
				<sortIcon>fonticon fonticon-clock,fonticon-sort-num-asc,fonticon-sort-num-desc</sortIcon>
			</fieldDef>
			<fieldDef id="estimated_duration" isFacet="false">
				<label>${i18n['meta_estimated_duration']}</label>
				<meta>estimated_duration</meta>
				<type>Number</type>
				<sortField>project_estimated_duration</sortField>
				<sortLabel>${i18n['plma.sort.est_duration']}</sortLabel>
				<sortIcon>fonticon fonticon-clock,fonticon-sort-num-asc,fonticon-sort-num-desc</sortIcon>
			</fieldDef>
			<fieldDef id="collaborative_space" isFacet="true">
				<label>${i18n['facet_Top/classproperties/collaborative_space']}</label>
				<meta>collaborative_space</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="owner" isFacet="true">
				<label>${i18n['facet_Top/classproperties/owner']}</label>
				<meta>owner</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="current" isFacet="true">
				<label>${i18n['facet_Top/classproperties/current']}</label>
				<meta>current</meta>
				<type>String</type>
				<sortField>document_current</sortField>
				<sortLabel>${i18n['plma.sort.state']}</sortLabel>
				<sortIcon>fonticon fonticon-tag,fonticon-sort-alpha-asc,fonticon-sort-alpha-desc</sortIcon>
			</fieldDef>
			<fieldDef id="id" isFacet="false">
				<label>${i18n['meta_id']}</label>
				<meta>id</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="vault" isFacet="false">
				<label>${i18n['meta_vault']}</label>
				<meta>vault</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assignee" isFacet="false">
				<label>${i18n['meta_assignee']}</label>
				<meta>assignee</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="planned_finish_date" isFacet="false">
				<label>${i18n['meta_planned_finish_date']}</label>
				<meta>planned_finish_date</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="planned_duration" isFacet="false">
				<label>${i18n['meta_planned_duration']}</label>
				<meta>planned_duration</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="actual_finish_date" isFacet="false">
				<label>${i18n['meta_actual_finish_date']}</label>
				<meta>actual_finish_date</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="actual_duration" isFacet="false">
				<label>${i18n['meta_actual_duration']}</label>
				<meta>actual_duration</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="estimated_finish_date" isFacet="false">
				<label>${i18n['meta_estimated_finish_date']}</label>
				<meta>estimated_finish_date</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="estimated_duration" isFacet="false">
				<label>${i18n['meta_estimated_duration']}</label>
				<meta>estimated_duration</meta>
				<type>String</type>
				<sortField>project_estimated_duration</sortField>
				<sortLabel>${i18n['plma.sort.est_duration']}</sortLabel>
				<sortIcon>fonticon fonticon-clock,fonticon-sort-num-asc,fonticon-sort-num-desc</sortIcon>
			</fieldDef>

			<fieldDef id="related_program" isFacet="true">
				<label>${i18n['meta_related_program']}</label>
				<meta>related_program</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="related_project" isFacet="true">
				<label>${i18n['meta_related_project']}</label>
				<meta>related_project</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="project_related_organization" isFacet="true">
				<label>${i18n['meta_related_organization']}</label>
				<meta>project_related_organization</meta>
				<type>String</type>
			</fieldDef>

			<fieldDef id="problem_type" isFacet="false">
				<label>${i18n['meta_problem_type']}</label>
				<meta>problem_type</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="issue_category" isFacet="false">
				<label>${i18n['meta_issue_category']}</label>
				<meta>issue_category</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="classification" isFacet="false">
				<label>${i18n['meta_classification']}</label>
				<meta>classification</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="issue_priority" isFacet="true">
				<label>${i18n['facet_top/classproperties/issue/priority']}</label>
				<meta>issue_priority</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="to_assigned_issue" isFacet="true">
				<label>${i18n['facet_Top/classproperties/issue/to_assigned_issue']}</label>
				<meta>issue_to_assigned_issue</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="issue_reporting_organization" isFacet="true">
				<label>${i18n['meta_reporting_organization']}</label>
				<meta>issue_reporting_organization</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="issue_part_name" isFacet="true">
				<label>${i18n['meta_part_name']}</label>
				<meta>issue_part_name</meta>
				<type>String</type>
			</fieldDef>

			<fieldDef id="week_ending_date" isFacet="false">
				<label>${i18n['meta_week_ending_date']}</label>
				<meta>week_ending_date</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="total_effort" isFacet="false">
				<label>${i18n['meta_total_effort']}</label>
				<meta>total_effort</meta>
				<type>String</type>
			</fieldDef>

			<fieldDef id="risk_impact" isFacet="true">
				<label>${i18n['facet_Top/classproperties/risk/impact']}</label>
				<meta>risk_impact</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="risk_probability" isFacet="true">
				<label>${i18n['facet_Top/classproperties/risk/probability']}</label>
				<meta>risk_probability</meta>
				<type>String</type>
			</fieldDef>

			<fieldDef id="assessment_assess_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/assess_status']}</label>
				<meta>assessment_assess_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_schedule_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/schedule_status']}</label>
				<meta>assessment_schedule_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_risk_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/risk_status']}</label>
				<meta>assessment_risk_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_resource_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/resource_status']}</label>
				<meta>assessment_resource_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_finance_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/finance_status']}</label>
				<meta>assessment_finance_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_quality_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/quality_status']}</label>
				<meta>assessment_quality_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_procurement_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/procurement_status']}</label>
				<meta>assessment_procurement_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_stakeholder_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/stakeholder_status']}</label>
				<meta>assessment_stakeholder_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_communication_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/communication_status']}</label>
				<meta>assessment_communication_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="assessment_scope_status" isFacet="true">
				<label>${i18n['facet_top/classproperties/assessment/scope_status']}</label>
				<meta>assessment_scope_status</meta>
				<type>String</type>
			</fieldDef>
			<fieldDef id="relevance" isFacet="false">
				<sortField>text_relevance</sortField> <!-- Not to be changed -->
				<sortLabel>${i18n['meta_relevance']}</sortLabel>
				<sortIcon>fonticon-sync,,</sortIcon>
			</fieldDef>
		</fieldDefList>

		<resultList id="rl-001">
			<label>${i18n['plma.preferences.resultlist.default.label']}</label>
			<description>${i18n['plma.preferences.resultlist.default.description']}</description>
			<columns>
				<column id="relevance" isShown="false"/>
				<column id="title" isShown="titleConfig" order="1"/>
				<column id="type" isShown="true" order="2"/>
				<column id="name" isShown="true" order="3"/>
				<column id="age_days" isShown="ifNotEmpty" order="4"/>
				<column id="estimated_duration" isShown="ifNotEmpty" order="5"/>
				<column id="collaborative_space" isShown="true" order="6"/>
				<column id="owner" isShown="true" order="7"/>
				<column id="current" isShown="true" order="8"/>
			</columns>
		</resultList>
		<resultList id="rl-002">
			<label>${i18n['plma.preferences.resultlist.compare.label']}</label>
			<description>${i18n['plma.preferences.resultlist.compare.description']}</description>
			<columns>
				<column id="title" isShown="titleConfig" order="1"/>
				<column id="type" isShown="true" order="2"/>
				<column id="name" isShown="true" order="3"/>
				<column id="age_days" isShown="ifNotEmpty" order="4"/>
				<column id="estimated_duration" isShown="ifNotEmpty" order="5"/>
				<column id="collaborative_space" isShown="true" order="6"/>
				<column id="owner" isShown="true" order="7"/>
				<column id="current" isShown="true" order="8"/>
			</columns>
		</resultList>

		<hitDetails id="hd-001">
			<label>Default View</label>
			<description>Default HitDetails View</description>

			<fieldGroup id="fg-001" order="0">
				<label>Details</label>
				<icon>fonticon-doc-text</icon>
				<description>Basic Details.</description>
			</fieldGroup>
			<field groupId="fg-001" id="name" order="0"/>
			<field groupId="fg-001" id="current" order="1"/>
			<field groupId="fg-001" id="type" order="2">
				<displayTemplate>
					<![CDATA[ <span style="color: #3d3d3d; background: #F2F5F7; padding: 2px 10px;">${entry.metas['type']}</span> ]]>
				</displayTemplate>
			</field>
			<field groupId="fg-001" id="related_program" order="3"/>
			<field groupId="fg-001" id="related_project" order="4"/>

			<fieldGroup id="fg-002" order="1">
				<icon>fonticon-3ds-where</icon>
				<label>Source</label>
				<description>Document Source Details.</description>
			</fieldGroup>
			<field groupId="fg-002" id="id" order="0"/>
			<field groupId="fg-002" id="vault" order="1"/>

			<fieldGroup id="fg-003" order="2">
				<icon>fonticon-3ds-who</icon>
				<label>People</label>
				<description>Information of the Owner, assignee etc for this Document.</description>
			</fieldGroup>
			<field groupId="fg-003" id="assignee" order="0"/>
			<field groupId="fg-003" id="owner" order="1"/>
			<field groupId="fg-003" id="project_related_organization" order="2">
				<displayCondition>
					<!-- Display if not empty -->
					<expr>${str:length(entry.metas['project_related_organization']) &gt; 0}</expr>
				</displayCondition>
			</field>

			<fieldGroup id="fg-004" order="3">
				<icon>fonticon-clock</icon>
				<label>Schedule</label>
				<description>Schedule Details for current Document.</description>
			</fieldGroup>
			<field groupId="fg-004" id="planned_finish_date" order="0"/>
			<field groupId="fg-004" id="planned_duration" order="1"/>
			<field groupId="fg-004" id="actual_finish_date" order="2"/>
			<field groupId="fg-004" id="actual_duration" order="3"/>
			<field groupId="fg-004" id="estimated_finish_date" order="4"/>
			<field groupId="fg-004" id="estimated_duration" order="5"/>

			<fieldGroup id="fg-005" order="4">
				<icon>fonticon-issue</icon>
				<label>Issue Details</label>
				<description>Details of Issue Object</description>
				<displayCondition>
					<!-- Display if not empty -->
					<expr>${entry.metas['type']=='Issue'}</expr>
				</displayCondition>
			</fieldGroup>
			<field groupId="fg-005" id="problem_type" order="0"/>
			<field groupId="fg-005" id="issue_category" order="1"/>
			<field groupId="fg-005" id="classification" order="2"/>
			<field groupId="fg-005" id="issue_priority" order="3"/>
			<field groupId="fg-005" id="to_assigned_issue" order="4"/>
			<field groupId="fg-005" id="issue_reporting_organization" order="5"/>
			<field groupId="fg-005" id="issue_part_name" order="6"/>

			<fieldGroup id="fg-006" order="5">
				<icon>fonticon-users-group</icon>
				<label>Efforts</label>
				<description>Details of Efforts Object</description>
				<displayCondition>
					<!-- Display if not empty -->
					<expr>${entry.metas['type']=='Effort'}</expr>
				</displayCondition>
			</fieldGroup>
			<field groupId="fg-006" id="week_ending_date" order="0"/>
			<field groupId="fg-006" id="total_effort" order="1"/>

			<fieldGroup id="fg-007" order="6">
				<icon>fonticon-folder-share</icon>
				<label>Assessment</label>
				<description>Details of Assessment Object</description>
				<displayCondition>
					<!-- Display if not empty -->
					<expr>${entry.metas['type']=='Assessment'}</expr>
				</displayCondition>
			</fieldGroup>
			<field groupId="fg-007" id="assessment_assess_status" order="0">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_assess_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_assess_status", entry.facets["assessment_assess_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_schedule_status" order="1">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_schedule_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_schedule_status", entry.facets["assessment_schedule_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_risk_status" order="2">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_risk_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_risk_status", entry.facets["assessment_risk_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_resource_status" order="3">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_resource_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_resource_status", entry.facets["assessment_resource_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_finance_status" order="4">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_finance_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_finance_status", entry.facets["assessment_finance_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_quality_status" order="5">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_quality_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_quality_status", entry.facets["assessment_quality_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_procurement_status" order="6">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_procurement_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_procurement_status", entry.facets["assessment_procurement_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_stakeholder_status" order="7">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_stakeholder_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_stakeholder_status", entry.facets["assessment_stakeholder_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_communication_status" order="8">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_communication_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_communication_status", entry.facets["assessment_communication_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>
			<field groupId="fg-007" id="assessment_scope_status" order="9">
				<displayTemplate><![CDATA[
					${entry.facets['assessment_scope_status'].all}
					<i style='color:${app:getCategoryColor("project", "project", "assessment_scope_status", entry.facets["assessment_scope_status"].leaves[0].description)}' class="fonticon fonticon-record"></i>
				]]></displayTemplate>
			</field>

			<fieldGroup id="fg-008" order="7">
				<icon>fonticon-bell</icon>
				<label>Risk</label>
				<description>Details of Risk Object</description>
				<displayCondition>
					<!-- Display if not empty -->
					<expr>${entry.metas['type']=='Risk'}</expr>
				</displayCondition>
			</fieldGroup>
			<field groupId="fg-008" id="risk_impact" order="0"/>
			<field groupId="fg-008" id="risk_probability" order="1"/>

			<fieldGroup id="fg-009" order="8">
				<icon>fonticon-trash</icon>
				<label>Hidden</label>
				<description>Container for Hidden Fields.</description>
				<displayCondition>
					<expr>false</expr> <!-- Never Display this group -->
				</displayCondition>
			</fieldGroup>
		</hitDetails>

		<!-- refinesPanel configurations
		displayCondition/exp:
		      you can use ${pageName} for pageName based condition
		aggr:
		      This attribute enables which facet aggr to display. Default=count.
		      If empty string aggr="" then no agg displayed.
		order:
			  This attribute can be specified to order the refineBy. Default=0
		defaultView:
			  This attribute modifies facet's default view.
			  Views are list, pie, and column. Default=list.
		enableSort:
			  This attribute enables sorting option(count and alphabet). Default=false.
		Others(for type=facet) with default values
			  isDisjunctive=false, displayExclude=true, sortMode=default, iterMode=all, nbSubFacets=0, drillDown=false
	  	-->
		<refinesPanel id="rp-001">
			<refineBy id="resource_fte_monthofyear" order="11" type="date">
				<displayCondition>
					<expr>${str:contains('resource_management', ${pageName})}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="issue_actual_start_date" order="12" type="date">
				<displayCondition>
					<expr>${str:contains('health_issue', ${pageName})}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="issue_actual_end_date" order="13" type="date">
				<displayCondition>
					<expr>${str:contains('health_issue', ${pageName})}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="project_actual_start_date" order="14" type="date">
				<displayCondition>
					<expr>${str:contains('health_issue,health_assessment,schedule_deliverable,resource_management', ${pageName}) == false}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="project_actual_finish_date" order="15" type="date">
				<displayCondition>
					<expr>${str:contains('health_issue,health_assessment,schedule_deliverable,resource_management', ${pageName}) == false}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="project_estimated_start_date" order="16" type="date">
				<displayCondition>
					<expr>${str:contains('health_assessment', ${pageName}) == false}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="project_estimated_finish_date" order="17" type="date">
				<displayCondition>
					<expr>${str:contains('health_assessment', ${pageName}) == false}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="issue_priority" order="18" type="facet">
				<displayCondition>
					<expr>${str:contains('health_issue', ${pageName})}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="rpn" max="25" min="0" order="19" step="1" type="range">
				<displayCondition>
					<expr>${str:contains('health_risk', ${pageName})}</expr>
				</displayCondition>
			</refineBy>
			<refineBy id="type" order="20" type="facet"/>
			<refineBy id="current" order="21" type="facet"/>
			<refineBy id="owner" order="22" type="facet"/>
			<refineBy id="related_program" order="23" type="facet"/>
			<refineBy id="related_project" order="24" type="facet"/>
			<refineBy id="collaborative_space" order="25" type="facet"/>
			<refineBy id="task_mgmt_assignee" order="26" type="facet"/>
			<refineBy id="project_related_organization" order="27" type="facet"/>
			<refineBy id="issue_to_reporting_organization" order="28" type="facet"/>
			<refineBy id="project_space_classification" order="29" type="facet">
				<displayCondition>
					<expr>${str:contains('schedule_project', ${pageName})}</expr>
				</displayCondition>
			</refineBy>
		</refinesPanel>
	</application>
</ApplicationsConfig>