<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Thu Feb  7 11:21:26 2019
 By Aleksey,,,
Copyright (c) 2010 by Dassault Systmes Company. All rights reserved.
</metadata>
<defs>
<font id="3ds-Light" horiz-adv-x="605" >
  <font-face 
    font-family="3ds Light"
    font-weight="300"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 3 2 0 0 2 0 4"
    ascent="800"
    descent="-200"
    x-height="479"
    cap-height="665"
    bbox="-30 -232 1182 1021"
    underline-thickness="50"
    underline-position="-150"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="623" 
d="M507 665v-665h-391v665h391zM176 53h271v559h-271v-559z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="559" 
d="M121 479v91q0 79 49 121.5t134 42.5q43 0 80.5 -6t60.5 -12l-10 -49q-23 6 -58.5 11.5t-70.5 5.5q-61 0 -94.5 -26.5t-33.5 -87.5v-91h282v-479h-57v433h-225v-433h-57v433h-81v46h81z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="558" 
d="M121 479v91q0 47 15 78.5t41 50.5t61 27t75 8q36 0 74.5 -6.5t71.5 -14.5v-713h-56v674q-20 4 -44 7t-46 3q-28 0 -52.5 -5t-43 -17.5t-29 -35t-10.5 -56.5v-91h141v-46h-141v-433h-57v433h-81v46h81z" />
    <glyph glyph-name=".notdef" horiz-adv-x="623" 
d="M507 665v-665h-391v665h391zM176 53h271v559h-271v-559z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="255" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="255" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="298" 
d="M179 665l-4 -515h-52l-4 515h60zM150 71q18 0 29 -10t11 -29t-11 -29.5t-29 -10.5q-19 0 -30.5 10.5t-11.5 29.5t11.5 29t30.5 10z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="450" 
d="M178 665l-7 -213h-46l-6 213h59zM331 665l-7 -213h-46l-6 213h59z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="715" 
d="M339 665l-41 -205h168l41 205h57l-40 -205h146l-10 -49h-146l-30 -156h146l-10 -49h-146l-39 -206h-58l40 206h-168l-40 -206h-57l39 206h-146l9 49h147l30 156h-147l10 49h147l40 205h58zM258 255h169l30 156h-169z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M286 19q-27 1 -55.5 4.5t-55.5 9t-50.5 12.5t-39.5 15l13 50q35 -14 89.5 -26t111.5 -12q32 0 59.5 3t48 12.5t32 26.5t11.5 45q0 36 -26.5 63t-66 50.5t-86 46t-86 48t-66 57.5t-26.5 75q0 40 15.5 66t41.5 41.5t60.5 22t73.5 8.5v93h54v-93q37 -1 77.5 -5t65.5 -9
l-8 -54q-13 3 -32.5 5.5t-41.5 5t-44 3.5t-40 1q-30 0 -59 -2.5t-51.5 -10.5t-36 -24t-13.5 -43q0 -31 26.5 -56.5t66.5 -48.5t86.5 -46t86.5 -50t66.5 -60.5t26.5 -77.5q0 -41 -14 -67t-38 -42.5t-55.5 -24t-66.5 -10.5v-106h-54v104z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="860" 
d="M209 632q-19 0 -36.5 -6t-30.5 -20.5t-20.5 -39.5t-7.5 -63t7.5 -63t20.5 -40t30.5 -21.5t36.5 -6.5q18 0 35 6t29.5 21t20 40t7.5 63q0 39 -7.5 64t-20 39.5t-29.5 20.5t-35 6zM209 676q32 0 59.5 -9t47.5 -30t31 -54t11 -81q0 -47 -11 -80t-31 -54t-47.5 -31t-59.5 -10
q-33 0 -60.5 10t-47.5 31t-31 54.5t-11 80.5q0 48 11 81t31 53.5t47.5 29.5t60.5 9zM652 297q-19 0 -36 -6t-30 -20.5t-20.5 -39.5t-7.5 -63t7.5 -63t20.5 -40t30 -21.5t36 -6.5t35.5 6t29.5 21t20.5 40t7.5 63q0 39 -7.5 64t-20.5 39.5t-29.5 20.5t-35.5 6zM652 341
q32 0 59.5 -9t47.5 -30t31 -54t11 -81q0 -47 -11 -80t-31 -54t-47.5 -31t-59.5 -10q-33 0 -60.5 10t-47.5 31t-31 54.5t-11 80.5q0 48 11 81t31 53.5t47.5 29.5t60.5 9zM676 673l-434 -673h-65l433 673h66z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="590" 
d="M467 150q22 23 37.5 53t27.5 62l46 -18q-14 -40 -32.5 -74.5t-46.5 -65.5l91 -107h-70l-58 66q-37 -35 -86.5 -55.5t-112.5 -20.5q-100 0 -154.5 45.5t-54.5 130.5q0 36 10.5 65t30.5 53t47.5 44t62.5 39q-16 18 -33 37t-30.5 39.5t-22.5 43.5t-9 50q0 68 43 103.5
t112 35.5q34 0 64 -8.5t51.5 -25t34 -41.5t12.5 -58q0 -31 -10.5 -58t-28.5 -50.5t-42 -43.5t-52 -38zM244 324q-33 -19 -57 -35t-40.5 -33t-24.5 -38t-8 -50q0 -36 11 -60t31 -39t48 -21t63 -6q51 0 91.5 19t72.5 46zM258 396q49 32 78.5 64t29.5 81q0 22 -8 37.5t-22 25
t-32 14t-37 4.5q-18 0 -35.5 -4t-31 -13t-21.5 -24.5t-8 -39.5q0 -38 26 -71.5t61 -73.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="297" 
d="M178 665l-7 -213h-46l-6 213h59z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="306" 
d="M296 730q-38 -47 -68.5 -95.5t-53 -104t-34.5 -119.5t-12 -141t12 -141t34.5 -119t53 -103.5t68.5 -94.5h-64q-81 88 -123.5 207t-42.5 251t42.5 252t123.5 208h64z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="306" 
d="M74 730q81 -88 123.5 -208t42.5 -252t-42.5 -251t-123.5 -207h-64q37 46 68 94.5t53.5 103.5t34.5 119t12 141t-12 141t-34.5 119.5t-53.5 104t-68 95.5h64z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="418" 
d="M186 547v118h46v-118l129 40l15 -42l-126 -36l82 -91l-35 -31l-88 100l-88 -100l-35 31l82 91l-126 36l15 42z" />
    <glyph glyph-name="plus" unicode="+" 
d="M278 342v181h48v-181h205v-45h-205v-182h-48v182h-204v45h204z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="275" 
d="M168 68l-90 -201h-54l82 201h62z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="385" 
d="M319 298v-54h-253v54h253z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="274" 
d="M137 71q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="479" 
d="M451 730l-356 -783h-57l354 783h59z" />
    <glyph glyph-name="zero" unicode="0" 
d="M537 333q0 -93 -14 -158t-43.5 -106t-73.5 -59.5t-104 -18.5t-104 18.5t-73.5 59.5t-43.5 106t-14 158q0 92 14 157t43.5 106t73.5 60t104 19t104 -18.5t73.5 -59.5t43.5 -106t14 -158zM474 333q0 81 -9.5 136t-30.5 89t-53.5 49t-78.5 15t-78.5 -15t-53.5 -49t-30.5 -89
t-9.5 -136t9.5 -136t30.5 -89.5t53.5 -49.5t78.5 -15t78.5 15t53.5 49.5t30.5 89.5t9.5 136z" />
    <glyph glyph-name="one" unicode="1" 
d="M312 665h49v-665h-61v607l-154 -42v55z" />
    <glyph glyph-name="two" unicode="2" 
d="M88 656q29 8 75.5 13.5t89.5 5.5q45 0 87 -8t73.5 -28t50.5 -54t19 -86q0 -44 -13.5 -80t-37 -68.5t-54 -63.5t-63.5 -63l-177 -171h350v-53h-426v49l213 216q32 33 59 61.5t46 56t29.5 56t10.5 61.5q0 36 -13.5 59.5t-36 37.5t-53 19.5t-63.5 5.5q-41 0 -84.5 -6
t-75.5 -13z" />
    <glyph glyph-name="three" unicode="3" 
d="M483 665v-44l-220 -235h5q44 0 85.5 -10.5t73.5 -33t51.5 -58t19.5 -86.5q0 -61 -20 -101t-54 -64t-80.5 -33.5t-98.5 -9.5q-44 0 -89.5 5t-87.5 15l6 51q39 -8 84.5 -13.5t87.5 -5.5q44 0 79 7.5t59.5 25.5t37.5 47.5t13 73.5q0 42 -15.5 69t-41 42.5t-58 21.5t-65.5 6
h-67v46l213 231h-325v53h407z" />
    <glyph glyph-name="four" unicode="4" 
d="M455 665v-439h98v-49h-98v-177h-59v177h-364v41l339 447h84zM396 226v384l-291 -384h291z" />
    <glyph glyph-name="five" unicode="5" 
d="M104 61q45 -9 83 -14t80 -5q39 0 72 6.5t56.5 24.5t36.5 49t13 80q0 47 -12 77t-34 47.5t-53.5 24t-70.5 6.5q-16 0 -35.5 -1.5t-39 -3.5t-38.5 -5t-34 -6l-23 23l27 301h355v-54h-303l-18 -211q8 1 21 3t28 3.5t31 2.5t31 1q54 0 96.5 -10.5t72.5 -35t46 -64t16 -97.5
q0 -62 -18 -103t-49.5 -65.5t-76 -34.5t-96.5 -10q-44 0 -84.5 4.5t-85.5 15.5l6 51v0z" />
    <glyph glyph-name="six" unicode="6" 
d="M499 606q-34 7 -73.5 11.5t-77.5 4.5q-47 0 -84 -13t-63 -42.5t-40 -78t-14 -119.5q37 11 81.5 19t92.5 8q100 0 156.5 -43t56.5 -147q0 -49 -14.5 -89t-42 -68t-67.5 -43.5t-90 -15.5q-60 0 -105 19t-74 59.5t-43.5 105t-14.5 154.5q0 99 18 165.5t52.5 107t83.5 57.5
t112 17q42 0 83 -4.5t75 -10.5zM147 313q0 -46 5 -94t23 -87.5t52.5 -64.5t92.5 -25q69 0 110.5 43.5t41.5 119.5q0 77 -37.5 107.5t-115.5 30.5q-39 0 -84.5 -8t-87.5 -22z" />
    <glyph glyph-name="seven" unicode="7" 
d="M520 665v-44l-329 -621h-64l322 612h-365v53h436z" />
    <glyph glyph-name="eight" unicode="8" 
d="M302 316q-79 0 -123.5 -32t-44.5 -109q0 -64 44.5 -98.5t123.5 -34.5t123.5 34.5t44.5 98.5q0 77 -44.5 109t-123.5 32zM302 -10q-114 0 -172.5 51t-58.5 134q0 39 10.5 68.5t27.5 50.5t40 33.5t48 17.5q-112 35 -112 159q0 40 15.5 71.5t44 53.5t68.5 34t89 12t89 -12
t68.5 -34t44 -53.5t15.5 -71.5q0 -124 -112 -159q25 -5 48 -17.5t40 -33.5t27.5 -50.5t10.5 -68.5q0 -83 -58.5 -134t-172.5 -51zM302 376q71 0 112.5 30t41.5 96q0 57 -40.5 88.5t-113.5 31.5t-113.5 -31.5t-40.5 -88.5q0 -66 41.5 -96t112.5 -30z" />
    <glyph glyph-name="nine" unicode="9" 
d="M106 60q34 -8 71 -13t70 -5q57 0 97 17.5t65 50t36.5 80t12.5 107.5q-37 -11 -81.5 -19t-92.5 -8q-100 0 -156.5 43t-56.5 147q0 49 15 89t43 68t68 43t90 15q60 0 104.5 -19t73.5 -59.5t43 -104.5t14 -154q0 -102 -20 -169.5t-56 -107t-86.5 -55.5t-111.5 -16
q-40 0 -78.5 5t-69.5 13zM458 353q0 46 -5 94t-22.5 87t-51.5 64t-92 25q-69 0 -111.5 -43t-42.5 -119q0 -77 37.5 -107.5t115.5 -30.5q39 0 84.5 8t87.5 22z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="275" 
d="M137 71q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10zM138 487q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="275" 
d="M138 487q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10zM168 68l-90 -201h-54l82 201h62z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M531 527v-47l-397 -161l397 -161v-47l-457 186v44z" />
    <glyph glyph-name="equal" unicode="=" 
d="M531 241v-44h-457v44h457zM531 441v-44h-457v44h457z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M531 341v-44l-457 -186v47l397 161l-397 161v47z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="415" 
d="M37 641q29 12 74 23t92 11q81 0 132 -37.5t51 -117.5q0 -38 -12 -65.5t-30.5 -49.5t-39.5 -42t-39.5 -42t-30.5 -48.5t-12 -62.5v-60h-53v60q0 41 11.5 71.5t29.5 55t38 45t38 41t29.5 43t11.5 51.5q0 57 -31.5 81t-95.5 24q-38 0 -77.5 -9.5t-68.5 -22.5zM196 71
q18 0 29 -10t11 -29t-11 -29.5t-29 -10.5q-19 0 -30.5 10.5t-11.5 29.5t11.5 29t30.5 10z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="871" 
d="M513 350q-18 5 -38 7.5t-40 2.5q-49 0 -76 -33t-27 -105q0 -47 8 -76t20.5 -44.5t27.5 -20.5t30 -5q32 0 54.5 12.5t40.5 29.5v232zM567 169q0 -21 3 -39t12 -31.5t24.5 -21.5t40.5 -8q60 0 86.5 52.5t26.5 164.5q0 57 -15.5 108.5t-52.5 90.5t-99 61.5t-154 22.5
t-154.5 -28t-101 -75.5t-55.5 -110t-17 -131.5q0 -68 13 -131t48.5 -111t99 -77t164.5 -29q71 0 130.5 15.5t116.5 36.5l14 -49q-59 -24 -124 -40t-138 -16q-112 0 -186 33.5t-117.5 89t-61 128t-17.5 150.5q0 80 22 152t68.5 126t120 86t175.5 32q106 0 178.5 -27.5
t117 -74t64 -107.5t19.5 -127q0 -137 -43.5 -201.5t-128.5 -64.5q-48 0 -74.5 16.5t-40.5 34.5q-25 -18 -50.5 -30.5t-63.5 -12.5q-32 0 -57.5 11t-44.5 34.5t-29 61t-10 90.5q0 46 12 81t33.5 58t50.5 34.5t63 11.5q37 0 69 -5.5t63 -15.5v-218z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="626" 
d="M176 60q40 -8 79 -11.5t68 -3.5q48 0 80.5 9t52.5 26t28.5 41t8.5 54q0 67 -37 100t-133 33h-147v-248zM176 361h146q72 0 111.5 33.5t39.5 99.5q0 63 -37 90.5t-118 27.5h-142v-251zM318 665q105 0 161.5 -39.5t56.5 -131.5q0 -68 -29 -104t-71 -50q24 -5 45.5 -16.5
t38 -31t26 -48t9.5 -67.5q0 -41 -12.5 -75.5t-40.5 -59t-72 -38t-108 -13.5q-18 0 -42.5 2t-52 5t-56 7.5t-55.5 10.5v649h202z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="578" 
d="M70 331q0 101 20 167.5t57 105.5t89.5 55t118.5 16q43 0 84.5 -4.5t79.5 -11.5l-8 -53q-38 7 -79 11.5t-78 4.5q-53 0 -94 -12.5t-69.5 -45t-43 -89t-14.5 -143.5q0 -86 14.5 -142t42 -89t67.5 -46t90 -13q39 0 83.5 5.5t86.5 13.5l6 -51q-43 -10 -90 -15t-86 -5
q-63 0 -114 16.5t-87.5 56t-56 105t-19.5 163.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="679" 
d="M318 665q70 0 124 -19t91 -59t56.5 -102.5t19.5 -148.5q0 -172 -70.5 -259t-218.5 -87q-51 0 -102 6.5t-102 18.5v650h202zM176 57q32 -6 68.5 -9t75.5 -3q54 0 96.5 16t71.5 51t44 90.5t15 134.5q0 73 -15.5 125.5t-45 85.5t-72 48.5t-96.5 15.5h-142v-555z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="550" 
d="M490 665v-53h-314v-247h277v-53h-277v-312h-60v665h374z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="675" 
d="M561 603q-35 7 -86 13t-117 6q-57 0 -99.5 -15.5t-70.5 -50t-41.5 -89t-13.5 -132.5q0 -84 14.5 -140.5t42.5 -90t70 -48t97 -14.5q22 0 46.5 1.5t48 3.5t43.5 5t34 6v254h-166v53h227v-347q-53 -14 -115.5 -21t-119.5 -7q-67 0 -120 17.5t-89.5 58t-56 107t-19.5 164.5
q0 178 71.5 258t215.5 80q72 0 124.5 -6t87.5 -12z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="748" 
d="M176 665v-295h396v295h60v-665h-60v317h-396v-317h-60v665h60z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="292" 
d="M176 665v-665h-60v665h60z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="291" 
d="M176 665v-607q0 -37 -9 -68t-28 -60.5t-49.5 -60t-73.5 -66.5l-34 43q38 33 64 60.5t41.5 52.5t22 49t6.5 50v607h60z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="592" 
d="M176 665v-370l335 370h73l-267 -297q70 -20 115 -69t74 -115l81 -184h-67l-73 165q-26 60 -67 102t-104 53l-100 -102v-218h-60v665h60z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="508" 
d="M176 665v-612h307v-53h-367v665h60z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="904" 
d="M422 215l-248 374v-589h-58v665h77l259 -390l259 390h77v-665h-58v589l-248 -374h-60z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="754" 
d="M168 665l412 -425v425h58v-665h-58v159l-406 417v-576h-58v665h52z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="629" 
d="M176 315q39 -7 74 -10t67 -3q51 0 86.5 10t57 30t31 49.5t9.5 68.5q0 43 -10 72t-32 47t-55.5 25.5t-81.5 7.5h-146v-297zM322 665q59 0 104 -10t76 -34t46.5 -63.5t15.5 -97.5q0 -113 -64 -161.5t-184 -48.5q-32 0 -67 3.5t-73 9.5v-263h-60v665h206z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="684" 
d="M296 -69q27 0 63.5 -8.5t73.5 -18t69.5 -18t51.5 -8.5q29 0 46.5 10t37.5 28l35 -40q-23 -25 -51 -38.5t-67 -13.5q-24 0 -59 8.5t-72 19t-71.5 19t-56.5 8.5v52zM342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59
t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49
t-90 15z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="648" 
d="M322 665q59 0 104 -9.5t76 -32.5t46.5 -61.5t15.5 -96.5q0 -82 -36.5 -126t-102.5 -63q32 -19 49.5 -45.5t36.5 -69.5l67 -161h-64l-61 148q-9 22 -21 42.5t-28 36t-37.5 25t-50.5 9.5q-32 0 -67 3.5t-73 9.5v-274h-60v665h206zM176 326q39 -7 74 -10t67 -3
q51 0 86.5 9.5t57 28t31 47t9.5 67.5q0 43 -10 71.5t-32 45t-55.5 23.5t-81.5 7h-146v-286z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="561" 
d="M446 605q-13 3 -32 6t-40.5 5.5t-43 4t-38.5 1.5q-31 0 -59 -2.5t-49.5 -11t-34 -25t-12.5 -44.5q0 -31 26 -60.5t65 -59t84 -60.5t84 -64.5t65 -71.5t26 -82t-16 -73t-43.5 -46t-65.5 -24.5t-82 -7.5q-60 0 -116 10.5t-93 23.5l12 51q35 -12 89 -22.5t111 -10.5
q63 0 102.5 20t39.5 70q0 36 -26 69t-65.5 64.5t-85 63t-85 64t-65.5 67.5t-26 74q0 45 18.5 72.5t48.5 42.5t69.5 20.5t81.5 5.5q46 0 92 -5.5t72 -10.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="564" 
d="M544 665v-53h-232v-612h-60v612h-232v53h524z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="557" 
d="M257 0l-262 665h62l234 -602q34 65 66 137.5t60 149t49.5 156t34.5 159.5h56q-35 -188 -97.5 -350t-140.5 -315h-62z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="893" 
d="M191 0l-178 665h61l154 -606l182 469h71l181 -469q62 143 100.5 290.5t63.5 315.5h54q-16 -98 -33.5 -184t-40 -165.5t-50 -157t-63.5 -158.5h-67l-181 467l-185 -467h-69z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="559" 
d="M99 665l186 -274l191 274h66l-224 -324l230 -341h-71l-199 293l-201 -293h-66l235 343l-217 322h70z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="537" 
d="M247 226l-257 439h71l223 -381q42 41 75 88t57.5 96.5t41 99.5t25.5 97h59q-28 -128 -88 -241.5t-149 -196.5v-227h-58v226z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="602" 
d="M541 665v-47l-410 -565h419v-53h-498v45l415 567h-401v53h475z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="312" 
d="M301 730v-49h-123v-820h123v-49h-180v918h180z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="479" 
d="M85 730l354 -783h-57l-356 783h59z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="312" 
d="M191 730v-918h-180v49h123v820h-123v49h180z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M324 665l173 -304h-55l-140 249l-139 -249h-55l172 304h44z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="681" 
d="M681 -11v-40h-681v40h681z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="550" 
d="M241 730l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="563" 
d="M153 730v-263q32 8 69.5 14t76.5 6q41 0 78 -11.5t65 -40t44.5 -76t16.5 -118.5q0 -75 -17.5 -123.5t-47 -76.5t-68.5 -39t-83 -11q-48 0 -97 9t-94 19v711h57zM153 57q32 -6 66.5 -10.5t66.5 -4.5q35 0 64 8t49.5 30t32 60.5t11.5 100.5q0 58 -11 96t-30.5 60t-46 31
t-57.5 9q-35 0 -71.5 -6t-73.5 -15v-359z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="464" 
d="M395 425q-25 5 -55 8.5t-61 3.5q-35 0 -64 -8.5t-50 -30.5t-33 -60.5t-12 -98.5t12 -98t33.5 -60t50.5 -30.5t63 -8.5q33 0 63 3.5t60 10.5l6 -49q-34 -8 -63 -12t-64 -4q-47 0 -87.5 11.5t-70 40t-46.5 76.5t-17 120q0 73 16.5 120.5t45.5 76t70 40t89 11.5
q32 0 64.5 -3.5t56.5 -8.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="562" 
d="M409 416q-37 10 -70 15.5t-68 5.5q-29 0 -56.5 -9t-48.5 -31t-34 -60t-13 -96q0 -62 11.5 -100.5t32.5 -60.5t49.5 -30t63.5 -8q32 0 66.5 4.5t66.5 10.5v359zM466 730v-711q-45 -10 -94.5 -19t-96.5 -9q-44 0 -83 11t-68.5 39t-46.5 76.5t-17 123.5q0 71 18 118.5
t47.5 76t67.5 40t79 11.5q39 0 70.5 -5t66.5 -13v261h57z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="366" 
d="M121 479v91q0 84 49 123.5t131 39.5q20 0 42 -1.5t41 -4.5l-7 -49q-17 3 -37 4t-39 1q-26 0 -48.5 -5t-39 -18t-26 -35t-9.5 -55v-91h175v-49h-175v-430h-57v430h-81v49h81z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="563" 
d="M467 -21q0 -91 -49 -133.5t-144 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-32 -5 -66.5 -9.5t-70.5 -4.5q-42 0 -80.5 9.5t-68 36t-47 74.5t-17.5 125q0 70 18 117t48 74.5t70 39t84 11.5q49 0 94.5 -10t92.5 -24v-474z
M410 416q-12 3 -27.5 7t-33 7t-35.5 5t-33 2q-33 0 -62.5 -8t-51 -29t-34.5 -58.5t-13 -96.5q0 -62 11.5 -100.5t32 -59.5t48.5 -28t60 -7q33 0 68 4.5t70 10.5v351z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="587" 
d="M99 730h57v-264q34 8 72 14.5t76 6.5t73 -8t62 -26.5t43 -49.5t16 -77v-326h-57v323q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v730z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="255" 
d="M170 666q0 -17 -10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11t32.5 -11t10.5 -28zM156 479v-479h-57v479h57z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="255" 
d="M156 479v-424q0 -42 -5.5 -72t-21 -57t-43.5 -55.5t-73 -66.5l-33 38q39 34 62 60t36 50t17 48.5t4 54.5v424h57zM170 666q0 -17 -10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11t32.5 -11t10.5 -28z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="516" 
d="M156 730v-496l263 245h72l-203 -194q50 -11 83 -49t55 -85l70 -151h-63l-59 126q-10 21 -22.5 41.5t-28 37t-34.5 27.5t-43 14l-90 -79v-167h-57v730h57z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="255" 
d="M156 730v-730h-57v730h57z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="885" 
d="M419 323q0 34 -10 56t-27.5 35t-40.5 18t-48 5q-35 0 -71 -6.5t-66 -14.5v-416h-57v453q42 11 93 22.5t102 11.5q41 0 77 -10.5t62 -34.5q38 20 85.5 32.5t95.5 12.5q36 0 69 -7.5t58 -26.5t40 -50t15 -77v-326h-57v323q0 34 -10 56t-27.5 35t-40.5 18t-48 5
q-47 0 -86.5 -12.5t-64.5 -24.5q14 -31 14 -74v-326h-57v323z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="587" 
d="M99 453q19 6 43.5 12t51 11t54.5 8t56 3q38 0 73 -8t62 -26.5t43 -49.5t16 -77v-326h-57v323q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v453z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="548" 
d="M274 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM275 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="566" 
d="M99 453q47 14 99 24t98 10q44 0 82 -12t66.5 -40.5t45 -76t16.5 -117.5q0 -77 -17.5 -125.5t-47 -76t-68 -37.5t-80.5 -10q-36 0 -70.5 4.5t-66.5 9.5v-194h-57v641zM156 57q35 -6 70 -10.5t68 -4.5q32 0 60 7.5t48.5 29.5t32 61t11.5 101q0 59 -11 97t-31.5 60t-48 30.5
t-60.5 8.5q-15 0 -34.5 -2t-39 -5t-36.5 -7t-29 -7v-359z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="566" 
d="M467 -188h-57v194q-32 -5 -66.5 -9.5t-70.5 -4.5q-42 0 -80.5 10t-68 37.5t-47 76t-17.5 125.5q0 70 18 117.5t48 76t70 40.5t84 12q46 0 93 -10t94 -24v-641zM410 416q-12 3 -27.5 7t-33 7t-35.5 5t-33 2q-33 0 -62.5 -8.5t-51 -30.5t-34.5 -60t-13 -97q0 -62 11.5 -101
t32 -61t48.5 -29.5t60 -7.5q33 0 69.5 4.5t68.5 10.5v359z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="354" 
d="M99 453q68 20 125.5 27t109.5 7v-51q-45 0 -94.5 -5.5t-83.5 -15.5v-415h-57v453z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="446" 
d="M369 425q-22 4 -58 8t-69 4q-26 0 -49.5 -3t-41.5 -10.5t-28.5 -20.5t-10.5 -33q0 -24 21 -41.5t53 -33.5t68.5 -33t68.5 -38t53 -49t21 -66q0 -61 -46 -89t-119 -28q-50 0 -95.5 8.5t-77.5 19.5l9 50q32 -11 74.5 -19.5t89.5 -8.5q20 0 39.5 3t34.5 10.5t24 20.5t9 33
q0 28 -21 48.5t-52.5 37.5t-68.5 33t-68.5 35.5t-52.5 44.5t-21 60q0 34 14.5 56.5t40 36.5t59 20t71.5 6t75 -3.5t60 -8.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="365" 
d="M174 621v-142h149v-49h-149v-329q0 -33 22 -46t51 -13q26 0 50.5 5.5t42.5 10.5l3 -50q-19 -7 -45.5 -11.5t-53.5 -4.5q-25 0 -48 5.5t-40.5 18t-28 33.5t-10.5 53v328h-82v49h82l15 142h42z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="475" 
d="M10 479h59l174 -424q22 40 47.5 92.5t49 109.5t42.5 114.5t29 107.5h54q-16 -68 -37 -130.5t-46.5 -121.5t-54.5 -115.5t-61 -111.5h-58z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="812" 
d="M178 0l-160 479h60l134 -424l162 424h64l160 -424q48 91 87 200.5t56 223.5h53q-20 -134 -65 -250.5t-104 -228.5h-60l-160 427l-162 -427h-65z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="461" 
d="M89 479l144 -193l143 193h64l-175 -234l181 -245h-66l-150 202l-152 -202h-63l182 243l-174 236h66z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="574" 
d="M475 479v-500q0 -91 -48 -133.5t-145 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-25 -5 -61 -9.5t-76 -4.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v313h57v-310q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 5
t61.5 10v414h57z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="483" 
d="M436 479v-42l-317 -388h325v-49h-400v42l319 388h-311v49h384z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="359" 
d="M337 679q-12 3 -32 5t-34 2q-29 0 -46 -7t-26 -20t-11.5 -31t-2.5 -39q0 -24 3 -52.5t6 -57.5t6 -57t3 -50q0 -27 -6 -45t-16 -29.5t-22 -17.5t-24 -9q12 -3 24 -8.5t22 -17t16 -30t6 -45.5q0 -23 -3 -50.5t-6 -57t-6 -57.5t-3 -51q0 -22 2.5 -40t11.5 -31t26 -20t46 -7
q14 0 34 2t32 5l6 -52q-6 -2 -15 -3t-19.5 -2t-21 -1.5t-17.5 -0.5q-40 0 -67 10.5t-43.5 30t-24 47t-7.5 62.5q0 24 3 52t7 56.5t7 56.5t3 52q0 43 -25 57t-73 15v56q48 1 73 15.5t25 56.5q0 23 -3 51t-7 56.5t-7 57t-3 53.5q0 34 7.5 62t24 47t43.5 29.5t67 10.5
q7 0 17.5 -0.5t21 -1.5t19.5 -2.5l15 -2.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="294" 
d="M173 800v-1000h-52v1000h52z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="359" 
d="M16 731l15 2.5t19.5 2.5t20.5 1.5t18 0.5q79 0 110.5 -40t31.5 -109q0 -25 -3 -53.5t-7 -57t-7 -56.5t-3 -51q0 -42 25 -56.5t73 -15.5v-56q-48 -1 -73 -15t-25 -57q0 -24 3 -52t7 -56.5t7 -56.5t3 -52q0 -70 -31.5 -110t-110.5 -40q-8 0 -18 0.5t-20.5 1.5t-19.5 2
t-15 3l6 52q12 -3 32 -5t34 -2q28 0 45 7t26 20t12 31t3 40q0 23 -3 51t-6 57.5t-6 57t-3 50.5q0 27 6 45.5t16 30t22 17t24 8.5q-12 3 -24 9t-22 17.5t-16 29.5t-6 45q0 22 3 50t6 57t6 57.5t3 52.5q0 21 -3 39t-12 31t-26 20t-45 7q-14 0 -34 -2t-32 -5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M63 292q12 34 41 56t74 22q21 0 55.5 -8.5t71.5 -18.5t70 -18.5t51 -8.5q35 0 49.5 14.5t26.5 36.5l40 -18q-12 -34 -41 -56t-74 -22q-20 0 -53.5 8.5t-70.5 18.5t-70.5 18.5t-53.5 8.5q-35 0 -49.5 -14t-26.5 -37z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="255" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="298" 
d="M119 -188l4 517h52l4 -517h-60zM150 408q-19 0 -30.5 10t-11.5 29t11.5 29.5t30.5 10.5q18 0 29 -10.5t11 -29.5t-11 -29t-29 -10z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M491 516q-12 3 -29.5 7t-38.5 7.5t-44 6t-45 2.5q-38 0 -71.5 -8.5t-59 -31.5t-40.5 -64t-15 -105q0 -110 48.5 -158.5t140.5 -48.5q40 0 83.5 8.5t73.5 19.5l10 -52q-32 -11 -71 -20t-81 -10v-153h-54v155q-47 4 -86.5 20t-67.5 46.5t-43.5 78t-15.5 114.5
q0 69 16.5 116.5t45 78.5t67.5 46t84 18v141h54v-138q45 -2 85.5 -9.5t62.5 -13.5l-9 -53v0z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M220 325l-12 -116q-5 -49 -21 -89.5t-48 -66.5h377v-53h-457v44q46 29 64 71.5t24 97.5l11 112h-100v51h107l15 129q6 51 25 84t47.5 52t63.5 26.5t73 7.5q40 0 78 -5.5t67 -12.5l-8 -53q-31 8 -68 13t-70 5q-63 0 -101 -24.5t-46 -92.5l-15 -129h253v-51h-259z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M108 665l208 -345l190 345h60l-131 -240h131v-49h-157l-57 -105h214v-54h-222v-217h-59v217h-221v54h214l-64 105h-150v49h121l-146 240h69z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="294" 
d="M173 800v-401h-52v401h52zM173 201v-401h-52v401h52z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="548" 
d="M490 188q0 -44 -14.5 -72t-39.5 -46q19 -20 30 -44.5t11 -54.5q0 -50 -18.5 -82t-48.5 -51t-68.5 -26.5t-78.5 -7.5q-26 0 -55.5 4t-58 11t-53.5 14.5t-42 15.5l15 52q16 -7 39 -15t49 -14.5t54 -11t55 -4.5q31 0 58 5.5t47.5 18.5t32 34t11.5 51q0 38 -26 65.5t-65 50
t-84 43t-84 44.5t-65 55t-26 75t17.5 71.5t46.5 44.5q-23 20 -37 44.5t-14 56.5q0 46 18.5 77t48.5 49.5t69.5 26t81.5 7.5q25 0 49 -1.5t46 -4t39.5 -5t29.5 -5.5l-8 -54q-12 3 -31 6t-41 5.5t-43.5 4t-38.5 1.5q-30 0 -57.5 -4.5t-49.5 -16t-35 -31.5t-13 -50
q0 -32 25.5 -55.5t64.5 -45t83.5 -42.5t83.5 -47t64.5 -60.5t25.5 -81.5zM431 182q0 37 -25.5 64.5t-64.5 49.5t-84 42.5t-84 44.5q-21 -12 -33.5 -31.5t-12.5 -48.5q0 -33 28 -57.5t69 -46.5t88 -44.5t86 -50.5q15 13 24 32t9 46z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="550" 
d="M188 688q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM361 688q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="794" 
d="M240 332q0 52 11.5 88.5t33.5 58.5t54 32t72 10q14 0 30.5 -1.5t31.5 -4t28 -5t21 -5.5l-8 -44q-17 5 -46 9.5t-56 4.5q-30 0 -52.5 -5.5t-37.5 -21.5t-22.5 -43.5t-7.5 -72.5t7.5 -72.5t22.5 -43.5t37.5 -21.5t52.5 -5.5q27 0 56.5 5.5t48.5 11.5l8 -44
q-19 -8 -52.5 -13.5t-62.5 -5.5q-40 0 -71.5 10t-53.5 32t-33.5 58t-11.5 89zM734 332q0 -342 -337 -342t-337 342q0 343 337 343t337 -343zM685 332q0 148 -70 223t-218 75t-218 -75t-70 -223t70 -222.5t218 -74.5t218 74.5t70 222.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="358" 
d="M232 501q-38 8 -76 8q-12 0 -23.5 -2t-21 -7.5t-15 -16t-5.5 -27.5q0 -30 20.5 -41.5t50.5 -11.5q34 0 70 11v87zM70 663q23 5 47 8.5t47 3.5q24 0 45 -4.5t37 -15.5t25 -29t9 -44v-193q-20 -7 -52 -15.5t-67 -8.5q-22 0 -42.5 4.5t-36.5 15t-25.5 28t-9.5 44.5t9.5 45
t25.5 28t35 14t39 4q17 0 38 -2t38 -6v41q0 34 -20 44t-49 10q-25 0 -47 -3.5t-39 -8.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="546" 
d="M270 452l-139 -181l139 -181h-60l-144 181l144 181h60zM480 452l-139 -181l139 -181h-60l-144 181l144 181h60z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M531 441v-244h-49v200h-408v44h457z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="385" 
d="M319 298v-54h-253v54h253z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="660" 
d="M283 433q8 -1 19 -2t22 -1q29 0 47 8.5t18 44.5q0 26 -14.5 36t-46.5 10h-45v-96zM332 566q49 0 75.5 -19t26.5 -63q0 -38 -15 -54t-35 -22q15 -8 23.5 -23t16.5 -35l24 -67h-48l-26 68q-5 14 -16 28t-37 14q-10 0 -20 0.5t-18 1.5v-112h-46v283h95zM610 419
q0 -121 -73.5 -188t-206.5 -67t-206.5 67t-73.5 188t73.5 188t206.5 67t206.5 -67t73.5 -188zM563 419q0 104 -60 159t-173 55t-173 -55t-60 -159t60 -159t173 -55t173 55t60 159z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="550" 
d="M392 674v-50h-234v50h234z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="385" 
d="M319 561q0 -51 -34.5 -82.5t-91.5 -31.5t-91.5 31.5t-34.5 82.5t34.5 82.5t91.5 31.5t91.5 -31.5t34.5 -82.5zM267 561q0 32 -20 51t-54 19t-54 -19t-20 -51t20 -51t54 -19t54 19t20 51z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M278 342v181h48v-181h205v-45h-205v-182h-48v182h-204v45h204zM531 45v-45h-457v45h457z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="330" 
d="M57 661q20 6 44.5 9t50.5 3q54 0 87.5 -22.5t33.5 -72.5q0 -38 -23.5 -67t-63.5 -68l-70 -68h159v-43h-227v37l104 105q19 19 32.5 33t21.5 25t12 22t4 24q0 30 -21.5 41t-50.5 11q-23 0 -47.5 -4t-41.5 -8z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="330" 
d="M261 665v-41l-105 -97h1q23 0 44 -5.5t37 -17t25.5 -30t9.5 -43.5q0 -57 -38.5 -85t-96.5 -28q-32 0 -54.5 3.5t-45.5 10.5l7 44q21 -5 42.5 -9t50.5 -4q17 0 32 3t26.5 10.5t18.5 20.5t7 33q0 18 -7 29.5t-19.5 17.5t-29 8t-35.5 2h-43v37l105 97h-150v44h218z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="550" 
d="M378 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="571" 
d="M90 479h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453q-39 -13 -90.5 -23.5t-103.5 -10.5q-41 0 -75 10t-59 35v-225h-57v667z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" horiz-adv-x="571" 
d="M90 479h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453q-39 -13 -90.5 -23.5t-103.5 -10.5q-41 0 -75 10t-59 35v-225h-57v667z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="644" 
d="M396 -158h-54v394q-15 -1 -31 -1h-32q-61 0 -103.5 11.5t-69.5 37.5t-39 68t-12 104q0 58 12.5 98t39.5 65t69.5 35.5t102.5 10.5h117v-823zM528 665v-823h-54v823h54z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="275" 
d="M137 310q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="550" 
d="M320 -53q12 -19 20 -38.5t8 -36.5q0 -32 -17.5 -50t-59.5 -18q-15 0 -34.5 3.5t-31.5 8.5l11 43q12 -5 24.5 -7.5t22.5 -2.5q19 0 25 6.5t6 20.5q0 16 -8 34t-18 37h52z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="301" 
d="M148 665h37v-334h-52v285l-88 -22v46z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="358" 
d="M317 517q0 -42 -10 -71.5t-28 -48t-43 -27.5t-56 -9q-62 0 -100 36.5t-38 119.5t38 119.5t100 36.5q31 0 56 -9t43 -27.5t28 -48t10 -71.5zM271 517q0 33 -7 55.5t-19 36t-29 19t-37 5.5t-36.5 -5.5t-28.5 -19t-19 -36t-7 -55.5t7 -55.5t19 -36t28.5 -19t36.5 -5.5
t37 5.5t29 19t19 36t7 55.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="546" 
d="M336 452l144 -181l-144 -181h-60l139 181l-139 181h60zM126 452l144 -181l-144 -181h-60l139 181l-139 181h60z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="787" 
d="M690 343v-223h50v-43h-50v-77h-54v77h-156v39l147 227h63zM636 120v152l-99 -152h99zM607 665l-429 -665h-63l428 665h64zM153 665h37v-334h-52v285l-88 -22v46z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="782" 
d="M602 665l-429 -665h-63l428 665h64zM485 329q20 6 44.5 9t50.5 3q54 0 87.5 -22.5t33.5 -72.5q0 -38 -23.5 -67t-63.5 -68l-70 -68h159v-43h-227v37l104 105q19 19 32.5 33t21.5 25t12 22t4 24q0 30 -21.5 41t-50.5 11q-23 0 -47.5 -4t-41.5 -8zM153 665h37v-334h-52v285
l-88 -22v46z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="787" 
d="M690 343v-223h50v-43h-50v-77h-54v77h-156v39l147 227h63zM636 120v152l-99 -152h99zM261 665v-41l-105 -97h1q23 0 44 -5.5t37 -17t25.5 -30t9.5 -43.5q0 -57 -38.5 -85t-96.5 -28q-32 0 -54.5 3.5t-45.5 10.5l7 44q21 -5 42.5 -9t50.5 -4q17 0 32 3t26.5 10.5
t18.5 20.5t7 33q0 18 -7 29.5t-19.5 17.5t-29 8t-35.5 2h-43v37l105 97h-150v44h218zM651 665l-429 -665h-63l428 665h64z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="373" 
d="M357 -162q-29 -12 -74 -23t-92 -11q-81 0 -132 37.5t-51 117.5q0 38 12 65.5t30.5 49.5t39.5 42t39.5 42t30.5 48.5t12 62.5v60h53v-60q0 -41 -11.5 -71.5t-29.5 -55t-38 -45t-38 -41t-29.5 -43.5t-11.5 -51q0 -57 31.5 -81t95.5 -24q38 0 77.5 9.5t68.5 22.5zM198 408
q-18 0 -29 10t-11 29t11 29.5t29 10.5q19 0 30.5 -10.5t11.5 -29.5t-11.5 -29t-30.5 -10z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362zM278 916l89 -161h-57l-102 161h70z
" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362zM469 916l-102 -161h-56l89 161h69z
" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362zM373 916l96 -161h-59l-69 113
l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362zM197 806q11 31 29.5 49.5t53.5 18.5
q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362zM255 874q22 0 32.5 -11t10.5 -28
t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM428 874q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362zM434 836q0 -37 -24.5 -59t-68.5 -22
t-68.5 22t-24.5 59t24.5 59t68.5 22t68.5 -22t24.5 -59zM386 836q0 20 -10.5 31t-34.5 11t-34.5 -11t-10.5 -31t10.5 -31t34.5 -11t34.5 11t10.5 31z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="971" 
d="M896 665v-53h-314v-242h277v-53h-277v-180q0 -45 27 -64.5t67 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v180h-362v-317h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 112 -19.5t69 -53.5v63h374zM522 370v61q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15
t-55.5 -40.5t-28 -60.5t-7.5 -75v-61h362z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="578" 
d="M70 331q0 101 20 167.5t57 105.5t89.5 55t118.5 16q43 0 84.5 -4.5t79.5 -11.5l-8 -53q-38 7 -79 11.5t-78 4.5q-53 0 -94 -12.5t-69.5 -45t-43 -89t-14.5 -143.5q0 -86 14.5 -142t42 -89t67.5 -46t90 -13q39 0 83.5 5.5t86.5 13.5l6 -51q-43 -10 -90 -15t-86 -5
q-63 0 -114 16.5t-87.5 56t-56 105t-19.5 163.5zM351 -53q12 -19 20 -38.5t8 -36.5q0 -32 -17.5 -50t-59.5 -18q-15 0 -34.5 3.5t-31.5 8.5l11 43q12 -5 24.5 -7.5t22.5 -2.5q19 0 25 6.5t6 20.5q0 16 -8 34t-18 37h52z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374zM246 916l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374zM432 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374zM327 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374zM212 875q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM385 875q21 0 32 -11t11 -28t-11 -28t-32 -11
q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="291" 
d="M176 665v-665h-60v665h60zM83 916l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="291" 
d="M176 665v-665h-60v665h60zM276 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="291" 
d="M176 665v-665h-60v665h60zM177 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="291" 
d="M176 665v-665h-60v665h60zM60 875q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM233 875q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="678" 
d="M20 365h95v300h202q70 0 124 -19t91 -59t56.5 -102.5t19.5 -148.5q0 -172 -70.5 -259t-218.5 -87q-51 0 -102 6.5t-102 18.5v297h-95v53zM175 365h182v-53h-182v-255q32 -6 68.5 -9t75.5 -3q54 0 96.5 16t71.5 51t44 90.5t15 134.5q0 73 -15.5 125.5t-45 85.5t-72 48.5
t-96.5 15.5h-142v-247z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="754" 
d="M168 665l412 -425v425h58v-665h-58v159l-406 417v-576h-58v665h52zM233 806q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5
t-12 -23.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15zM282 916l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15zM469 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15zM374 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15zM198 806q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5
t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15zM256 875q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM429 875q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11
t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M301 351l176 160l36 -32l-176 -160l173 -158l-34 -32l-175 159l-174 -159l-36 31l175 159l-170 156l35 32z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="684" 
d="M342 675q100 0 163 -44l56 87l42 -25l-61 -95q35 -41 53.5 -106t18.5 -160q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-50 0 -91.5 10.5t-73.5 33.5l-55 -85l-42 25l61 94q-35 41 -53 105.5t-18 158.5t18.5 159.5t53 106t85 59t115.5 18.5zM551 332
q0 74 -11 125.5t-33 86.5l-299 -462q26 -21 59 -30.5t75 -9.5q51 0 90 15t65.5 49t40 89.5t13.5 136.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5q0 -73 10.5 -124.5t31.5 -86.5l299 462q-49 39 -132 39z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM292 916l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM478 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM383 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM265 874q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28
t32.5 11zM438 874q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="537" 
d="M399 916l-102 -161h-56l89 161h69zM247 226l-257 439h71l223 -381q42 41 75 88t57.5 96.5t41 99.5t25.5 97h59q-28 -128 -88 -241.5t-149 -196.5v-227h-58v226z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="629" 
d="M176 665v-118h146q59 0 104 -10t76 -34t46.5 -63.5t15.5 -97.5q0 -113 -64 -161.5t-184 -48.5q-32 0 -67 3.5t-73 9.5v-145h-60v665h60zM176 197q39 -7 74 -10t67 -3q51 0 86.5 10t57 30t31 49.5t9.5 68.5q0 43 -10 72t-32 47t-55.5 25.5t-81.5 7.5h-146v-297z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="519" 
d="M100 574q0 79 44.5 121.5t132.5 42.5q81 0 128.5 -41t47.5 -112q0 -50 -19 -80t-42.5 -52t-42.5 -42.5t-19 -50.5q0 -21 14 -38t34.5 -34t45 -34t45 -37.5t34.5 -44.5t14 -55q0 -37 -13 -61t-35.5 -38.5t-52 -20t-62.5 -5.5q-27 0 -57 3.5t-55 11.5l6 48q24 -7 53 -11
t52 -4q49 0 77.5 15.5t28.5 62.5q0 23 -13.5 41.5t-34.5 35.5t-44.5 33.5t-44.5 35.5t-34.5 42t-13.5 53q0 27 9 47t22.5 36t29.5 30t29.5 30t22.5 36t9 48q0 55 -33 78.5t-86 23.5q-65 0 -92.5 -26.5t-27.5 -87.5v-574h-57v574z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160zM193 730l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160zM371 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160zM290 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160zM114 620q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5
q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160zM172 688q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28
t32.5 11zM345 688q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160zM351 648q0 -37 -24.5 -59t-68.5 -22t-68.5 22t-24.5 59t24.5 59t68.5 22t68.5 -22
t24.5 -59zM303 648q0 20 -10.5 31t-34.5 11t-34.5 -11t-10.5 -31t10.5 -31t34.5 -11t34.5 11t10.5 31z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="814" 
d="M99 473q24 5 59 9.5t68 4.5q57 0 106 -17.5t72 -64.5q25 43 66 62.5t99 19.5q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 35.5 1.5t38 4t35 5.5t25.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-58 0 -104.5 11.5
t-78.5 40.5q-41 -23 -89 -37.5t-107 -14.5q-38 0 -71 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q33 0 68 -3.5t61 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5t-52 4.5q-32 0 -63.5 -4t-56.5 -9zM390 86q-30 51 -31 136q-27 4 -59.5 7.5t-65.5 3.5
q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t48.5 -5q21 0 43.5 3t44 9t41 14t33.5 18zM700 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="464" 
d="M395 425q-25 5 -55 8.5t-61 3.5q-35 0 -64 -8.5t-50 -30.5t-33 -60.5t-12 -98.5t12 -98t33.5 -60t50.5 -30.5t63 -8.5q33 0 63 3.5t60 10.5l6 -49q-34 -8 -63 -12t-64 -4q-47 0 -87.5 11.5t-70 40t-46.5 76.5t-17 120q0 73 16.5 120.5t45.5 76t70 40t89 11.5
q32 0 64.5 -3.5t56.5 -8.5zM281 -53q12 -19 20 -38.5t8 -36.5q0 -32 -17.5 -50t-59.5 -18q-15 0 -34.5 3.5t-31.5 8.5l11 43q12 -5 24.5 -7.5t22.5 -2.5q19 0 25 6.5t6 20.5q0 16 -8 34t-18 37h52z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM212 730l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM394 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM300 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM182 688q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM355 688q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="255" 
d="M156 479v-479h-57v479h57zM67 730l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="255" 
d="M156 479v-479h-57v479h57zM257 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="255" 
d="M156 479v-479h-57v479h57zM159 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="255" 
d="M156 479v-479h-57v479h57zM41 688q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM214 688q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="535" 
d="M313 730q20 -21 36 -42t32 -46l104 20v-46l-79 -15q40 -83 58 -173t18 -184q0 -130 -55.5 -191t-158.5 -61q-48 0 -87 13t-67 42.5t-43.5 76.5t-15.5 115q0 132 57 190t157 58q50 0 86 -16t57 -45q-8 36 -23 82t-37 82l-178 -33v46l153 28q-17 27 -37 51t-42 48h65z
M268 437q-35 0 -63 -9t-48 -31.5t-31 -60.5t-11 -97q0 -58 11 -96t31 -60.5t48 -31.5t63 -9q34 0 63 9t49 31t31.5 59.5t11.5 94.5q0 43 -6 79.5t-22.5 63.5t-47 42.5t-79.5 15.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="587" 
d="M99 453q19 6 43.5 12t51 11t54.5 8t56 3q38 0 73 -8t62 -26.5t43 -49.5t16 -77v-326h-57v323q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v453zM156 620q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5
q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="535" 
d="M269 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM270 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM199 730
l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="535" 
d="M269 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM270 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM399 730
l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="535" 
d="M269 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM270 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM299 730
l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="535" 
d="M269 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM270 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM124 620
q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="535" 
d="M269 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM270 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM181 688
q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM354 688q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M307 190q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10zM307 528q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10zM531 342v-45h-457v45h457z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="538" 
d="M475 518l-52 -85q29 -30 44.5 -77.5t15.5 -116.5q0 -132 -56 -189.5t-158 -57.5q-64 0 -107 20l-58 -95l-41 22l60 98q-33 29 -50.5 78.5t-17.5 123.5q0 132 56 190t158 58q70 0 116 -26l49 79zM358 416q-18 11 -40 16t-48 5q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5
t-11 -97q0 -57 10 -94t28 -60zM423 239q0 51 -8 86t-23 58l-202 -326q17 -8 37 -11.5t43 -3.5q35 0 63 8.5t48 31t31 60.5t11 97z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM233 730l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM401 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM315 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM197 688q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28
t32.5 11zM370 688q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="574" 
d="M475 479v-500q0 -91 -48 -133.5t-145 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-25 -5 -61 -9.5t-76 -4.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v313h57v-310q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 5
t61.5 10v414h57zM421 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="560" 
d="M156 730v-262q35 8 71.5 13.5t68.5 5.5q44 0 82 -12t66.5 -40.5t45 -76t16.5 -117.5q0 -77 -17.5 -125.5t-47 -76t-68 -37.5t-80.5 -10q-36 0 -70.5 4.5t-66.5 9.5v-194h-57v918h57zM156 57q35 -6 70 -10.5t68 -4.5q32 0 60 7.5t48.5 29.5t32 61t11.5 101q0 59 -11 97
t-31.5 60t-48 30.5t-60.5 8.5q-15 0 -34.5 -2t-39 -5t-36.5 -7t-29 -7v-359z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="574" 
d="M475 479v-500q0 -91 -48 -133.5t-145 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-25 -5 -61 -9.5t-76 -4.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v313h57v-310q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 5
t61.5 10v414h57zM197 688q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM370 688q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362zM458 860v-50h-234v50h234z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160zM375 674v-50h-234v50h234z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="682" 
d="M160 0h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362zM252 897q1 -32 21.5 -53.5t67.5 -21.5
t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5
t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160zM169 711q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5
t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="682" 
d="M522 0v264h-362v-264h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432q-6 -7 -14.5 -18t-16 -23.5t-13 -26t-5.5 -25.5q0 -18 11 -24.5t28 -6.5q8 0 22.5 2t25.5 6l8 -46q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5
t-26.5 54.5q0 37 16.5 62.5t28.5 39.5h-5zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="507" 
d="M99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-6 -7 -14.5 -18t-16 -23.5t-13 -26t-5.5 -25.5q0 -18 11 -24.5t28 -6.5q8 0 22.5 2t25.5 6l8 -46q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5t-26.5 54.5q0 27 10 48.5t21 35.5
q-28 -6 -59 -10t-67 -4q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29
t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="578" 
d="M70 331q0 101 20 167.5t57 105.5t89.5 55t118.5 16q43 0 84.5 -4.5t79.5 -11.5l-8 -53q-38 7 -79 11.5t-78 4.5q-53 0 -94 -12.5t-69.5 -45t-43 -89t-14.5 -143.5q0 -86 14.5 -142t42 -89t67.5 -46t90 -13q39 0 83.5 5.5t86.5 13.5l6 -51q-43 -10 -90 -15t-86 -5
q-63 0 -114 16.5t-87.5 56t-56 105t-19.5 163.5zM452 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="464" 
d="M395 425q-25 5 -55 8.5t-61 3.5q-35 0 -64 -8.5t-50 -30.5t-33 -60.5t-12 -98.5t12 -98t33.5 -60t50.5 -30.5t63 -8.5q33 0 63 3.5t60 10.5l6 -49q-34 -8 -63 -12t-64 -4q-47 0 -87.5 11.5t-70 40t-46.5 76.5t-17 120q0 73 16.5 120.5t45.5 76t70 40t89 11.5
q32 0 64.5 -3.5t56.5 -8.5zM390 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="578" 
d="M70 331q0 101 20 167.5t57 105.5t89.5 55t118.5 16q43 0 84.5 -4.5t79.5 -11.5l-8 -53q-38 7 -79 11.5t-78 4.5q-53 0 -94 -12.5t-69.5 -45t-43 -89t-14.5 -143.5q0 -86 14.5 -142t42 -89t67.5 -46t90 -13q39 0 83.5 5.5t86.5 13.5l6 -51q-43 -10 -90 -15t-86 -5
q-63 0 -114 16.5t-87.5 56t-56 105t-19.5 163.5zM352 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="464" 
d="M395 425q-25 5 -55 8.5t-61 3.5q-35 0 -64 -8.5t-50 -30.5t-33 -60.5t-12 -98.5t12 -98t33.5 -60t50.5 -30.5t63 -8.5q33 0 63 3.5t60 10.5l6 -49q-34 -8 -63 -12t-64 -4q-47 0 -87.5 11.5t-70 40t-46.5 76.5t-17 120q0 73 16.5 120.5t45.5 76t70 40t89 11.5
q32 0 64.5 -3.5t56.5 -8.5zM294 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="578" 
d="M70 331q0 101 20 167.5t57 105.5t89.5 55t118.5 16q43 0 84.5 -4.5t79.5 -11.5l-8 -53q-38 7 -79 11.5t-78 4.5q-53 0 -94 -12.5t-69.5 -45t-43 -89t-14.5 -143.5q0 -86 14.5 -142t42 -89t67.5 -46t90 -13q39 0 83.5 5.5t86.5 13.5l6 -51q-43 -10 -90 -15t-86 -5
q-63 0 -114 16.5t-87.5 56t-56 105t-19.5 163.5zM320 875q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="464" 
d="M395 425q-25 5 -55 8.5t-61 3.5q-35 0 -64 -8.5t-50 -30.5t-33 -60.5t-12 -98.5t12 -98t33.5 -60t50.5 -30.5t63 -8.5q33 0 63 3.5t60 10.5l6 -49q-34 -8 -63 -12t-64 -4q-47 0 -87.5 11.5t-70 40t-46.5 76.5t-17 120q0 73 16.5 120.5t45.5 76t70 40t89 11.5
q32 0 64.5 -3.5t56.5 -8.5zM262 689q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="578" 
d="M70 331q0 101 20 167.5t57 105.5t89.5 55t118.5 16q43 0 84.5 -4.5t79.5 -11.5l-8 -53q-38 7 -79 11.5t-78 4.5q-53 0 -94 -12.5t-69.5 -45t-43 -89t-14.5 -143.5q0 -86 14.5 -142t42 -89t67.5 -46t90 -13q39 0 83.5 5.5t86.5 13.5l6 -51q-43 -10 -90 -15t-86 -5
q-63 0 -114 16.5t-87.5 56t-56 105t-19.5 163.5zM288 755l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="464" 
d="M395 425q-25 5 -55 8.5t-61 3.5q-35 0 -64 -8.5t-50 -30.5t-33 -60.5t-12 -98.5t12 -98t33.5 -60t50.5 -30.5t63 -8.5q33 0 63 3.5t60 10.5l6 -49q-34 -8 -63 -12t-64 -4q-47 0 -87.5 11.5t-70 40t-46.5 76.5t-17 120q0 73 16.5 120.5t45.5 76t70 40t89 11.5
q32 0 64.5 -3.5t56.5 -8.5zM231 569l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="679" 
d="M318 665q70 0 124 -19t91 -59t56.5 -102.5t19.5 -148.5q0 -172 -70.5 -259t-218.5 -87q-51 0 -102 6.5t-102 18.5v650h202zM176 57q32 -6 68.5 -9t75.5 -3q54 0 96.5 16t71.5 51t44 90.5t15 134.5q0 73 -15.5 125.5t-45 85.5t-72 48.5t-96.5 15.5h-142v-555zM308 755
l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" 
d="M409 416q-37 10 -70 15.5t-68 5.5q-29 0 -56.5 -9t-48.5 -31t-34 -60t-13 -96q0 -62 11.5 -100.5t32.5 -60.5t49.5 -30t63.5 -8q32 0 66.5 4.5t66.5 10.5v359zM466 730v-711q-45 -10 -94.5 -19t-96.5 -9q-44 0 -83 11t-68.5 39t-46.5 76.5t-17 123.5q0 71 18 118.5
t47.5 76t67.5 40t79 11.5q39 0 70.5 -5t66.5 -13v261h57zM649 665l-83 -213h-52l76 213h59z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="678" 
d="M20 365h95v300h202q70 0 124 -19t91 -59t56.5 -102.5t19.5 -148.5q0 -172 -70.5 -259t-218.5 -87q-51 0 -102 6.5t-102 18.5v297h-95v53zM175 365h182v-53h-182v-255q32 -6 68.5 -9t75.5 -3q54 0 96.5 16t71.5 51t44 90.5t15 134.5q0 73 -15.5 125.5t-45 85.5t-72 48.5
t-96.5 15.5h-142v-247z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="565" 
d="M250 624h159v106h57v-106h89v-49h-89v-556q-45 -10 -94.5 -19t-96.5 -9q-44 0 -83 11t-68.5 39t-46.5 76.5t-17 123.5q0 71 18 118.5t47.5 76t67.5 40t79 11.5q39 0 70.5 -5t66.5 -13v106h-159v49zM409 416q-37 10 -70 15.5t-68 5.5q-29 0 -56.5 -9t-48.5 -31t-34 -60
t-13 -96q0 -62 11.5 -100.5t32.5 -60.5t49.5 -30t63.5 -8q32 0 66.5 4.5t66.5 10.5v359z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374zM415 860v-50h-234v50h234z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM385 674v-50h-234v50h234z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374zM209 897q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5
t-11 54h50z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM179 711q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374zM298 875q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM268 689q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="559" 
d="M264 0q-72 0 -113 35.5t-41 101.5v528h374v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-39q-6 -7 -14.5 -18t-16 -23.5t-13 -26t-5.5 -25.5q0 -18 13.5 -24.5t30.5 -6.5q8 0 20 1.5t23 5.5l8 -45q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5
t-26.5 54.5q0 37 16.5 62.5t28.5 39.5h-141z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-6 -7 -14.5 -18t-16 -23.5t-13 -26t-5.5 -25.5q0 -18 11 -24.5t28 -6.5q8 0 22.5 2t25.5 6l8 -46q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5t-26.5 54.5q0 29 11 51.5t22 37.5
q-17 -2 -35.5 -3.5t-36.5 -1.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339zM401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="559" 
d="M484 665v-53h-314v-242h277v-53h-277v-180q0 -45 24.5 -64.5t69.5 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v528h374zM266 755l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="515" 
d="M121 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 34.5 1.5t36.5 4t34.5 5.5t28.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-57 0 -102.5 10.5t-77.5 38.5t-49.5 76t-17.5 122q0 66 14.5 113t41.5 77t66 44t88 14q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339z
M401 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM236 569l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="675" 
d="M561 603q-35 7 -86 13t-117 6q-57 0 -99.5 -15.5t-70.5 -50t-41.5 -89t-13.5 -132.5q0 -84 14.5 -140.5t42.5 -90t70 -48t97 -14.5q22 0 46.5 1.5t48 3.5t43.5 5t34 6v254h-166v53h227v-347q-53 -14 -115.5 -21t-119.5 -7q-67 0 -120 17.5t-89.5 58t-56 107t-19.5 164.5
q0 178 71.5 258t215.5 80q72 0 124.5 -6t87.5 -12zM369 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="563" 
d="M467 -21q0 -91 -49 -133.5t-144 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-32 -5 -66.5 -9.5t-70.5 -4.5q-42 0 -80.5 9.5t-68 36t-47 74.5t-17.5 125q0 70 18 117t48 74.5t70 39t84 11.5q49 0 94.5 -10t92.5 -24v-474z
M410 416q-12 3 -27.5 7t-33 7t-35.5 5t-33 2q-33 0 -62.5 -8t-51 -29t-34.5 -58.5t-13 -96.5q0 -62 11.5 -100.5t32 -59.5t48.5 -28t60 -7q33 0 68 4.5t70 10.5v351zM300 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="675" 
d="M561 603q-35 7 -86 13t-117 6q-57 0 -99.5 -15.5t-70.5 -50t-41.5 -89t-13.5 -132.5q0 -84 14.5 -140.5t42.5 -90t70 -48t97 -14.5q22 0 46.5 1.5t48 3.5t43.5 5t34 6v254h-166v53h227v-347q-53 -14 -115.5 -21t-119.5 -7q-67 0 -120 17.5t-89.5 58t-56 107t-19.5 164.5
q0 178 71.5 258t215.5 80q72 0 124.5 -6t87.5 -12zM248 897q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="563" 
d="M467 -21q0 -91 -49 -133.5t-144 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-32 -5 -66.5 -9.5t-70.5 -4.5q-42 0 -80.5 9.5t-68 36t-47 74.5t-17.5 125q0 70 18 117t48 74.5t70 39t84 11.5q49 0 94.5 -10t92.5 -24v-474z
M410 416q-12 3 -27.5 7t-33 7t-35.5 5t-33 2q-33 0 -62.5 -8t-51 -29t-34.5 -58.5t-13 -96.5q0 -62 11.5 -100.5t32 -59.5t48.5 -28t60 -7q33 0 68 4.5t70 10.5v351zM179 711q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5
t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="675" 
d="M561 603q-35 7 -86 13t-117 6q-57 0 -99.5 -15.5t-70.5 -50t-41.5 -89t-13.5 -132.5q0 -84 14.5 -140.5t42.5 -90t70 -48t97 -14.5q22 0 46.5 1.5t48 3.5t43.5 5t34 6v254h-166v53h227v-347q-53 -14 -115.5 -21t-119.5 -7q-67 0 -120 17.5t-89.5 58t-56 107t-19.5 164.5
q0 178 71.5 258t215.5 80q72 0 124.5 -6t87.5 -12zM337 875q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="563" 
d="M467 -21q0 -91 -49 -133.5t-144 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-32 -5 -66.5 -9.5t-70.5 -4.5q-42 0 -80.5 9.5t-68 36t-47 74.5t-17.5 125q0 70 18 117t48 74.5t70 39t84 11.5q49 0 94.5 -10t92.5 -24v-474z
M410 416q-12 3 -27.5 7t-33 7t-35.5 5t-33 2q-33 0 -62.5 -8t-51 -29t-34.5 -58.5t-13 -96.5q0 -62 11.5 -100.5t32 -59.5t48.5 -28t60 -7q33 0 68 4.5t70 10.5v351zM268 689q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="675" 
d="M561 603q-35 7 -86 13t-117 6q-57 0 -99.5 -15.5t-70.5 -50t-41.5 -89t-13.5 -132.5q0 -84 14.5 -140.5t42.5 -90t70 -48t97 -14.5q22 0 46.5 1.5t48 3.5t43.5 5t34 6v254h-166v53h227v-347q-53 -14 -115.5 -21t-119.5 -7q-67 0 -120 17.5t-89.5 58t-56 107t-19.5 164.5
q0 178 71.5 258t215.5 80q72 0 124.5 -6t87.5 -12zM413 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="563" 
d="M467 -21q0 -91 -49 -133.5t-144 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-32 -5 -66.5 -9.5t-70.5 -4.5q-42 0 -80.5 9.5t-68 36t-47 74.5t-17.5 125q0 70 18 117t48 74.5t70 39t84 11.5q49 0 94.5 -10t92.5 -24v-474z
M410 416q-12 3 -27.5 7t-33 7t-35.5 5t-33 2q-33 0 -62.5 -8t-51 -29t-34.5 -58.5t-13 -96.5q0 -62 11.5 -100.5t32 -59.5t48.5 -28t60 -7q33 0 68 4.5t70 10.5v351zM228 569l102 161h56l-89 -161h-69z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="748" 
d="M176 665v-295h396v295h60v-665h-60v317h-396v-317h-60v665h60zM406 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="587" 
d="M99 730h57v-264q34 8 72 14.5t76 6.5t73 -8t62 -26.5t43 -49.5t16 -77v-326h-57v323q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v730zM161 942l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="746" 
d="M175 665v-128h396v128h60v-128h95v-51h-95v-486h-60v317h-396v-317h-60v486h-95v51h95v128h60zM571 370v116h-396v-116h396z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="587" 
d="M99 575h-89v49h89v106h57v-106h159v-49h-159v-109q34 8 72 14.5t76 6.5t73 -8t62 -26.5t43 -49.5t16 -77v-326h-57v323q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v575z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="291" 
d="M176 665v-665h-60v665h60zM2 806q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="255" 
d="M156 479v-479h-57v479h57zM-17 620q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="291" 
d="M176 665v-665h-60v665h60zM263 860v-50h-234v50h234z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="255" 
d="M156 479v-479h-57v479h57zM244 674v-50h-234v50h234z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="291" 
d="M176 665v-665h-60v665h60zM57 897q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="255" 
d="M156 479v-479h-57v479h57zM38 711q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="291" 
d="M116 0v665h60v-665q-6 -7 -14.5 -18t-16 -23.5t-13 -26t-5.5 -25.5q0 -18 11 -24.5t28 -6.5q8 0 22.5 2t25.5 6l8 -46q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5t-26.5 54.5q0 37 16.5 62.5t28.5 39.5h-5z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="255" 
d="M99 479h57v-479q-6 -7 -14.5 -18t-16 -23.5t-13 -26t-5.5 -25.5q0 -18 11 -24.5t28 -6.5q8 0 22.5 2t25.5 6l8 -46q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5t-26.5 54.5q0 18 4.5 34t11.5 29t14.5 23t12.5 16v479zM170 666q0 -17 -10.5 -28t-32.5 -11t-32.5 11t-10.5 28
t10.5 28t32.5 11t32.5 -11t10.5 -28z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="291" 
d="M176 665v-665h-60v665h60zM146 875q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="255" 
d="M156 479v-479h-57v479h57z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="291" 
d="M176 665v-607q0 -37 -9 -68t-28 -60.5t-49.5 -60t-73.5 -66.5l-34 43q38 33 64 60.5t41.5 52.5t22 49t6.5 50v607h60zM178 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="255" 
d="M156 479v-424q0 -42 -5.5 -72t-21 -57t-43.5 -55.5t-73 -66.5l-33 38q39 34 62 60t36 50t17 48.5t4 54.5v424h57zM160 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="592" 
d="M176 665v-370l335 370h73l-267 -297q70 -20 115 -69t74 -115l81 -184h-67l-73 165q-26 60 -67 102t-104 53l-100 -102v-218h-60v665h60zM392 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="516" 
d="M156 730v-496l263 245h72l-203 -194q50 -11 83 -49t55 -85l70 -151h-63l-59 126q-10 21 -22.5 41.5t-28 37t-34.5 27.5t-43 14l-90 -79v-167h-57v730h57zM337 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="508" 
d="M176 665v-612h307v-53h-367v665h60zM276 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="255" 
d="M156 730v-730h-57v730h57zM257 933l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="508" 
d="M176 665v-612h307v-53h-367v665h60zM353 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="255" 
d="M156 730v-730h-57v730h57zM165 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="508" 
d="M176 665v-612h307v-53h-367v665h60zM114 755l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="296" 
d="M156 730v-730h-57v730h57zM340 665l-83 -213h-52l76 213h59z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="508" 
d="M176 665v-311l200 78v-55l-200 -78v-246h307v-53h-367v275l-95 -37v55l95 37v335h60z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="255" 
d="M156 730v-326l79 31v-54l-79 -31v-350h-57v328l-79 -31v54l79 31v348h57z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="754" 
d="M168 665l412 -425v425h58v-665h-58v159l-406 417v-576h-58v665h52zM492 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="587" 
d="M99 453q19 6 43.5 12t51 11t54.5 8t56 3q38 0 73 -8t62 -26.5t43 -49.5t16 -77v-326h-57v323q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v453zM419 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="754" 
d="M168 665l412 -425v425h58v-665h-58v159l-406 417v-576h-58v665h52zM425 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="587" 
d="M99 453q19 6 43.5 12t51 11t54.5 8t56 3q38 0 73 -8t62 -26.5t43 -49.5t16 -77v-326h-57v323q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v453zM347 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="754" 
d="M168 665l412 -425v425h58v-665h-58v159l-406 417v-576h-58v665h52zM345 755l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="587" 
d="M99 453q19 6 43.5 12t51 11t54.5 8t56 3q38 0 73 -8t62 -26.5t43 -49.5t16 -77v-326h-57v323q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v453zM267 569l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="754" 
d="M168 665l412 -425v425h58v-607q0 -37 -9 -68t-28 -60.5t-49.5 -60t-73.5 -66.5l-34 43q38 33 64 60.5t42 52.5t23 49t7 50v101l-406 417v-576h-58v665h52z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="587" 
d="M99 453q19 6 43.5 12t51 11t54.5 8t56 3q38 0 73 -8t62 -26.5t43 -49.5t16 -77v-271q0 -42 -5.5 -72t-21 -57t-43.5 -55.5t-73 -66.5l-33 38q39 34 62 60t36 50t17 48.5t4 54.5v268q0 33 -11 55t-29.5 35t-44.5 18.5t-56 5.5q-37 0 -75 -6.5t-69 -14.5v-416h-57v453z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15zM459 861v-50h-234v50h234z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="538" 
d="M269 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM270 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM386 674v-50
h-234v50h234z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15zM253 898q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z
" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="538" 
d="M269 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM270 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM180 711
q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="684" 
d="M342 675q64 0 115 -18.5t85.5 -59t53 -106t18.5 -159.5q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5
t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15zM386 916l-69 -161h-51l52 161h68zM524 916l-96 -161h-51l75 161h72z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="538" 
d="M269 487q102 0 158 -58t56 -190t-56 -189.5t-158 -57.5t-158 57.5t-56 189.5t56 190t158 58zM270 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM304 730
l-69 -161h-51l52 161h68zM442 730l-96 -161h-51l75 161h72z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1000" 
d="M925 612h-314v-242h277v-53h-277v-180q0 -45 27 -64.5t67 -19.5h235v-53h-235q-60 0 -98.5 24.5t-50.5 71.5q-33 -55 -87 -80.5t-127 -25.5q-65 0 -115.5 18.5t-85 59t-53 106t-18.5 158.5q0 94 18.5 159.5t53 106t85 59t115.5 18.5q71 0 123 -23t86 -74v87h374v-53z
M342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5t13.5 -136.5t40 -89.5t65.5 -49t90 -15t90 15t65.5 49t40 89.5t13.5 136.5t-13.5 136.5t-40 89.5t-65.5 49t-90 15z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="883" 
d="M489 230q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 35.5 1.5t38 4t35 5.5t25.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-81 0 -136 22.5t-81 85.5q-23 -57 -69 -82.5t-115 -25.5q-102 0 -158 57.5t-56 189.5t56 190t158 58q69 0 115 -26t69 -83q23 57 68.5 83
t111.5 26q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339zM275 437q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5t-11 -97t11 -97t31 -60.5t48.5 -31t64.5 -8.5q35 0 63 8.5t48 31t31 60.5t11 97t-11 97t-31 60.5t-48 31.5t-63 9zM769 279q0 158 -132 158q-73 0 -107 -40
t-40 -118h279z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="648" 
d="M322 665q59 0 104 -9.5t76 -32.5t46.5 -61.5t15.5 -96.5q0 -82 -36.5 -126t-102.5 -63q32 -19 49.5 -45.5t36.5 -69.5l67 -161h-64l-61 148q-9 22 -21 42.5t-28 36t-37.5 25t-50.5 9.5q-32 0 -67 3.5t-73 9.5v-274h-60v665h206zM176 326q39 -7 74 -10t67 -3
q51 0 86.5 9.5t57 28t31 47t9.5 67.5q0 43 -10 71.5t-32 45t-55.5 23.5t-81.5 7h-146v-286zM432 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="354" 
d="M99 453q68 20 125.5 27t109.5 7v-51q-45 0 -94.5 -5.5t-83.5 -15.5v-415h-57v453zM340 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="648" 
d="M322 665q59 0 104 -9.5t76 -32.5t46.5 -61.5t15.5 -96.5q0 -82 -36.5 -126t-102.5 -63q32 -19 49.5 -45.5t36.5 -69.5l67 -161h-64l-61 148q-9 22 -21 42.5t-28 36t-37.5 25t-50.5 9.5q-32 0 -67 3.5t-73 9.5v-274h-60v665h206zM176 326q39 -7 74 -10t67 -3
q51 0 86.5 9.5t57 28t31 47t9.5 67.5q0 43 -10 71.5t-32 45t-55.5 23.5t-81.5 7h-146v-286zM395 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="354" 
d="M99 453q68 20 125.5 27t109.5 7v-51q-45 0 -94.5 -5.5t-83.5 -15.5v-415h-57v453zM158 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="648" 
d="M322 665q59 0 104 -9.5t76 -32.5t46.5 -61.5t15.5 -96.5q0 -82 -36.5 -126t-102.5 -63q32 -19 49.5 -45.5t36.5 -69.5l67 -161h-64l-61 148q-9 22 -21 42.5t-28 36t-37.5 25t-50.5 9.5q-32 0 -67 3.5t-73 9.5v-274h-60v665h206zM176 326q39 -7 74 -10t67 -3
q51 0 86.5 9.5t57 28t31 47t9.5 67.5q0 43 -10 71.5t-32 45t-55.5 23.5t-81.5 7h-146v-286zM277 755l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="354" 
d="M99 453q68 20 125.5 27t109.5 7v-51q-45 0 -94.5 -5.5t-83.5 -15.5v-415h-57v453zM185 569l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="561" 
d="M446 605q-13 3 -32 6t-40.5 5.5t-43 4t-38.5 1.5q-31 0 -59 -2.5t-49.5 -11t-34 -25t-12.5 -44.5q0 -31 26 -60.5t65 -59t84 -60.5t84 -64.5t65 -71.5t26 -82t-16 -73t-43.5 -46t-65.5 -24.5t-82 -7.5q-60 0 -116 10.5t-93 23.5l12 51q35 -12 89 -22.5t111 -10.5
q63 0 102.5 20t39.5 70q0 36 -26 69t-65.5 64.5t-85 63t-85 64t-65.5 67.5t-26 74q0 45 18.5 72.5t48.5 42.5t69.5 20.5t81.5 5.5q46 0 92 -5.5t72 -10.5zM373 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="446" 
d="M369 425q-22 4 -58 8t-69 4q-26 0 -49.5 -3t-41.5 -10.5t-28.5 -20.5t-10.5 -33q0 -24 21 -41.5t53 -33.5t68.5 -33t68.5 -38t53 -49t21 -66q0 -61 -46 -89t-119 -28q-50 0 -95.5 8.5t-77.5 19.5l9 50q32 -11 74.5 -19.5t89.5 -8.5q20 0 39.5 3t34.5 10.5t24 20.5t9 33
q0 28 -21 48.5t-52.5 37.5t-68.5 33t-68.5 35.5t-52.5 44.5t-21 60q0 34 14.5 56.5t40 36.5t59 20t71.5 6t75 -3.5t60 -8.5zM342 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="561" 
d="M446 605q-13 3 -32 6t-40.5 5.5t-43 4t-38.5 1.5q-31 0 -59 -2.5t-49.5 -11t-34 -25t-12.5 -44.5q0 -31 26 -60.5t65 -59t84 -60.5t84 -64.5t65 -71.5t26 -82t-16 -73t-43.5 -46t-65.5 -24.5t-82 -7.5q-60 0 -116 10.5t-93 23.5l12 51q35 -12 89 -22.5t111 -10.5
q63 0 102.5 20t39.5 70q0 36 -26 69t-65.5 64.5t-85 63t-85 64t-65.5 67.5t-26 74q0 45 18.5 72.5t48.5 42.5t69.5 20.5t81.5 5.5q46 0 92 -5.5t72 -10.5zM300 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="446" 
d="M369 425q-22 4 -58 8t-69 4q-26 0 -49.5 -3t-41.5 -10.5t-28.5 -20.5t-10.5 -33q0 -24 21 -41.5t53 -33.5t68.5 -33t68.5 -38t53 -49t21 -66q0 -61 -46 -89t-119 -28q-50 0 -95.5 8.5t-77.5 19.5l9 50q32 -11 74.5 -19.5t89.5 -8.5q20 0 39.5 3t34.5 10.5t24 20.5t9 33
q0 28 -21 48.5t-52.5 37.5t-68.5 33t-68.5 35.5t-52.5 44.5t-21 60q0 34 14.5 56.5t40 36.5t59 20t71.5 6t75 -3.5t60 -8.5zM258 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="561" 
d="M446 605q-13 3 -32 6t-40.5 5.5t-43 4t-38.5 1.5q-31 0 -59 -2.5t-49.5 -11t-34 -25t-12.5 -44.5q0 -31 26 -60.5t65 -59t84 -60.5t84 -64.5t65 -71.5t26 -82t-16 -73t-43.5 -46t-65.5 -24.5t-82 -7.5q-60 0 -116 10.5t-93 23.5l12 51q35 -12 89 -22.5t111 -10.5
q63 0 102.5 20t39.5 70q0 36 -26 69t-65.5 64.5t-85 63t-85 64t-65.5 67.5t-26 74q0 45 18.5 72.5t48.5 42.5t69.5 20.5t81.5 5.5q46 0 92 -5.5t72 -10.5zM312 -53q12 -19 20 -38.5t8 -36.5q0 -32 -17.5 -50t-59.5 -18q-15 0 -34.5 3.5t-31.5 8.5l11 43q12 -5 24.5 -7.5
t22.5 -2.5q19 0 25 6.5t6 20.5q0 16 -8 34t-18 37h52z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="446" 
d="M369 425q-22 4 -58 8t-69 4q-26 0 -49.5 -3t-41.5 -10.5t-28.5 -20.5t-10.5 -33q0 -24 21 -41.5t53 -33.5t68.5 -33t68.5 -38t53 -49t21 -66q0 -61 -46 -89t-119 -28q-50 0 -95.5 8.5t-77.5 19.5l9 50q32 -11 74.5 -19.5t89.5 -8.5q20 0 39.5 3t34.5 10.5t24 20.5t9 33
q0 28 -21 48.5t-52.5 37.5t-68.5 33t-68.5 35.5t-52.5 44.5t-21 60q0 34 14.5 56.5t40 36.5t59 20t71.5 6t75 -3.5t60 -8.5zM259 -53q12 -19 20 -38.5t8 -36.5q0 -32 -17.5 -50t-59.5 -18q-15 0 -34.5 3.5t-31.5 8.5l11 43q12 -5 24.5 -7.5t22.5 -2.5q19 0 25 6.5t6 20.5
q0 16 -8 34t-18 37h52z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="561" 
d="M446 605q-13 3 -32 6t-40.5 5.5t-43 4t-38.5 1.5q-31 0 -59 -2.5t-49.5 -11t-34 -25t-12.5 -44.5q0 -31 26 -60.5t65 -59t84 -60.5t84 -64.5t65 -71.5t26 -82t-16 -73t-43.5 -46t-65.5 -24.5t-82 -7.5q-60 0 -116 10.5t-93 23.5l12 51q35 -12 89 -22.5t111 -10.5
q63 0 102.5 20t39.5 70q0 36 -26 69t-65.5 64.5t-85 63t-85 64t-65.5 67.5t-26 74q0 45 18.5 72.5t48.5 42.5t69.5 20.5t81.5 5.5q46 0 92 -5.5t72 -10.5zM233 755l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="446" 
d="M369 425q-22 4 -58 8t-69 4q-26 0 -49.5 -3t-41.5 -10.5t-28.5 -20.5t-10.5 -33q0 -24 21 -41.5t53 -33.5t68.5 -33t68.5 -38t53 -49t21 -66q0 -61 -46 -89t-119 -28q-50 0 -95.5 8.5t-77.5 19.5l9 50q32 -11 74.5 -19.5t89.5 -8.5q20 0 39.5 3t34.5 10.5t24 20.5t9 33
q0 28 -21 48.5t-52.5 37.5t-68.5 33t-68.5 35.5t-52.5 44.5t-21 60q0 34 14.5 56.5t40 36.5t59 20t71.5 6t75 -3.5t60 -8.5zM178 569l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="564" 
d="M544 665v-53h-232v-612h-60v612h-232v53h524zM313 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="365" 
d="M174 621v-142h149v-49h-149v-329q0 -33 22 -46t51 -13q26 0 50.5 5.5t42.5 10.5l3 -50q-19 -7 -45.5 -11.5t-53.5 -4.5q-25 0 -48 5.5t-40.5 18t-28 33.5t-10.5 53v328h-82v49h82l15 142h42zM296 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="564" 
d="M544 665v-53h-232v-612h-60v612h-232v53h524zM250 755l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="365" 
d="M366 665l-62 -140h-52l53 140h61zM174 621v-142h149v-49h-149v-329q0 -33 22 -46t51 -13q26 0 50.5 5.5t42.5 10.5l3 -50q-19 -7 -45.5 -11.5t-53.5 -4.5q-25 0 -48 5.5t-40.5 18t-28 33.5t-10.5 53v328h-82v49h82l15 142h42z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="564" 
d="M56 365h196v247h-232v53h524v-53h-232v-247h196v-53h-196v-312h-60v312h-196v53z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="387" 
d="M189 621v-142h149v-49h-149v-163h129v-49h-129v-117q0 -33 22 -46t51 -13q26 0 51 5.5t42 10.5l3 -50q-21 -8 -46.5 -12t-52.5 -4q-25 0 -48 5.5t-40.5 18t-28 33.5t-10.5 53v116h-82v49h82v163h-82v49h82l15 142h42z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM206 806q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5
q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM139 620q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5
q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM467 860v-50h-234v50h234z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM408 674v-50h-234v50h234z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM261 897q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54
t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM202 711q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5
t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM443 836q0 -37 -24.5 -59t-68.5 -22t-68.5 22t-24.5 59t24.5 59t68.5 22t68.5 -22
t24.5 -59zM395 836q0 20 -10.5 31t-34.5 11t-34.5 -11t-10.5 -31t10.5 -31t34.5 -11t34.5 11t10.5 31z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM384 648q0 -37 -24.5 -59t-68.5 -22t-68.5 22t-24.5 59t24.5 59t68.5 22t68.5 -22
t24.5 -59zM336 648q0 20 -10.5 31t-34.5 11t-34.5 -11t-10.5 -31t10.5 -31t34.5 -11t34.5 11t10.5 31z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-41 -13 -98 -26.5t-141 -13.5q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60zM374 916l-69 -161h-51l52 161h68zM512 916l-96 -161h-51l75 161h72z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="571" 
d="M475 26q-39 -13 -90.5 -23.5t-103.5 -10.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453zM321 730l-69 -161h-51l52 161h68zM459 730l-96 -161h-51l75 161h72z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="701" 
d="M165 665v-431q0 -40 9.5 -75t32 -60.5t59.5 -40t92 -14.5q56 0 101.5 7.5t77.5 16.5v597h59v-635q-6 -7 -14.5 -18t-16 -23.5t-13 -26t-5.5 -25.5q0 -18 11 -24.5t28 -6.5q8 0 22.5 2t25.5 6l8 -46q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5t-26.5 54.5q0 26 9.5 47
t20.5 35q-34 -8 -75 -14t-94 -6q-68 0 -116 18.5t-78 50.5t-44 76.5t-14 97.5v432h60z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="571" 
d="M475 26q-6 -7 -14.5 -18t-16 -24t-13 -26.5t-5.5 -25.5q0 -18 11 -24.5t28 -6.5q8 0 22.5 2t25.5 6l8 -46q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5t-26.5 54.5q0 27 9 48t20 35q-29 -6 -60.5 -10t-62.5 -4q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v321h57v-318
q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 6t61.5 13v418h57v-453z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="893" 
d="M478 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64zM191 0l-178 665h61l154 -606l182 469h71l181 -469q62 143 100.5 290.5t63.5 315.5h54q-16 -98 -33.5 -184t-40 -165.5t-50 -157t-63.5 -158.5h-67l-181 467l-185 -467h-69z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="812" 
d="M437 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64zM178 0l-160 479h60l134 -424l162 424h64l160 -424q48 91 87 200.5t56 223.5h53q-20 -134 -65 -250.5t-104 -228.5h-60l-160 427l-162 -427h-65z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="537" 
d="M317 916l96 -161h-59l-69 113l-69 -113h-59l96 161h64zM247 226l-257 439h71l223 -381q42 41 75 88t57.5 96.5t41 99.5t25.5 97h59q-28 -128 -88 -241.5t-149 -196.5v-227h-58v226z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="574" 
d="M475 479v-500q0 -91 -48 -133.5t-145 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-25 -5 -61 -9.5t-76 -4.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v313h57v-310q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 5
t61.5 10v414h57zM315 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="537" 
d="M202 874q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM375 874q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11zM247 226l-257 439h71l223 -381q42 41 75 88t57.5 96.5t41 99.5t25.5 97h59
q-28 -128 -88 -241.5t-149 -196.5v-227h-58v226z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="602" 
d="M541 665v-47l-410 -565h419v-53h-498v45l415 567h-401v53h475zM423 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="483" 
d="M436 479v-42l-317 -388h325v-49h-400v42l319 388h-311v49h384zM373 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="602" 
d="M541 665v-47l-410 -565h419v-53h-498v45l415 567h-401v53h475zM300 875q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="483" 
d="M436 479v-42l-317 -388h325v-49h-400v42l319 388h-311v49h384zM242 689q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="602" 
d="M541 665v-47l-410 -565h419v-53h-498v45l415 567h-401v53h475zM268 755l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="483" 
d="M436 479v-42l-317 -388h325v-49h-400v42l319 388h-311v49h384zM209 569l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="344" 
d="M121 479v91q0 84 49 123.5t131 39.5q20 0 42 -1.5t41 -4.5l-7 -49q-17 3 -37 4t-39 1q-26 0 -48.5 -5t-39 -18t-26 -35t-9.5 -55v-570h-57v430h-81v49h81z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M211 376v133q0 45 14 76.5t39 51.5t57.5 29t70.5 9q33 0 71.5 -4.5t67.5 -11.5l-8 -52q-29 6 -63.5 10.5t-65.5 4.5q-27 0 -49.5 -5t-38.5 -17.5t-25 -34.5t-9 -56v-133h243v-51h-243v-325h-61v325h-133v51h133z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="682" 
d="M434 836q0 -37 -24.5 -59t-68.5 -22t-68.5 22t-24.5 59q0 32 18 52.5t51 26.5l59 106h69l-76 -107q31 -7 48 -27.5t17 -50.5zM386 836q0 20 -10.5 31t-34.5 11t-34.5 -11t-10.5 -31t10.5 -31t34.5 -11t34.5 11t10.5 31zM160 0h-60v432q0 52 12 97t40 77t74.5 50.5
t114.5 18.5q69 0 115.5 -18.5t74 -50.5t39.5 -77t12 -97v-432h-60v264h-362v-264zM522 317v114q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15t-55.5 -40.5t-28 -60.5t-7.5 -75v-114h362z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="507" 
d="M351 648q0 -37 -24.5 -59t-68.5 -22t-68.5 22t-24.5 59q0 32 18 52.5t51 26.5l59 106h69l-76 -107q31 -7 48 -27.5t17 -50.5zM303 648q0 20 -10.5 31t-34.5 11t-34.5 -11t-10.5 -31t10.5 -31t34.5 -11t34.5 11t10.5 31zM99 473q23 5 58.5 9.5t68.5 4.5q37 0 72.5 -7
t62.5 -24t43.5 -46.5t16.5 -74.5v-311q-41 -11 -88 -21.5t-107 -10.5q-37 0 -70 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q40 0 71.5 -3.5t57.5 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5t-52 4.5q-32 0 -64 -4t-57 -9zM364 221q-27 5 -61 8.5
t-69 3.5q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t47.5 -5q42 0 76.5 6t60.5 13v160z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="971" 
d="M896 665v-53h-314v-242h277v-53h-277v-180q0 -45 27 -64.5t67 -19.5h235v-53h-235q-72 0 -113 35.5t-41 101.5v180h-362v-317h-60v432q0 52 12 97t40 77t74.5 50.5t114.5 18.5q69 0 112 -19.5t69 -53.5v63h374zM522 370v61q0 40 -8 75t-28 60.5t-55 40.5t-90 15t-90 -15
t-55.5 -40.5t-28 -60.5t-7.5 -75v-61h362zM688 916l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="814" 
d="M99 473q24 5 59 9.5t68 4.5q57 0 106 -17.5t72 -64.5q25 43 66 62.5t99 19.5q47 0 82.5 -14t59.5 -39.5t36 -60.5t12 -78v-65h-339q0 -45 8 -80t29 -59t57 -36.5t92 -12.5q16 0 35.5 1.5t38 4t35 5.5t25.5 6l5 -49q-25 -7 -62.5 -12.5t-76.5 -5.5q-58 0 -104.5 11.5
t-78.5 40.5q-41 -23 -89 -37.5t-107 -14.5q-38 0 -71 8t-57.5 26t-39 46.5t-14.5 70.5q0 41 16 68t42 43t60.5 22.5t71.5 6.5q33 0 68 -3.5t61 -7.5v62q0 30 -11.5 50t-31 32t-44.5 16.5t-52 4.5q-32 0 -63.5 -4t-56.5 -9zM390 86q-30 51 -31 136q-27 4 -59.5 7.5t-65.5 3.5
q-26 0 -49.5 -3.5t-41.5 -14t-29 -29t-11 -48.5q0 -27 10 -45.5t27 -29.5t39.5 -16t48.5 -5q21 0 43.5 3t44 9t41 14t33.5 18zM700 279q0 158 -132 158q-73 0 -107 -40t-40 -118h279zM541 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="684" 
d="M468 916l-102 -161h-56l89 161h69zM342 675q100 0 163 -44l56 87l42 -25l-61 -95q35 -41 53.5 -106t18.5 -160q0 -93 -18.5 -158.5t-53 -106t-85.5 -59t-115 -18.5q-50 0 -91.5 10.5t-73.5 33.5l-55 -85l-42 25l61 94q-35 41 -53 105.5t-18 158.5t18.5 159.5t53 106
t85 59t115.5 18.5zM551 332q0 74 -11 125.5t-33 86.5l-299 -462q26 -21 59 -30.5t75 -9.5q51 0 90 15t65.5 49t40 89.5t13.5 136.5zM342 622q-51 0 -90 -15t-65.5 -49t-40 -89.5t-13.5 -136.5q0 -73 10.5 -124.5t31.5 -86.5l299 462q-49 39 -132 39z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="538" 
d="M475 518l-52 -85q29 -30 44.5 -77.5t15.5 -116.5q0 -132 -56 -189.5t-158 -57.5q-64 0 -107 20l-58 -95l-41 22l60 98q-33 29 -50.5 78.5t-17.5 123.5q0 132 56 190t158 58q70 0 116 -26l49 79zM358 416q-18 11 -40 16t-48 5q-36 0 -64.5 -9t-48.5 -31.5t-31 -60.5
t-11 -97q0 -57 10 -94t28 -60zM423 239q0 51 -8 86t-23 58l-202 -326q17 -8 37 -11.5t43 -3.5q35 0 63 8.5t48 31t31 60.5t11 97zM399 730l-102 -161h-56l89 161h69z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="561" 
d="M446 605q-13 3 -32 6t-40.5 5.5t-43 4t-38.5 1.5q-31 0 -59 -2.5t-49.5 -11t-34 -25t-12.5 -44.5q0 -31 26 -60.5t65 -59t84 -60.5t84 -64.5t65 -71.5t26 -82t-16 -73t-43.5 -46t-65.5 -24.5t-82 -7.5q-60 0 -116 10.5t-93 23.5l12 51q35 -12 89 -22.5t111 -10.5
q63 0 102.5 20t39.5 70q0 36 -26 69t-65.5 64.5t-85 63t-85 64t-65.5 67.5t-26 74q0 45 18.5 72.5t48.5 42.5t69.5 20.5t81.5 5.5q46 0 92 -5.5t72 -10.5zM307 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="446" 
d="M369 425q-22 4 -58 8t-69 4q-26 0 -49.5 -3t-41.5 -10.5t-28.5 -20.5t-10.5 -33q0 -24 21 -41.5t53 -33.5t68.5 -33t68.5 -38t53 -49t21 -66q0 -61 -46 -89t-119 -28q-50 0 -95.5 8.5t-77.5 19.5l9 50q32 -11 74.5 -19.5t89.5 -8.5q20 0 39.5 3t34.5 10.5t24 20.5t9 33
q0 28 -21 48.5t-52.5 37.5t-68.5 33t-68.5 35.5t-52.5 44.5t-21 60q0 34 14.5 56.5t40 36.5t59 20t71.5 6t75 -3.5t60 -8.5zM299 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="550" 
d="M307 730l96 -161h-59l-69 113l-69 -113h-59l96 161h64z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="550" 
d="M243 569l-96 161h59l69 -113l69 113h59l-96 -161h-64z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="550" 
d="M186 711q1 -32 21.5 -53.5t67.5 -21.5t67.5 21.5t21.5 53.5h50q0 -31 -11 -54t-30 -38.5t-44.5 -23.5t-53.5 -8q-29 0 -54 8t-44 23.5t-30 38.5t-11 54h50z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="550" 
d="M275 689q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="550" 
d="M367 648q0 -37 -24.5 -59t-68.5 -22t-68.5 22t-24.5 59t24.5 59t68.5 22t68.5 -22t24.5 -59zM319 648q0 20 -10.5 31t-34.5 11t-34.5 -11t-10.5 -31t10.5 -31t34.5 -11t34.5 11t10.5 31z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="550" 
d="M303 0q-6 -7 -14.5 -18t-16 -23.5t-13 -26t-5.5 -25.5q0 -18 11 -24.5t28 -6.5q8 0 22.5 2t25.5 6l8 -46q-11 -5 -27 -8t-33 -3q-33 0 -59.5 16.5t-26.5 54.5q0 37 16.5 62.5t28.5 39.5h55z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="550" 
d="M131 620q11 31 29.5 49.5t53.5 18.5q19 0 36 -5t32.5 -11.5t29.5 -11.5t27 -5q18 0 23.5 13t10.5 25l46 -14q-9 -37 -27.5 -54.5t-53.5 -17.5q-15 0 -31.5 5t-33.5 11.5t-32 11.5t-27 5q-19 0 -26 -10.5t-12 -23.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="550" 
d="M275 730l-69 -161h-51l52 161h68zM413 730l-96 -161h-51l75 161h72z" />
    <glyph glyph-name="Delta" unicode="&#x394;" 
d="M335 666l230 -666h-525l229 666h66zM116 45h374l-188 559z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" 
d="M335 666l230 -666h-525l229 666h66zM116 45h374l-188 559z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="693" 
d="M197 49q-63 37 -92 107t-29 178q0 178 69 258.5t201 80.5t201.5 -80.5t69.5 -258.5q0 -108 -29 -178t-92 -107h142v-49h-252v46q43 12 75.5 33.5t54.5 56t33 83.5t11 115q0 81 -14 136.5t-41 90t-67 49.5t-92 15t-92 -15t-67 -49.5t-40.5 -90t-13.5 -136.5q0 -66 11 -115
t33 -83.5t54.5 -56t75.5 -33.5v-46h-252v49h142z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="693" 
d="M197 49q-63 37 -92 107t-29 178q0 178 69 258.5t201 80.5t201.5 -80.5t69.5 -258.5q0 -108 -29 -178t-92 -107h142v-49h-252v46q43 12 75.5 33.5t54.5 56t33 83.5t11 115q0 81 -14 136.5t-41 90t-67 49.5t-92 15t-92 -15t-67 -49.5t-40.5 -90t-13.5 -136.5q0 -66 11 -115
t33 -83.5t54.5 -56t75.5 -33.5v-46h-252v49h142z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="610" 
d="M580 479v-50h-105v-429h-57v429h-224v-429h-57v429h-107v50h550z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="893" 
d="M394 916l89 -161h-57l-102 161h70zM191 0l-178 665h61l154 -606l182 469h71l181 -469q62 143 100.5 290.5t63.5 315.5h54q-16 -98 -33.5 -184t-40 -165.5t-50 -157t-63.5 -158.5h-67l-181 467l-185 -467h-69z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="812" 
d="M347 730l89 -161h-57l-102 161h70zM178 0l-160 479h60l134 -424l162 424h64l160 -424q48 91 87 200.5t56 223.5h53q-20 -134 -65 -250.5t-104 -228.5h-60l-160 427l-162 -427h-65z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="893" 
d="M583 916l-102 -161h-56l89 161h69zM191 0l-178 665h61l154 -606l182 469h71l181 -469q62 143 100.5 290.5t63.5 315.5h54q-16 -98 -33.5 -184t-40 -165.5t-50 -157t-63.5 -158.5h-67l-181 467l-185 -467h-69z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="812" 
d="M539 730l-102 -161h-56l89 161h69zM178 0l-160 479h60l134 -424l162 424h64l160 -424q48 91 87 200.5t56 223.5h53q-20 -134 -65 -250.5t-104 -228.5h-60l-160 427l-162 -427h-65z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="893" 
d="M360 874q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM533 874q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11zM191 0l-178 665h61l154 -606l182 469h71l181 -469q62 143 100.5 290.5t63.5 315.5h54
q-16 -98 -33.5 -184t-40 -165.5t-50 -157t-63.5 -158.5h-67l-181 467l-185 -467h-69z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="812" 
d="M319 688q22 0 32.5 -11t10.5 -28t-10.5 -28t-32.5 -11t-32.5 11t-10.5 28t10.5 28t32.5 11zM492 688q21 0 32 -11t11 -28t-11 -28t-32 -11q-22 0 -32.5 11t-10.5 28t10.5 28t32.5 11zM178 0l-160 479h60l134 -424l162 424h64l160 -424q48 91 87 200.5t56 223.5h53
q-20 -134 -65 -250.5t-104 -228.5h-60l-160 427l-162 -427h-65z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="537" 
d="M237 916l89 -161h-57l-102 161h70zM247 226l-257 439h71l223 -381q42 41 75 88t57.5 96.5t41 99.5t25.5 97h59q-28 -128 -88 -241.5t-149 -196.5v-227h-58v226z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="574" 
d="M475 479v-500q0 -91 -48 -133.5t-145 -42.5q-39 0 -85.5 5.5t-86.5 17.5l9 51q39 -11 83 -17t79 -6q75 0 106 30.5t31 91.5v38q-25 -5 -61 -9.5t-76 -4.5q-38 0 -72.5 8.5t-60.5 28t-42 51t-16 78.5v313h57v-310q0 -34 11.5 -57t30 -36.5t43 -19.5t51.5 -6q36 0 73.5 5
t61.5 10v414h57zM239 730l89 -161h-57l-102 161h70z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="581" 
d="M515 295v-48h-449v48h449z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="801" 
d="M735 295v-48h-669v48h669z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="297" 
d="M81 452l83 213h52l-76 -213h-59z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="297" 
d="M216 665l-83 -213h-52l76 213h59z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="297" 
d="M216 53l-83 -213h-52l76 213h59z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="484" 
d="M81 452l83 213h52l-76 -213h-59zM278 452l73 213h52l-66 -213h-59z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="484" 
d="M206 665l-73 -213h-52l66 213h59zM403 665l-83 -213h-52l76 213h59z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="484" 
d="M206 53l-73 -213h-52l66 213h59zM403 53l-83 -213h-52l76 213h59z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="438" 
d="M246 665v-186h159v-48h-159v-283h-55v283h-158v48h158v186h55z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="438" 
d="M246 665v-154h159v-49h-159v-101h159v-49h-159v-164h-55v164h-158v49h158v101h-158v49h158v154h55z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="385" 
d="M193 351q37 0 60 -21t23 -59t-23 -59.5t-60 -21.5t-61 21.5t-24 59.5t24 59t61 21z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="853" 
d="M716 71q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10zM137 71q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5t-11.5 29.5t11.5 29t29.5 10zM426 71q19 0 30 -10t11 -29t-11 -29.5t-30 -10.5q-18 0 -29.5 10.5
t-11.5 29.5t11.5 29t29.5 10z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1241" 
d="M1033 297q-19 0 -36.5 -6t-30.5 -20.5t-20.5 -39.5t-7.5 -63t7.5 -63t20.5 -40t30.5 -21.5t36.5 -6.5q18 0 35 6t29.5 21t20 40t7.5 63q0 39 -7.5 64t-20 39.5t-29.5 20.5t-35 6zM1033 341q32 0 59.5 -9t47.5 -30t31 -54t11 -81q0 -47 -11 -80t-31 -54t-47.5 -31
t-59.5 -10q-33 0 -60.5 10t-47.5 31t-31 54.5t-11 80.5q0 48 11 81t31 53.5t47.5 29.5t60.5 9zM209 632q-19 0 -36.5 -6t-30.5 -20.5t-20.5 -39.5t-7.5 -63t7.5 -63t20.5 -40t30.5 -21.5t36.5 -6.5q18 0 35 6t29.5 21t20 40t7.5 63q0 39 -7.5 64t-20 39.5t-29.5 20.5t-35 6z
M209 676q32 0 59.5 -9t47.5 -30t31 -54t11 -81q0 -47 -11 -80t-31 -54t-47.5 -31t-59.5 -10q-33 0 -60.5 10t-47.5 31t-31 54.5t-11 80.5q0 48 11 81t31 53.5t47.5 29.5t60.5 9zM652 297q-19 0 -36 -6t-30 -20.5t-20.5 -39.5t-7.5 -63t7.5 -63t20.5 -40t30 -21.5t36 -6.5
t35.5 6t29.5 21t20.5 40t7.5 63q0 39 -7.5 64t-20.5 39.5t-29.5 20.5t-35.5 6zM652 341q32 0 59.5 -9t47.5 -30t31 -54t11 -81q0 -47 -11 -80t-31 -54t-47.5 -31t-59.5 -10q-33 0 -60.5 10t-47.5 31t-31 54.5t-11 80.5q0 48 11 81t31 53.5t47.5 29.5t60.5 9zM676 673
l-434 -673h-65l433 673h66z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="344" 
d="M278 452l-147 -181l147 -181h-60l-152 181l152 181h60z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="344" 
d="M126 452l152 -181l-152 -181h-60l147 181l-147 181h60z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="514" 
d="M503 665l-429 -665h-63l428 665h64z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M202 213q13 -93 58 -132t118 -39q37 0 77 4.5t75 13.5l6 -51q-35 -10 -77.5 -14.5t-81.5 -4.5q-98 0 -159.5 53t-78.5 170h-101v51h95q-2 16 -2.5 30.5t-0.5 34.5v22.5t1 21.5h-93v51h97q13 134 75.5 192.5t168.5 58.5q39 0 80.5 -4.5t72.5 -11.5l-8 -53q-31 7 -70 11.5
t-76 4.5q-40 0 -71 -10t-53.5 -33t-36.5 -61t-20 -94h286l-22 -51h-266q-1 -10 -1 -21.5v-23.5q0 -17 0.5 -33.5t1.5 -30.5h231l-22 -51h-203z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="799" 
d="M292 665v-40h-101v-259h-51v259h-102v40h254zM558 429h-42l-105 168v-231h-49v299h55l120 -190l121 190h55v-299h-48v231z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M180 641q33 14 74 24t97 10q94 0 138 -47.5t44 -147.5q0 -30 -3.5 -67.5t-11.5 -79.5q-18 -90 -44.5 -154.5t-63 -106.5t-83.5 -61.5t-106 -19.5q-81 0 -127 45.5t-46 123.5q0 112 63 173.5t184 61.5q57 0 103 -13.5t69 -26.5q8 41 10.5 71t2.5 55q0 82 -34.5 115
t-103.5 33q-49 0 -85 -10t-70 -23zM458 306q-32 17 -71 29.5t-97 12.5q-42 0 -77 -10t-59.5 -32t-38.5 -58t-14 -87q0 -57 34 -90t93 -33q44 0 79.5 14.5t63.5 46.5t49 83t38 124z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="715" 
d="M669 665v-44h-82v-621h-55v621h-349v-621h-55v621h-82v44h623z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M524 665v-44h-374l237 -274l-238 -302h402v-45h-475v40l245 306l-245 281v38h448z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M531 342v-45h-457v45h457z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M179 479l134 -419l199 605h54l-218 -665h-68l-145 434h-97v45h141z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="682" 
d="M314 318q-40 38 -68.5 57.5t-62.5 19.5q-36 0 -60 -17t-24 -58t24 -58t60 -17q34 0 62.5 17.5t68.5 55.5zM368 322q40 -38 68.5 -57.5t62.5 -19.5q36 0 60 17t24 58t-24 58t-60 17q-34 0 -62.5 -17.5t-68.5 -55.5zM341 357q40 41 77.5 61.5t80.5 20.5q60 0 98.5 -30
t38.5 -89t-38.5 -89t-98.5 -30q-43 0 -80.5 20.5t-77.5 61.5q-40 -41 -77.5 -61.5t-80.5 -20.5q-60 0 -98.5 30t-38.5 89t38.5 89t98.5 30q43 0 80.5 -20.5t77.5 -61.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="286" 
d="M311 685q-3 0 -7 0.5t-8 0.5q-60 0 -93 -27.5t-33 -88.5v-599q0 -79 -44.5 -121.5t-129.5 -42.5h-14t-12 1l5 48q3 0 7 -0.5t8 -0.5q60 0 93 27.5t33 88.5v599q0 79 44.5 121.5t129.5 42.5h14t12 -1z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M63 201q12 34 41 56t74 22q21 0 55.5 -8.5t71.5 -18.5t70 -18.5t51 -8.5q35 0 49.5 14.5t26.5 36.5l40 -18q-12 -34 -41 -56t-74 -22q-20 0 -53.5 8.5t-70.5 18.5t-70.5 18.5t-53.5 8.5q-35 0 -49.5 -14t-26.5 -37zM63 377q12 34 41 56t74 22q21 0 55.5 -8.5t71.5 -18.5
t70 -18.5t51 -8.5q35 0 49.5 14.5t26.5 36.5l40 -18q-12 -34 -41 -56t-74 -22q-20 0 -53.5 8.5t-70.5 18.5t-70.5 18.5t-53.5 8.5q-35 0 -49.5 -14t-26.5 -37z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M333 441l65 139h50l-65 -139h148v-44h-168l-73 -156h241v-44h-262l-64 -139h-50l65 139h-146v44h166l72 156h-238v44h259z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M531 527v-47l-399 -161l399 -161v-47l-457 186v44zM531 45v-45h-457v45h457z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M531 45v-45h-457v45h457zM531 341v-44l-457 -186v47l397 161l-397 161v47z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M327 666l208 -333l-208 -333h-49l-208 333l208 333h49zM133 333l169 -273l170 273l-170 273z" />
    <glyph glyph-name="commaaccentcomb" horiz-adv-x="0" 
d="M337 -46l-93 -142h-56l83 142h66z" />
    <glyph glyph-name="apple" horiz-adv-x="1100" 
d="M821 784h5q30 0 42 -38l47 -156l-499 59l-56 -72l217 -33l-123 -351l-317 23q-13 1 -26 4.5t-23.5 10t-17 16t-6.5 22.5q0 5 1 12t3 14l153 410q7 16 29 26.5t48 12.5zM169 290q-1 -5 -1.5 -9t-0.5 -7q0 -16 12 -25t31 -11l226 -21l97 265l-188 27l-8 68l71 89l481 -54
l-34 112q-5 14 -17.5 18.5t-24.5 5.5l-452 -34q-16 -1 -33.5 -8.5t-23.5 -26.5zM944 486l80 -279q1 -2 1 -9q0 -19 -16 -29.5t-42 -10.5l-234 13v348zM815 190l151 -12h3q14 0 22.5 8.5t8.5 18.5q0 6 -1 7l-65 211l-119 22v-255zM61 9h25q19 -1 32 10t13 38q0 29 -13 40
t-32 10h-25v-98zM88 122q27 0 44.5 -15.5t18.5 -49.5q-1 -33 -18.5 -48t-44.5 -15h-45v128h45zM233 52q-3 21 -23 21q-12 0 -19 -8t-7 -21zM185 32q1 -13 8.5 -19t18.5 -6q9 -1 15.5 3t11.5 11l12 -6q-6 -10 -15.5 -16t-23.5 -6q-21 0 -34 12.5t-14 35.5q1 19 13 32t33 14
q22 -1 32 -13t11 -32zM280 21q8 -14 25 -14q22 0 19 13q1 7 -7.5 9.5t-19 5.5t-19.5 8t-9 17t10 19t27 8q25 -1 36 -18l-12 -8q-6 12 -25 12q-18 0 -18 -13q0 -7 8.5 -9.5t19 -5t19 -8t8.5 -17.5q0 -13 -9.5 -20t-26.5 -7q-15 0 -25 6t-15 16zM385 113q-2 -8 -11 -8
q-10 0 -10 8q0 9 10 9q9 0 11 -9zM383 86v-92h-18v92h18zM474 69q-7 4 -21 4q-12 0 -20 -8t-8 -24q0 -32 26 -32q17 0 23 13v47zM493 -8q0 -39 -43 -39q-28 0 -41 21l12 9q5 -8 11.5 -12t17.5 -4q26 0 24 24v16q-8 -13 -27 -13q-20 0 -30 12.5t-11 34.5q1 23 14.5 34.5
t33.5 11.5q26 -2 39 -11v-84zM541 86v-17q10 16 32 18q31 -1 32 -31v-62h-19v62q0 17 -18 17q-10 0 -15.5 -4.5t-10.5 -11.5v-63h-17v92h16zM694 52q-3 21 -23 21q-13 0 -20.5 -8t-7.5 -21zM645 32q3 -25 28 -25q18 -2 25 14l14 -6q-5 -10 -15 -16t-24 -6q-47 0 -47 48
q0 19 12 32t33 14q22 -1 32.5 -13t10.5 -32zM796 63q-8 10 -23 10q-13 0 -20 -9t-7 -24t7 -24t20 -9q15 0 23 13v43zM815 126v-108q0 -5 0.5 -12t2.5 -12l-16 -1q0 3 -1 6.5t-1 7.5q-5 -6 -12.5 -10t-18.5 -4q-19 0 -30.5 13t-11.5 35q0 21 13 33t30 13q20 -1 26 -10v49h19z
M914 20q10 -13 24 -13t20.5 9.5t6.5 24.5q0 32 -27 32q-8 0 -13.5 -4.5t-10.5 -10.5v-38zM914 126v-54q9 13 31 15q19 -1 28 -13t9 -33q0 -22 -10.5 -35t-28.5 -13q-23 0 -32 14q0 -8 -3 -14l-16 1q2 6 3.5 12.5t1.5 11.5v108h17zM1009 86l27 -75l27 75h18l-40 -100
q-6 -17 -16 -25t-29 -9l-4 13q23 1 31 21l3 7l-35 93h18zM197 -62l-1 -14h-39v-113h-18v113h-39v14h97zM305 -163q6 -13 24 -13q13 0 19 9t6 24q0 32 -24 32q-16 0 -25 -14v-38zM302 -97v-15q12 14 33 16q20 -1 29 -13.5t9 -33.5q1 -21 -9 -34.5t-32 -13.5q-10 0 -16.5 4
t-10.5 8v-50l-18 -1v132zM433 -96q22 -1 34 -14t13 -33q-1 -21 -13 -34t-34 -14q-21 1 -33 14t-13 34q1 20 13 33t33 14zM433 -111q-13 0 -20 -9t-7 -23q0 -15 7 -24t20 -9t20 9t7 24q0 14 -7 23t-20 9zM520 -163q8 -13 23 -13q14 0 20 9t6 24t-5.5 23.5t-19.5 8.5
q-15 0 -24 -14v-38zM518 -97v-15q10 14 33 16q18 -1 27.5 -13.5t9.5 -33.5q2 -21 -9 -34.5t-31 -13.5q-9 0 -16 4t-12 8v-50l-18 -1v132zM630 -57v-54q10 13 31 15q31 -1 32 -31v-62h-19v62q0 16 -18 16t-26 -16v-62h-18v131zM714 -115q6 7 15.5 12.5t24.5 6.5
q20 -2 27.5 -11t7.5 -21v-37q0 -5 1 -11.5t3 -11.5l-16 -3q-1 4 -1 7.5t-2 7.5q-4 -6 -11 -10.5t-18 -4.5q-32 0 -32 26q-1 17 19 24q5 2 17 4t22 3v7q0 7 -4 11.5t-16 4.5q-10 0 -15 -3.5t-9 -8.5zM771 -146q-8 -1 -17.5 -3t-14.5 -3q-9 -4 -9 -11q0 -13 18 -13q17 0 23 14
v16zM878 -115q-8 6 -21 4q-13 0 -21 -7.5t-8 -24.5q0 -31 27 -32q16 2 23 14v46zM895 -191q0 -39 -41 -39q-31 0 -41 21l12 9q5 -8 11.5 -12t17.5 -4q26 0 24 24v16q-8 -13 -28 -13q-19 0 -29 12.5t-10 33.5q0 23 13.5 35t33.5 12q11 -1 21 -3.5t16 -7.5v-84zM987 -131
q-1 20 -23 20q-12 0 -19 -8t-7 -20zM938 -151q2 -13 9.5 -19t18.5 -6q18 -1 25 13l13 -5q-5 -10 -14 -16.5t-24 -6.5q-21 0 -33.5 12.5t-13.5 35.5q1 20 13 33t32 14q21 -1 31.5 -13t12.5 -32zM197 -98l27 -75l27 75h18l-40 -100q-6 -17 -16 -25t-29 -9l-4 13q23 1 31 21
l4 7l-36 93h18z" />
    <hkern u1="&#x28;" u2="&#x134;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-40" />
    <hkern u1="&#x28;" u2="J" k="-50" />
    <hkern u1="&#x2a;" u2="&#x2026;" k="175" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="175" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="175" />
    <hkern u1="&#x2c;" g2="fl" k="10" />
    <hkern u1="&#x2c;" g2="fi" k="10" />
    <hkern u1="&#x2c;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2c;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2c;" u2="&#x1ef2;" k="135" />
    <hkern u1="&#x2c;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2c;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2c;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2c;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2c;" u2="&#x1fe;" k="25" />
    <hkern u1="&#x2c;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x2c;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2c;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x178;" k="135" />
    <hkern u1="&#x2c;" u2="&#x177;" k="20" />
    <hkern u1="&#x2c;" u2="&#x176;" k="135" />
    <hkern u1="&#x2c;" u2="&#x175;" k="65" />
    <hkern u1="&#x2c;" u2="&#x174;" k="55" />
    <hkern u1="&#x2c;" u2="&#x173;" k="20" />
    <hkern u1="&#x2c;" u2="&#x172;" k="30" />
    <hkern u1="&#x2c;" u2="&#x171;" k="20" />
    <hkern u1="&#x2c;" u2="&#x170;" k="30" />
    <hkern u1="&#x2c;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16e;" k="30" />
    <hkern u1="&#x2c;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16c;" k="30" />
    <hkern u1="&#x2c;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16a;" k="30" />
    <hkern u1="&#x2c;" u2="&#x169;" k="20" />
    <hkern u1="&#x2c;" u2="&#x168;" k="30" />
    <hkern u1="&#x2c;" u2="&#x167;" k="20" />
    <hkern u1="&#x2c;" u2="&#x165;" k="20" />
    <hkern u1="&#x2c;" u2="&#x164;" k="110" />
    <hkern u1="&#x2c;" u2="&#x163;" k="20" />
    <hkern u1="&#x2c;" u2="&#x162;" k="110" />
    <hkern u1="&#x2c;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x153;" k="20" />
    <hkern u1="&#x2c;" u2="&#x152;" k="25" />
    <hkern u1="&#x2c;" u2="&#x151;" k="20" />
    <hkern u1="&#x2c;" u2="&#x150;" k="25" />
    <hkern u1="&#x2c;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x14e;" k="25" />
    <hkern u1="&#x2c;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x14c;" k="25" />
    <hkern u1="&#x2c;" u2="&#x123;" k="20" />
    <hkern u1="&#x2c;" u2="&#x122;" k="25" />
    <hkern u1="&#x2c;" u2="&#x121;" k="20" />
    <hkern u1="&#x2c;" u2="&#x120;" k="25" />
    <hkern u1="&#x2c;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11e;" k="25" />
    <hkern u1="&#x2c;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11c;" k="25" />
    <hkern u1="&#x2c;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2c;" u2="&#x119;" k="20" />
    <hkern u1="&#x2c;" u2="&#x118;" k="20" />
    <hkern u1="&#x2c;" u2="&#x117;" k="20" />
    <hkern u1="&#x2c;" u2="&#x116;" k="20" />
    <hkern u1="&#x2c;" u2="&#x115;" k="20" />
    <hkern u1="&#x2c;" u2="&#x114;" k="20" />
    <hkern u1="&#x2c;" u2="&#x113;" k="20" />
    <hkern u1="&#x2c;" u2="&#x112;" k="20" />
    <hkern u1="&#x2c;" u2="&#x111;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10c;" k="25" />
    <hkern u1="&#x2c;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10a;" k="25" />
    <hkern u1="&#x2c;" u2="&#x109;" k="20" />
    <hkern u1="&#x2c;" u2="&#x108;" k="25" />
    <hkern u1="&#x2c;" u2="&#x107;" k="20" />
    <hkern u1="&#x2c;" u2="&#x106;" k="25" />
    <hkern u1="&#x2c;" u2="&#x105;" k="10" />
    <hkern u1="&#x2c;" u2="&#x103;" k="10" />
    <hkern u1="&#x2c;" u2="&#x101;" k="10" />
    <hkern u1="&#x2c;" u2="&#xff;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2c;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xea;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe5;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe4;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe3;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe1;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe0;" k="10" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="135" />
    <hkern u1="&#x2c;" u2="&#xdc;" k="30" />
    <hkern u1="&#x2c;" u2="&#xdb;" k="30" />
    <hkern u1="&#x2c;" u2="&#xda;" k="30" />
    <hkern u1="&#x2c;" u2="&#xd9;" k="30" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="25" />
    <hkern u1="&#x2c;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xca;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="25" />
    <hkern u1="&#x2c;" u2="&#xab;" k="50" />
    <hkern u1="&#x2c;" u2="&#xa9;" k="25" />
    <hkern u1="&#x2c;" u2="y" k="20" />
    <hkern u1="&#x2c;" u2="x" k="-20" />
    <hkern u1="&#x2c;" u2="v" k="65" />
    <hkern u1="&#x2c;" u2="q" k="20" />
    <hkern u1="&#x2c;" u2="o" k="20" />
    <hkern u1="&#x2c;" u2="g" k="20" />
    <hkern u1="&#x2c;" u2="e" k="20" />
    <hkern u1="&#x2c;" u2="d" k="20" />
    <hkern u1="&#x2c;" u2="X" k="-30" />
    <hkern u1="&#x2c;" u2="V" k="110" />
    <hkern u1="&#x2c;" u2="Q" k="25" />
    <hkern u1="&#x2c;" u2="O" k="25" />
    <hkern u1="&#x2c;" u2="G" k="25" />
    <hkern u1="&#x2d;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2d;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2d;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2d;" u2="&#x218;" k="95" />
    <hkern u1="&#x2d;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x17d;" k="75" />
    <hkern u1="&#x2d;" u2="&#x17b;" k="75" />
    <hkern u1="&#x2d;" u2="&#x179;" k="75" />
    <hkern u1="&#x2d;" u2="&#x178;" k="10" />
    <hkern u1="&#x2d;" u2="&#x176;" k="10" />
    <hkern u1="&#x2d;" u2="&#x175;" k="10" />
    <hkern u1="&#x2d;" u2="&#x174;" k="20" />
    <hkern u1="&#x2d;" u2="&#x164;" k="110" />
    <hkern u1="&#x2d;" u2="&#x162;" k="110" />
    <hkern u1="&#x2d;" u2="&#x160;" k="95" />
    <hkern u1="&#x2d;" u2="&#x15e;" k="95" />
    <hkern u1="&#x2d;" u2="&#x15c;" k="95" />
    <hkern u1="&#x2d;" u2="&#x15a;" k="95" />
    <hkern u1="&#x2d;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2d;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2d;" u2="z" k="35" />
    <hkern u1="&#x2d;" u2="x" k="20" />
    <hkern u1="&#x2d;" u2="X" k="65" />
    <hkern u1="&#x2d;" u2="V" k="35" />
    <hkern u1="&#x2d;" u2="Q" k="-15" />
    <hkern u1="&#x2d;" u2="O" k="-15" />
    <hkern u1="&#x2d;" u2="G" k="-15" />
    <hkern u1="&#x2e;" g2="fl" k="10" />
    <hkern u1="&#x2e;" g2="fi" k="10" />
    <hkern u1="&#x2e;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2e;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="60" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2e;" u2="&#x1ef2;" k="135" />
    <hkern u1="&#x2e;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2e;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2e;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2e;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2e;" u2="&#x1fe;" k="25" />
    <hkern u1="&#x2e;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x2e;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2e;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x178;" k="135" />
    <hkern u1="&#x2e;" u2="&#x177;" k="20" />
    <hkern u1="&#x2e;" u2="&#x176;" k="135" />
    <hkern u1="&#x2e;" u2="&#x175;" k="65" />
    <hkern u1="&#x2e;" u2="&#x174;" k="55" />
    <hkern u1="&#x2e;" u2="&#x173;" k="20" />
    <hkern u1="&#x2e;" u2="&#x172;" k="30" />
    <hkern u1="&#x2e;" u2="&#x171;" k="20" />
    <hkern u1="&#x2e;" u2="&#x170;" k="30" />
    <hkern u1="&#x2e;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16e;" k="30" />
    <hkern u1="&#x2e;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16c;" k="30" />
    <hkern u1="&#x2e;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16a;" k="30" />
    <hkern u1="&#x2e;" u2="&#x169;" k="20" />
    <hkern u1="&#x2e;" u2="&#x168;" k="30" />
    <hkern u1="&#x2e;" u2="&#x167;" k="20" />
    <hkern u1="&#x2e;" u2="&#x165;" k="20" />
    <hkern u1="&#x2e;" u2="&#x164;" k="110" />
    <hkern u1="&#x2e;" u2="&#x163;" k="20" />
    <hkern u1="&#x2e;" u2="&#x162;" k="110" />
    <hkern u1="&#x2e;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x153;" k="20" />
    <hkern u1="&#x2e;" u2="&#x152;" k="25" />
    <hkern u1="&#x2e;" u2="&#x151;" k="20" />
    <hkern u1="&#x2e;" u2="&#x150;" k="25" />
    <hkern u1="&#x2e;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x14e;" k="25" />
    <hkern u1="&#x2e;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x14c;" k="25" />
    <hkern u1="&#x2e;" u2="&#x123;" k="20" />
    <hkern u1="&#x2e;" u2="&#x122;" k="25" />
    <hkern u1="&#x2e;" u2="&#x121;" k="20" />
    <hkern u1="&#x2e;" u2="&#x120;" k="25" />
    <hkern u1="&#x2e;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11e;" k="25" />
    <hkern u1="&#x2e;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11c;" k="25" />
    <hkern u1="&#x2e;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2e;" u2="&#x119;" k="20" />
    <hkern u1="&#x2e;" u2="&#x118;" k="20" />
    <hkern u1="&#x2e;" u2="&#x117;" k="20" />
    <hkern u1="&#x2e;" u2="&#x116;" k="20" />
    <hkern u1="&#x2e;" u2="&#x115;" k="20" />
    <hkern u1="&#x2e;" u2="&#x114;" k="20" />
    <hkern u1="&#x2e;" u2="&#x113;" k="20" />
    <hkern u1="&#x2e;" u2="&#x112;" k="20" />
    <hkern u1="&#x2e;" u2="&#x111;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10c;" k="25" />
    <hkern u1="&#x2e;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10a;" k="25" />
    <hkern u1="&#x2e;" u2="&#x109;" k="20" />
    <hkern u1="&#x2e;" u2="&#x108;" k="25" />
    <hkern u1="&#x2e;" u2="&#x107;" k="20" />
    <hkern u1="&#x2e;" u2="&#x106;" k="25" />
    <hkern u1="&#x2e;" u2="&#x105;" k="10" />
    <hkern u1="&#x2e;" u2="&#x103;" k="10" />
    <hkern u1="&#x2e;" u2="&#x101;" k="10" />
    <hkern u1="&#x2e;" u2="&#xff;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2e;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xea;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe5;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe4;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe3;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe1;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe0;" k="10" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="135" />
    <hkern u1="&#x2e;" u2="&#xdc;" k="30" />
    <hkern u1="&#x2e;" u2="&#xdb;" k="30" />
    <hkern u1="&#x2e;" u2="&#xda;" k="30" />
    <hkern u1="&#x2e;" u2="&#xd9;" k="30" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="25" />
    <hkern u1="&#x2e;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xca;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="25" />
    <hkern u1="&#x2e;" u2="&#xab;" k="50" />
    <hkern u1="&#x2e;" u2="&#xa9;" k="25" />
    <hkern u1="&#x2e;" u2="y" k="20" />
    <hkern u1="&#x2e;" u2="x" k="-20" />
    <hkern u1="&#x2e;" u2="w" k="65" />
    <hkern u1="&#x2e;" u2="v" k="65" />
    <hkern u1="&#x2e;" u2="u" k="20" />
    <hkern u1="&#x2e;" u2="t" k="20" />
    <hkern u1="&#x2e;" u2="q" k="20" />
    <hkern u1="&#x2e;" u2="o" k="20" />
    <hkern u1="&#x2e;" u2="g" k="20" />
    <hkern u1="&#x2e;" u2="f" k="10" />
    <hkern u1="&#x2e;" u2="e" k="20" />
    <hkern u1="&#x2e;" u2="d" k="20" />
    <hkern u1="&#x2e;" u2="c" k="20" />
    <hkern u1="&#x2e;" u2="a" k="10" />
    <hkern u1="&#x2e;" u2="Z" k="-30" />
    <hkern u1="&#x2e;" u2="Y" k="135" />
    <hkern u1="&#x2e;" u2="X" k="-30" />
    <hkern u1="&#x2e;" u2="W" k="55" />
    <hkern u1="&#x2e;" u2="V" k="110" />
    <hkern u1="&#x2e;" u2="U" k="30" />
    <hkern u1="&#x2e;" u2="T" k="110" />
    <hkern u1="&#x2e;" u2="S" k="-20" />
    <hkern u1="&#x2e;" u2="Q" k="25" />
    <hkern u1="&#x2e;" u2="O" k="25" />
    <hkern u1="&#x2e;" u2="G" k="25" />
    <hkern u1="&#x2e;" u2="E" k="20" />
    <hkern u1="&#x2e;" u2="C" k="25" />
    <hkern u1="&#x2f;" u2="&#x1ff;" k="65" />
    <hkern u1="&#x2f;" u2="&#x153;" k="65" />
    <hkern u1="&#x2f;" u2="&#x151;" k="65" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x123;" k="65" />
    <hkern u1="&#x2f;" u2="&#x121;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="65" />
    <hkern u1="&#x2f;" u2="&#x119;" k="65" />
    <hkern u1="&#x2f;" u2="&#x117;" k="65" />
    <hkern u1="&#x2f;" u2="&#x115;" k="65" />
    <hkern u1="&#x2f;" u2="&#x113;" k="65" />
    <hkern u1="&#x2f;" u2="&#x111;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="65" />
    <hkern u1="&#x2f;" u2="&#x109;" k="65" />
    <hkern u1="&#x2f;" u2="&#x107;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="65" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="65" />
    <hkern u1="&#x2f;" u2="&#xea;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="65" />
    <hkern u1="&#x2f;" u2="q" k="65" />
    <hkern u1="&#x2f;" u2="o" k="65" />
    <hkern u1="&#x2f;" u2="g" k="65" />
    <hkern u1="&#x2f;" u2="e" k="65" />
    <hkern u1="&#x2f;" u2="d" k="65" />
    <hkern u1="&#x2f;" u2="c" k="65" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="180" />
    <hkern u1="&#x40;" u2="&#x2026;" k="35" />
    <hkern u1="&#x40;" u2="&#x2e;" k="35" />
    <hkern u1="&#x40;" u2="&#x2c;" k="35" />
    <hkern u1="A" u2="&#x164;" k="35" />
    <hkern u1="A" u2="&#x162;" k="35" />
    <hkern u1="B" u2="&#x17d;" k="20" />
    <hkern u1="B" u2="&#x17b;" k="20" />
    <hkern u1="B" u2="&#x179;" k="20" />
    <hkern u1="B" u2="Z" k="20" />
    <hkern u1="B" u2="X" k="10" />
    <hkern u1="C" u2="&#x2026;" k="-30" />
    <hkern u1="C" u2="&#x2014;" k="85" />
    <hkern u1="C" u2="&#x2013;" k="85" />
    <hkern u1="C" u2="&#x1e85;" k="10" />
    <hkern u1="C" u2="&#x1e83;" k="10" />
    <hkern u1="C" u2="&#x1e81;" k="10" />
    <hkern u1="C" u2="&#x1ff;" k="10" />
    <hkern u1="C" u2="&#x1fe;" k="30" />
    <hkern u1="C" u2="&#x175;" k="10" />
    <hkern u1="C" u2="&#x153;" k="10" />
    <hkern u1="C" u2="&#x152;" k="30" />
    <hkern u1="C" u2="&#x151;" k="10" />
    <hkern u1="C" u2="&#x150;" k="30" />
    <hkern u1="C" u2="&#x14f;" k="10" />
    <hkern u1="C" u2="&#x14e;" k="30" />
    <hkern u1="C" u2="&#x14d;" k="10" />
    <hkern u1="C" u2="&#x14c;" k="30" />
    <hkern u1="C" u2="&#x123;" k="10" />
    <hkern u1="C" u2="&#x122;" k="30" />
    <hkern u1="C" u2="&#x121;" k="10" />
    <hkern u1="C" u2="&#x120;" k="30" />
    <hkern u1="C" u2="&#x11f;" k="10" />
    <hkern u1="C" u2="&#x11e;" k="30" />
    <hkern u1="C" u2="&#x11d;" k="10" />
    <hkern u1="C" u2="&#x11c;" k="30" />
    <hkern u1="C" u2="&#x11b;" k="10" />
    <hkern u1="C" u2="&#x119;" k="10" />
    <hkern u1="C" u2="&#x117;" k="10" />
    <hkern u1="C" u2="&#x115;" k="10" />
    <hkern u1="C" u2="&#x113;" k="10" />
    <hkern u1="C" u2="&#x111;" k="10" />
    <hkern u1="C" u2="&#x10f;" k="10" />
    <hkern u1="C" u2="&#x10d;" k="10" />
    <hkern u1="C" u2="&#x10c;" k="30" />
    <hkern u1="C" u2="&#x10b;" k="10" />
    <hkern u1="C" u2="&#x10a;" k="30" />
    <hkern u1="C" u2="&#x109;" k="10" />
    <hkern u1="C" u2="&#x108;" k="30" />
    <hkern u1="C" u2="&#x107;" k="10" />
    <hkern u1="C" u2="&#x106;" k="30" />
    <hkern u1="C" u2="&#xf8;" k="10" />
    <hkern u1="C" u2="&#xf6;" k="10" />
    <hkern u1="C" u2="&#xf5;" k="10" />
    <hkern u1="C" u2="&#xf4;" k="10" />
    <hkern u1="C" u2="&#xf3;" k="10" />
    <hkern u1="C" u2="&#xf2;" k="10" />
    <hkern u1="C" u2="&#xf0;" k="10" />
    <hkern u1="C" u2="&#xeb;" k="10" />
    <hkern u1="C" u2="&#xea;" k="10" />
    <hkern u1="C" u2="&#xe9;" k="10" />
    <hkern u1="C" u2="&#xe8;" k="10" />
    <hkern u1="C" u2="&#xe7;" k="10" />
    <hkern u1="C" u2="&#xd8;" k="30" />
    <hkern u1="C" u2="&#xd6;" k="30" />
    <hkern u1="C" u2="&#xd5;" k="30" />
    <hkern u1="C" u2="&#xd4;" k="30" />
    <hkern u1="C" u2="&#xd3;" k="30" />
    <hkern u1="C" u2="&#xd2;" k="30" />
    <hkern u1="C" u2="&#xc7;" k="30" />
    <hkern u1="C" u2="&#xa9;" k="30" />
    <hkern u1="C" u2="x" k="-20" />
    <hkern u1="C" u2="v" k="35" />
    <hkern u1="C" u2="q" k="10" />
    <hkern u1="C" u2="o" k="10" />
    <hkern u1="C" u2="g" k="10" />
    <hkern u1="C" u2="e" k="10" />
    <hkern u1="C" u2="d" k="10" />
    <hkern u1="C" u2="_" k="-20" />
    <hkern u1="C" u2="V" k="-15" />
    <hkern u1="C" u2="Q" k="30" />
    <hkern u1="C" u2="O" k="30" />
    <hkern u1="C" u2="G" k="30" />
    <hkern u1="C" u2="A" k="7" />
    <hkern u1="C" u2="&#x2e;" k="-30" />
    <hkern u1="D" u2="&#x2026;" k="25" />
    <hkern u1="D" u2="&#x2014;" k="-15" />
    <hkern u1="D" u2="&#x2013;" k="-15" />
    <hkern u1="D" u2="&#x1fe;" k="-5" />
    <hkern u1="D" u2="&#x164;" k="15" />
    <hkern u1="D" u2="&#x162;" k="15" />
    <hkern u1="D" u2="&#x152;" k="-5" />
    <hkern u1="D" u2="&#x150;" k="-5" />
    <hkern u1="D" u2="&#x14e;" k="-5" />
    <hkern u1="D" u2="&#x14c;" k="-5" />
    <hkern u1="D" u2="&#x122;" k="-5" />
    <hkern u1="D" u2="&#x120;" k="-5" />
    <hkern u1="D" u2="&#x11e;" k="-5" />
    <hkern u1="D" u2="&#x11c;" k="-5" />
    <hkern u1="D" u2="&#x10c;" k="-5" />
    <hkern u1="D" u2="&#x10a;" k="-5" />
    <hkern u1="D" u2="&#x108;" k="-5" />
    <hkern u1="D" u2="&#x106;" k="-5" />
    <hkern u1="D" u2="&#xd8;" k="-5" />
    <hkern u1="D" u2="&#xd6;" k="-5" />
    <hkern u1="D" u2="&#xd5;" k="-5" />
    <hkern u1="D" u2="&#xd4;" k="-5" />
    <hkern u1="D" u2="&#xd3;" k="-5" />
    <hkern u1="D" u2="&#xd2;" k="-5" />
    <hkern u1="D" u2="&#xc7;" k="-5" />
    <hkern u1="D" u2="&#xa9;" k="-5" />
    <hkern u1="D" u2="X" k="10" />
    <hkern u1="D" u2="Q" k="-5" />
    <hkern u1="D" u2="O" k="-5" />
    <hkern u1="D" u2="G" k="-5" />
    <hkern u1="D" u2="&#x2e;" k="25" />
    <hkern u1="E" u2="&#x2026;" k="-20" />
    <hkern u1="E" u2="&#x1fe;" k="7" />
    <hkern u1="E" u2="&#x152;" k="7" />
    <hkern u1="E" u2="&#x150;" k="7" />
    <hkern u1="E" u2="&#x14e;" k="7" />
    <hkern u1="E" u2="&#x14c;" k="7" />
    <hkern u1="E" u2="&#x122;" k="7" />
    <hkern u1="E" u2="&#x120;" k="7" />
    <hkern u1="E" u2="&#x11e;" k="7" />
    <hkern u1="E" u2="&#x11c;" k="7" />
    <hkern u1="E" u2="&#x10c;" k="7" />
    <hkern u1="E" u2="&#x10a;" k="7" />
    <hkern u1="E" u2="&#x108;" k="7" />
    <hkern u1="E" u2="&#x106;" k="7" />
    <hkern u1="E" u2="&#xd8;" k="7" />
    <hkern u1="E" u2="&#xd6;" k="7" />
    <hkern u1="E" u2="&#xd5;" k="7" />
    <hkern u1="E" u2="&#xd4;" k="7" />
    <hkern u1="E" u2="&#xd3;" k="7" />
    <hkern u1="E" u2="&#xd2;" k="7" />
    <hkern u1="E" u2="&#xc7;" k="7" />
    <hkern u1="E" u2="&#xa9;" k="7" />
    <hkern u1="E" u2="Q" k="7" />
    <hkern u1="E" u2="O" k="7" />
    <hkern u1="E" u2="G" k="7" />
    <hkern u1="E" u2="&#x2e;" k="-20" />
    <hkern u1="F" u2="&#x2026;" k="150" />
    <hkern u1="F" u2="&#x1ff;" k="35" />
    <hkern u1="F" u2="&#x1fd;" k="30" />
    <hkern u1="F" u2="&#x1fb;" k="30" />
    <hkern u1="F" u2="&#x159;" k="10" />
    <hkern u1="F" u2="&#x157;" k="10" />
    <hkern u1="F" u2="&#x155;" k="10" />
    <hkern u1="F" u2="&#x153;" k="35" />
    <hkern u1="F" u2="&#x151;" k="35" />
    <hkern u1="F" u2="&#x14f;" k="35" />
    <hkern u1="F" u2="&#x14d;" k="35" />
    <hkern u1="F" u2="&#x14b;" k="10" />
    <hkern u1="F" u2="&#x148;" k="10" />
    <hkern u1="F" u2="&#x146;" k="10" />
    <hkern u1="F" u2="&#x144;" k="10" />
    <hkern u1="F" u2="&#x123;" k="35" />
    <hkern u1="F" u2="&#x121;" k="35" />
    <hkern u1="F" u2="&#x11f;" k="35" />
    <hkern u1="F" u2="&#x11d;" k="35" />
    <hkern u1="F" u2="&#x11b;" k="35" />
    <hkern u1="F" u2="&#x119;" k="35" />
    <hkern u1="F" u2="&#x117;" k="35" />
    <hkern u1="F" u2="&#x115;" k="35" />
    <hkern u1="F" u2="&#x113;" k="35" />
    <hkern u1="F" u2="&#x111;" k="35" />
    <hkern u1="F" u2="&#x10f;" k="35" />
    <hkern u1="F" u2="&#x10d;" k="35" />
    <hkern u1="F" u2="&#x10b;" k="35" />
    <hkern u1="F" u2="&#x109;" k="35" />
    <hkern u1="F" u2="&#x107;" k="35" />
    <hkern u1="F" u2="&#x105;" k="30" />
    <hkern u1="F" u2="&#x103;" k="30" />
    <hkern u1="F" u2="&#x101;" k="30" />
    <hkern u1="F" u2="&#xf8;" k="35" />
    <hkern u1="F" u2="&#xf6;" k="35" />
    <hkern u1="F" u2="&#xf5;" k="35" />
    <hkern u1="F" u2="&#xf4;" k="35" />
    <hkern u1="F" u2="&#xf3;" k="35" />
    <hkern u1="F" u2="&#xf2;" k="35" />
    <hkern u1="F" u2="&#xf1;" k="10" />
    <hkern u1="F" u2="&#xf0;" k="35" />
    <hkern u1="F" u2="&#xeb;" k="35" />
    <hkern u1="F" u2="&#xea;" k="35" />
    <hkern u1="F" u2="&#xe9;" k="35" />
    <hkern u1="F" u2="&#xe8;" k="35" />
    <hkern u1="F" u2="&#xe7;" k="35" />
    <hkern u1="F" u2="&#xe6;" k="30" />
    <hkern u1="F" u2="&#xe5;" k="30" />
    <hkern u1="F" u2="&#xe4;" k="30" />
    <hkern u1="F" u2="&#xe3;" k="30" />
    <hkern u1="F" u2="&#xe2;" k="30" />
    <hkern u1="F" u2="&#xe1;" k="30" />
    <hkern u1="F" u2="&#xe0;" k="30" />
    <hkern u1="F" u2="v" k="20" />
    <hkern u1="F" u2="r" k="10" />
    <hkern u1="F" u2="q" k="35" />
    <hkern u1="F" u2="p" k="10" />
    <hkern u1="F" u2="o" k="35" />
    <hkern u1="F" u2="n" k="10" />
    <hkern u1="F" u2="m" k="10" />
    <hkern u1="F" u2="g" k="35" />
    <hkern u1="F" u2="e" k="35" />
    <hkern u1="F" u2="d" k="35" />
    <hkern u1="F" u2="c" k="35" />
    <hkern u1="F" u2="a" k="30" />
    <hkern u1="F" u2="_" k="85" />
    <hkern u1="F" u2="A" k="15" />
    <hkern u1="F" u2="&#x2f;" k="75" />
    <hkern u1="F" u2="&#x2e;" k="150" />
    <hkern u1="F" u2="&#x2c;" k="150" />
    <hkern u1="G" u2="&#x164;" k="20" />
    <hkern u1="G" u2="&#x162;" k="20" />
    <hkern u1="G" u2="T" k="20" />
    <hkern u1="K" u2="&#x2026;" k="-50" />
    <hkern u1="K" u2="&#x2014;" k="10" />
    <hkern u1="K" u2="&#x2013;" k="10" />
    <hkern u1="K" u2="&#xef;" k="-10" />
    <hkern u1="K" u2="v" k="-20" />
    <hkern u1="K" u2="_" k="-50" />
    <hkern u1="K" u2="&#x2e;" k="-50" />
    <hkern u1="L" u2="&#x2122;" k="65" />
    <hkern u1="L" u2="&#x2026;" k="-40" />
    <hkern u1="L" u2="&#x201d;" k="110" />
    <hkern u1="L" u2="&#x201c;" k="110" />
    <hkern u1="L" u2="&#x2014;" k="100" />
    <hkern u1="L" u2="&#x2013;" k="100" />
    <hkern u1="L" u2="&#x1ef2;" k="105" />
    <hkern u1="L" u2="&#x1e85;" k="30" />
    <hkern u1="L" u2="&#x1e84;" k="45" />
    <hkern u1="L" u2="&#x1e83;" k="30" />
    <hkern u1="L" u2="&#x1e82;" k="45" />
    <hkern u1="L" u2="&#x1e81;" k="30" />
    <hkern u1="L" u2="&#x1e80;" k="45" />
    <hkern u1="L" u2="&#x1fe;" k="20" />
    <hkern u1="L" u2="&#x1fd;" k="-30" />
    <hkern u1="L" u2="&#x1fb;" k="-30" />
    <hkern u1="L" u2="&#x178;" k="105" />
    <hkern u1="L" u2="&#x176;" k="105" />
    <hkern u1="L" u2="&#x175;" k="30" />
    <hkern u1="L" u2="&#x174;" k="45" />
    <hkern u1="L" u2="&#x164;" k="75" />
    <hkern u1="L" u2="&#x162;" k="75" />
    <hkern u1="L" u2="&#x152;" k="20" />
    <hkern u1="L" u2="&#x150;" k="20" />
    <hkern u1="L" u2="&#x14e;" k="20" />
    <hkern u1="L" u2="&#x14c;" k="20" />
    <hkern u1="L" u2="&#x122;" k="20" />
    <hkern u1="L" u2="&#x120;" k="20" />
    <hkern u1="L" u2="&#x11e;" k="20" />
    <hkern u1="L" u2="&#x11c;" k="20" />
    <hkern u1="L" u2="&#x10c;" k="20" />
    <hkern u1="L" u2="&#x10a;" k="20" />
    <hkern u1="L" u2="&#x108;" k="20" />
    <hkern u1="L" u2="&#x106;" k="20" />
    <hkern u1="L" u2="&#x105;" k="-30" />
    <hkern u1="L" u2="&#x103;" k="-30" />
    <hkern u1="L" u2="&#x101;" k="-30" />
    <hkern u1="L" u2="&#xe6;" k="-30" />
    <hkern u1="L" u2="&#xe5;" k="-30" />
    <hkern u1="L" u2="&#xe4;" k="-30" />
    <hkern u1="L" u2="&#xe3;" k="-30" />
    <hkern u1="L" u2="&#xe2;" k="-30" />
    <hkern u1="L" u2="&#xe1;" k="-30" />
    <hkern u1="L" u2="&#xe0;" k="-30" />
    <hkern u1="L" u2="&#xdd;" k="105" />
    <hkern u1="L" u2="&#xd8;" k="20" />
    <hkern u1="L" u2="&#xd6;" k="20" />
    <hkern u1="L" u2="&#xd5;" k="20" />
    <hkern u1="L" u2="&#xd4;" k="20" />
    <hkern u1="L" u2="&#xd3;" k="20" />
    <hkern u1="L" u2="&#xd2;" k="20" />
    <hkern u1="L" u2="&#xc7;" k="20" />
    <hkern u1="L" u2="&#xa9;" k="20" />
    <hkern u1="L" u2="v" k="40" />
    <hkern u1="L" u2="_" k="-50" />
    <hkern u1="L" u2="V" k="70" />
    <hkern u1="L" u2="Q" k="20" />
    <hkern u1="L" u2="O" k="20" />
    <hkern u1="L" u2="G" k="20" />
    <hkern u1="L" u2="&#x2e;" k="-40" />
    <hkern u1="L" u2="&#x2a;" k="80" />
    <hkern u1="L" u2="&#x27;" k="125" />
    <hkern u1="L" u2="&#x22;" k="125" />
    <hkern u1="O" u2="&#x2026;" k="25" />
    <hkern u1="O" u2="&#x2014;" k="-15" />
    <hkern u1="O" u2="&#x2013;" k="-15" />
    <hkern u1="O" u2="&#x1fe;" k="-5" />
    <hkern u1="O" u2="&#x164;" k="15" />
    <hkern u1="O" u2="&#x162;" k="15" />
    <hkern u1="O" u2="&#x152;" k="-5" />
    <hkern u1="O" u2="&#x150;" k="-5" />
    <hkern u1="O" u2="&#x14e;" k="-5" />
    <hkern u1="O" u2="&#x14c;" k="-5" />
    <hkern u1="O" u2="&#x122;" k="-5" />
    <hkern u1="O" u2="&#x120;" k="-5" />
    <hkern u1="O" u2="&#x11e;" k="-5" />
    <hkern u1="O" u2="&#x11c;" k="-5" />
    <hkern u1="O" u2="&#x10c;" k="-5" />
    <hkern u1="O" u2="&#x10a;" k="-5" />
    <hkern u1="O" u2="&#x108;" k="-5" />
    <hkern u1="O" u2="&#x106;" k="-5" />
    <hkern u1="O" u2="&#xd8;" k="-5" />
    <hkern u1="O" u2="&#xd6;" k="-5" />
    <hkern u1="O" u2="&#xd5;" k="-5" />
    <hkern u1="O" u2="&#xd4;" k="-5" />
    <hkern u1="O" u2="&#xd3;" k="-5" />
    <hkern u1="O" u2="&#xd2;" k="-5" />
    <hkern u1="O" u2="&#xc7;" k="-5" />
    <hkern u1="O" u2="&#xa9;" k="-5" />
    <hkern u1="O" u2="X" k="10" />
    <hkern u1="O" u2="T" k="15" />
    <hkern u1="O" u2="Q" k="-5" />
    <hkern u1="O" u2="O" k="-5" />
    <hkern u1="O" u2="G" k="-5" />
    <hkern u1="O" u2="C" k="-5" />
    <hkern u1="O" u2="&#x2e;" k="25" />
    <hkern u1="O" u2="&#x2d;" k="-15" />
    <hkern u1="O" u2="&#x2c;" k="25" />
    <hkern u1="P" u2="&#x2026;" k="140" />
    <hkern u1="P" u2="&#x2014;" k="10" />
    <hkern u1="P" u2="&#x2013;" k="10" />
    <hkern u1="P" u2="&#x1ff;" k="15" />
    <hkern u1="P" u2="&#x1fd;" k="10" />
    <hkern u1="P" u2="&#x1fb;" k="10" />
    <hkern u1="P" u2="&#x153;" k="15" />
    <hkern u1="P" u2="&#x151;" k="15" />
    <hkern u1="P" u2="&#x14f;" k="15" />
    <hkern u1="P" u2="&#x14d;" k="15" />
    <hkern u1="P" u2="&#x123;" k="15" />
    <hkern u1="P" u2="&#x121;" k="15" />
    <hkern u1="P" u2="&#x11f;" k="15" />
    <hkern u1="P" u2="&#x11d;" k="15" />
    <hkern u1="P" u2="&#x11b;" k="15" />
    <hkern u1="P" u2="&#x119;" k="15" />
    <hkern u1="P" u2="&#x117;" k="15" />
    <hkern u1="P" u2="&#x115;" k="15" />
    <hkern u1="P" u2="&#x113;" k="15" />
    <hkern u1="P" u2="&#x111;" k="15" />
    <hkern u1="P" u2="&#x10f;" k="15" />
    <hkern u1="P" u2="&#x10d;" k="15" />
    <hkern u1="P" u2="&#x10b;" k="15" />
    <hkern u1="P" u2="&#x109;" k="15" />
    <hkern u1="P" u2="&#x107;" k="15" />
    <hkern u1="P" u2="&#x105;" k="10" />
    <hkern u1="P" u2="&#x103;" k="10" />
    <hkern u1="P" u2="&#x101;" k="10" />
    <hkern u1="P" u2="&#xf8;" k="15" />
    <hkern u1="P" u2="&#xf6;" k="15" />
    <hkern u1="P" u2="&#xf5;" k="15" />
    <hkern u1="P" u2="&#xf4;" k="15" />
    <hkern u1="P" u2="&#xf3;" k="15" />
    <hkern u1="P" u2="&#xf2;" k="15" />
    <hkern u1="P" u2="&#xf0;" k="15" />
    <hkern u1="P" u2="&#xeb;" k="15" />
    <hkern u1="P" u2="&#xea;" k="15" />
    <hkern u1="P" u2="&#xe9;" k="15" />
    <hkern u1="P" u2="&#xe8;" k="15" />
    <hkern u1="P" u2="&#xe7;" k="15" />
    <hkern u1="P" u2="&#xe6;" k="10" />
    <hkern u1="P" u2="&#xe5;" k="10" />
    <hkern u1="P" u2="&#xe4;" k="10" />
    <hkern u1="P" u2="&#xe3;" k="10" />
    <hkern u1="P" u2="&#xe2;" k="10" />
    <hkern u1="P" u2="&#xe1;" k="10" />
    <hkern u1="P" u2="&#xe0;" k="10" />
    <hkern u1="P" u2="q" k="15" />
    <hkern u1="P" u2="o" k="15" />
    <hkern u1="P" u2="g" k="15" />
    <hkern u1="P" u2="e" k="15" />
    <hkern u1="P" u2="d" k="15" />
    <hkern u1="P" u2="c" k="15" />
    <hkern u1="P" u2="a" k="10" />
    <hkern u1="P" u2="_" k="100" />
    <hkern u1="P" u2="A" k="10" />
    <hkern u1="P" u2="&#x2f;" k="45" />
    <hkern u1="P" u2="&#x2e;" k="140" />
    <hkern u1="P" u2="&#x2d;" k="10" />
    <hkern u1="P" u2="&#x2c;" k="140" />
    <hkern u1="Q" u2="&#x2026;" k="25" />
    <hkern u1="Q" u2="&#x2014;" k="-15" />
    <hkern u1="Q" u2="&#x2013;" k="-15" />
    <hkern u1="Q" u2="&#x1fe;" k="-5" />
    <hkern u1="Q" u2="&#x164;" k="15" />
    <hkern u1="Q" u2="&#x162;" k="15" />
    <hkern u1="Q" u2="&#x152;" k="-5" />
    <hkern u1="Q" u2="&#x150;" k="-5" />
    <hkern u1="Q" u2="&#x14e;" k="-5" />
    <hkern u1="Q" u2="&#x14c;" k="-5" />
    <hkern u1="Q" u2="&#x122;" k="-5" />
    <hkern u1="Q" u2="&#x120;" k="-5" />
    <hkern u1="Q" u2="&#x11e;" k="-5" />
    <hkern u1="Q" u2="&#x11c;" k="-5" />
    <hkern u1="Q" u2="&#x10c;" k="-5" />
    <hkern u1="Q" u2="&#x10a;" k="-5" />
    <hkern u1="Q" u2="&#x108;" k="-5" />
    <hkern u1="Q" u2="&#x106;" k="-5" />
    <hkern u1="Q" u2="&#xd8;" k="-5" />
    <hkern u1="Q" u2="&#xd6;" k="-5" />
    <hkern u1="Q" u2="&#xd5;" k="-5" />
    <hkern u1="Q" u2="&#xd4;" k="-5" />
    <hkern u1="Q" u2="&#xd3;" k="-5" />
    <hkern u1="Q" u2="&#xd2;" k="-5" />
    <hkern u1="Q" u2="&#xc7;" k="-5" />
    <hkern u1="Q" u2="&#xa9;" k="-5" />
    <hkern u1="Q" u2="X" k="10" />
    <hkern u1="Q" u2="T" k="15" />
    <hkern u1="Q" u2="Q" k="-5" />
    <hkern u1="Q" u2="O" k="-5" />
    <hkern u1="Q" u2="G" k="-5" />
    <hkern u1="Q" u2="C" k="-5" />
    <hkern u1="Q" u2="&#x2e;" k="25" />
    <hkern u1="Q" u2="&#x2d;" k="-15" />
    <hkern u1="Q" u2="&#x2c;" k="25" />
    <hkern u1="R" u2="&#x2026;" k="-10" />
    <hkern u1="R" u2="&#x2014;" k="30" />
    <hkern u1="R" u2="&#x2013;" k="30" />
    <hkern u1="R" u2="&#x164;" k="25" />
    <hkern u1="R" u2="&#x162;" k="25" />
    <hkern u1="R" u2="&#x2e;" k="-10" />
    <hkern u1="S" u2="&#x1ef2;" k="40" />
    <hkern u1="S" u2="&#x218;" k="25" />
    <hkern u1="S" u2="&#x178;" k="40" />
    <hkern u1="S" u2="&#x176;" k="40" />
    <hkern u1="S" u2="&#x164;" k="10" />
    <hkern u1="S" u2="&#x162;" k="10" />
    <hkern u1="S" u2="&#x160;" k="25" />
    <hkern u1="S" u2="&#x15e;" k="25" />
    <hkern u1="S" u2="&#x15c;" k="25" />
    <hkern u1="S" u2="&#x15a;" k="25" />
    <hkern u1="S" u2="&#xdd;" k="40" />
    <hkern u1="S" u2="V" k="20" />
    <hkern u1="T" u2="&#x2026;" k="110" />
    <hkern u1="T" u2="&#x2014;" k="110" />
    <hkern u1="T" u2="&#x2013;" k="110" />
    <hkern u1="T" u2="&#x1ef3;" k="105" />
    <hkern u1="T" u2="&#x1e85;" k="55" />
    <hkern u1="T" u2="&#x1e83;" k="55" />
    <hkern u1="T" u2="&#x1e81;" k="55" />
    <hkern u1="T" u2="&#x219;" k="90" />
    <hkern u1="T" u2="&#x1ff;" k="75" />
    <hkern u1="T" u2="&#x1fe;" k="15" />
    <hkern u1="T" u2="&#x1fd;" k="95" />
    <hkern u1="T" u2="&#x1fb;" k="95" />
    <hkern u1="T" u2="&#x177;" k="105" />
    <hkern u1="T" u2="&#x175;" k="55" />
    <hkern u1="T" u2="&#x173;" k="105" />
    <hkern u1="T" u2="&#x171;" k="105" />
    <hkern u1="T" u2="&#x16f;" k="105" />
    <hkern u1="T" u2="&#x16d;" k="105" />
    <hkern u1="T" u2="&#x16b;" k="105" />
    <hkern u1="T" u2="&#x169;" k="105" />
    <hkern u1="T" u2="&#x161;" k="90" />
    <hkern u1="T" u2="&#x15f;" k="90" />
    <hkern u1="T" u2="&#x15d;" k="90" />
    <hkern u1="T" u2="&#x15b;" k="90" />
    <hkern u1="T" u2="&#x159;" k="105" />
    <hkern u1="T" u2="&#x157;" k="105" />
    <hkern u1="T" u2="&#x155;" k="105" />
    <hkern u1="T" u2="&#x153;" k="75" />
    <hkern u1="T" u2="&#x152;" k="15" />
    <hkern u1="T" u2="&#x151;" k="75" />
    <hkern u1="T" u2="&#x150;" k="15" />
    <hkern u1="T" u2="&#x14f;" k="75" />
    <hkern u1="T" u2="&#x14e;" k="15" />
    <hkern u1="T" u2="&#x14d;" k="75" />
    <hkern u1="T" u2="&#x14c;" k="15" />
    <hkern u1="T" u2="&#x14b;" k="105" />
    <hkern u1="T" u2="&#x148;" k="105" />
    <hkern u1="T" u2="&#x146;" k="105" />
    <hkern u1="T" u2="&#x144;" k="105" />
    <hkern u1="T" u2="&#x123;" k="75" />
    <hkern u1="T" u2="&#x122;" k="15" />
    <hkern u1="T" u2="&#x121;" k="75" />
    <hkern u1="T" u2="&#x120;" k="15" />
    <hkern u1="T" u2="&#x11f;" k="75" />
    <hkern u1="T" u2="&#x11e;" k="15" />
    <hkern u1="T" u2="&#x11d;" k="75" />
    <hkern u1="T" u2="&#x11c;" k="15" />
    <hkern u1="T" u2="&#x11b;" k="75" />
    <hkern u1="T" u2="&#x119;" k="75" />
    <hkern u1="T" u2="&#x117;" k="75" />
    <hkern u1="T" u2="&#x115;" k="75" />
    <hkern u1="T" u2="&#x113;" k="75" />
    <hkern u1="T" u2="&#x111;" k="75" />
    <hkern u1="T" u2="&#x10f;" k="75" />
    <hkern u1="T" u2="&#x10d;" k="75" />
    <hkern u1="T" u2="&#x10c;" k="15" />
    <hkern u1="T" u2="&#x10b;" k="75" />
    <hkern u1="T" u2="&#x10a;" k="15" />
    <hkern u1="T" u2="&#x109;" k="75" />
    <hkern u1="T" u2="&#x108;" k="15" />
    <hkern u1="T" u2="&#x107;" k="75" />
    <hkern u1="T" u2="&#x106;" k="15" />
    <hkern u1="T" u2="&#x105;" k="95" />
    <hkern u1="T" u2="&#x103;" k="95" />
    <hkern u1="T" u2="&#x101;" k="95" />
    <hkern u1="T" u2="&#xff;" k="105" />
    <hkern u1="T" u2="&#xfd;" k="105" />
    <hkern u1="T" u2="&#xfc;" k="105" />
    <hkern u1="T" u2="&#xfb;" k="105" />
    <hkern u1="T" u2="&#xfa;" k="105" />
    <hkern u1="T" u2="&#xf9;" k="105" />
    <hkern u1="T" u2="&#xf8;" k="75" />
    <hkern u1="T" u2="&#xf6;" k="75" />
    <hkern u1="T" u2="&#xf5;" k="75" />
    <hkern u1="T" u2="&#xf4;" k="75" />
    <hkern u1="T" u2="&#xf3;" k="75" />
    <hkern u1="T" u2="&#xf2;" k="75" />
    <hkern u1="T" u2="&#xf1;" k="105" />
    <hkern u1="T" u2="&#xf0;" k="75" />
    <hkern u1="T" u2="&#xeb;" k="75" />
    <hkern u1="T" u2="&#xea;" k="75" />
    <hkern u1="T" u2="&#xe9;" k="75" />
    <hkern u1="T" u2="&#xe8;" k="75" />
    <hkern u1="T" u2="&#xe7;" k="75" />
    <hkern u1="T" u2="&#xe6;" k="95" />
    <hkern u1="T" u2="&#xe5;" k="95" />
    <hkern u1="T" u2="&#xe4;" k="95" />
    <hkern u1="T" u2="&#xe3;" k="95" />
    <hkern u1="T" u2="&#xe2;" k="95" />
    <hkern u1="T" u2="&#xe1;" k="95" />
    <hkern u1="T" u2="&#xe0;" k="95" />
    <hkern u1="T" u2="&#xd8;" k="15" />
    <hkern u1="T" u2="&#xd6;" k="15" />
    <hkern u1="T" u2="&#xd5;" k="15" />
    <hkern u1="T" u2="&#xd4;" k="15" />
    <hkern u1="T" u2="&#xd3;" k="15" />
    <hkern u1="T" u2="&#xd2;" k="15" />
    <hkern u1="T" u2="&#xc7;" k="15" />
    <hkern u1="T" u2="&#xa9;" k="15" />
    <hkern u1="T" u2="z" k="80" />
    <hkern u1="T" u2="y" k="105" />
    <hkern u1="T" u2="x" k="65" />
    <hkern u1="T" u2="v" k="40" />
    <hkern u1="T" u2="r" k="105" />
    <hkern u1="T" u2="q" k="75" />
    <hkern u1="T" u2="p" k="105" />
    <hkern u1="T" u2="o" k="75" />
    <hkern u1="T" u2="n" k="105" />
    <hkern u1="T" u2="g" k="75" />
    <hkern u1="T" u2="e" k="75" />
    <hkern u1="T" u2="d" k="75" />
    <hkern u1="T" u2="_" k="45" />
    <hkern u1="T" u2="Q" k="15" />
    <hkern u1="T" u2="O" k="15" />
    <hkern u1="T" u2="G" k="15" />
    <hkern u1="T" u2="A" k="35" />
    <hkern u1="T" u2="&#x2f;" k="85" />
    <hkern u1="T" u2="&#x2e;" k="110" />
    <hkern u1="V" u2="&#x2026;" k="110" />
    <hkern u1="V" u2="&#x2014;" k="35" />
    <hkern u1="V" u2="&#x2013;" k="35" />
    <hkern u1="V" u2="&#x1ef3;" k="10" />
    <hkern u1="V" u2="&#x1ef2;" k="-20" />
    <hkern u1="V" u2="&#x1e84;" k="-20" />
    <hkern u1="V" u2="&#x1e82;" k="-20" />
    <hkern u1="V" u2="&#x1e80;" k="-20" />
    <hkern u1="V" u2="&#x1ff;" k="20" />
    <hkern u1="V" u2="&#x1fd;" k="10" />
    <hkern u1="V" u2="&#x1fb;" k="10" />
    <hkern u1="V" u2="&#x178;" k="-20" />
    <hkern u1="V" u2="&#x177;" k="10" />
    <hkern u1="V" u2="&#x176;" k="-20" />
    <hkern u1="V" u2="&#x174;" k="-20" />
    <hkern u1="V" u2="&#x173;" k="10" />
    <hkern u1="V" u2="&#x171;" k="10" />
    <hkern u1="V" u2="&#x16f;" k="10" />
    <hkern u1="V" u2="&#x16d;" k="10" />
    <hkern u1="V" u2="&#x16b;" k="10" />
    <hkern u1="V" u2="&#x169;" k="10" />
    <hkern u1="V" u2="&#x164;" k="-15" />
    <hkern u1="V" u2="&#x162;" k="-15" />
    <hkern u1="V" u2="&#x159;" k="10" />
    <hkern u1="V" u2="&#x157;" k="10" />
    <hkern u1="V" u2="&#x155;" k="10" />
    <hkern u1="V" u2="&#x153;" k="20" />
    <hkern u1="V" u2="&#x151;" k="20" />
    <hkern u1="V" u2="&#x14f;" k="20" />
    <hkern u1="V" u2="&#x14d;" k="20" />
    <hkern u1="V" u2="&#x14b;" k="10" />
    <hkern u1="V" u2="&#x148;" k="10" />
    <hkern u1="V" u2="&#x146;" k="10" />
    <hkern u1="V" u2="&#x144;" k="10" />
    <hkern u1="V" u2="&#x123;" k="20" />
    <hkern u1="V" u2="&#x121;" k="20" />
    <hkern u1="V" u2="&#x11f;" k="20" />
    <hkern u1="V" u2="&#x11d;" k="20" />
    <hkern u1="V" u2="&#x11b;" k="20" />
    <hkern u1="V" u2="&#x119;" k="20" />
    <hkern u1="V" u2="&#x117;" k="20" />
    <hkern u1="V" u2="&#x115;" k="20" />
    <hkern u1="V" u2="&#x113;" k="20" />
    <hkern u1="V" u2="&#x111;" k="20" />
    <hkern u1="V" u2="&#x10f;" k="20" />
    <hkern u1="V" u2="&#x10d;" k="20" />
    <hkern u1="V" u2="&#x10b;" k="20" />
    <hkern u1="V" u2="&#x109;" k="20" />
    <hkern u1="V" u2="&#x107;" k="20" />
    <hkern u1="V" u2="&#x105;" k="10" />
    <hkern u1="V" u2="&#x103;" k="10" />
    <hkern u1="V" u2="&#x101;" k="10" />
    <hkern u1="V" u2="&#xff;" k="10" />
    <hkern u1="V" u2="&#xfd;" k="10" />
    <hkern u1="V" u2="&#xfc;" k="10" />
    <hkern u1="V" u2="&#xfb;" k="10" />
    <hkern u1="V" u2="&#xfa;" k="10" />
    <hkern u1="V" u2="&#xf9;" k="10" />
    <hkern u1="V" u2="&#xf8;" k="20" />
    <hkern u1="V" u2="&#xf6;" k="20" />
    <hkern u1="V" u2="&#xf5;" k="20" />
    <hkern u1="V" u2="&#xf4;" k="20" />
    <hkern u1="V" u2="&#xf3;" k="20" />
    <hkern u1="V" u2="&#xf2;" k="20" />
    <hkern u1="V" u2="&#xf1;" k="10" />
    <hkern u1="V" u2="&#xf0;" k="20" />
    <hkern u1="V" u2="&#xef;" k="-20" />
    <hkern u1="V" u2="&#xeb;" k="20" />
    <hkern u1="V" u2="&#xea;" k="20" />
    <hkern u1="V" u2="&#xe9;" k="20" />
    <hkern u1="V" u2="&#xe8;" k="20" />
    <hkern u1="V" u2="&#xe7;" k="20" />
    <hkern u1="V" u2="&#xe6;" k="10" />
    <hkern u1="V" u2="&#xe5;" k="10" />
    <hkern u1="V" u2="&#xe4;" k="10" />
    <hkern u1="V" u2="&#xe3;" k="10" />
    <hkern u1="V" u2="&#xe2;" k="10" />
    <hkern u1="V" u2="&#xe1;" k="10" />
    <hkern u1="V" u2="&#xe0;" k="10" />
    <hkern u1="V" u2="&#xdd;" k="-20" />
    <hkern u1="V" u2="y" k="10" />
    <hkern u1="V" u2="v" k="-20" />
    <hkern u1="V" u2="u" k="10" />
    <hkern u1="V" u2="r" k="10" />
    <hkern u1="V" u2="q" k="20" />
    <hkern u1="V" u2="p" k="15" />
    <hkern u1="V" u2="o" k="20" />
    <hkern u1="V" u2="n" k="10" />
    <hkern u1="V" u2="m" k="10" />
    <hkern u1="V" u2="g" k="20" />
    <hkern u1="V" u2="e" k="20" />
    <hkern u1="V" u2="d" k="20" />
    <hkern u1="V" u2="c" k="20" />
    <hkern u1="V" u2="a" k="10" />
    <hkern u1="V" u2="_" k="65" />
    <hkern u1="V" u2="Y" k="-20" />
    <hkern u1="V" u2="W" k="-20" />
    <hkern u1="V" u2="V" k="-15" />
    <hkern u1="V" u2="T" k="-15" />
    <hkern u1="V" u2="&#x2f;" k="55" />
    <hkern u1="V" u2="&#x2e;" k="110" />
    <hkern u1="V" u2="&#x2d;" k="35" />
    <hkern u1="V" u2="&#x2c;" k="110" />
    <hkern u1="W" u2="&#x2026;" k="55" />
    <hkern u1="W" u2="&#x2014;" k="20" />
    <hkern u1="W" u2="&#x2013;" k="20" />
    <hkern u1="W" u2="&#x1ff;" k="10" />
    <hkern u1="W" u2="&#x1fd;" k="10" />
    <hkern u1="W" u2="&#x1fb;" k="10" />
    <hkern u1="W" u2="&#x153;" k="10" />
    <hkern u1="W" u2="&#x151;" k="10" />
    <hkern u1="W" u2="&#x14f;" k="10" />
    <hkern u1="W" u2="&#x14d;" k="10" />
    <hkern u1="W" u2="&#x123;" k="10" />
    <hkern u1="W" u2="&#x121;" k="10" />
    <hkern u1="W" u2="&#x11f;" k="10" />
    <hkern u1="W" u2="&#x11d;" k="10" />
    <hkern u1="W" u2="&#x11b;" k="10" />
    <hkern u1="W" u2="&#x119;" k="10" />
    <hkern u1="W" u2="&#x117;" k="10" />
    <hkern u1="W" u2="&#x115;" k="10" />
    <hkern u1="W" u2="&#x113;" k="10" />
    <hkern u1="W" u2="&#x111;" k="10" />
    <hkern u1="W" u2="&#x10f;" k="10" />
    <hkern u1="W" u2="&#x10d;" k="10" />
    <hkern u1="W" u2="&#x10b;" k="10" />
    <hkern u1="W" u2="&#x109;" k="10" />
    <hkern u1="W" u2="&#x107;" k="10" />
    <hkern u1="W" u2="&#x105;" k="10" />
    <hkern u1="W" u2="&#x103;" k="10" />
    <hkern u1="W" u2="&#x101;" k="10" />
    <hkern u1="W" u2="&#xf8;" k="10" />
    <hkern u1="W" u2="&#xf6;" k="10" />
    <hkern u1="W" u2="&#xf5;" k="10" />
    <hkern u1="W" u2="&#xf4;" k="10" />
    <hkern u1="W" u2="&#xf3;" k="10" />
    <hkern u1="W" u2="&#xf2;" k="10" />
    <hkern u1="W" u2="&#xf0;" k="10" />
    <hkern u1="W" u2="&#xef;" k="-20" />
    <hkern u1="W" u2="&#xeb;" k="10" />
    <hkern u1="W" u2="&#xea;" k="10" />
    <hkern u1="W" u2="&#xe9;" k="10" />
    <hkern u1="W" u2="&#xe8;" k="10" />
    <hkern u1="W" u2="&#xe7;" k="10" />
    <hkern u1="W" u2="&#xe6;" k="10" />
    <hkern u1="W" u2="&#xe5;" k="10" />
    <hkern u1="W" u2="&#xe4;" k="10" />
    <hkern u1="W" u2="&#xe3;" k="10" />
    <hkern u1="W" u2="&#xe2;" k="10" />
    <hkern u1="W" u2="&#xe1;" k="10" />
    <hkern u1="W" u2="&#xe0;" k="10" />
    <hkern u1="W" u2="v" k="-20" />
    <hkern u1="W" u2="q" k="10" />
    <hkern u1="W" u2="o" k="10" />
    <hkern u1="W" u2="g" k="10" />
    <hkern u1="W" u2="e" k="10" />
    <hkern u1="W" u2="d" k="10" />
    <hkern u1="W" u2="_" k="45" />
    <hkern u1="W" u2="V" k="-20" />
    <hkern u1="W" u2="&#x2f;" k="55" />
    <hkern u1="W" u2="&#x2e;" k="55" />
    <hkern u1="X" u2="&#x2026;" k="-30" />
    <hkern u1="X" u2="&#x2014;" k="65" />
    <hkern u1="X" u2="&#x2013;" k="65" />
    <hkern u1="X" u2="&#x1ef2;" k="10" />
    <hkern u1="X" u2="&#x1fe;" k="10" />
    <hkern u1="X" u2="&#x178;" k="10" />
    <hkern u1="X" u2="&#x176;" k="10" />
    <hkern u1="X" u2="&#x152;" k="10" />
    <hkern u1="X" u2="&#x150;" k="10" />
    <hkern u1="X" u2="&#x14e;" k="10" />
    <hkern u1="X" u2="&#x14c;" k="10" />
    <hkern u1="X" u2="&#x122;" k="10" />
    <hkern u1="X" u2="&#x120;" k="10" />
    <hkern u1="X" u2="&#x11e;" k="10" />
    <hkern u1="X" u2="&#x11c;" k="10" />
    <hkern u1="X" u2="&#x11a;" k="7" />
    <hkern u1="X" u2="&#x118;" k="7" />
    <hkern u1="X" u2="&#x116;" k="7" />
    <hkern u1="X" u2="&#x114;" k="7" />
    <hkern u1="X" u2="&#x112;" k="7" />
    <hkern u1="X" u2="&#x10c;" k="10" />
    <hkern u1="X" u2="&#x10a;" k="10" />
    <hkern u1="X" u2="&#x108;" k="10" />
    <hkern u1="X" u2="&#x106;" k="10" />
    <hkern u1="X" u2="&#xdd;" k="10" />
    <hkern u1="X" u2="&#xd8;" k="10" />
    <hkern u1="X" u2="&#xd6;" k="10" />
    <hkern u1="X" u2="&#xd5;" k="10" />
    <hkern u1="X" u2="&#xd4;" k="10" />
    <hkern u1="X" u2="&#xd3;" k="10" />
    <hkern u1="X" u2="&#xd2;" k="10" />
    <hkern u1="X" u2="&#xcb;" k="7" />
    <hkern u1="X" u2="&#xca;" k="7" />
    <hkern u1="X" u2="&#xc9;" k="7" />
    <hkern u1="X" u2="&#xc8;" k="7" />
    <hkern u1="X" u2="&#xc7;" k="10" />
    <hkern u1="X" u2="&#xa9;" k="10" />
    <hkern u1="X" u2="_" k="-50" />
    <hkern u1="X" u2="Q" k="10" />
    <hkern u1="X" u2="O" k="10" />
    <hkern u1="X" u2="G" k="10" />
    <hkern u1="X" u2="E" k="7" />
    <hkern u1="X" u2="C" k="10" />
    <hkern u1="X" u2="&#x2e;" k="-30" />
    <hkern u1="X" u2="&#x2d;" k="65" />
    <hkern u1="X" u2="&#x2c;" k="-30" />
    <hkern u1="Y" u2="V" k="-20" />
    <hkern u1="Z" u2="&#x2026;" k="-30" />
    <hkern u1="Z" u2="&#x2014;" k="120" />
    <hkern u1="Z" u2="&#x2013;" k="120" />
    <hkern u1="Z" u2="&#x1fe;" k="10" />
    <hkern u1="Z" u2="&#x152;" k="10" />
    <hkern u1="Z" u2="&#x150;" k="10" />
    <hkern u1="Z" u2="&#x14e;" k="10" />
    <hkern u1="Z" u2="&#x14c;" k="10" />
    <hkern u1="Z" u2="&#x122;" k="10" />
    <hkern u1="Z" u2="&#x120;" k="10" />
    <hkern u1="Z" u2="&#x11e;" k="10" />
    <hkern u1="Z" u2="&#x11c;" k="10" />
    <hkern u1="Z" u2="&#x10c;" k="10" />
    <hkern u1="Z" u2="&#x10a;" k="10" />
    <hkern u1="Z" u2="&#x108;" k="10" />
    <hkern u1="Z" u2="&#x106;" k="10" />
    <hkern u1="Z" u2="&#xd8;" k="10" />
    <hkern u1="Z" u2="&#xd6;" k="10" />
    <hkern u1="Z" u2="&#xd5;" k="10" />
    <hkern u1="Z" u2="&#xd4;" k="10" />
    <hkern u1="Z" u2="&#xd3;" k="10" />
    <hkern u1="Z" u2="&#xd2;" k="10" />
    <hkern u1="Z" u2="&#xc7;" k="10" />
    <hkern u1="Z" u2="&#xa9;" k="10" />
    <hkern u1="Z" u2="v" k="30" />
    <hkern u1="Z" u2="Q" k="10" />
    <hkern u1="Z" u2="O" k="10" />
    <hkern u1="Z" u2="G" k="10" />
    <hkern u1="Z" u2="&#x2e;" k="-30" />
    <hkern u1="[" u2="&#x134;" k="-50" />
    <hkern u1="[" u2="j" k="-40" />
    <hkern u1="[" u2="J" k="-50" />
    <hkern u1="_" u2="&#x1ef2;" k="60" />
    <hkern u1="_" u2="&#x1e85;" k="35" />
    <hkern u1="_" u2="&#x1e84;" k="45" />
    <hkern u1="_" u2="&#x1e83;" k="35" />
    <hkern u1="_" u2="&#x1e82;" k="45" />
    <hkern u1="_" u2="&#x1e81;" k="35" />
    <hkern u1="_" u2="&#x1e80;" k="45" />
    <hkern u1="_" u2="&#x178;" k="60" />
    <hkern u1="_" u2="&#x176;" k="60" />
    <hkern u1="_" u2="&#x175;" k="35" />
    <hkern u1="_" u2="&#x174;" k="45" />
    <hkern u1="_" u2="&#x164;" k="45" />
    <hkern u1="_" u2="&#x162;" k="45" />
    <hkern u1="_" u2="&#x135;" k="-65" />
    <hkern u1="_" u2="&#xdd;" k="60" />
    <hkern u1="_" u2="x" k="-40" />
    <hkern u1="_" u2="w" k="35" />
    <hkern u1="_" u2="v" k="35" />
    <hkern u1="_" u2="j" k="-65" />
    <hkern u1="_" u2="Y" k="60" />
    <hkern u1="_" u2="X" k="-50" />
    <hkern u1="_" u2="W" k="45" />
    <hkern u1="_" u2="V" k="65" />
    <hkern u1="_" u2="T" k="45" />
    <hkern u1="a" u2="&#x1e84;" k="10" />
    <hkern u1="a" u2="&#x1e82;" k="10" />
    <hkern u1="a" u2="&#x1e80;" k="10" />
    <hkern u1="a" u2="&#x174;" k="10" />
    <hkern u1="a" u2="&#x164;" k="70" />
    <hkern u1="a" u2="&#x162;" k="70" />
    <hkern u1="a" u2="W" k="10" />
    <hkern u1="a" u2="V" k="30" />
    <hkern u1="a" u2="T" k="70" />
    <hkern u1="b" u2="&#x2026;" k="20" />
    <hkern u1="b" u2="&#x1e84;" k="10" />
    <hkern u1="b" u2="&#x1e82;" k="10" />
    <hkern u1="b" u2="&#x1e80;" k="10" />
    <hkern u1="b" u2="&#x174;" k="10" />
    <hkern u1="b" u2="&#x164;" k="75" />
    <hkern u1="b" u2="&#x162;" k="75" />
    <hkern u1="b" u2="z" k="20" />
    <hkern u1="b" u2="x" k="10" />
    <hkern u1="b" u2="V" k="20" />
    <hkern u1="b" u2="&#x2e;" k="20" />
    <hkern u1="c" u2="&#x2026;" k="-20" />
    <hkern u1="c" u2="&#x201c;" k="-40" />
    <hkern u1="c" u2="&#x1ef3;" k="10" />
    <hkern u1="c" u2="&#x1ff;" k="25" />
    <hkern u1="c" u2="&#x1fd;" k="10" />
    <hkern u1="c" u2="&#x1fb;" k="10" />
    <hkern u1="c" u2="&#x177;" k="10" />
    <hkern u1="c" u2="&#x173;" k="10" />
    <hkern u1="c" u2="&#x171;" k="10" />
    <hkern u1="c" u2="&#x16f;" k="10" />
    <hkern u1="c" u2="&#x16d;" k="10" />
    <hkern u1="c" u2="&#x16b;" k="10" />
    <hkern u1="c" u2="&#x169;" k="10" />
    <hkern u1="c" u2="&#x164;" k="80" />
    <hkern u1="c" u2="&#x162;" k="80" />
    <hkern u1="c" u2="&#x153;" k="25" />
    <hkern u1="c" u2="&#x151;" k="25" />
    <hkern u1="c" u2="&#x14f;" k="25" />
    <hkern u1="c" u2="&#x14d;" k="25" />
    <hkern u1="c" u2="&#x123;" k="25" />
    <hkern u1="c" u2="&#x121;" k="25" />
    <hkern u1="c" u2="&#x11f;" k="25" />
    <hkern u1="c" u2="&#x11d;" k="25" />
    <hkern u1="c" u2="&#x11b;" k="25" />
    <hkern u1="c" u2="&#x119;" k="25" />
    <hkern u1="c" u2="&#x117;" k="25" />
    <hkern u1="c" u2="&#x115;" k="25" />
    <hkern u1="c" u2="&#x113;" k="25" />
    <hkern u1="c" u2="&#x111;" k="25" />
    <hkern u1="c" u2="&#x10f;" k="25" />
    <hkern u1="c" u2="&#x10d;" k="25" />
    <hkern u1="c" u2="&#x10b;" k="25" />
    <hkern u1="c" u2="&#x109;" k="25" />
    <hkern u1="c" u2="&#x107;" k="25" />
    <hkern u1="c" u2="&#x105;" k="10" />
    <hkern u1="c" u2="&#x103;" k="10" />
    <hkern u1="c" u2="&#x101;" k="10" />
    <hkern u1="c" u2="&#xff;" k="10" />
    <hkern u1="c" u2="&#xfd;" k="10" />
    <hkern u1="c" u2="&#xfc;" k="10" />
    <hkern u1="c" u2="&#xfb;" k="10" />
    <hkern u1="c" u2="&#xfa;" k="10" />
    <hkern u1="c" u2="&#xf9;" k="10" />
    <hkern u1="c" u2="&#xf8;" k="25" />
    <hkern u1="c" u2="&#xf6;" k="25" />
    <hkern u1="c" u2="&#xf5;" k="25" />
    <hkern u1="c" u2="&#xf4;" k="25" />
    <hkern u1="c" u2="&#xf3;" k="25" />
    <hkern u1="c" u2="&#xf2;" k="25" />
    <hkern u1="c" u2="&#xf0;" k="25" />
    <hkern u1="c" u2="&#xeb;" k="25" />
    <hkern u1="c" u2="&#xea;" k="25" />
    <hkern u1="c" u2="&#xe9;" k="25" />
    <hkern u1="c" u2="&#xe8;" k="25" />
    <hkern u1="c" u2="&#xe7;" k="25" />
    <hkern u1="c" u2="&#xe6;" k="10" />
    <hkern u1="c" u2="&#xe5;" k="10" />
    <hkern u1="c" u2="&#xe4;" k="10" />
    <hkern u1="c" u2="&#xe3;" k="10" />
    <hkern u1="c" u2="&#xe2;" k="10" />
    <hkern u1="c" u2="&#xe1;" k="10" />
    <hkern u1="c" u2="&#xe0;" k="10" />
    <hkern u1="c" u2="y" k="10" />
    <hkern u1="c" u2="x" k="-13" />
    <hkern u1="c" u2="q" k="25" />
    <hkern u1="c" u2="o" k="25" />
    <hkern u1="c" u2="g" k="25" />
    <hkern u1="c" u2="e" k="25" />
    <hkern u1="c" u2="d" k="25" />
    <hkern u1="c" u2="&#x2e;" k="-20" />
    <hkern u1="e" u2="&#x1e84;" k="10" />
    <hkern u1="e" u2="&#x1e82;" k="10" />
    <hkern u1="e" u2="&#x1e80;" k="10" />
    <hkern u1="e" u2="&#x219;" k="-5" />
    <hkern u1="e" u2="&#x174;" k="10" />
    <hkern u1="e" u2="&#x164;" k="70" />
    <hkern u1="e" u2="&#x162;" k="70" />
    <hkern u1="e" u2="&#x161;" k="-5" />
    <hkern u1="e" u2="&#x15f;" k="-5" />
    <hkern u1="e" u2="&#x15d;" k="-5" />
    <hkern u1="e" u2="&#x15b;" k="-5" />
    <hkern u1="e" u2="V" k="20" />
    <hkern u1="f" u2="&#x2122;" k="-75" />
    <hkern u1="f" u2="&#x2026;" k="95" />
    <hkern u1="f" u2="&#x201d;" k="-45" />
    <hkern u1="f" u2="&#x201c;" k="-40" />
    <hkern u1="f" u2="&#x2019;" k="-45" />
    <hkern u1="f" u2="&#x2018;" k="-40" />
    <hkern u1="f" u2="&#x2014;" k="20" />
    <hkern u1="f" u2="&#x2013;" k="20" />
    <hkern u1="f" u2="&#x1ef2;" k="-65" />
    <hkern u1="f" u2="&#x1e84;" k="-85" />
    <hkern u1="f" u2="&#x1e82;" k="-85" />
    <hkern u1="f" u2="&#x1e80;" k="-85" />
    <hkern u1="f" u2="&#x1ff;" k="15" />
    <hkern u1="f" u2="&#x1fd;" k="10" />
    <hkern u1="f" u2="&#x1fb;" k="10" />
    <hkern u1="f" u2="&#x17d;" k="-55" />
    <hkern u1="f" u2="&#x17b;" k="-55" />
    <hkern u1="f" u2="&#x179;" k="-55" />
    <hkern u1="f" u2="&#x178;" k="-65" />
    <hkern u1="f" u2="&#x176;" k="-65" />
    <hkern u1="f" u2="&#x174;" k="-85" />
    <hkern u1="f" u2="&#x164;" k="-85" />
    <hkern u1="f" u2="&#x162;" k="-85" />
    <hkern u1="f" u2="&#x153;" k="15" />
    <hkern u1="f" u2="&#x151;" k="15" />
    <hkern u1="f" u2="&#x14f;" k="15" />
    <hkern u1="f" u2="&#x14d;" k="15" />
    <hkern u1="f" u2="&#x123;" k="15" />
    <hkern u1="f" u2="&#x121;" k="15" />
    <hkern u1="f" u2="&#x11f;" k="15" />
    <hkern u1="f" u2="&#x11d;" k="15" />
    <hkern u1="f" u2="&#x11b;" k="15" />
    <hkern u1="f" u2="&#x119;" k="15" />
    <hkern u1="f" u2="&#x117;" k="15" />
    <hkern u1="f" u2="&#x115;" k="15" />
    <hkern u1="f" u2="&#x113;" k="15" />
    <hkern u1="f" u2="&#x111;" k="15" />
    <hkern u1="f" u2="&#x10f;" k="15" />
    <hkern u1="f" u2="&#x10d;" k="15" />
    <hkern u1="f" u2="&#x10b;" k="15" />
    <hkern u1="f" u2="&#x109;" k="15" />
    <hkern u1="f" u2="&#x107;" k="15" />
    <hkern u1="f" u2="&#x105;" k="10" />
    <hkern u1="f" u2="&#x103;" k="10" />
    <hkern u1="f" u2="&#x101;" k="10" />
    <hkern u1="f" u2="&#xf8;" k="15" />
    <hkern u1="f" u2="&#xf6;" k="15" />
    <hkern u1="f" u2="&#xf5;" k="15" />
    <hkern u1="f" u2="&#xf4;" k="15" />
    <hkern u1="f" u2="&#xf3;" k="15" />
    <hkern u1="f" u2="&#xf2;" k="15" />
    <hkern u1="f" u2="&#xf0;" k="15" />
    <hkern u1="f" u2="&#xeb;" k="15" />
    <hkern u1="f" u2="&#xea;" k="15" />
    <hkern u1="f" u2="&#xe9;" k="15" />
    <hkern u1="f" u2="&#xe8;" k="15" />
    <hkern u1="f" u2="&#xe7;" k="15" />
    <hkern u1="f" u2="&#xe6;" k="10" />
    <hkern u1="f" u2="&#xe5;" k="10" />
    <hkern u1="f" u2="&#xe4;" k="10" />
    <hkern u1="f" u2="&#xe3;" k="10" />
    <hkern u1="f" u2="&#xe2;" k="10" />
    <hkern u1="f" u2="&#xe1;" k="10" />
    <hkern u1="f" u2="&#xe0;" k="10" />
    <hkern u1="f" u2="&#xdd;" k="-65" />
    <hkern u1="f" u2="&#xba;" k="-20" />
    <hkern u1="f" u2="&#xaa;" k="-20" />
    <hkern u1="f" u2="&#x7d;" k="-100" />
    <hkern u1="f" u2="q" k="15" />
    <hkern u1="f" u2="o" k="15" />
    <hkern u1="f" u2="g" k="15" />
    <hkern u1="f" u2="e" k="15" />
    <hkern u1="f" u2="d" k="15" />
    <hkern u1="f" u2="c" k="15" />
    <hkern u1="f" u2="a" k="10" />
    <hkern u1="f" u2="]" k="-100" />
    <hkern u1="f" u2="\" k="-30" />
    <hkern u1="f" u2="Z" k="-55" />
    <hkern u1="f" u2="Y" k="-65" />
    <hkern u1="f" u2="X" k="-75" />
    <hkern u1="f" u2="W" k="-85" />
    <hkern u1="f" u2="V" k="-95" />
    <hkern u1="f" u2="T" k="-85" />
    <hkern u1="f" u2="&#x3f;" k="-70" />
    <hkern u1="f" u2="&#x2f;" k="10" />
    <hkern u1="f" u2="&#x2e;" k="95" />
    <hkern u1="f" u2="&#x2d;" k="20" />
    <hkern u1="f" u2="&#x2c;" k="95" />
    <hkern u1="f" u2="&#x2a;" k="-70" />
    <hkern u1="f" u2="&#x29;" k="-80" />
    <hkern u1="f" u2="&#x21;" k="-45" />
    <hkern u1="g" u2="&#x1e84;" k="10" />
    <hkern u1="g" u2="&#x1e82;" k="10" />
    <hkern u1="g" u2="&#x1e80;" k="10" />
    <hkern u1="g" u2="&#x174;" k="10" />
    <hkern u1="g" u2="&#x164;" k="70" />
    <hkern u1="g" u2="&#x162;" k="70" />
    <hkern u1="g" u2="V" k="20" />
    <hkern u1="h" u2="&#x164;" k="70" />
    <hkern u1="h" u2="&#x162;" k="70" />
    <hkern u1="k" u2="&#x2026;" k="-30" />
    <hkern u1="k" u2="&#x2014;" k="45" />
    <hkern u1="k" u2="&#x2013;" k="45" />
    <hkern u1="k" u2="&#x1ff;" k="10" />
    <hkern u1="k" u2="&#x1fd;" k="-10" />
    <hkern u1="k" u2="&#x1fb;" k="-10" />
    <hkern u1="k" u2="&#x164;" k="40" />
    <hkern u1="k" u2="&#x162;" k="40" />
    <hkern u1="k" u2="&#x153;" k="10" />
    <hkern u1="k" u2="&#x151;" k="10" />
    <hkern u1="k" u2="&#x14f;" k="10" />
    <hkern u1="k" u2="&#x14d;" k="10" />
    <hkern u1="k" u2="&#x123;" k="10" />
    <hkern u1="k" u2="&#x121;" k="10" />
    <hkern u1="k" u2="&#x11f;" k="10" />
    <hkern u1="k" u2="&#x11d;" k="10" />
    <hkern u1="k" u2="&#x11b;" k="10" />
    <hkern u1="k" u2="&#x119;" k="10" />
    <hkern u1="k" u2="&#x117;" k="10" />
    <hkern u1="k" u2="&#x115;" k="10" />
    <hkern u1="k" u2="&#x113;" k="10" />
    <hkern u1="k" u2="&#x111;" k="10" />
    <hkern u1="k" u2="&#x10f;" k="10" />
    <hkern u1="k" u2="&#x10d;" k="10" />
    <hkern u1="k" u2="&#x10b;" k="10" />
    <hkern u1="k" u2="&#x109;" k="10" />
    <hkern u1="k" u2="&#x107;" k="10" />
    <hkern u1="k" u2="&#x105;" k="-10" />
    <hkern u1="k" u2="&#x103;" k="-10" />
    <hkern u1="k" u2="&#x101;" k="-10" />
    <hkern u1="k" u2="&#xf8;" k="10" />
    <hkern u1="k" u2="&#xf6;" k="10" />
    <hkern u1="k" u2="&#xf5;" k="10" />
    <hkern u1="k" u2="&#xf4;" k="10" />
    <hkern u1="k" u2="&#xf3;" k="10" />
    <hkern u1="k" u2="&#xf2;" k="10" />
    <hkern u1="k" u2="&#xf0;" k="10" />
    <hkern u1="k" u2="&#xeb;" k="10" />
    <hkern u1="k" u2="&#xea;" k="10" />
    <hkern u1="k" u2="&#xe9;" k="10" />
    <hkern u1="k" u2="&#xe8;" k="10" />
    <hkern u1="k" u2="&#xe7;" k="10" />
    <hkern u1="k" u2="&#xe6;" k="-20" />
    <hkern u1="k" u2="&#xe5;" k="-10" />
    <hkern u1="k" u2="&#xe4;" k="-10" />
    <hkern u1="k" u2="&#xe3;" k="-10" />
    <hkern u1="k" u2="&#xe2;" k="-10" />
    <hkern u1="k" u2="&#xe1;" k="-10" />
    <hkern u1="k" u2="&#xe0;" k="-10" />
    <hkern u1="k" u2="q" k="10" />
    <hkern u1="k" u2="o" k="10" />
    <hkern u1="k" u2="g" k="10" />
    <hkern u1="k" u2="e" k="10" />
    <hkern u1="k" u2="d" k="10" />
    <hkern u1="k" u2="&#x2e;" k="-30" />
    <hkern u1="m" u2="&#x164;" k="70" />
    <hkern u1="m" u2="&#x162;" k="70" />
    <hkern u1="m" u2="T" k="70" />
    <hkern u1="n" u2="&#x164;" k="70" />
    <hkern u1="n" u2="&#x162;" k="70" />
    <hkern u1="n" u2="T" k="70" />
    <hkern u1="o" u2="&#x2026;" k="20" />
    <hkern u1="o" u2="&#x1e84;" k="10" />
    <hkern u1="o" u2="&#x1e82;" k="10" />
    <hkern u1="o" u2="&#x1e80;" k="10" />
    <hkern u1="o" u2="&#x174;" k="10" />
    <hkern u1="o" u2="&#x164;" k="75" />
    <hkern u1="o" u2="&#x162;" k="75" />
    <hkern u1="o" u2="z" k="20" />
    <hkern u1="o" u2="x" k="10" />
    <hkern u1="o" u2="W" k="10" />
    <hkern u1="o" u2="V" k="20" />
    <hkern u1="o" u2="T" k="75" />
    <hkern u1="o" u2="&#x2e;" k="20" />
    <hkern u1="o" u2="&#x2c;" k="20" />
    <hkern u1="p" u2="&#x2026;" k="20" />
    <hkern u1="p" u2="&#x1ef2;" k="35" />
    <hkern u1="p" u2="&#x1e84;" k="10" />
    <hkern u1="p" u2="&#x1e82;" k="10" />
    <hkern u1="p" u2="&#x1e80;" k="10" />
    <hkern u1="p" u2="&#x178;" k="35" />
    <hkern u1="p" u2="&#x176;" k="35" />
    <hkern u1="p" u2="&#x174;" k="10" />
    <hkern u1="p" u2="&#x164;" k="75" />
    <hkern u1="p" u2="&#x162;" k="75" />
    <hkern u1="p" u2="&#xdd;" k="35" />
    <hkern u1="p" u2="z" k="20" />
    <hkern u1="p" u2="x" k="10" />
    <hkern u1="p" u2="Y" k="35" />
    <hkern u1="p" u2="W" k="10" />
    <hkern u1="p" u2="V" k="20" />
    <hkern u1="p" u2="T" k="75" />
    <hkern u1="p" u2="&#x2e;" k="20" />
    <hkern u1="p" u2="&#x2c;" k="20" />
    <hkern u1="q" u2="&#x1e84;" k="10" />
    <hkern u1="q" u2="&#x1e82;" k="10" />
    <hkern u1="q" u2="&#x1e80;" k="10" />
    <hkern u1="q" u2="&#x174;" k="10" />
    <hkern u1="q" u2="&#x164;" k="70" />
    <hkern u1="q" u2="&#x162;" k="70" />
    <hkern u1="q" u2="W" k="10" />
    <hkern u1="q" u2="V" k="20" />
    <hkern u1="q" u2="T" k="70" />
    <hkern u1="r" g2="fl" k="-20" />
    <hkern u1="r" g2="fi" k="-20" />
    <hkern u1="r" u2="&#x2026;" k="115" />
    <hkern u1="r" u2="&#x201c;" k="-40" />
    <hkern u1="r" u2="&#x2014;" k="60" />
    <hkern u1="r" u2="&#x2013;" k="60" />
    <hkern u1="r" u2="&#x1e85;" k="-30" />
    <hkern u1="r" u2="&#x1e83;" k="-30" />
    <hkern u1="r" u2="&#x1e81;" k="-30" />
    <hkern u1="r" u2="&#x1ff;" k="20" />
    <hkern u1="r" u2="&#x1fd;" k="10" />
    <hkern u1="r" u2="&#x1fb;" k="10" />
    <hkern u1="r" u2="&#x17f;" k="-20" />
    <hkern u1="r" u2="&#x175;" k="-30" />
    <hkern u1="r" u2="&#x167;" k="-20" />
    <hkern u1="r" u2="&#x165;" k="-20" />
    <hkern u1="r" u2="&#x164;" k="30" />
    <hkern u1="r" u2="&#x163;" k="-20" />
    <hkern u1="r" u2="&#x162;" k="30" />
    <hkern u1="r" u2="&#x153;" k="20" />
    <hkern u1="r" u2="&#x151;" k="20" />
    <hkern u1="r" u2="&#x14f;" k="20" />
    <hkern u1="r" u2="&#x14d;" k="20" />
    <hkern u1="r" u2="&#x123;" k="20" />
    <hkern u1="r" u2="&#x121;" k="20" />
    <hkern u1="r" u2="&#x11f;" k="20" />
    <hkern u1="r" u2="&#x11d;" k="20" />
    <hkern u1="r" u2="&#x11b;" k="20" />
    <hkern u1="r" u2="&#x119;" k="20" />
    <hkern u1="r" u2="&#x117;" k="20" />
    <hkern u1="r" u2="&#x115;" k="20" />
    <hkern u1="r" u2="&#x113;" k="20" />
    <hkern u1="r" u2="&#x111;" k="20" />
    <hkern u1="r" u2="&#x10f;" k="20" />
    <hkern u1="r" u2="&#x10d;" k="20" />
    <hkern u1="r" u2="&#x10b;" k="20" />
    <hkern u1="r" u2="&#x109;" k="20" />
    <hkern u1="r" u2="&#x107;" k="20" />
    <hkern u1="r" u2="&#x105;" k="10" />
    <hkern u1="r" u2="&#x103;" k="10" />
    <hkern u1="r" u2="&#x101;" k="10" />
    <hkern u1="r" u2="&#xf8;" k="20" />
    <hkern u1="r" u2="&#xf6;" k="20" />
    <hkern u1="r" u2="&#xf5;" k="20" />
    <hkern u1="r" u2="&#xf4;" k="20" />
    <hkern u1="r" u2="&#xf3;" k="20" />
    <hkern u1="r" u2="&#xf2;" k="20" />
    <hkern u1="r" u2="&#xf0;" k="20" />
    <hkern u1="r" u2="&#xeb;" k="20" />
    <hkern u1="r" u2="&#xea;" k="20" />
    <hkern u1="r" u2="&#xe9;" k="20" />
    <hkern u1="r" u2="&#xe8;" k="20" />
    <hkern u1="r" u2="&#xe7;" k="20" />
    <hkern u1="r" u2="&#xe6;" k="10" />
    <hkern u1="r" u2="&#xe5;" k="10" />
    <hkern u1="r" u2="&#xe4;" k="10" />
    <hkern u1="r" u2="&#xe3;" k="10" />
    <hkern u1="r" u2="&#xe2;" k="10" />
    <hkern u1="r" u2="&#xe1;" k="10" />
    <hkern u1="r" u2="&#xe0;" k="10" />
    <hkern u1="r" u2="x" k="-10" />
    <hkern u1="r" u2="v" k="-30" />
    <hkern u1="r" u2="q" k="20" />
    <hkern u1="r" u2="o" k="20" />
    <hkern u1="r" u2="g" k="20" />
    <hkern u1="r" u2="e" k="20" />
    <hkern u1="r" u2="d" k="20" />
    <hkern u1="r" u2="&#x2f;" k="50" />
    <hkern u1="r" u2="&#x2e;" k="115" />
    <hkern u1="s" u2="&#x219;" k="20" />
    <hkern u1="s" u2="&#x164;" k="70" />
    <hkern u1="s" u2="&#x162;" k="70" />
    <hkern u1="s" u2="&#x161;" k="20" />
    <hkern u1="s" u2="&#x15f;" k="20" />
    <hkern u1="s" u2="&#x15d;" k="20" />
    <hkern u1="s" u2="&#x15b;" k="20" />
    <hkern u1="t" u2="&#x2026;" k="-50" />
    <hkern u1="t" u2="&#x201c;" k="-20" />
    <hkern u1="t" u2="&#x2018;" k="-20" />
    <hkern u1="t" u2="&#x2014;" k="65" />
    <hkern u1="t" u2="&#x2013;" k="65" />
    <hkern u1="t" u2="&#x1ff;" k="10" />
    <hkern u1="t" u2="&#x164;" k="50" />
    <hkern u1="t" u2="&#x162;" k="50" />
    <hkern u1="t" u2="&#x153;" k="10" />
    <hkern u1="t" u2="&#x151;" k="10" />
    <hkern u1="t" u2="&#x14f;" k="10" />
    <hkern u1="t" u2="&#x14d;" k="10" />
    <hkern u1="t" u2="&#x123;" k="10" />
    <hkern u1="t" u2="&#x121;" k="10" />
    <hkern u1="t" u2="&#x11f;" k="10" />
    <hkern u1="t" u2="&#x11d;" k="10" />
    <hkern u1="t" u2="&#x11b;" k="10" />
    <hkern u1="t" u2="&#x119;" k="10" />
    <hkern u1="t" u2="&#x117;" k="10" />
    <hkern u1="t" u2="&#x115;" k="10" />
    <hkern u1="t" u2="&#x113;" k="10" />
    <hkern u1="t" u2="&#x111;" k="10" />
    <hkern u1="t" u2="&#x10f;" k="10" />
    <hkern u1="t" u2="&#x10d;" k="10" />
    <hkern u1="t" u2="&#x10b;" k="10" />
    <hkern u1="t" u2="&#x109;" k="10" />
    <hkern u1="t" u2="&#x107;" k="10" />
    <hkern u1="t" u2="&#xf8;" k="10" />
    <hkern u1="t" u2="&#xf6;" k="10" />
    <hkern u1="t" u2="&#xf5;" k="10" />
    <hkern u1="t" u2="&#xf4;" k="10" />
    <hkern u1="t" u2="&#xf3;" k="10" />
    <hkern u1="t" u2="&#xf2;" k="10" />
    <hkern u1="t" u2="&#xf0;" k="10" />
    <hkern u1="t" u2="&#xeb;" k="10" />
    <hkern u1="t" u2="&#xea;" k="10" />
    <hkern u1="t" u2="&#xe9;" k="10" />
    <hkern u1="t" u2="&#xe8;" k="10" />
    <hkern u1="t" u2="&#xe7;" k="10" />
    <hkern u1="t" u2="x" k="-10" />
    <hkern u1="t" u2="q" k="10" />
    <hkern u1="t" u2="o" k="10" />
    <hkern u1="t" u2="g" k="10" />
    <hkern u1="t" u2="e" k="10" />
    <hkern u1="t" u2="d" k="10" />
    <hkern u1="t" u2="c" k="10" />
    <hkern u1="t" u2="T" k="50" />
    <hkern u1="t" u2="&#x2e;" k="-50" />
    <hkern u1="t" u2="&#x2d;" k="65" />
    <hkern u1="t" u2="&#x2c;" k="-50" />
    <hkern u1="u" u2="&#x164;" k="110" />
    <hkern u1="u" u2="&#x162;" k="110" />
    <hkern u1="u" u2="T" k="110" />
    <hkern u1="v" u2="&#x2026;" k="65" />
    <hkern u1="v" u2="&#x201c;" k="-40" />
    <hkern u1="v" u2="&#x2018;" k="-40" />
    <hkern u1="v" u2="&#x1fe;" k="-20" />
    <hkern u1="v" u2="&#x164;" k="55" />
    <hkern u1="v" u2="&#x162;" k="55" />
    <hkern u1="v" u2="&#x152;" k="-20" />
    <hkern u1="v" u2="&#x150;" k="-20" />
    <hkern u1="v" u2="&#x14e;" k="-20" />
    <hkern u1="v" u2="&#x14c;" k="-20" />
    <hkern u1="v" u2="&#x122;" k="-20" />
    <hkern u1="v" u2="&#x120;" k="-20" />
    <hkern u1="v" u2="&#x11e;" k="-20" />
    <hkern u1="v" u2="&#x11c;" k="-20" />
    <hkern u1="v" u2="&#x10c;" k="-20" />
    <hkern u1="v" u2="&#x10a;" k="-20" />
    <hkern u1="v" u2="&#x108;" k="-20" />
    <hkern u1="v" u2="&#x106;" k="-20" />
    <hkern u1="v" u2="&#xd8;" k="-20" />
    <hkern u1="v" u2="&#xd6;" k="-20" />
    <hkern u1="v" u2="&#xd5;" k="-20" />
    <hkern u1="v" u2="&#xd4;" k="-20" />
    <hkern u1="v" u2="&#xd3;" k="-20" />
    <hkern u1="v" u2="&#xd2;" k="-20" />
    <hkern u1="v" u2="&#xc7;" k="-20" />
    <hkern u1="v" u2="&#xa9;" k="-20" />
    <hkern u1="v" u2="_" k="35" />
    <hkern u1="v" u2="T" k="40" />
    <hkern u1="v" u2="Q" k="-20" />
    <hkern u1="v" u2="O" k="-20" />
    <hkern u1="v" u2="G" k="-20" />
    <hkern u1="v" u2="C" k="-20" />
    <hkern u1="v" u2="&#x2f;" k="35" />
    <hkern u1="v" u2="&#x2e;" k="65" />
    <hkern u1="v" u2="&#x2c;" k="65" />
    <hkern u1="w" u2="&#x2026;" k="65" />
    <hkern u1="w" u2="&#x201c;" k="-40" />
    <hkern u1="w" u2="&#x2014;" k="10" />
    <hkern u1="w" u2="&#x2013;" k="10" />
    <hkern u1="w" u2="&#x164;" k="55" />
    <hkern u1="w" u2="&#x162;" k="55" />
    <hkern u1="w" u2="_" k="35" />
    <hkern u1="w" u2="&#x2f;" k="20" />
    <hkern u1="w" u2="&#x2e;" k="65" />
    <hkern u1="x" u2="&#x2026;" k="-20" />
    <hkern u1="x" u2="&#x201c;" k="-40" />
    <hkern u1="x" u2="&#x2018;" k="-40" />
    <hkern u1="x" u2="&#x2014;" k="20" />
    <hkern u1="x" u2="&#x2013;" k="20" />
    <hkern u1="x" u2="&#x1ff;" k="10" />
    <hkern u1="x" u2="&#x164;" k="65" />
    <hkern u1="x" u2="&#x162;" k="65" />
    <hkern u1="x" u2="&#x153;" k="10" />
    <hkern u1="x" u2="&#x151;" k="10" />
    <hkern u1="x" u2="&#x14f;" k="10" />
    <hkern u1="x" u2="&#x14d;" k="10" />
    <hkern u1="x" u2="&#x123;" k="10" />
    <hkern u1="x" u2="&#x121;" k="10" />
    <hkern u1="x" u2="&#x11f;" k="10" />
    <hkern u1="x" u2="&#x11d;" k="10" />
    <hkern u1="x" u2="&#x11b;" k="10" />
    <hkern u1="x" u2="&#x119;" k="10" />
    <hkern u1="x" u2="&#x117;" k="10" />
    <hkern u1="x" u2="&#x115;" k="10" />
    <hkern u1="x" u2="&#x113;" k="10" />
    <hkern u1="x" u2="&#x111;" k="10" />
    <hkern u1="x" u2="&#x10f;" k="10" />
    <hkern u1="x" u2="&#x10d;" k="10" />
    <hkern u1="x" u2="&#x10b;" k="10" />
    <hkern u1="x" u2="&#x109;" k="10" />
    <hkern u1="x" u2="&#x107;" k="10" />
    <hkern u1="x" u2="&#xf8;" k="10" />
    <hkern u1="x" u2="&#xf6;" k="10" />
    <hkern u1="x" u2="&#xf5;" k="10" />
    <hkern u1="x" u2="&#xf4;" k="10" />
    <hkern u1="x" u2="&#xf3;" k="10" />
    <hkern u1="x" u2="&#xf2;" k="10" />
    <hkern u1="x" u2="&#xf0;" k="10" />
    <hkern u1="x" u2="&#xeb;" k="10" />
    <hkern u1="x" u2="&#xea;" k="10" />
    <hkern u1="x" u2="&#xe9;" k="10" />
    <hkern u1="x" u2="&#xe8;" k="10" />
    <hkern u1="x" u2="&#xe7;" k="10" />
    <hkern u1="x" u2="&#xe6;" k="-10" />
    <hkern u1="x" u2="q" k="10" />
    <hkern u1="x" u2="o" k="10" />
    <hkern u1="x" u2="g" k="10" />
    <hkern u1="x" u2="e" k="10" />
    <hkern u1="x" u2="d" k="10" />
    <hkern u1="x" u2="c" k="10" />
    <hkern u1="x" u2="_" k="-40" />
    <hkern u1="x" u2="T" k="65" />
    <hkern u1="x" u2="&#x2e;" k="-20" />
    <hkern u1="x" u2="&#x2d;" k="20" />
    <hkern u1="x" u2="&#x2c;" k="-20" />
    <hkern u1="y" u2="&#x1e84;" k="10" />
    <hkern u1="y" u2="&#x1e82;" k="10" />
    <hkern u1="y" u2="&#x1e80;" k="10" />
    <hkern u1="y" u2="&#x174;" k="10" />
    <hkern u1="y" u2="&#x164;" k="70" />
    <hkern u1="y" u2="&#x162;" k="70" />
    <hkern u1="y" u2="W" k="10" />
    <hkern u1="y" u2="V" k="20" />
    <hkern u1="y" u2="T" k="70" />
    <hkern u1="z" u2="&#x2014;" k="35" />
    <hkern u1="z" u2="&#x2013;" k="35" />
    <hkern u1="z" u2="&#x1ff;" k="10" />
    <hkern u1="z" u2="&#x164;" k="50" />
    <hkern u1="z" u2="&#x162;" k="50" />
    <hkern u1="z" u2="&#x153;" k="10" />
    <hkern u1="z" u2="&#x151;" k="10" />
    <hkern u1="z" u2="&#x14f;" k="10" />
    <hkern u1="z" u2="&#x14d;" k="10" />
    <hkern u1="z" u2="&#x123;" k="10" />
    <hkern u1="z" u2="&#x121;" k="10" />
    <hkern u1="z" u2="&#x11f;" k="10" />
    <hkern u1="z" u2="&#x11d;" k="10" />
    <hkern u1="z" u2="&#x11b;" k="10" />
    <hkern u1="z" u2="&#x119;" k="10" />
    <hkern u1="z" u2="&#x117;" k="10" />
    <hkern u1="z" u2="&#x115;" k="10" />
    <hkern u1="z" u2="&#x113;" k="10" />
    <hkern u1="z" u2="&#x111;" k="10" />
    <hkern u1="z" u2="&#x10f;" k="10" />
    <hkern u1="z" u2="&#x10d;" k="10" />
    <hkern u1="z" u2="&#x10b;" k="10" />
    <hkern u1="z" u2="&#x109;" k="10" />
    <hkern u1="z" u2="&#x107;" k="10" />
    <hkern u1="z" u2="&#xf8;" k="10" />
    <hkern u1="z" u2="&#xf6;" k="10" />
    <hkern u1="z" u2="&#xf5;" k="10" />
    <hkern u1="z" u2="&#xf4;" k="10" />
    <hkern u1="z" u2="&#xf3;" k="10" />
    <hkern u1="z" u2="&#xf2;" k="10" />
    <hkern u1="z" u2="&#xf0;" k="10" />
    <hkern u1="z" u2="&#xeb;" k="10" />
    <hkern u1="z" u2="&#xea;" k="10" />
    <hkern u1="z" u2="&#xe9;" k="10" />
    <hkern u1="z" u2="&#xe8;" k="10" />
    <hkern u1="z" u2="&#xe7;" k="10" />
    <hkern u1="z" u2="q" k="10" />
    <hkern u1="z" u2="o" k="10" />
    <hkern u1="z" u2="g" k="10" />
    <hkern u1="z" u2="e" k="10" />
    <hkern u1="z" u2="d" k="10" />
    <hkern u1="&#x7b;" u2="&#x134;" k="-50" />
    <hkern u1="&#x7b;" u2="j" k="-40" />
    <hkern u1="&#x7b;" u2="J" k="-50" />
    <hkern u1="&#xa9;" u2="&#x2026;" k="25" />
    <hkern u1="&#xa9;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x164;" k="15" />
    <hkern u1="&#xa9;" u2="&#x162;" k="15" />
    <hkern u1="&#xa9;" u2="&#x152;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x150;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x122;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x120;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x108;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x106;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xa9;" u2="X" k="10" />
    <hkern u1="&#xa9;" u2="T" k="15" />
    <hkern u1="&#xa9;" u2="Q" k="-5" />
    <hkern u1="&#xa9;" u2="O" k="-5" />
    <hkern u1="&#xa9;" u2="G" k="-5" />
    <hkern u1="&#xa9;" u2="C" k="-5" />
    <hkern u1="&#xa9;" u2="&#x2e;" k="25" />
    <hkern u1="&#xa9;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x2c;" k="25" />
    <hkern u1="&#xaa;" u2="&#x2026;" k="130" />
    <hkern u1="&#xaa;" u2="&#x2e;" k="130" />
    <hkern u1="&#xaa;" u2="&#x2c;" k="130" />
    <hkern u1="&#xae;" u2="&#x2026;" k="120" />
    <hkern u1="&#xae;" u2="&#x2e;" k="120" />
    <hkern u1="&#xae;" u2="&#x2c;" k="120" />
    <hkern u1="&#xb0;" u2="&#x2026;" k="185" />
    <hkern u1="&#xb0;" u2="&#x2e;" k="185" />
    <hkern u1="&#xb0;" u2="&#x2c;" k="185" />
    <hkern u1="&#xba;" u2="&#x2026;" k="130" />
    <hkern u1="&#xba;" u2="&#x2e;" k="130" />
    <hkern u1="&#xba;" u2="&#x2c;" k="130" />
    <hkern u1="&#xbb;" u2="&#x2026;" k="50" />
    <hkern u1="&#xbb;" u2="&#x2e;" k="50" />
    <hkern u1="&#xbb;" u2="&#x2c;" k="50" />
    <hkern u1="&#xbf;" u2="&#x2026;" k="50" />
    <hkern u1="&#xbf;" u2="&#x134;" k="-40" />
    <hkern u1="&#xbf;" u2="j" k="-40" />
    <hkern u1="&#xbf;" u2="J" k="-40" />
    <hkern u1="&#xbf;" u2="&#x2e;" k="50" />
    <hkern u1="&#xbf;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc0;" u2="&#x164;" k="35" />
    <hkern u1="&#xc0;" u2="&#x162;" k="35" />
    <hkern u1="&#xc0;" u2="T" k="35" />
    <hkern u1="&#xc1;" u2="&#x164;" k="35" />
    <hkern u1="&#xc1;" u2="&#x162;" k="35" />
    <hkern u1="&#xc1;" u2="T" k="35" />
    <hkern u1="&#xc2;" u2="&#x164;" k="35" />
    <hkern u1="&#xc2;" u2="&#x162;" k="35" />
    <hkern u1="&#xc2;" u2="T" k="35" />
    <hkern u1="&#xc3;" u2="&#x164;" k="35" />
    <hkern u1="&#xc3;" u2="&#x162;" k="35" />
    <hkern u1="&#xc3;" u2="T" k="35" />
    <hkern u1="&#xc4;" u2="&#x164;" k="35" />
    <hkern u1="&#xc4;" u2="&#x162;" k="35" />
    <hkern u1="&#xc4;" u2="T" k="35" />
    <hkern u1="&#xc5;" u2="&#x164;" k="35" />
    <hkern u1="&#xc5;" u2="&#x162;" k="35" />
    <hkern u1="&#xc5;" u2="T" k="35" />
    <hkern u1="&#xc6;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc6;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc6;" u2="&#x152;" k="7" />
    <hkern u1="&#xc6;" u2="&#x150;" k="7" />
    <hkern u1="&#xc6;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc6;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x122;" k="7" />
    <hkern u1="&#xc6;" u2="&#x120;" k="7" />
    <hkern u1="&#xc6;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc6;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc6;" u2="&#x108;" k="7" />
    <hkern u1="&#xc6;" u2="&#x106;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc6;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc6;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc6;" u2="Q" k="7" />
    <hkern u1="&#xc6;" u2="O" k="7" />
    <hkern u1="&#xc6;" u2="G" k="7" />
    <hkern u1="&#xc6;" u2="C" k="7" />
    <hkern u1="&#xc6;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc6;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xc7;" u2="&#x2026;" k="-30" />
    <hkern u1="&#xc7;" u2="&#x2014;" k="85" />
    <hkern u1="&#xc7;" u2="&#x2013;" k="85" />
    <hkern u1="&#xc7;" u2="&#x1e85;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1e83;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1e81;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1ff;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1fe;" k="30" />
    <hkern u1="&#xc7;" u2="&#x175;" k="10" />
    <hkern u1="&#xc7;" u2="&#x153;" k="10" />
    <hkern u1="&#xc7;" u2="&#x152;" k="30" />
    <hkern u1="&#xc7;" u2="&#x151;" k="10" />
    <hkern u1="&#xc7;" u2="&#x150;" k="30" />
    <hkern u1="&#xc7;" u2="&#x14f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x14e;" k="30" />
    <hkern u1="&#xc7;" u2="&#x14d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x14c;" k="30" />
    <hkern u1="&#xc7;" u2="&#x123;" k="10" />
    <hkern u1="&#xc7;" u2="&#x122;" k="30" />
    <hkern u1="&#xc7;" u2="&#x121;" k="10" />
    <hkern u1="&#xc7;" u2="&#x120;" k="30" />
    <hkern u1="&#xc7;" u2="&#x11f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x11e;" k="30" />
    <hkern u1="&#xc7;" u2="&#x11d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x11c;" k="30" />
    <hkern u1="&#xc7;" u2="&#x11b;" k="10" />
    <hkern u1="&#xc7;" u2="&#x119;" k="10" />
    <hkern u1="&#xc7;" u2="&#x117;" k="10" />
    <hkern u1="&#xc7;" u2="&#x115;" k="10" />
    <hkern u1="&#xc7;" u2="&#x113;" k="10" />
    <hkern u1="&#xc7;" u2="&#x111;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10c;" k="30" />
    <hkern u1="&#xc7;" u2="&#x10b;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10a;" k="30" />
    <hkern u1="&#xc7;" u2="&#x109;" k="10" />
    <hkern u1="&#xc7;" u2="&#x108;" k="30" />
    <hkern u1="&#xc7;" u2="&#x107;" k="10" />
    <hkern u1="&#xc7;" u2="&#x106;" k="30" />
    <hkern u1="&#xc7;" u2="&#xf8;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc7;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc7;" u2="&#xea;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc7;" u2="&#xd8;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd6;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd5;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd4;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd3;" k="30" />
    <hkern u1="&#xc7;" u2="&#xd2;" k="30" />
    <hkern u1="&#xc7;" u2="&#xc7;" k="30" />
    <hkern u1="&#xc7;" u2="&#xa9;" k="30" />
    <hkern u1="&#xc7;" u2="x" k="-20" />
    <hkern u1="&#xc7;" u2="w" k="10" />
    <hkern u1="&#xc7;" u2="v" k="20" />
    <hkern u1="&#xc7;" u2="q" k="10" />
    <hkern u1="&#xc7;" u2="o" k="10" />
    <hkern u1="&#xc7;" u2="g" k="10" />
    <hkern u1="&#xc7;" u2="e" k="10" />
    <hkern u1="&#xc7;" u2="d" k="10" />
    <hkern u1="&#xc7;" u2="c" k="10" />
    <hkern u1="&#xc7;" u2="_" k="-20" />
    <hkern u1="&#xc7;" u2="V" k="-15" />
    <hkern u1="&#xc7;" u2="Q" k="30" />
    <hkern u1="&#xc7;" u2="O" k="30" />
    <hkern u1="&#xc7;" u2="G" k="30" />
    <hkern u1="&#xc7;" u2="C" k="30" />
    <hkern u1="&#xc7;" u2="A" k="7" />
    <hkern u1="&#xc7;" u2="&#x2e;" k="-30" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="85" />
    <hkern u1="&#xc7;" u2="&#x2c;" k="-30" />
    <hkern u1="&#xc8;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc8;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc8;" u2="&#x152;" k="7" />
    <hkern u1="&#xc8;" u2="&#x150;" k="7" />
    <hkern u1="&#xc8;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc8;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x122;" k="7" />
    <hkern u1="&#xc8;" u2="&#x120;" k="7" />
    <hkern u1="&#xc8;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc8;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc8;" u2="&#x108;" k="7" />
    <hkern u1="&#xc8;" u2="&#x106;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc8;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc8;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc8;" u2="Q" k="7" />
    <hkern u1="&#xc8;" u2="O" k="7" />
    <hkern u1="&#xc8;" u2="G" k="7" />
    <hkern u1="&#xc8;" u2="C" k="7" />
    <hkern u1="&#xc8;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc8;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc9;" u2="&#x152;" k="7" />
    <hkern u1="&#xc9;" u2="&#x150;" k="7" />
    <hkern u1="&#xc9;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc9;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x122;" k="7" />
    <hkern u1="&#xc9;" u2="&#x120;" k="7" />
    <hkern u1="&#xc9;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc9;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc9;" u2="&#x108;" k="7" />
    <hkern u1="&#xc9;" u2="&#x106;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc9;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc9;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc9;" u2="Q" k="7" />
    <hkern u1="&#xc9;" u2="O" k="7" />
    <hkern u1="&#xc9;" u2="G" k="7" />
    <hkern u1="&#xc9;" u2="C" k="7" />
    <hkern u1="&#xc9;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xca;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xca;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xca;" u2="&#x152;" k="7" />
    <hkern u1="&#xca;" u2="&#x150;" k="7" />
    <hkern u1="&#xca;" u2="&#x14e;" k="7" />
    <hkern u1="&#xca;" u2="&#x14c;" k="7" />
    <hkern u1="&#xca;" u2="&#x122;" k="7" />
    <hkern u1="&#xca;" u2="&#x120;" k="7" />
    <hkern u1="&#xca;" u2="&#x11e;" k="7" />
    <hkern u1="&#xca;" u2="&#x11c;" k="7" />
    <hkern u1="&#xca;" u2="&#x10c;" k="7" />
    <hkern u1="&#xca;" u2="&#x10a;" k="7" />
    <hkern u1="&#xca;" u2="&#x108;" k="7" />
    <hkern u1="&#xca;" u2="&#x106;" k="7" />
    <hkern u1="&#xca;" u2="&#xd8;" k="7" />
    <hkern u1="&#xca;" u2="&#xd6;" k="7" />
    <hkern u1="&#xca;" u2="&#xd5;" k="7" />
    <hkern u1="&#xca;" u2="&#xd4;" k="7" />
    <hkern u1="&#xca;" u2="&#xd3;" k="7" />
    <hkern u1="&#xca;" u2="&#xd2;" k="7" />
    <hkern u1="&#xca;" u2="&#xc7;" k="7" />
    <hkern u1="&#xca;" u2="&#xa9;" k="7" />
    <hkern u1="&#xca;" u2="Q" k="7" />
    <hkern u1="&#xca;" u2="O" k="7" />
    <hkern u1="&#xca;" u2="G" k="7" />
    <hkern u1="&#xca;" u2="C" k="7" />
    <hkern u1="&#xca;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xca;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xcb;" u2="&#x152;" k="7" />
    <hkern u1="&#xcb;" u2="&#x150;" k="7" />
    <hkern u1="&#xcb;" u2="&#x14e;" k="7" />
    <hkern u1="&#xcb;" u2="&#x14c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x122;" k="7" />
    <hkern u1="&#xcb;" u2="&#x120;" k="7" />
    <hkern u1="&#xcb;" u2="&#x11e;" k="7" />
    <hkern u1="&#xcb;" u2="&#x11c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x10c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x10a;" k="7" />
    <hkern u1="&#xcb;" u2="&#x108;" k="7" />
    <hkern u1="&#xcb;" u2="&#x106;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd8;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd6;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd5;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd4;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd3;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd2;" k="7" />
    <hkern u1="&#xcb;" u2="&#xc7;" k="7" />
    <hkern u1="&#xcb;" u2="&#xa9;" k="7" />
    <hkern u1="&#xcb;" u2="Q" k="7" />
    <hkern u1="&#xcb;" u2="O" k="7" />
    <hkern u1="&#xcb;" u2="G" k="7" />
    <hkern u1="&#xcb;" u2="C" k="7" />
    <hkern u1="&#xcb;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xd0;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd0;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x164;" k="15" />
    <hkern u1="&#xd0;" u2="&#x162;" k="15" />
    <hkern u1="&#xd0;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd0;" u2="X" k="10" />
    <hkern u1="&#xd0;" u2="T" k="15" />
    <hkern u1="&#xd0;" u2="Q" k="-5" />
    <hkern u1="&#xd0;" u2="O" k="-5" />
    <hkern u1="&#xd0;" u2="G" k="-5" />
    <hkern u1="&#xd0;" u2="C" k="-5" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd0;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd2;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd2;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x164;" k="15" />
    <hkern u1="&#xd2;" u2="&#x162;" k="15" />
    <hkern u1="&#xd2;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd2;" u2="X" k="10" />
    <hkern u1="&#xd2;" u2="T" k="15" />
    <hkern u1="&#xd2;" u2="Q" k="-5" />
    <hkern u1="&#xd2;" u2="O" k="-5" />
    <hkern u1="&#xd2;" u2="G" k="-5" />
    <hkern u1="&#xd2;" u2="C" k="-5" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd2;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd3;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd3;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x164;" k="15" />
    <hkern u1="&#xd3;" u2="&#x162;" k="15" />
    <hkern u1="&#xd3;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd3;" u2="X" k="10" />
    <hkern u1="&#xd3;" u2="T" k="15" />
    <hkern u1="&#xd3;" u2="Q" k="-5" />
    <hkern u1="&#xd3;" u2="O" k="-5" />
    <hkern u1="&#xd3;" u2="G" k="-5" />
    <hkern u1="&#xd3;" u2="C" k="-5" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd3;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd4;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd4;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x164;" k="15" />
    <hkern u1="&#xd4;" u2="&#x162;" k="15" />
    <hkern u1="&#xd4;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd4;" u2="X" k="10" />
    <hkern u1="&#xd4;" u2="T" k="15" />
    <hkern u1="&#xd4;" u2="Q" k="-5" />
    <hkern u1="&#xd4;" u2="O" k="-5" />
    <hkern u1="&#xd4;" u2="G" k="-5" />
    <hkern u1="&#xd4;" u2="C" k="-5" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd4;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd5;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd5;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x164;" k="15" />
    <hkern u1="&#xd5;" u2="&#x162;" k="15" />
    <hkern u1="&#xd5;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd5;" u2="X" k="10" />
    <hkern u1="&#xd5;" u2="T" k="15" />
    <hkern u1="&#xd5;" u2="Q" k="-5" />
    <hkern u1="&#xd5;" u2="O" k="-5" />
    <hkern u1="&#xd5;" u2="G" k="-5" />
    <hkern u1="&#xd5;" u2="C" k="-5" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd5;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd6;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd6;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x164;" k="15" />
    <hkern u1="&#xd6;" u2="&#x162;" k="15" />
    <hkern u1="&#xd6;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd6;" u2="X" k="10" />
    <hkern u1="&#xd6;" u2="T" k="15" />
    <hkern u1="&#xd6;" u2="Q" k="-5" />
    <hkern u1="&#xd6;" u2="O" k="-5" />
    <hkern u1="&#xd6;" u2="G" k="-5" />
    <hkern u1="&#xd6;" u2="C" k="-5" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd6;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd8;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd8;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x164;" k="15" />
    <hkern u1="&#xd8;" u2="&#x162;" k="15" />
    <hkern u1="&#xd8;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd8;" u2="X" k="10" />
    <hkern u1="&#xd8;" u2="T" k="15" />
    <hkern u1="&#xd8;" u2="Q" k="-5" />
    <hkern u1="&#xd8;" u2="O" k="-5" />
    <hkern u1="&#xd8;" u2="G" k="-5" />
    <hkern u1="&#xd8;" u2="C" k="-5" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd8;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="25" />
    <hkern u1="&#xdd;" u2="V" k="-20" />
    <hkern u1="&#xe6;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe6;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe6;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe6;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x174;" k="10" />
    <hkern u1="&#xe6;" u2="&#x164;" k="70" />
    <hkern u1="&#xe6;" u2="&#x162;" k="70" />
    <hkern u1="&#xe6;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe6;" u2="s" k="-5" />
    <hkern u1="&#xe6;" u2="W" k="10" />
    <hkern u1="&#xe6;" u2="V" k="20" />
    <hkern u1="&#xe6;" u2="T" k="70" />
    <hkern u1="&#xe7;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xe7;" u2="&#x201c;" k="-40" />
    <hkern u1="&#xe7;" u2="&#x2018;" k="-40" />
    <hkern u1="&#xe7;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#xe7;" u2="&#x1ff;" k="25" />
    <hkern u1="&#xe7;" u2="&#x1fd;" k="10" />
    <hkern u1="&#xe7;" u2="&#x1fb;" k="10" />
    <hkern u1="&#xe7;" u2="&#x177;" k="10" />
    <hkern u1="&#xe7;" u2="&#x173;" k="10" />
    <hkern u1="&#xe7;" u2="&#x171;" k="10" />
    <hkern u1="&#xe7;" u2="&#x16f;" k="10" />
    <hkern u1="&#xe7;" u2="&#x16d;" k="10" />
    <hkern u1="&#xe7;" u2="&#x16b;" k="10" />
    <hkern u1="&#xe7;" u2="&#x169;" k="10" />
    <hkern u1="&#xe7;" u2="&#x164;" k="80" />
    <hkern u1="&#xe7;" u2="&#x162;" k="80" />
    <hkern u1="&#xe7;" u2="&#x153;" k="25" />
    <hkern u1="&#xe7;" u2="&#x151;" k="25" />
    <hkern u1="&#xe7;" u2="&#x14f;" k="25" />
    <hkern u1="&#xe7;" u2="&#x14d;" k="25" />
    <hkern u1="&#xe7;" u2="&#x123;" k="25" />
    <hkern u1="&#xe7;" u2="&#x121;" k="25" />
    <hkern u1="&#xe7;" u2="&#x11f;" k="25" />
    <hkern u1="&#xe7;" u2="&#x11d;" k="25" />
    <hkern u1="&#xe7;" u2="&#x11b;" k="25" />
    <hkern u1="&#xe7;" u2="&#x119;" k="25" />
    <hkern u1="&#xe7;" u2="&#x117;" k="25" />
    <hkern u1="&#xe7;" u2="&#x115;" k="25" />
    <hkern u1="&#xe7;" u2="&#x113;" k="25" />
    <hkern u1="&#xe7;" u2="&#x111;" k="25" />
    <hkern u1="&#xe7;" u2="&#x10f;" k="25" />
    <hkern u1="&#xe7;" u2="&#x10d;" k="25" />
    <hkern u1="&#xe7;" u2="&#x10b;" k="25" />
    <hkern u1="&#xe7;" u2="&#x109;" k="25" />
    <hkern u1="&#xe7;" u2="&#x107;" k="25" />
    <hkern u1="&#xe7;" u2="&#x105;" k="10" />
    <hkern u1="&#xe7;" u2="&#x103;" k="10" />
    <hkern u1="&#xe7;" u2="&#x101;" k="10" />
    <hkern u1="&#xe7;" u2="&#xff;" k="10" />
    <hkern u1="&#xe7;" u2="&#xfd;" k="10" />
    <hkern u1="&#xe7;" u2="&#xfc;" k="10" />
    <hkern u1="&#xe7;" u2="&#xfb;" k="10" />
    <hkern u1="&#xe7;" u2="&#xfa;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf9;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf8;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf6;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf5;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf4;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf3;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf2;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf0;" k="25" />
    <hkern u1="&#xe7;" u2="&#xeb;" k="25" />
    <hkern u1="&#xe7;" u2="&#xea;" k="25" />
    <hkern u1="&#xe7;" u2="&#xe9;" k="25" />
    <hkern u1="&#xe7;" u2="&#xe8;" k="25" />
    <hkern u1="&#xe7;" u2="&#xe7;" k="25" />
    <hkern u1="&#xe7;" u2="&#xe6;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe5;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe4;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe3;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe2;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe1;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe0;" k="10" />
    <hkern u1="&#xe7;" u2="y" k="10" />
    <hkern u1="&#xe7;" u2="x" k="-13" />
    <hkern u1="&#xe7;" u2="u" k="10" />
    <hkern u1="&#xe7;" u2="q" k="25" />
    <hkern u1="&#xe7;" u2="o" k="25" />
    <hkern u1="&#xe7;" u2="g" k="25" />
    <hkern u1="&#xe7;" u2="e" k="25" />
    <hkern u1="&#xe7;" u2="d" k="25" />
    <hkern u1="&#xe7;" u2="c" k="25" />
    <hkern u1="&#xe7;" u2="a" k="10" />
    <hkern u1="&#xe7;" u2="T" k="80" />
    <hkern u1="&#xe7;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xe7;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xe8;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe8;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe8;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe8;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x174;" k="10" />
    <hkern u1="&#xe8;" u2="&#x164;" k="70" />
    <hkern u1="&#xe8;" u2="&#x162;" k="70" />
    <hkern u1="&#xe8;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe8;" u2="s" k="-5" />
    <hkern u1="&#xe8;" u2="W" k="10" />
    <hkern u1="&#xe8;" u2="V" k="20" />
    <hkern u1="&#xe8;" u2="T" k="70" />
    <hkern u1="&#xe9;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe9;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe9;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe9;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x174;" k="10" />
    <hkern u1="&#xe9;" u2="&#x164;" k="70" />
    <hkern u1="&#xe9;" u2="&#x162;" k="70" />
    <hkern u1="&#xe9;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe9;" u2="s" k="-5" />
    <hkern u1="&#xe9;" u2="W" k="10" />
    <hkern u1="&#xe9;" u2="V" k="20" />
    <hkern u1="&#xe9;" u2="T" k="70" />
    <hkern u1="&#xea;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xea;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xea;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xea;" u2="&#x219;" k="-5" />
    <hkern u1="&#xea;" u2="&#x174;" k="10" />
    <hkern u1="&#xea;" u2="&#x164;" k="70" />
    <hkern u1="&#xea;" u2="&#x162;" k="70" />
    <hkern u1="&#xea;" u2="&#x161;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xea;" u2="s" k="-5" />
    <hkern u1="&#xea;" u2="W" k="10" />
    <hkern u1="&#xea;" u2="V" k="20" />
    <hkern u1="&#xea;" u2="T" k="70" />
    <hkern u1="&#xeb;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xeb;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xeb;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xeb;" u2="&#x219;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x174;" k="10" />
    <hkern u1="&#xeb;" u2="&#x164;" k="70" />
    <hkern u1="&#xeb;" u2="&#x162;" k="70" />
    <hkern u1="&#xeb;" u2="&#x161;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xeb;" u2="s" k="-5" />
    <hkern u1="&#xeb;" u2="W" k="10" />
    <hkern u1="&#xeb;" u2="V" k="20" />
    <hkern u1="&#xeb;" u2="T" k="70" />
    <hkern u1="&#xf1;" u2="&#x164;" k="70" />
    <hkern u1="&#xf1;" u2="&#x162;" k="70" />
    <hkern u1="&#xf1;" u2="T" k="70" />
    <hkern u1="&#xf2;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf2;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf2;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf2;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf2;" u2="&#x174;" k="10" />
    <hkern u1="&#xf2;" u2="&#x164;" k="75" />
    <hkern u1="&#xf2;" u2="&#x162;" k="75" />
    <hkern u1="&#xf2;" u2="z" k="20" />
    <hkern u1="&#xf2;" u2="x" k="10" />
    <hkern u1="&#xf2;" u2="W" k="10" />
    <hkern u1="&#xf2;" u2="V" k="20" />
    <hkern u1="&#xf2;" u2="T" k="75" />
    <hkern u1="&#xf2;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf2;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf3;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf3;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf3;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf3;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf3;" u2="&#x174;" k="10" />
    <hkern u1="&#xf3;" u2="&#x164;" k="75" />
    <hkern u1="&#xf3;" u2="&#x162;" k="75" />
    <hkern u1="&#xf3;" u2="z" k="20" />
    <hkern u1="&#xf3;" u2="x" k="10" />
    <hkern u1="&#xf3;" u2="W" k="10" />
    <hkern u1="&#xf3;" u2="V" k="20" />
    <hkern u1="&#xf3;" u2="T" k="75" />
    <hkern u1="&#xf3;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf3;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf4;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf4;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf4;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf4;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf4;" u2="&#x174;" k="10" />
    <hkern u1="&#xf4;" u2="&#x164;" k="75" />
    <hkern u1="&#xf4;" u2="&#x162;" k="75" />
    <hkern u1="&#xf4;" u2="z" k="20" />
    <hkern u1="&#xf4;" u2="x" k="10" />
    <hkern u1="&#xf4;" u2="W" k="10" />
    <hkern u1="&#xf4;" u2="V" k="20" />
    <hkern u1="&#xf4;" u2="T" k="75" />
    <hkern u1="&#xf4;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf4;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf5;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf5;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf5;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf5;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf5;" u2="&#x174;" k="10" />
    <hkern u1="&#xf5;" u2="&#x164;" k="75" />
    <hkern u1="&#xf5;" u2="&#x162;" k="75" />
    <hkern u1="&#xf5;" u2="z" k="20" />
    <hkern u1="&#xf5;" u2="x" k="10" />
    <hkern u1="&#xf5;" u2="W" k="10" />
    <hkern u1="&#xf5;" u2="V" k="20" />
    <hkern u1="&#xf5;" u2="T" k="75" />
    <hkern u1="&#xf5;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf5;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf6;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf6;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf6;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf6;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf6;" u2="&#x174;" k="10" />
    <hkern u1="&#xf6;" u2="&#x164;" k="75" />
    <hkern u1="&#xf6;" u2="&#x162;" k="75" />
    <hkern u1="&#xf6;" u2="z" k="20" />
    <hkern u1="&#xf6;" u2="x" k="10" />
    <hkern u1="&#xf6;" u2="W" k="10" />
    <hkern u1="&#xf6;" u2="V" k="20" />
    <hkern u1="&#xf6;" u2="T" k="75" />
    <hkern u1="&#xf6;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf6;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf8;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf8;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf8;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf8;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf8;" u2="&#x174;" k="10" />
    <hkern u1="&#xf8;" u2="&#x164;" k="75" />
    <hkern u1="&#xf8;" u2="&#x162;" k="75" />
    <hkern u1="&#xf8;" u2="z" k="20" />
    <hkern u1="&#xf8;" u2="x" k="10" />
    <hkern u1="&#xf8;" u2="W" k="10" />
    <hkern u1="&#xf8;" u2="V" k="20" />
    <hkern u1="&#xf8;" u2="T" k="75" />
    <hkern u1="&#xf8;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf8;" u2="&#x2c;" k="20" />
    <hkern u1="&#xfd;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xfd;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xfd;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xfd;" u2="&#x174;" k="10" />
    <hkern u1="&#xfd;" u2="&#x164;" k="70" />
    <hkern u1="&#xfd;" u2="&#x162;" k="70" />
    <hkern u1="&#xfd;" u2="W" k="10" />
    <hkern u1="&#xfd;" u2="V" k="20" />
    <hkern u1="&#xfd;" u2="T" k="70" />
    <hkern u1="&#xfe;" u2="&#x2026;" k="20" />
    <hkern u1="&#xfe;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xfe;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xfe;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xfe;" u2="&#x174;" k="10" />
    <hkern u1="&#xfe;" u2="&#x164;" k="75" />
    <hkern u1="&#xfe;" u2="&#x162;" k="75" />
    <hkern u1="&#xfe;" u2="z" k="20" />
    <hkern u1="&#xfe;" u2="x" k="10" />
    <hkern u1="&#xfe;" u2="W" k="10" />
    <hkern u1="&#xfe;" u2="V" k="20" />
    <hkern u1="&#xfe;" u2="T" k="75" />
    <hkern u1="&#xfe;" u2="&#x2e;" k="20" />
    <hkern u1="&#xfe;" u2="&#x2c;" k="20" />
    <hkern u1="&#xff;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xff;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xff;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xff;" u2="&#x174;" k="10" />
    <hkern u1="&#xff;" u2="&#x164;" k="70" />
    <hkern u1="&#xff;" u2="&#x162;" k="70" />
    <hkern u1="&#xff;" u2="W" k="10" />
    <hkern u1="&#xff;" u2="V" k="20" />
    <hkern u1="&#xff;" u2="T" k="70" />
    <hkern u1="&#x100;" u2="&#x164;" k="35" />
    <hkern u1="&#x100;" u2="&#x162;" k="35" />
    <hkern u1="&#x100;" u2="T" k="35" />
    <hkern u1="&#x102;" u2="&#x164;" k="35" />
    <hkern u1="&#x102;" u2="&#x162;" k="35" />
    <hkern u1="&#x102;" u2="T" k="35" />
    <hkern u1="&#x104;" u2="&#x164;" k="35" />
    <hkern u1="&#x104;" u2="&#x162;" k="35" />
    <hkern u1="&#x104;" u2="T" k="35" />
    <hkern u1="&#x106;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x106;" u2="&#x2014;" k="85" />
    <hkern u1="&#x106;" u2="&#x2013;" k="85" />
    <hkern u1="&#x106;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x106;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x106;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x106;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x106;" u2="&#x1fe;" k="30" />
    <hkern u1="&#x106;" u2="&#x175;" k="10" />
    <hkern u1="&#x106;" u2="&#x153;" k="10" />
    <hkern u1="&#x106;" u2="&#x152;" k="30" />
    <hkern u1="&#x106;" u2="&#x151;" k="10" />
    <hkern u1="&#x106;" u2="&#x150;" k="30" />
    <hkern u1="&#x106;" u2="&#x14f;" k="10" />
    <hkern u1="&#x106;" u2="&#x14e;" k="30" />
    <hkern u1="&#x106;" u2="&#x14d;" k="10" />
    <hkern u1="&#x106;" u2="&#x14c;" k="30" />
    <hkern u1="&#x106;" u2="&#x123;" k="10" />
    <hkern u1="&#x106;" u2="&#x122;" k="30" />
    <hkern u1="&#x106;" u2="&#x121;" k="10" />
    <hkern u1="&#x106;" u2="&#x120;" k="30" />
    <hkern u1="&#x106;" u2="&#x11f;" k="10" />
    <hkern u1="&#x106;" u2="&#x11e;" k="30" />
    <hkern u1="&#x106;" u2="&#x11d;" k="10" />
    <hkern u1="&#x106;" u2="&#x11c;" k="30" />
    <hkern u1="&#x106;" u2="&#x11b;" k="10" />
    <hkern u1="&#x106;" u2="&#x119;" k="10" />
    <hkern u1="&#x106;" u2="&#x117;" k="10" />
    <hkern u1="&#x106;" u2="&#x115;" k="10" />
    <hkern u1="&#x106;" u2="&#x113;" k="10" />
    <hkern u1="&#x106;" u2="&#x111;" k="10" />
    <hkern u1="&#x106;" u2="&#x10f;" k="10" />
    <hkern u1="&#x106;" u2="&#x10d;" k="10" />
    <hkern u1="&#x106;" u2="&#x10c;" k="30" />
    <hkern u1="&#x106;" u2="&#x10b;" k="10" />
    <hkern u1="&#x106;" u2="&#x10a;" k="30" />
    <hkern u1="&#x106;" u2="&#x109;" k="10" />
    <hkern u1="&#x106;" u2="&#x108;" k="30" />
    <hkern u1="&#x106;" u2="&#x107;" k="10" />
    <hkern u1="&#x106;" u2="&#x106;" k="30" />
    <hkern u1="&#x106;" u2="&#xf8;" k="10" />
    <hkern u1="&#x106;" u2="&#xf6;" k="10" />
    <hkern u1="&#x106;" u2="&#xf5;" k="10" />
    <hkern u1="&#x106;" u2="&#xf4;" k="10" />
    <hkern u1="&#x106;" u2="&#xf3;" k="10" />
    <hkern u1="&#x106;" u2="&#xf2;" k="10" />
    <hkern u1="&#x106;" u2="&#xf0;" k="10" />
    <hkern u1="&#x106;" u2="&#xeb;" k="10" />
    <hkern u1="&#x106;" u2="&#xea;" k="10" />
    <hkern u1="&#x106;" u2="&#xe9;" k="10" />
    <hkern u1="&#x106;" u2="&#xe8;" k="10" />
    <hkern u1="&#x106;" u2="&#xe7;" k="10" />
    <hkern u1="&#x106;" u2="&#xd8;" k="30" />
    <hkern u1="&#x106;" u2="&#xd6;" k="30" />
    <hkern u1="&#x106;" u2="&#xd5;" k="30" />
    <hkern u1="&#x106;" u2="&#xd4;" k="30" />
    <hkern u1="&#x106;" u2="&#xd3;" k="30" />
    <hkern u1="&#x106;" u2="&#xd2;" k="30" />
    <hkern u1="&#x106;" u2="&#xc7;" k="30" />
    <hkern u1="&#x106;" u2="&#xa9;" k="30" />
    <hkern u1="&#x106;" u2="x" k="-20" />
    <hkern u1="&#x106;" u2="w" k="10" />
    <hkern u1="&#x106;" u2="v" k="20" />
    <hkern u1="&#x106;" u2="q" k="10" />
    <hkern u1="&#x106;" u2="o" k="10" />
    <hkern u1="&#x106;" u2="g" k="10" />
    <hkern u1="&#x106;" u2="e" k="10" />
    <hkern u1="&#x106;" u2="d" k="10" />
    <hkern u1="&#x106;" u2="c" k="10" />
    <hkern u1="&#x106;" u2="_" k="-20" />
    <hkern u1="&#x106;" u2="V" k="-15" />
    <hkern u1="&#x106;" u2="Q" k="30" />
    <hkern u1="&#x106;" u2="O" k="30" />
    <hkern u1="&#x106;" u2="G" k="30" />
    <hkern u1="&#x106;" u2="C" k="30" />
    <hkern u1="&#x106;" u2="A" k="7" />
    <hkern u1="&#x106;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x106;" u2="&#x2d;" k="85" />
    <hkern u1="&#x106;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x107;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x107;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x107;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x107;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#x107;" u2="&#x1ff;" k="25" />
    <hkern u1="&#x107;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x107;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x107;" u2="&#x177;" k="10" />
    <hkern u1="&#x107;" u2="&#x173;" k="10" />
    <hkern u1="&#x107;" u2="&#x171;" k="10" />
    <hkern u1="&#x107;" u2="&#x16f;" k="10" />
    <hkern u1="&#x107;" u2="&#x16d;" k="10" />
    <hkern u1="&#x107;" u2="&#x16b;" k="10" />
    <hkern u1="&#x107;" u2="&#x169;" k="10" />
    <hkern u1="&#x107;" u2="&#x164;" k="80" />
    <hkern u1="&#x107;" u2="&#x162;" k="80" />
    <hkern u1="&#x107;" u2="&#x153;" k="25" />
    <hkern u1="&#x107;" u2="&#x151;" k="25" />
    <hkern u1="&#x107;" u2="&#x14f;" k="25" />
    <hkern u1="&#x107;" u2="&#x14d;" k="25" />
    <hkern u1="&#x107;" u2="&#x123;" k="25" />
    <hkern u1="&#x107;" u2="&#x121;" k="25" />
    <hkern u1="&#x107;" u2="&#x11f;" k="25" />
    <hkern u1="&#x107;" u2="&#x11d;" k="25" />
    <hkern u1="&#x107;" u2="&#x11b;" k="25" />
    <hkern u1="&#x107;" u2="&#x119;" k="25" />
    <hkern u1="&#x107;" u2="&#x117;" k="25" />
    <hkern u1="&#x107;" u2="&#x115;" k="25" />
    <hkern u1="&#x107;" u2="&#x113;" k="25" />
    <hkern u1="&#x107;" u2="&#x111;" k="25" />
    <hkern u1="&#x107;" u2="&#x10f;" k="25" />
    <hkern u1="&#x107;" u2="&#x10d;" k="25" />
    <hkern u1="&#x107;" u2="&#x10b;" k="25" />
    <hkern u1="&#x107;" u2="&#x109;" k="25" />
    <hkern u1="&#x107;" u2="&#x107;" k="25" />
    <hkern u1="&#x107;" u2="&#x105;" k="10" />
    <hkern u1="&#x107;" u2="&#x103;" k="10" />
    <hkern u1="&#x107;" u2="&#x101;" k="10" />
    <hkern u1="&#x107;" u2="&#xff;" k="10" />
    <hkern u1="&#x107;" u2="&#xfd;" k="10" />
    <hkern u1="&#x107;" u2="&#xfc;" k="10" />
    <hkern u1="&#x107;" u2="&#xfb;" k="10" />
    <hkern u1="&#x107;" u2="&#xfa;" k="10" />
    <hkern u1="&#x107;" u2="&#xf9;" k="10" />
    <hkern u1="&#x107;" u2="&#xf8;" k="25" />
    <hkern u1="&#x107;" u2="&#xf6;" k="25" />
    <hkern u1="&#x107;" u2="&#xf5;" k="25" />
    <hkern u1="&#x107;" u2="&#xf4;" k="25" />
    <hkern u1="&#x107;" u2="&#xf3;" k="25" />
    <hkern u1="&#x107;" u2="&#xf2;" k="25" />
    <hkern u1="&#x107;" u2="&#xf0;" k="25" />
    <hkern u1="&#x107;" u2="&#xeb;" k="25" />
    <hkern u1="&#x107;" u2="&#xea;" k="25" />
    <hkern u1="&#x107;" u2="&#xe9;" k="25" />
    <hkern u1="&#x107;" u2="&#xe8;" k="25" />
    <hkern u1="&#x107;" u2="&#xe7;" k="25" />
    <hkern u1="&#x107;" u2="&#xe6;" k="10" />
    <hkern u1="&#x107;" u2="&#xe5;" k="10" />
    <hkern u1="&#x107;" u2="&#xe4;" k="10" />
    <hkern u1="&#x107;" u2="&#xe3;" k="10" />
    <hkern u1="&#x107;" u2="&#xe2;" k="10" />
    <hkern u1="&#x107;" u2="&#xe1;" k="10" />
    <hkern u1="&#x107;" u2="&#xe0;" k="10" />
    <hkern u1="&#x107;" u2="y" k="10" />
    <hkern u1="&#x107;" u2="x" k="-13" />
    <hkern u1="&#x107;" u2="u" k="10" />
    <hkern u1="&#x107;" u2="q" k="25" />
    <hkern u1="&#x107;" u2="o" k="25" />
    <hkern u1="&#x107;" u2="g" k="25" />
    <hkern u1="&#x107;" u2="e" k="25" />
    <hkern u1="&#x107;" u2="d" k="25" />
    <hkern u1="&#x107;" u2="c" k="25" />
    <hkern u1="&#x107;" u2="a" k="10" />
    <hkern u1="&#x107;" u2="T" k="80" />
    <hkern u1="&#x107;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x107;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x108;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x108;" u2="&#x2014;" k="85" />
    <hkern u1="&#x108;" u2="&#x2013;" k="85" />
    <hkern u1="&#x108;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x108;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x108;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x108;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x108;" u2="&#x1fe;" k="30" />
    <hkern u1="&#x108;" u2="&#x175;" k="10" />
    <hkern u1="&#x108;" u2="&#x153;" k="10" />
    <hkern u1="&#x108;" u2="&#x152;" k="30" />
    <hkern u1="&#x108;" u2="&#x151;" k="10" />
    <hkern u1="&#x108;" u2="&#x150;" k="30" />
    <hkern u1="&#x108;" u2="&#x14f;" k="10" />
    <hkern u1="&#x108;" u2="&#x14e;" k="30" />
    <hkern u1="&#x108;" u2="&#x14d;" k="10" />
    <hkern u1="&#x108;" u2="&#x14c;" k="30" />
    <hkern u1="&#x108;" u2="&#x123;" k="10" />
    <hkern u1="&#x108;" u2="&#x122;" k="30" />
    <hkern u1="&#x108;" u2="&#x121;" k="10" />
    <hkern u1="&#x108;" u2="&#x120;" k="30" />
    <hkern u1="&#x108;" u2="&#x11f;" k="10" />
    <hkern u1="&#x108;" u2="&#x11e;" k="30" />
    <hkern u1="&#x108;" u2="&#x11d;" k="10" />
    <hkern u1="&#x108;" u2="&#x11c;" k="30" />
    <hkern u1="&#x108;" u2="&#x11b;" k="10" />
    <hkern u1="&#x108;" u2="&#x119;" k="10" />
    <hkern u1="&#x108;" u2="&#x117;" k="10" />
    <hkern u1="&#x108;" u2="&#x115;" k="10" />
    <hkern u1="&#x108;" u2="&#x113;" k="10" />
    <hkern u1="&#x108;" u2="&#x111;" k="10" />
    <hkern u1="&#x108;" u2="&#x10f;" k="10" />
    <hkern u1="&#x108;" u2="&#x10d;" k="10" />
    <hkern u1="&#x108;" u2="&#x10c;" k="30" />
    <hkern u1="&#x108;" u2="&#x10b;" k="10" />
    <hkern u1="&#x108;" u2="&#x10a;" k="30" />
    <hkern u1="&#x108;" u2="&#x109;" k="10" />
    <hkern u1="&#x108;" u2="&#x108;" k="30" />
    <hkern u1="&#x108;" u2="&#x107;" k="10" />
    <hkern u1="&#x108;" u2="&#x106;" k="30" />
    <hkern u1="&#x108;" u2="&#xf8;" k="10" />
    <hkern u1="&#x108;" u2="&#xf6;" k="10" />
    <hkern u1="&#x108;" u2="&#xf5;" k="10" />
    <hkern u1="&#x108;" u2="&#xf4;" k="10" />
    <hkern u1="&#x108;" u2="&#xf3;" k="10" />
    <hkern u1="&#x108;" u2="&#xf2;" k="10" />
    <hkern u1="&#x108;" u2="&#xf0;" k="10" />
    <hkern u1="&#x108;" u2="&#xeb;" k="10" />
    <hkern u1="&#x108;" u2="&#xea;" k="10" />
    <hkern u1="&#x108;" u2="&#xe9;" k="10" />
    <hkern u1="&#x108;" u2="&#xe8;" k="10" />
    <hkern u1="&#x108;" u2="&#xe7;" k="10" />
    <hkern u1="&#x108;" u2="&#xd8;" k="30" />
    <hkern u1="&#x108;" u2="&#xd6;" k="30" />
    <hkern u1="&#x108;" u2="&#xd5;" k="30" />
    <hkern u1="&#x108;" u2="&#xd4;" k="30" />
    <hkern u1="&#x108;" u2="&#xd3;" k="30" />
    <hkern u1="&#x108;" u2="&#xd2;" k="30" />
    <hkern u1="&#x108;" u2="&#xc7;" k="30" />
    <hkern u1="&#x108;" u2="&#xa9;" k="30" />
    <hkern u1="&#x108;" u2="x" k="-20" />
    <hkern u1="&#x108;" u2="w" k="10" />
    <hkern u1="&#x108;" u2="v" k="20" />
    <hkern u1="&#x108;" u2="q" k="10" />
    <hkern u1="&#x108;" u2="o" k="10" />
    <hkern u1="&#x108;" u2="g" k="10" />
    <hkern u1="&#x108;" u2="e" k="10" />
    <hkern u1="&#x108;" u2="d" k="10" />
    <hkern u1="&#x108;" u2="c" k="10" />
    <hkern u1="&#x108;" u2="_" k="-20" />
    <hkern u1="&#x108;" u2="V" k="-15" />
    <hkern u1="&#x108;" u2="Q" k="30" />
    <hkern u1="&#x108;" u2="O" k="30" />
    <hkern u1="&#x108;" u2="G" k="30" />
    <hkern u1="&#x108;" u2="C" k="30" />
    <hkern u1="&#x108;" u2="A" k="7" />
    <hkern u1="&#x108;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x108;" u2="&#x2d;" k="85" />
    <hkern u1="&#x108;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x109;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x109;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x109;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x109;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#x109;" u2="&#x1ff;" k="25" />
    <hkern u1="&#x109;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x109;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x109;" u2="&#x177;" k="10" />
    <hkern u1="&#x109;" u2="&#x173;" k="10" />
    <hkern u1="&#x109;" u2="&#x171;" k="10" />
    <hkern u1="&#x109;" u2="&#x16f;" k="10" />
    <hkern u1="&#x109;" u2="&#x16d;" k="10" />
    <hkern u1="&#x109;" u2="&#x16b;" k="10" />
    <hkern u1="&#x109;" u2="&#x169;" k="10" />
    <hkern u1="&#x109;" u2="&#x164;" k="80" />
    <hkern u1="&#x109;" u2="&#x162;" k="80" />
    <hkern u1="&#x109;" u2="&#x153;" k="25" />
    <hkern u1="&#x109;" u2="&#x151;" k="25" />
    <hkern u1="&#x109;" u2="&#x14f;" k="25" />
    <hkern u1="&#x109;" u2="&#x14d;" k="25" />
    <hkern u1="&#x109;" u2="&#x123;" k="25" />
    <hkern u1="&#x109;" u2="&#x121;" k="25" />
    <hkern u1="&#x109;" u2="&#x11f;" k="25" />
    <hkern u1="&#x109;" u2="&#x11d;" k="25" />
    <hkern u1="&#x109;" u2="&#x11b;" k="25" />
    <hkern u1="&#x109;" u2="&#x119;" k="25" />
    <hkern u1="&#x109;" u2="&#x117;" k="25" />
    <hkern u1="&#x109;" u2="&#x115;" k="25" />
    <hkern u1="&#x109;" u2="&#x113;" k="25" />
    <hkern u1="&#x109;" u2="&#x111;" k="25" />
    <hkern u1="&#x109;" u2="&#x10f;" k="25" />
    <hkern u1="&#x109;" u2="&#x10d;" k="25" />
    <hkern u1="&#x109;" u2="&#x10b;" k="25" />
    <hkern u1="&#x109;" u2="&#x109;" k="25" />
    <hkern u1="&#x109;" u2="&#x107;" k="25" />
    <hkern u1="&#x109;" u2="&#x105;" k="10" />
    <hkern u1="&#x109;" u2="&#x103;" k="10" />
    <hkern u1="&#x109;" u2="&#x101;" k="10" />
    <hkern u1="&#x109;" u2="&#xff;" k="10" />
    <hkern u1="&#x109;" u2="&#xfd;" k="10" />
    <hkern u1="&#x109;" u2="&#xfc;" k="10" />
    <hkern u1="&#x109;" u2="&#xfb;" k="10" />
    <hkern u1="&#x109;" u2="&#xfa;" k="10" />
    <hkern u1="&#x109;" u2="&#xf9;" k="10" />
    <hkern u1="&#x109;" u2="&#xf8;" k="25" />
    <hkern u1="&#x109;" u2="&#xf6;" k="25" />
    <hkern u1="&#x109;" u2="&#xf5;" k="25" />
    <hkern u1="&#x109;" u2="&#xf4;" k="25" />
    <hkern u1="&#x109;" u2="&#xf3;" k="25" />
    <hkern u1="&#x109;" u2="&#xf2;" k="25" />
    <hkern u1="&#x109;" u2="&#xf0;" k="25" />
    <hkern u1="&#x109;" u2="&#xeb;" k="25" />
    <hkern u1="&#x109;" u2="&#xea;" k="25" />
    <hkern u1="&#x109;" u2="&#xe9;" k="25" />
    <hkern u1="&#x109;" u2="&#xe8;" k="25" />
    <hkern u1="&#x109;" u2="&#xe7;" k="25" />
    <hkern u1="&#x109;" u2="&#xe6;" k="10" />
    <hkern u1="&#x109;" u2="&#xe5;" k="10" />
    <hkern u1="&#x109;" u2="&#xe4;" k="10" />
    <hkern u1="&#x109;" u2="&#xe3;" k="10" />
    <hkern u1="&#x109;" u2="&#xe2;" k="10" />
    <hkern u1="&#x109;" u2="&#xe1;" k="10" />
    <hkern u1="&#x109;" u2="&#xe0;" k="10" />
    <hkern u1="&#x109;" u2="y" k="10" />
    <hkern u1="&#x109;" u2="x" k="-13" />
    <hkern u1="&#x109;" u2="u" k="10" />
    <hkern u1="&#x109;" u2="q" k="25" />
    <hkern u1="&#x109;" u2="o" k="25" />
    <hkern u1="&#x109;" u2="g" k="25" />
    <hkern u1="&#x109;" u2="e" k="25" />
    <hkern u1="&#x109;" u2="d" k="25" />
    <hkern u1="&#x109;" u2="c" k="25" />
    <hkern u1="&#x109;" u2="a" k="10" />
    <hkern u1="&#x109;" u2="T" k="80" />
    <hkern u1="&#x109;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x109;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10a;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x10a;" u2="&#x2014;" k="85" />
    <hkern u1="&#x10a;" u2="&#x2013;" k="85" />
    <hkern u1="&#x10a;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1fe;" k="30" />
    <hkern u1="&#x10a;" u2="&#x175;" k="10" />
    <hkern u1="&#x10a;" u2="&#x153;" k="10" />
    <hkern u1="&#x10a;" u2="&#x152;" k="30" />
    <hkern u1="&#x10a;" u2="&#x151;" k="10" />
    <hkern u1="&#x10a;" u2="&#x150;" k="30" />
    <hkern u1="&#x10a;" u2="&#x14f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x14e;" k="30" />
    <hkern u1="&#x10a;" u2="&#x14d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x14c;" k="30" />
    <hkern u1="&#x10a;" u2="&#x123;" k="10" />
    <hkern u1="&#x10a;" u2="&#x122;" k="30" />
    <hkern u1="&#x10a;" u2="&#x121;" k="10" />
    <hkern u1="&#x10a;" u2="&#x120;" k="30" />
    <hkern u1="&#x10a;" u2="&#x11f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x11e;" k="30" />
    <hkern u1="&#x10a;" u2="&#x11d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x11c;" k="30" />
    <hkern u1="&#x10a;" u2="&#x11b;" k="10" />
    <hkern u1="&#x10a;" u2="&#x119;" k="10" />
    <hkern u1="&#x10a;" u2="&#x117;" k="10" />
    <hkern u1="&#x10a;" u2="&#x115;" k="10" />
    <hkern u1="&#x10a;" u2="&#x113;" k="10" />
    <hkern u1="&#x10a;" u2="&#x111;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10c;" k="30" />
    <hkern u1="&#x10a;" u2="&#x10b;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10a;" k="30" />
    <hkern u1="&#x10a;" u2="&#x109;" k="10" />
    <hkern u1="&#x10a;" u2="&#x108;" k="30" />
    <hkern u1="&#x10a;" u2="&#x107;" k="10" />
    <hkern u1="&#x10a;" u2="&#x106;" k="30" />
    <hkern u1="&#x10a;" u2="&#xf8;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf6;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf5;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf4;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf3;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf2;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf0;" k="10" />
    <hkern u1="&#x10a;" u2="&#xeb;" k="10" />
    <hkern u1="&#x10a;" u2="&#xea;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe9;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe8;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe7;" k="10" />
    <hkern u1="&#x10a;" u2="&#xd8;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd6;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd5;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd4;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd3;" k="30" />
    <hkern u1="&#x10a;" u2="&#xd2;" k="30" />
    <hkern u1="&#x10a;" u2="&#xc7;" k="30" />
    <hkern u1="&#x10a;" u2="&#xa9;" k="30" />
    <hkern u1="&#x10a;" u2="x" k="-20" />
    <hkern u1="&#x10a;" u2="w" k="10" />
    <hkern u1="&#x10a;" u2="v" k="20" />
    <hkern u1="&#x10a;" u2="q" k="10" />
    <hkern u1="&#x10a;" u2="o" k="10" />
    <hkern u1="&#x10a;" u2="g" k="10" />
    <hkern u1="&#x10a;" u2="e" k="10" />
    <hkern u1="&#x10a;" u2="d" k="10" />
    <hkern u1="&#x10a;" u2="c" k="10" />
    <hkern u1="&#x10a;" u2="_" k="-20" />
    <hkern u1="&#x10a;" u2="V" k="-15" />
    <hkern u1="&#x10a;" u2="Q" k="30" />
    <hkern u1="&#x10a;" u2="O" k="30" />
    <hkern u1="&#x10a;" u2="G" k="30" />
    <hkern u1="&#x10a;" u2="C" k="30" />
    <hkern u1="&#x10a;" u2="A" k="7" />
    <hkern u1="&#x10a;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x10a;" u2="&#x2d;" k="85" />
    <hkern u1="&#x10a;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x10b;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x10b;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x10b;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#x10b;" u2="&#x1ff;" k="25" />
    <hkern u1="&#x10b;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x10b;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x10b;" u2="&#x177;" k="10" />
    <hkern u1="&#x10b;" u2="&#x173;" k="10" />
    <hkern u1="&#x10b;" u2="&#x171;" k="10" />
    <hkern u1="&#x10b;" u2="&#x16f;" k="10" />
    <hkern u1="&#x10b;" u2="&#x16d;" k="10" />
    <hkern u1="&#x10b;" u2="&#x16b;" k="10" />
    <hkern u1="&#x10b;" u2="&#x169;" k="10" />
    <hkern u1="&#x10b;" u2="&#x164;" k="80" />
    <hkern u1="&#x10b;" u2="&#x162;" k="80" />
    <hkern u1="&#x10b;" u2="&#x153;" k="25" />
    <hkern u1="&#x10b;" u2="&#x151;" k="25" />
    <hkern u1="&#x10b;" u2="&#x14f;" k="25" />
    <hkern u1="&#x10b;" u2="&#x14d;" k="25" />
    <hkern u1="&#x10b;" u2="&#x123;" k="25" />
    <hkern u1="&#x10b;" u2="&#x121;" k="25" />
    <hkern u1="&#x10b;" u2="&#x11f;" k="25" />
    <hkern u1="&#x10b;" u2="&#x11d;" k="25" />
    <hkern u1="&#x10b;" u2="&#x11b;" k="25" />
    <hkern u1="&#x10b;" u2="&#x119;" k="25" />
    <hkern u1="&#x10b;" u2="&#x117;" k="25" />
    <hkern u1="&#x10b;" u2="&#x115;" k="25" />
    <hkern u1="&#x10b;" u2="&#x113;" k="25" />
    <hkern u1="&#x10b;" u2="&#x111;" k="25" />
    <hkern u1="&#x10b;" u2="&#x10f;" k="25" />
    <hkern u1="&#x10b;" u2="&#x10d;" k="25" />
    <hkern u1="&#x10b;" u2="&#x10b;" k="25" />
    <hkern u1="&#x10b;" u2="&#x109;" k="25" />
    <hkern u1="&#x10b;" u2="&#x107;" k="25" />
    <hkern u1="&#x10b;" u2="&#x105;" k="10" />
    <hkern u1="&#x10b;" u2="&#x103;" k="10" />
    <hkern u1="&#x10b;" u2="&#x101;" k="10" />
    <hkern u1="&#x10b;" u2="&#xff;" k="10" />
    <hkern u1="&#x10b;" u2="&#xfd;" k="10" />
    <hkern u1="&#x10b;" u2="&#xfc;" k="10" />
    <hkern u1="&#x10b;" u2="&#xfb;" k="10" />
    <hkern u1="&#x10b;" u2="&#xfa;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf9;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf8;" k="25" />
    <hkern u1="&#x10b;" u2="&#xf6;" k="25" />
    <hkern u1="&#x10b;" u2="&#xf5;" k="25" />
    <hkern u1="&#x10b;" u2="&#xf4;" k="25" />
    <hkern u1="&#x10b;" u2="&#xf3;" k="25" />
    <hkern u1="&#x10b;" u2="&#xf2;" k="25" />
    <hkern u1="&#x10b;" u2="&#xf0;" k="25" />
    <hkern u1="&#x10b;" u2="&#xeb;" k="25" />
    <hkern u1="&#x10b;" u2="&#xea;" k="25" />
    <hkern u1="&#x10b;" u2="&#xe9;" k="25" />
    <hkern u1="&#x10b;" u2="&#xe8;" k="25" />
    <hkern u1="&#x10b;" u2="&#xe7;" k="25" />
    <hkern u1="&#x10b;" u2="&#xe6;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe5;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe4;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe3;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe2;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe1;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe0;" k="10" />
    <hkern u1="&#x10b;" u2="y" k="10" />
    <hkern u1="&#x10b;" u2="x" k="-13" />
    <hkern u1="&#x10b;" u2="u" k="10" />
    <hkern u1="&#x10b;" u2="q" k="25" />
    <hkern u1="&#x10b;" u2="o" k="25" />
    <hkern u1="&#x10b;" u2="g" k="25" />
    <hkern u1="&#x10b;" u2="e" k="25" />
    <hkern u1="&#x10b;" u2="d" k="25" />
    <hkern u1="&#x10b;" u2="c" k="25" />
    <hkern u1="&#x10b;" u2="a" k="10" />
    <hkern u1="&#x10b;" u2="T" k="80" />
    <hkern u1="&#x10b;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10c;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x10c;" u2="&#x2014;" k="85" />
    <hkern u1="&#x10c;" u2="&#x2013;" k="85" />
    <hkern u1="&#x10c;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1fe;" k="30" />
    <hkern u1="&#x10c;" u2="&#x175;" k="10" />
    <hkern u1="&#x10c;" u2="&#x153;" k="10" />
    <hkern u1="&#x10c;" u2="&#x152;" k="30" />
    <hkern u1="&#x10c;" u2="&#x151;" k="10" />
    <hkern u1="&#x10c;" u2="&#x150;" k="30" />
    <hkern u1="&#x10c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x14e;" k="30" />
    <hkern u1="&#x10c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x14c;" k="30" />
    <hkern u1="&#x10c;" u2="&#x123;" k="10" />
    <hkern u1="&#x10c;" u2="&#x122;" k="30" />
    <hkern u1="&#x10c;" u2="&#x121;" k="10" />
    <hkern u1="&#x10c;" u2="&#x120;" k="30" />
    <hkern u1="&#x10c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x11e;" k="30" />
    <hkern u1="&#x10c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x11c;" k="30" />
    <hkern u1="&#x10c;" u2="&#x11b;" k="10" />
    <hkern u1="&#x10c;" u2="&#x119;" k="10" />
    <hkern u1="&#x10c;" u2="&#x117;" k="10" />
    <hkern u1="&#x10c;" u2="&#x115;" k="10" />
    <hkern u1="&#x10c;" u2="&#x113;" k="10" />
    <hkern u1="&#x10c;" u2="&#x111;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10c;" k="30" />
    <hkern u1="&#x10c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10a;" k="30" />
    <hkern u1="&#x10c;" u2="&#x109;" k="10" />
    <hkern u1="&#x10c;" u2="&#x108;" k="30" />
    <hkern u1="&#x10c;" u2="&#x107;" k="10" />
    <hkern u1="&#x10c;" u2="&#x106;" k="30" />
    <hkern u1="&#x10c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf0;" k="10" />
    <hkern u1="&#x10c;" u2="&#xeb;" k="10" />
    <hkern u1="&#x10c;" u2="&#xea;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe9;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe8;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x10c;" u2="&#xd8;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd6;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd5;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd4;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd3;" k="30" />
    <hkern u1="&#x10c;" u2="&#xd2;" k="30" />
    <hkern u1="&#x10c;" u2="&#xc7;" k="30" />
    <hkern u1="&#x10c;" u2="&#xa9;" k="30" />
    <hkern u1="&#x10c;" u2="x" k="-20" />
    <hkern u1="&#x10c;" u2="w" k="10" />
    <hkern u1="&#x10c;" u2="v" k="20" />
    <hkern u1="&#x10c;" u2="q" k="10" />
    <hkern u1="&#x10c;" u2="o" k="10" />
    <hkern u1="&#x10c;" u2="g" k="10" />
    <hkern u1="&#x10c;" u2="e" k="10" />
    <hkern u1="&#x10c;" u2="d" k="10" />
    <hkern u1="&#x10c;" u2="c" k="10" />
    <hkern u1="&#x10c;" u2="_" k="-20" />
    <hkern u1="&#x10c;" u2="V" k="-15" />
    <hkern u1="&#x10c;" u2="Q" k="30" />
    <hkern u1="&#x10c;" u2="O" k="30" />
    <hkern u1="&#x10c;" u2="G" k="30" />
    <hkern u1="&#x10c;" u2="C" k="30" />
    <hkern u1="&#x10c;" u2="A" k="7" />
    <hkern u1="&#x10c;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x10c;" u2="&#x2d;" k="85" />
    <hkern u1="&#x10c;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x10d;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x10d;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x10d;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#x10d;" u2="&#x1ff;" k="25" />
    <hkern u1="&#x10d;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x10d;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x10d;" u2="&#x177;" k="10" />
    <hkern u1="&#x10d;" u2="&#x173;" k="10" />
    <hkern u1="&#x10d;" u2="&#x171;" k="10" />
    <hkern u1="&#x10d;" u2="&#x16f;" k="10" />
    <hkern u1="&#x10d;" u2="&#x16d;" k="10" />
    <hkern u1="&#x10d;" u2="&#x16b;" k="10" />
    <hkern u1="&#x10d;" u2="&#x169;" k="10" />
    <hkern u1="&#x10d;" u2="&#x164;" k="80" />
    <hkern u1="&#x10d;" u2="&#x162;" k="80" />
    <hkern u1="&#x10d;" u2="&#x153;" k="25" />
    <hkern u1="&#x10d;" u2="&#x151;" k="25" />
    <hkern u1="&#x10d;" u2="&#x14f;" k="25" />
    <hkern u1="&#x10d;" u2="&#x14d;" k="25" />
    <hkern u1="&#x10d;" u2="&#x123;" k="25" />
    <hkern u1="&#x10d;" u2="&#x121;" k="25" />
    <hkern u1="&#x10d;" u2="&#x11f;" k="25" />
    <hkern u1="&#x10d;" u2="&#x11d;" k="25" />
    <hkern u1="&#x10d;" u2="&#x11b;" k="25" />
    <hkern u1="&#x10d;" u2="&#x119;" k="25" />
    <hkern u1="&#x10d;" u2="&#x117;" k="25" />
    <hkern u1="&#x10d;" u2="&#x115;" k="25" />
    <hkern u1="&#x10d;" u2="&#x113;" k="25" />
    <hkern u1="&#x10d;" u2="&#x111;" k="25" />
    <hkern u1="&#x10d;" u2="&#x10f;" k="25" />
    <hkern u1="&#x10d;" u2="&#x10d;" k="25" />
    <hkern u1="&#x10d;" u2="&#x10b;" k="25" />
    <hkern u1="&#x10d;" u2="&#x109;" k="25" />
    <hkern u1="&#x10d;" u2="&#x107;" k="25" />
    <hkern u1="&#x10d;" u2="&#x105;" k="10" />
    <hkern u1="&#x10d;" u2="&#x103;" k="10" />
    <hkern u1="&#x10d;" u2="&#x101;" k="10" />
    <hkern u1="&#x10d;" u2="&#xff;" k="10" />
    <hkern u1="&#x10d;" u2="&#xfd;" k="10" />
    <hkern u1="&#x10d;" u2="&#xfc;" k="10" />
    <hkern u1="&#x10d;" u2="&#xfb;" k="10" />
    <hkern u1="&#x10d;" u2="&#xfa;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf9;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf8;" k="25" />
    <hkern u1="&#x10d;" u2="&#xf6;" k="25" />
    <hkern u1="&#x10d;" u2="&#xf5;" k="25" />
    <hkern u1="&#x10d;" u2="&#xf4;" k="25" />
    <hkern u1="&#x10d;" u2="&#xf3;" k="25" />
    <hkern u1="&#x10d;" u2="&#xf2;" k="25" />
    <hkern u1="&#x10d;" u2="&#xf0;" k="25" />
    <hkern u1="&#x10d;" u2="&#xeb;" k="25" />
    <hkern u1="&#x10d;" u2="&#xea;" k="25" />
    <hkern u1="&#x10d;" u2="&#xe9;" k="25" />
    <hkern u1="&#x10d;" u2="&#xe8;" k="25" />
    <hkern u1="&#x10d;" u2="&#xe7;" k="25" />
    <hkern u1="&#x10d;" u2="&#xe6;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe5;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe4;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe3;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe2;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe1;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe0;" k="10" />
    <hkern u1="&#x10d;" u2="y" k="10" />
    <hkern u1="&#x10d;" u2="x" k="-13" />
    <hkern u1="&#x10d;" u2="u" k="10" />
    <hkern u1="&#x10d;" u2="q" k="25" />
    <hkern u1="&#x10d;" u2="o" k="25" />
    <hkern u1="&#x10d;" u2="g" k="25" />
    <hkern u1="&#x10d;" u2="e" k="25" />
    <hkern u1="&#x10d;" u2="d" k="25" />
    <hkern u1="&#x10d;" u2="c" k="25" />
    <hkern u1="&#x10d;" u2="a" k="10" />
    <hkern u1="&#x10d;" u2="T" k="80" />
    <hkern u1="&#x10d;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10e;" u2="&#x2026;" k="25" />
    <hkern u1="&#x10e;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x164;" k="15" />
    <hkern u1="&#x10e;" u2="&#x162;" k="15" />
    <hkern u1="&#x10e;" u2="&#x152;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x150;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x122;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x120;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x108;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x106;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x10e;" u2="X" k="10" />
    <hkern u1="&#x10e;" u2="T" k="15" />
    <hkern u1="&#x10e;" u2="Q" k="-5" />
    <hkern u1="&#x10e;" u2="O" k="-5" />
    <hkern u1="&#x10e;" u2="G" k="-5" />
    <hkern u1="&#x10e;" u2="C" k="-5" />
    <hkern u1="&#x10e;" u2="&#x2e;" k="25" />
    <hkern u1="&#x10e;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x2c;" k="25" />
    <hkern u1="&#x110;" u2="&#x2026;" k="25" />
    <hkern u1="&#x110;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x110;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x110;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x110;" u2="&#x164;" k="15" />
    <hkern u1="&#x110;" u2="&#x162;" k="15" />
    <hkern u1="&#x110;" u2="&#x152;" k="-5" />
    <hkern u1="&#x110;" u2="&#x150;" k="-5" />
    <hkern u1="&#x110;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x110;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x122;" k="-5" />
    <hkern u1="&#x110;" u2="&#x120;" k="-5" />
    <hkern u1="&#x110;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x110;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x110;" u2="&#x108;" k="-5" />
    <hkern u1="&#x110;" u2="&#x106;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x110;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x110;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x110;" u2="X" k="10" />
    <hkern u1="&#x110;" u2="T" k="15" />
    <hkern u1="&#x110;" u2="Q" k="-5" />
    <hkern u1="&#x110;" u2="O" k="-5" />
    <hkern u1="&#x110;" u2="G" k="-5" />
    <hkern u1="&#x110;" u2="C" k="-5" />
    <hkern u1="&#x110;" u2="&#x2e;" k="25" />
    <hkern u1="&#x110;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x110;" u2="&#x2c;" k="25" />
    <hkern u1="&#x112;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x112;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x112;" u2="&#x152;" k="7" />
    <hkern u1="&#x112;" u2="&#x150;" k="7" />
    <hkern u1="&#x112;" u2="&#x14e;" k="7" />
    <hkern u1="&#x112;" u2="&#x14c;" k="7" />
    <hkern u1="&#x112;" u2="&#x122;" k="7" />
    <hkern u1="&#x112;" u2="&#x120;" k="7" />
    <hkern u1="&#x112;" u2="&#x11e;" k="7" />
    <hkern u1="&#x112;" u2="&#x11c;" k="7" />
    <hkern u1="&#x112;" u2="&#x10c;" k="7" />
    <hkern u1="&#x112;" u2="&#x10a;" k="7" />
    <hkern u1="&#x112;" u2="&#x108;" k="7" />
    <hkern u1="&#x112;" u2="&#x106;" k="7" />
    <hkern u1="&#x112;" u2="&#xd8;" k="7" />
    <hkern u1="&#x112;" u2="&#xd6;" k="7" />
    <hkern u1="&#x112;" u2="&#xd5;" k="7" />
    <hkern u1="&#x112;" u2="&#xd4;" k="7" />
    <hkern u1="&#x112;" u2="&#xd3;" k="7" />
    <hkern u1="&#x112;" u2="&#xd2;" k="7" />
    <hkern u1="&#x112;" u2="&#xc7;" k="7" />
    <hkern u1="&#x112;" u2="&#xa9;" k="7" />
    <hkern u1="&#x112;" u2="Q" k="7" />
    <hkern u1="&#x112;" u2="O" k="7" />
    <hkern u1="&#x112;" u2="G" k="7" />
    <hkern u1="&#x112;" u2="C" k="7" />
    <hkern u1="&#x112;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x112;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x113;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x113;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x113;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x113;" u2="&#x219;" k="-5" />
    <hkern u1="&#x113;" u2="&#x174;" k="10" />
    <hkern u1="&#x113;" u2="&#x164;" k="70" />
    <hkern u1="&#x113;" u2="&#x162;" k="70" />
    <hkern u1="&#x113;" u2="&#x161;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x113;" u2="s" k="-5" />
    <hkern u1="&#x113;" u2="W" k="10" />
    <hkern u1="&#x113;" u2="V" k="20" />
    <hkern u1="&#x113;" u2="T" k="70" />
    <hkern u1="&#x114;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x114;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x114;" u2="&#x152;" k="7" />
    <hkern u1="&#x114;" u2="&#x150;" k="7" />
    <hkern u1="&#x114;" u2="&#x14e;" k="7" />
    <hkern u1="&#x114;" u2="&#x14c;" k="7" />
    <hkern u1="&#x114;" u2="&#x122;" k="7" />
    <hkern u1="&#x114;" u2="&#x120;" k="7" />
    <hkern u1="&#x114;" u2="&#x11e;" k="7" />
    <hkern u1="&#x114;" u2="&#x11c;" k="7" />
    <hkern u1="&#x114;" u2="&#x10c;" k="7" />
    <hkern u1="&#x114;" u2="&#x10a;" k="7" />
    <hkern u1="&#x114;" u2="&#x108;" k="7" />
    <hkern u1="&#x114;" u2="&#x106;" k="7" />
    <hkern u1="&#x114;" u2="&#xd8;" k="7" />
    <hkern u1="&#x114;" u2="&#xd6;" k="7" />
    <hkern u1="&#x114;" u2="&#xd5;" k="7" />
    <hkern u1="&#x114;" u2="&#xd4;" k="7" />
    <hkern u1="&#x114;" u2="&#xd3;" k="7" />
    <hkern u1="&#x114;" u2="&#xd2;" k="7" />
    <hkern u1="&#x114;" u2="&#xc7;" k="7" />
    <hkern u1="&#x114;" u2="&#xa9;" k="7" />
    <hkern u1="&#x114;" u2="Q" k="7" />
    <hkern u1="&#x114;" u2="O" k="7" />
    <hkern u1="&#x114;" u2="G" k="7" />
    <hkern u1="&#x114;" u2="C" k="7" />
    <hkern u1="&#x114;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x114;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x115;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x115;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x115;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x115;" u2="&#x219;" k="-5" />
    <hkern u1="&#x115;" u2="&#x174;" k="10" />
    <hkern u1="&#x115;" u2="&#x164;" k="70" />
    <hkern u1="&#x115;" u2="&#x162;" k="70" />
    <hkern u1="&#x115;" u2="&#x161;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x115;" u2="s" k="-5" />
    <hkern u1="&#x115;" u2="W" k="10" />
    <hkern u1="&#x115;" u2="V" k="20" />
    <hkern u1="&#x115;" u2="T" k="70" />
    <hkern u1="&#x116;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x116;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x116;" u2="&#x152;" k="7" />
    <hkern u1="&#x116;" u2="&#x150;" k="7" />
    <hkern u1="&#x116;" u2="&#x14e;" k="7" />
    <hkern u1="&#x116;" u2="&#x14c;" k="7" />
    <hkern u1="&#x116;" u2="&#x122;" k="7" />
    <hkern u1="&#x116;" u2="&#x120;" k="7" />
    <hkern u1="&#x116;" u2="&#x11e;" k="7" />
    <hkern u1="&#x116;" u2="&#x11c;" k="7" />
    <hkern u1="&#x116;" u2="&#x10c;" k="7" />
    <hkern u1="&#x116;" u2="&#x10a;" k="7" />
    <hkern u1="&#x116;" u2="&#x108;" k="7" />
    <hkern u1="&#x116;" u2="&#x106;" k="7" />
    <hkern u1="&#x116;" u2="&#xd8;" k="7" />
    <hkern u1="&#x116;" u2="&#xd6;" k="7" />
    <hkern u1="&#x116;" u2="&#xd5;" k="7" />
    <hkern u1="&#x116;" u2="&#xd4;" k="7" />
    <hkern u1="&#x116;" u2="&#xd3;" k="7" />
    <hkern u1="&#x116;" u2="&#xd2;" k="7" />
    <hkern u1="&#x116;" u2="&#xc7;" k="7" />
    <hkern u1="&#x116;" u2="&#xa9;" k="7" />
    <hkern u1="&#x116;" u2="Q" k="7" />
    <hkern u1="&#x116;" u2="O" k="7" />
    <hkern u1="&#x116;" u2="G" k="7" />
    <hkern u1="&#x116;" u2="C" k="7" />
    <hkern u1="&#x116;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x116;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x117;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x117;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x117;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x117;" u2="&#x219;" k="-5" />
    <hkern u1="&#x117;" u2="&#x174;" k="10" />
    <hkern u1="&#x117;" u2="&#x164;" k="70" />
    <hkern u1="&#x117;" u2="&#x162;" k="70" />
    <hkern u1="&#x117;" u2="&#x161;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x117;" u2="s" k="-5" />
    <hkern u1="&#x117;" u2="W" k="10" />
    <hkern u1="&#x117;" u2="V" k="20" />
    <hkern u1="&#x117;" u2="T" k="70" />
    <hkern u1="&#x118;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x118;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x118;" u2="&#x152;" k="7" />
    <hkern u1="&#x118;" u2="&#x150;" k="7" />
    <hkern u1="&#x118;" u2="&#x14e;" k="7" />
    <hkern u1="&#x118;" u2="&#x14c;" k="7" />
    <hkern u1="&#x118;" u2="&#x122;" k="7" />
    <hkern u1="&#x118;" u2="&#x120;" k="7" />
    <hkern u1="&#x118;" u2="&#x11e;" k="7" />
    <hkern u1="&#x118;" u2="&#x11c;" k="7" />
    <hkern u1="&#x118;" u2="&#x10c;" k="7" />
    <hkern u1="&#x118;" u2="&#x10a;" k="7" />
    <hkern u1="&#x118;" u2="&#x108;" k="7" />
    <hkern u1="&#x118;" u2="&#x106;" k="7" />
    <hkern u1="&#x118;" u2="&#xd8;" k="7" />
    <hkern u1="&#x118;" u2="&#xd6;" k="7" />
    <hkern u1="&#x118;" u2="&#xd5;" k="7" />
    <hkern u1="&#x118;" u2="&#xd4;" k="7" />
    <hkern u1="&#x118;" u2="&#xd3;" k="7" />
    <hkern u1="&#x118;" u2="&#xd2;" k="7" />
    <hkern u1="&#x118;" u2="&#xc7;" k="7" />
    <hkern u1="&#x118;" u2="&#xa9;" k="7" />
    <hkern u1="&#x118;" u2="Q" k="7" />
    <hkern u1="&#x118;" u2="O" k="7" />
    <hkern u1="&#x118;" u2="G" k="7" />
    <hkern u1="&#x118;" u2="C" k="7" />
    <hkern u1="&#x118;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x119;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x119;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x119;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x119;" u2="&#x219;" k="-5" />
    <hkern u1="&#x119;" u2="&#x174;" k="10" />
    <hkern u1="&#x119;" u2="&#x164;" k="70" />
    <hkern u1="&#x119;" u2="&#x162;" k="70" />
    <hkern u1="&#x119;" u2="&#x161;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x119;" u2="s" k="-5" />
    <hkern u1="&#x119;" u2="W" k="10" />
    <hkern u1="&#x119;" u2="V" k="20" />
    <hkern u1="&#x119;" u2="T" k="70" />
    <hkern u1="&#x11a;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x11a;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x11a;" u2="&#x152;" k="7" />
    <hkern u1="&#x11a;" u2="&#x150;" k="7" />
    <hkern u1="&#x11a;" u2="&#x14e;" k="7" />
    <hkern u1="&#x11a;" u2="&#x14c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x122;" k="7" />
    <hkern u1="&#x11a;" u2="&#x120;" k="7" />
    <hkern u1="&#x11a;" u2="&#x11e;" k="7" />
    <hkern u1="&#x11a;" u2="&#x11c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x10c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x10a;" k="7" />
    <hkern u1="&#x11a;" u2="&#x108;" k="7" />
    <hkern u1="&#x11a;" u2="&#x106;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd8;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd6;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd5;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd4;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd3;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd2;" k="7" />
    <hkern u1="&#x11a;" u2="&#xc7;" k="7" />
    <hkern u1="&#x11a;" u2="&#xa9;" k="7" />
    <hkern u1="&#x11a;" u2="Q" k="7" />
    <hkern u1="&#x11a;" u2="O" k="7" />
    <hkern u1="&#x11a;" u2="G" k="7" />
    <hkern u1="&#x11a;" u2="C" k="7" />
    <hkern u1="&#x11a;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x11a;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x11b;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x11b;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x11b;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x11b;" u2="&#x219;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x174;" k="10" />
    <hkern u1="&#x11b;" u2="&#x164;" k="70" />
    <hkern u1="&#x11b;" u2="&#x162;" k="70" />
    <hkern u1="&#x11b;" u2="&#x161;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x11b;" u2="s" k="-5" />
    <hkern u1="&#x11b;" u2="W" k="10" />
    <hkern u1="&#x11b;" u2="V" k="20" />
    <hkern u1="&#x11b;" u2="T" k="70" />
    <hkern u1="&#x11d;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x11d;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x11d;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x11d;" u2="&#x174;" k="10" />
    <hkern u1="&#x11d;" u2="&#x164;" k="70" />
    <hkern u1="&#x11d;" u2="&#x162;" k="70" />
    <hkern u1="&#x11d;" u2="W" k="10" />
    <hkern u1="&#x11d;" u2="V" k="20" />
    <hkern u1="&#x11d;" u2="T" k="70" />
    <hkern u1="&#x11f;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x11f;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x11f;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x11f;" u2="&#x174;" k="10" />
    <hkern u1="&#x11f;" u2="&#x164;" k="70" />
    <hkern u1="&#x11f;" u2="&#x162;" k="70" />
    <hkern u1="&#x11f;" u2="W" k="10" />
    <hkern u1="&#x11f;" u2="V" k="20" />
    <hkern u1="&#x11f;" u2="T" k="70" />
    <hkern u1="&#x121;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x121;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x121;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x121;" u2="&#x174;" k="10" />
    <hkern u1="&#x121;" u2="&#x164;" k="70" />
    <hkern u1="&#x121;" u2="&#x162;" k="70" />
    <hkern u1="&#x121;" u2="W" k="10" />
    <hkern u1="&#x121;" u2="V" k="20" />
    <hkern u1="&#x121;" u2="T" k="70" />
    <hkern u1="&#x123;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x123;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x123;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x123;" u2="&#x174;" k="10" />
    <hkern u1="&#x123;" u2="&#x164;" k="70" />
    <hkern u1="&#x123;" u2="&#x162;" k="70" />
    <hkern u1="&#x123;" u2="W" k="10" />
    <hkern u1="&#x123;" u2="V" k="20" />
    <hkern u1="&#x123;" u2="T" k="70" />
    <hkern u1="&#x125;" u2="&#x164;" k="70" />
    <hkern u1="&#x125;" u2="&#x162;" k="70" />
    <hkern u1="&#x125;" u2="T" k="70" />
    <hkern u1="&#x127;" u2="&#x164;" k="70" />
    <hkern u1="&#x127;" u2="&#x162;" k="70" />
    <hkern u1="&#x127;" u2="T" k="70" />
    <hkern u1="&#x136;" u2="&#x2026;" k="-50" />
    <hkern u1="&#x136;" u2="&#x2014;" k="10" />
    <hkern u1="&#x136;" u2="&#x2013;" k="10" />
    <hkern u1="&#x136;" u2="&#xef;" k="-10" />
    <hkern u1="&#x136;" u2="v" k="-20" />
    <hkern u1="&#x136;" u2="_" k="-50" />
    <hkern u1="&#x136;" u2="&#x2e;" k="-50" />
    <hkern u1="&#x136;" u2="&#x2d;" k="10" />
    <hkern u1="&#x136;" u2="&#x2c;" k="-50" />
    <hkern u1="&#x137;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x137;" u2="&#x2014;" k="45" />
    <hkern u1="&#x137;" u2="&#x2013;" k="45" />
    <hkern u1="&#x137;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x137;" u2="&#x1fd;" k="-10" />
    <hkern u1="&#x137;" u2="&#x1fb;" k="-10" />
    <hkern u1="&#x137;" u2="&#x164;" k="40" />
    <hkern u1="&#x137;" u2="&#x162;" k="40" />
    <hkern u1="&#x137;" u2="&#x153;" k="10" />
    <hkern u1="&#x137;" u2="&#x151;" k="10" />
    <hkern u1="&#x137;" u2="&#x14f;" k="10" />
    <hkern u1="&#x137;" u2="&#x14d;" k="10" />
    <hkern u1="&#x137;" u2="&#x123;" k="10" />
    <hkern u1="&#x137;" u2="&#x121;" k="10" />
    <hkern u1="&#x137;" u2="&#x11f;" k="10" />
    <hkern u1="&#x137;" u2="&#x11d;" k="10" />
    <hkern u1="&#x137;" u2="&#x11b;" k="10" />
    <hkern u1="&#x137;" u2="&#x119;" k="10" />
    <hkern u1="&#x137;" u2="&#x117;" k="10" />
    <hkern u1="&#x137;" u2="&#x115;" k="10" />
    <hkern u1="&#x137;" u2="&#x113;" k="10" />
    <hkern u1="&#x137;" u2="&#x111;" k="10" />
    <hkern u1="&#x137;" u2="&#x10f;" k="10" />
    <hkern u1="&#x137;" u2="&#x10d;" k="10" />
    <hkern u1="&#x137;" u2="&#x10b;" k="10" />
    <hkern u1="&#x137;" u2="&#x109;" k="10" />
    <hkern u1="&#x137;" u2="&#x107;" k="10" />
    <hkern u1="&#x137;" u2="&#x105;" k="-10" />
    <hkern u1="&#x137;" u2="&#x103;" k="-10" />
    <hkern u1="&#x137;" u2="&#x101;" k="-10" />
    <hkern u1="&#x137;" u2="&#xf8;" k="10" />
    <hkern u1="&#x137;" u2="&#xf6;" k="10" />
    <hkern u1="&#x137;" u2="&#xf5;" k="10" />
    <hkern u1="&#x137;" u2="&#xf4;" k="10" />
    <hkern u1="&#x137;" u2="&#xf3;" k="10" />
    <hkern u1="&#x137;" u2="&#xf2;" k="10" />
    <hkern u1="&#x137;" u2="&#xf0;" k="10" />
    <hkern u1="&#x137;" u2="&#xeb;" k="10" />
    <hkern u1="&#x137;" u2="&#xea;" k="10" />
    <hkern u1="&#x137;" u2="&#xe9;" k="10" />
    <hkern u1="&#x137;" u2="&#xe8;" k="10" />
    <hkern u1="&#x137;" u2="&#xe7;" k="10" />
    <hkern u1="&#x137;" u2="&#xe6;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe5;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe4;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe3;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe2;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe1;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe0;" k="-10" />
    <hkern u1="&#x137;" u2="q" k="10" />
    <hkern u1="&#x137;" u2="o" k="10" />
    <hkern u1="&#x137;" u2="g" k="10" />
    <hkern u1="&#x137;" u2="e" k="10" />
    <hkern u1="&#x137;" u2="d" k="10" />
    <hkern u1="&#x137;" u2="c" k="10" />
    <hkern u1="&#x137;" u2="a" k="-10" />
    <hkern u1="&#x137;" u2="T" k="40" />
    <hkern u1="&#x137;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x137;" u2="&#x2d;" k="45" />
    <hkern u1="&#x137;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x139;" u2="&#x2122;" k="65" />
    <hkern u1="&#x139;" u2="&#x2026;" k="-40" />
    <hkern u1="&#x139;" u2="&#x201d;" k="110" />
    <hkern u1="&#x139;" u2="&#x201c;" k="110" />
    <hkern u1="&#x139;" u2="&#x2019;" k="110" />
    <hkern u1="&#x139;" u2="&#x2018;" k="110" />
    <hkern u1="&#x139;" u2="&#x2014;" k="100" />
    <hkern u1="&#x139;" u2="&#x2013;" k="100" />
    <hkern u1="&#x139;" u2="&#x1ef2;" k="105" />
    <hkern u1="&#x139;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e84;" k="45" />
    <hkern u1="&#x139;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e82;" k="45" />
    <hkern u1="&#x139;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e80;" k="45" />
    <hkern u1="&#x139;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x139;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x139;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x139;" u2="&#x178;" k="105" />
    <hkern u1="&#x139;" u2="&#x176;" k="105" />
    <hkern u1="&#x139;" u2="&#x175;" k="30" />
    <hkern u1="&#x139;" u2="&#x174;" k="45" />
    <hkern u1="&#x139;" u2="&#x164;" k="75" />
    <hkern u1="&#x139;" u2="&#x162;" k="75" />
    <hkern u1="&#x139;" u2="&#x152;" k="20" />
    <hkern u1="&#x139;" u2="&#x150;" k="20" />
    <hkern u1="&#x139;" u2="&#x14e;" k="20" />
    <hkern u1="&#x139;" u2="&#x14c;" k="20" />
    <hkern u1="&#x139;" u2="&#x122;" k="20" />
    <hkern u1="&#x139;" u2="&#x120;" k="20" />
    <hkern u1="&#x139;" u2="&#x11e;" k="20" />
    <hkern u1="&#x139;" u2="&#x11c;" k="20" />
    <hkern u1="&#x139;" u2="&#x10c;" k="20" />
    <hkern u1="&#x139;" u2="&#x10a;" k="20" />
    <hkern u1="&#x139;" u2="&#x108;" k="20" />
    <hkern u1="&#x139;" u2="&#x106;" k="20" />
    <hkern u1="&#x139;" u2="&#x105;" k="-30" />
    <hkern u1="&#x139;" u2="&#x103;" k="-30" />
    <hkern u1="&#x139;" u2="&#x101;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x139;" u2="&#xdd;" k="105" />
    <hkern u1="&#x139;" u2="&#xd8;" k="20" />
    <hkern u1="&#x139;" u2="&#xd6;" k="20" />
    <hkern u1="&#x139;" u2="&#xd5;" k="20" />
    <hkern u1="&#x139;" u2="&#xd4;" k="20" />
    <hkern u1="&#x139;" u2="&#xd3;" k="20" />
    <hkern u1="&#x139;" u2="&#xd2;" k="20" />
    <hkern u1="&#x139;" u2="&#xc7;" k="20" />
    <hkern u1="&#x139;" u2="&#xa9;" k="20" />
    <hkern u1="&#x139;" u2="w" k="30" />
    <hkern u1="&#x139;" u2="v" k="40" />
    <hkern u1="&#x139;" u2="a" k="-30" />
    <hkern u1="&#x139;" u2="_" k="-50" />
    <hkern u1="&#x139;" u2="Y" k="105" />
    <hkern u1="&#x139;" u2="W" k="45" />
    <hkern u1="&#x139;" u2="V" k="70" />
    <hkern u1="&#x139;" u2="T" k="75" />
    <hkern u1="&#x139;" u2="Q" k="20" />
    <hkern u1="&#x139;" u2="O" k="20" />
    <hkern u1="&#x139;" u2="G" k="20" />
    <hkern u1="&#x139;" u2="C" k="20" />
    <hkern u1="&#x139;" u2="&#x2e;" k="-40" />
    <hkern u1="&#x139;" u2="&#x2d;" k="100" />
    <hkern u1="&#x139;" u2="&#x2c;" k="-40" />
    <hkern u1="&#x139;" u2="&#x2a;" k="80" />
    <hkern u1="&#x139;" u2="&#x27;" k="125" />
    <hkern u1="&#x139;" u2="&#x22;" k="125" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="65" />
    <hkern u1="&#x13b;" u2="&#x2026;" k="-40" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="110" />
    <hkern u1="&#x13b;" u2="&#x201c;" k="110" />
    <hkern u1="&#x13b;" u2="&#x2019;" k="110" />
    <hkern u1="&#x13b;" u2="&#x2018;" k="110" />
    <hkern u1="&#x13b;" u2="&#x2014;" k="100" />
    <hkern u1="&#x13b;" u2="&#x2013;" k="100" />
    <hkern u1="&#x13b;" u2="&#x1ef2;" k="105" />
    <hkern u1="&#x13b;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e84;" k="45" />
    <hkern u1="&#x13b;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e82;" k="45" />
    <hkern u1="&#x13b;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e80;" k="45" />
    <hkern u1="&#x13b;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x13b;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x178;" k="105" />
    <hkern u1="&#x13b;" u2="&#x176;" k="105" />
    <hkern u1="&#x13b;" u2="&#x175;" k="30" />
    <hkern u1="&#x13b;" u2="&#x174;" k="45" />
    <hkern u1="&#x13b;" u2="&#x164;" k="75" />
    <hkern u1="&#x13b;" u2="&#x162;" k="75" />
    <hkern u1="&#x13b;" u2="&#x152;" k="20" />
    <hkern u1="&#x13b;" u2="&#x150;" k="20" />
    <hkern u1="&#x13b;" u2="&#x14e;" k="20" />
    <hkern u1="&#x13b;" u2="&#x14c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x122;" k="20" />
    <hkern u1="&#x13b;" u2="&#x120;" k="20" />
    <hkern u1="&#x13b;" u2="&#x11e;" k="20" />
    <hkern u1="&#x13b;" u2="&#x11c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x10c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x10a;" k="20" />
    <hkern u1="&#x13b;" u2="&#x108;" k="20" />
    <hkern u1="&#x13b;" u2="&#x106;" k="20" />
    <hkern u1="&#x13b;" u2="&#x105;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x103;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x101;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xdd;" k="105" />
    <hkern u1="&#x13b;" u2="&#xd8;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd6;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd5;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd4;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd3;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd2;" k="20" />
    <hkern u1="&#x13b;" u2="&#xc7;" k="20" />
    <hkern u1="&#x13b;" u2="&#xa9;" k="20" />
    <hkern u1="&#x13b;" u2="w" k="30" />
    <hkern u1="&#x13b;" u2="v" k="40" />
    <hkern u1="&#x13b;" u2="a" k="-30" />
    <hkern u1="&#x13b;" u2="_" k="-50" />
    <hkern u1="&#x13b;" u2="Y" k="105" />
    <hkern u1="&#x13b;" u2="W" k="45" />
    <hkern u1="&#x13b;" u2="V" k="70" />
    <hkern u1="&#x13b;" u2="T" k="75" />
    <hkern u1="&#x13b;" u2="Q" k="20" />
    <hkern u1="&#x13b;" u2="O" k="20" />
    <hkern u1="&#x13b;" u2="G" k="20" />
    <hkern u1="&#x13b;" u2="C" k="20" />
    <hkern u1="&#x13b;" u2="&#x2e;" k="-40" />
    <hkern u1="&#x13b;" u2="&#x2d;" k="100" />
    <hkern u1="&#x13b;" u2="&#x2c;" k="-40" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="80" />
    <hkern u1="&#x13b;" u2="&#x27;" k="125" />
    <hkern u1="&#x13b;" u2="&#x22;" k="125" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="65" />
    <hkern u1="&#x13d;" u2="&#x2026;" k="-40" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="110" />
    <hkern u1="&#x13d;" u2="&#x201c;" k="110" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="110" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="110" />
    <hkern u1="&#x13d;" u2="&#x2014;" k="100" />
    <hkern u1="&#x13d;" u2="&#x2013;" k="100" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="105" />
    <hkern u1="&#x13d;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="45" />
    <hkern u1="&#x13d;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="45" />
    <hkern u1="&#x13d;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="45" />
    <hkern u1="&#x13d;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x13d;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x178;" k="105" />
    <hkern u1="&#x13d;" u2="&#x176;" k="105" />
    <hkern u1="&#x13d;" u2="&#x175;" k="30" />
    <hkern u1="&#x13d;" u2="&#x174;" k="45" />
    <hkern u1="&#x13d;" u2="&#x164;" k="75" />
    <hkern u1="&#x13d;" u2="&#x162;" k="75" />
    <hkern u1="&#x13d;" u2="&#x152;" k="20" />
    <hkern u1="&#x13d;" u2="&#x150;" k="20" />
    <hkern u1="&#x13d;" u2="&#x14e;" k="20" />
    <hkern u1="&#x13d;" u2="&#x14c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x122;" k="20" />
    <hkern u1="&#x13d;" u2="&#x120;" k="20" />
    <hkern u1="&#x13d;" u2="&#x11e;" k="20" />
    <hkern u1="&#x13d;" u2="&#x11c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x10c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x10a;" k="20" />
    <hkern u1="&#x13d;" u2="&#x108;" k="20" />
    <hkern u1="&#x13d;" u2="&#x106;" k="20" />
    <hkern u1="&#x13d;" u2="&#x105;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x103;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x101;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="105" />
    <hkern u1="&#x13d;" u2="&#xd8;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd6;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd5;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd4;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd3;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd2;" k="20" />
    <hkern u1="&#x13d;" u2="&#xc7;" k="20" />
    <hkern u1="&#x13d;" u2="&#xa9;" k="20" />
    <hkern u1="&#x13d;" u2="w" k="30" />
    <hkern u1="&#x13d;" u2="v" k="40" />
    <hkern u1="&#x13d;" u2="a" k="-30" />
    <hkern u1="&#x13d;" u2="_" k="-50" />
    <hkern u1="&#x13d;" u2="Y" k="105" />
    <hkern u1="&#x13d;" u2="W" k="45" />
    <hkern u1="&#x13d;" u2="V" k="70" />
    <hkern u1="&#x13d;" u2="T" k="75" />
    <hkern u1="&#x13d;" u2="Q" k="20" />
    <hkern u1="&#x13d;" u2="O" k="20" />
    <hkern u1="&#x13d;" u2="G" k="20" />
    <hkern u1="&#x13d;" u2="C" k="20" />
    <hkern u1="&#x13d;" u2="&#x2e;" k="-40" />
    <hkern u1="&#x13d;" u2="&#x2d;" k="100" />
    <hkern u1="&#x13d;" u2="&#x2c;" k="-40" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="80" />
    <hkern u1="&#x13d;" u2="&#x27;" k="125" />
    <hkern u1="&#x13d;" u2="&#x22;" k="125" />
    <hkern u1="&#x141;" u2="&#x2122;" k="65" />
    <hkern u1="&#x141;" u2="&#x2026;" k="-40" />
    <hkern u1="&#x141;" u2="&#x201d;" k="110" />
    <hkern u1="&#x141;" u2="&#x201c;" k="110" />
    <hkern u1="&#x141;" u2="&#x2019;" k="110" />
    <hkern u1="&#x141;" u2="&#x2018;" k="110" />
    <hkern u1="&#x141;" u2="&#x2014;" k="100" />
    <hkern u1="&#x141;" u2="&#x2013;" k="100" />
    <hkern u1="&#x141;" u2="&#x1ef2;" k="105" />
    <hkern u1="&#x141;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e84;" k="45" />
    <hkern u1="&#x141;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e82;" k="45" />
    <hkern u1="&#x141;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e80;" k="45" />
    <hkern u1="&#x141;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x141;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x141;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x141;" u2="&#x178;" k="105" />
    <hkern u1="&#x141;" u2="&#x176;" k="105" />
    <hkern u1="&#x141;" u2="&#x175;" k="30" />
    <hkern u1="&#x141;" u2="&#x174;" k="45" />
    <hkern u1="&#x141;" u2="&#x164;" k="75" />
    <hkern u1="&#x141;" u2="&#x162;" k="75" />
    <hkern u1="&#x141;" u2="&#x152;" k="20" />
    <hkern u1="&#x141;" u2="&#x150;" k="20" />
    <hkern u1="&#x141;" u2="&#x14e;" k="20" />
    <hkern u1="&#x141;" u2="&#x14c;" k="20" />
    <hkern u1="&#x141;" u2="&#x122;" k="20" />
    <hkern u1="&#x141;" u2="&#x120;" k="20" />
    <hkern u1="&#x141;" u2="&#x11e;" k="20" />
    <hkern u1="&#x141;" u2="&#x11c;" k="20" />
    <hkern u1="&#x141;" u2="&#x10c;" k="20" />
    <hkern u1="&#x141;" u2="&#x10a;" k="20" />
    <hkern u1="&#x141;" u2="&#x108;" k="20" />
    <hkern u1="&#x141;" u2="&#x106;" k="20" />
    <hkern u1="&#x141;" u2="&#x105;" k="-30" />
    <hkern u1="&#x141;" u2="&#x103;" k="-30" />
    <hkern u1="&#x141;" u2="&#x101;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x141;" u2="&#xdd;" k="105" />
    <hkern u1="&#x141;" u2="&#xd8;" k="20" />
    <hkern u1="&#x141;" u2="&#xd6;" k="20" />
    <hkern u1="&#x141;" u2="&#xd5;" k="20" />
    <hkern u1="&#x141;" u2="&#xd4;" k="20" />
    <hkern u1="&#x141;" u2="&#xd3;" k="20" />
    <hkern u1="&#x141;" u2="&#xd2;" k="20" />
    <hkern u1="&#x141;" u2="&#xc7;" k="20" />
    <hkern u1="&#x141;" u2="&#xa9;" k="20" />
    <hkern u1="&#x141;" u2="w" k="30" />
    <hkern u1="&#x141;" u2="v" k="40" />
    <hkern u1="&#x141;" u2="a" k="-30" />
    <hkern u1="&#x141;" u2="_" k="-50" />
    <hkern u1="&#x141;" u2="Y" k="105" />
    <hkern u1="&#x141;" u2="W" k="45" />
    <hkern u1="&#x141;" u2="V" k="70" />
    <hkern u1="&#x141;" u2="T" k="75" />
    <hkern u1="&#x141;" u2="Q" k="20" />
    <hkern u1="&#x141;" u2="O" k="20" />
    <hkern u1="&#x141;" u2="G" k="20" />
    <hkern u1="&#x141;" u2="C" k="20" />
    <hkern u1="&#x141;" u2="&#x2e;" k="-40" />
    <hkern u1="&#x141;" u2="&#x2d;" k="100" />
    <hkern u1="&#x141;" u2="&#x2c;" k="-40" />
    <hkern u1="&#x141;" u2="&#x2a;" k="80" />
    <hkern u1="&#x141;" u2="&#x27;" k="125" />
    <hkern u1="&#x141;" u2="&#x22;" k="125" />
    <hkern u1="&#x144;" u2="&#x164;" k="70" />
    <hkern u1="&#x144;" u2="&#x162;" k="70" />
    <hkern u1="&#x144;" u2="T" k="70" />
    <hkern u1="&#x146;" u2="&#x164;" k="70" />
    <hkern u1="&#x146;" u2="&#x162;" k="70" />
    <hkern u1="&#x146;" u2="T" k="70" />
    <hkern u1="&#x148;" u2="&#x164;" k="70" />
    <hkern u1="&#x148;" u2="&#x162;" k="70" />
    <hkern u1="&#x148;" u2="T" k="70" />
    <hkern u1="&#x14b;" u2="&#x164;" k="70" />
    <hkern u1="&#x14b;" u2="&#x162;" k="70" />
    <hkern u1="&#x14b;" u2="T" k="70" />
    <hkern u1="&#x14c;" u2="&#x2026;" k="25" />
    <hkern u1="&#x14c;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x164;" k="15" />
    <hkern u1="&#x14c;" u2="&#x162;" k="15" />
    <hkern u1="&#x14c;" u2="&#x152;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x150;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x122;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x120;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x108;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x106;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x14c;" u2="X" k="10" />
    <hkern u1="&#x14c;" u2="T" k="15" />
    <hkern u1="&#x14c;" u2="Q" k="-5" />
    <hkern u1="&#x14c;" u2="O" k="-5" />
    <hkern u1="&#x14c;" u2="G" k="-5" />
    <hkern u1="&#x14c;" u2="C" k="-5" />
    <hkern u1="&#x14c;" u2="&#x2e;" k="25" />
    <hkern u1="&#x14c;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x2c;" k="25" />
    <hkern u1="&#x14d;" u2="&#x2026;" k="20" />
    <hkern u1="&#x14d;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x14d;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x14d;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x14d;" u2="&#x174;" k="10" />
    <hkern u1="&#x14d;" u2="&#x164;" k="75" />
    <hkern u1="&#x14d;" u2="&#x162;" k="75" />
    <hkern u1="&#x14d;" u2="z" k="20" />
    <hkern u1="&#x14d;" u2="x" k="10" />
    <hkern u1="&#x14d;" u2="W" k="10" />
    <hkern u1="&#x14d;" u2="V" k="20" />
    <hkern u1="&#x14d;" u2="T" k="75" />
    <hkern u1="&#x14d;" u2="&#x2e;" k="20" />
    <hkern u1="&#x14d;" u2="&#x2c;" k="20" />
    <hkern u1="&#x14e;" u2="&#x2026;" k="25" />
    <hkern u1="&#x14e;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x164;" k="15" />
    <hkern u1="&#x14e;" u2="&#x162;" k="15" />
    <hkern u1="&#x14e;" u2="&#x152;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x150;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x122;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x120;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x108;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x106;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x14e;" u2="X" k="10" />
    <hkern u1="&#x14e;" u2="T" k="15" />
    <hkern u1="&#x14e;" u2="Q" k="-5" />
    <hkern u1="&#x14e;" u2="O" k="-5" />
    <hkern u1="&#x14e;" u2="G" k="-5" />
    <hkern u1="&#x14e;" u2="C" k="-5" />
    <hkern u1="&#x14e;" u2="&#x2e;" k="25" />
    <hkern u1="&#x14e;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x2c;" k="25" />
    <hkern u1="&#x14f;" u2="&#x2026;" k="20" />
    <hkern u1="&#x14f;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x14f;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x14f;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x14f;" u2="&#x174;" k="10" />
    <hkern u1="&#x14f;" u2="&#x164;" k="75" />
    <hkern u1="&#x14f;" u2="&#x162;" k="75" />
    <hkern u1="&#x14f;" u2="z" k="20" />
    <hkern u1="&#x14f;" u2="x" k="10" />
    <hkern u1="&#x14f;" u2="W" k="10" />
    <hkern u1="&#x14f;" u2="V" k="20" />
    <hkern u1="&#x14f;" u2="T" k="75" />
    <hkern u1="&#x14f;" u2="&#x2e;" k="20" />
    <hkern u1="&#x14f;" u2="&#x2c;" k="20" />
    <hkern u1="&#x150;" u2="&#x2026;" k="25" />
    <hkern u1="&#x150;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x150;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x150;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x150;" u2="&#x164;" k="15" />
    <hkern u1="&#x150;" u2="&#x162;" k="15" />
    <hkern u1="&#x150;" u2="&#x152;" k="-5" />
    <hkern u1="&#x150;" u2="&#x150;" k="-5" />
    <hkern u1="&#x150;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x150;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x122;" k="-5" />
    <hkern u1="&#x150;" u2="&#x120;" k="-5" />
    <hkern u1="&#x150;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x150;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x150;" u2="&#x108;" k="-5" />
    <hkern u1="&#x150;" u2="&#x106;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x150;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x150;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x150;" u2="X" k="10" />
    <hkern u1="&#x150;" u2="T" k="15" />
    <hkern u1="&#x150;" u2="Q" k="-5" />
    <hkern u1="&#x150;" u2="O" k="-5" />
    <hkern u1="&#x150;" u2="G" k="-5" />
    <hkern u1="&#x150;" u2="C" k="-5" />
    <hkern u1="&#x150;" u2="&#x2e;" k="25" />
    <hkern u1="&#x150;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x150;" u2="&#x2c;" k="25" />
    <hkern u1="&#x151;" u2="&#x2026;" k="20" />
    <hkern u1="&#x151;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x151;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x151;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x151;" u2="&#x174;" k="10" />
    <hkern u1="&#x151;" u2="&#x164;" k="75" />
    <hkern u1="&#x151;" u2="&#x162;" k="75" />
    <hkern u1="&#x151;" u2="z" k="20" />
    <hkern u1="&#x151;" u2="x" k="10" />
    <hkern u1="&#x151;" u2="W" k="10" />
    <hkern u1="&#x151;" u2="V" k="20" />
    <hkern u1="&#x151;" u2="T" k="75" />
    <hkern u1="&#x151;" u2="&#x2e;" k="20" />
    <hkern u1="&#x151;" u2="&#x2c;" k="20" />
    <hkern u1="&#x152;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x152;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x152;" u2="&#x152;" k="7" />
    <hkern u1="&#x152;" u2="&#x150;" k="7" />
    <hkern u1="&#x152;" u2="&#x14e;" k="7" />
    <hkern u1="&#x152;" u2="&#x14c;" k="7" />
    <hkern u1="&#x152;" u2="&#x122;" k="7" />
    <hkern u1="&#x152;" u2="&#x120;" k="7" />
    <hkern u1="&#x152;" u2="&#x11e;" k="7" />
    <hkern u1="&#x152;" u2="&#x11c;" k="7" />
    <hkern u1="&#x152;" u2="&#x10c;" k="7" />
    <hkern u1="&#x152;" u2="&#x10a;" k="7" />
    <hkern u1="&#x152;" u2="&#x108;" k="7" />
    <hkern u1="&#x152;" u2="&#x106;" k="7" />
    <hkern u1="&#x152;" u2="&#xd8;" k="7" />
    <hkern u1="&#x152;" u2="&#xd6;" k="7" />
    <hkern u1="&#x152;" u2="&#xd5;" k="7" />
    <hkern u1="&#x152;" u2="&#xd4;" k="7" />
    <hkern u1="&#x152;" u2="&#xd3;" k="7" />
    <hkern u1="&#x152;" u2="&#xd2;" k="7" />
    <hkern u1="&#x152;" u2="&#xc7;" k="7" />
    <hkern u1="&#x152;" u2="&#xa9;" k="7" />
    <hkern u1="&#x152;" u2="Q" k="7" />
    <hkern u1="&#x152;" u2="O" k="7" />
    <hkern u1="&#x152;" u2="G" k="7" />
    <hkern u1="&#x152;" u2="C" k="7" />
    <hkern u1="&#x152;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x152;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x153;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x153;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x153;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x153;" u2="&#x219;" k="-5" />
    <hkern u1="&#x153;" u2="&#x174;" k="10" />
    <hkern u1="&#x153;" u2="&#x164;" k="70" />
    <hkern u1="&#x153;" u2="&#x162;" k="70" />
    <hkern u1="&#x153;" u2="&#x161;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x153;" u2="s" k="-5" />
    <hkern u1="&#x153;" u2="W" k="10" />
    <hkern u1="&#x153;" u2="V" k="20" />
    <hkern u1="&#x153;" u2="T" k="70" />
    <hkern u1="&#x154;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x154;" u2="&#x2014;" k="30" />
    <hkern u1="&#x154;" u2="&#x2013;" k="30" />
    <hkern u1="&#x154;" u2="&#x164;" k="25" />
    <hkern u1="&#x154;" u2="&#x162;" k="25" />
    <hkern u1="&#x154;" u2="T" k="25" />
    <hkern u1="&#x154;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x154;" u2="&#x2d;" k="30" />
    <hkern u1="&#x154;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x155;" g2="fl" k="-20" />
    <hkern u1="&#x155;" g2="fi" k="-20" />
    <hkern u1="&#x155;" u2="&#x2026;" k="115" />
    <hkern u1="&#x155;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x155;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x155;" u2="&#x2014;" k="60" />
    <hkern u1="&#x155;" u2="&#x2013;" k="60" />
    <hkern u1="&#x155;" u2="&#x1e85;" k="-30" />
    <hkern u1="&#x155;" u2="&#x1e83;" k="-30" />
    <hkern u1="&#x155;" u2="&#x1e81;" k="-30" />
    <hkern u1="&#x155;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x155;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x155;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x155;" u2="&#x17f;" k="-20" />
    <hkern u1="&#x155;" u2="&#x175;" k="-30" />
    <hkern u1="&#x155;" u2="&#x167;" k="-20" />
    <hkern u1="&#x155;" u2="&#x165;" k="-20" />
    <hkern u1="&#x155;" u2="&#x164;" k="30" />
    <hkern u1="&#x155;" u2="&#x163;" k="-20" />
    <hkern u1="&#x155;" u2="&#x162;" k="30" />
    <hkern u1="&#x155;" u2="&#x153;" k="20" />
    <hkern u1="&#x155;" u2="&#x151;" k="20" />
    <hkern u1="&#x155;" u2="&#x14f;" k="20" />
    <hkern u1="&#x155;" u2="&#x14d;" k="20" />
    <hkern u1="&#x155;" u2="&#x123;" k="20" />
    <hkern u1="&#x155;" u2="&#x121;" k="20" />
    <hkern u1="&#x155;" u2="&#x11f;" k="20" />
    <hkern u1="&#x155;" u2="&#x11d;" k="20" />
    <hkern u1="&#x155;" u2="&#x11b;" k="20" />
    <hkern u1="&#x155;" u2="&#x119;" k="20" />
    <hkern u1="&#x155;" u2="&#x117;" k="20" />
    <hkern u1="&#x155;" u2="&#x115;" k="20" />
    <hkern u1="&#x155;" u2="&#x113;" k="20" />
    <hkern u1="&#x155;" u2="&#x111;" k="20" />
    <hkern u1="&#x155;" u2="&#x10f;" k="20" />
    <hkern u1="&#x155;" u2="&#x10d;" k="20" />
    <hkern u1="&#x155;" u2="&#x10b;" k="20" />
    <hkern u1="&#x155;" u2="&#x109;" k="20" />
    <hkern u1="&#x155;" u2="&#x107;" k="20" />
    <hkern u1="&#x155;" u2="&#x105;" k="10" />
    <hkern u1="&#x155;" u2="&#x103;" k="10" />
    <hkern u1="&#x155;" u2="&#x101;" k="10" />
    <hkern u1="&#x155;" u2="&#xf8;" k="20" />
    <hkern u1="&#x155;" u2="&#xf6;" k="20" />
    <hkern u1="&#x155;" u2="&#xf5;" k="20" />
    <hkern u1="&#x155;" u2="&#xf4;" k="20" />
    <hkern u1="&#x155;" u2="&#xf3;" k="20" />
    <hkern u1="&#x155;" u2="&#xf2;" k="20" />
    <hkern u1="&#x155;" u2="&#xf0;" k="20" />
    <hkern u1="&#x155;" u2="&#xeb;" k="20" />
    <hkern u1="&#x155;" u2="&#xea;" k="20" />
    <hkern u1="&#x155;" u2="&#xe9;" k="20" />
    <hkern u1="&#x155;" u2="&#xe8;" k="20" />
    <hkern u1="&#x155;" u2="&#xe7;" k="20" />
    <hkern u1="&#x155;" u2="&#xe6;" k="10" />
    <hkern u1="&#x155;" u2="&#xe5;" k="10" />
    <hkern u1="&#x155;" u2="&#xe4;" k="10" />
    <hkern u1="&#x155;" u2="&#xe3;" k="10" />
    <hkern u1="&#x155;" u2="&#xe2;" k="10" />
    <hkern u1="&#x155;" u2="&#xe1;" k="10" />
    <hkern u1="&#x155;" u2="&#xe0;" k="10" />
    <hkern u1="&#x155;" u2="x" k="-10" />
    <hkern u1="&#x155;" u2="w" k="-30" />
    <hkern u1="&#x155;" u2="v" k="-30" />
    <hkern u1="&#x155;" u2="t" k="-20" />
    <hkern u1="&#x155;" u2="q" k="20" />
    <hkern u1="&#x155;" u2="o" k="20" />
    <hkern u1="&#x155;" u2="g" k="20" />
    <hkern u1="&#x155;" u2="f" k="-20" />
    <hkern u1="&#x155;" u2="e" k="20" />
    <hkern u1="&#x155;" u2="d" k="20" />
    <hkern u1="&#x155;" u2="c" k="20" />
    <hkern u1="&#x155;" u2="a" k="10" />
    <hkern u1="&#x155;" u2="T" k="30" />
    <hkern u1="&#x155;" u2="&#x2f;" k="50" />
    <hkern u1="&#x155;" u2="&#x2e;" k="115" />
    <hkern u1="&#x155;" u2="&#x2d;" k="60" />
    <hkern u1="&#x155;" u2="&#x2c;" k="115" />
    <hkern u1="&#x156;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x156;" u2="&#x2014;" k="30" />
    <hkern u1="&#x156;" u2="&#x2013;" k="30" />
    <hkern u1="&#x156;" u2="&#x164;" k="25" />
    <hkern u1="&#x156;" u2="&#x162;" k="25" />
    <hkern u1="&#x156;" u2="T" k="25" />
    <hkern u1="&#x156;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x156;" u2="&#x2d;" k="30" />
    <hkern u1="&#x156;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x157;" g2="fl" k="-20" />
    <hkern u1="&#x157;" g2="fi" k="-20" />
    <hkern u1="&#x157;" u2="&#x2026;" k="115" />
    <hkern u1="&#x157;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x157;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x157;" u2="&#x2014;" k="60" />
    <hkern u1="&#x157;" u2="&#x2013;" k="60" />
    <hkern u1="&#x157;" u2="&#x1e85;" k="-30" />
    <hkern u1="&#x157;" u2="&#x1e83;" k="-30" />
    <hkern u1="&#x157;" u2="&#x1e81;" k="-30" />
    <hkern u1="&#x157;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x157;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x157;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x157;" u2="&#x17f;" k="-20" />
    <hkern u1="&#x157;" u2="&#x175;" k="-30" />
    <hkern u1="&#x157;" u2="&#x167;" k="-20" />
    <hkern u1="&#x157;" u2="&#x165;" k="-20" />
    <hkern u1="&#x157;" u2="&#x164;" k="30" />
    <hkern u1="&#x157;" u2="&#x163;" k="-20" />
    <hkern u1="&#x157;" u2="&#x162;" k="30" />
    <hkern u1="&#x157;" u2="&#x153;" k="20" />
    <hkern u1="&#x157;" u2="&#x151;" k="20" />
    <hkern u1="&#x157;" u2="&#x14f;" k="20" />
    <hkern u1="&#x157;" u2="&#x14d;" k="20" />
    <hkern u1="&#x157;" u2="&#x123;" k="20" />
    <hkern u1="&#x157;" u2="&#x121;" k="20" />
    <hkern u1="&#x157;" u2="&#x11f;" k="20" />
    <hkern u1="&#x157;" u2="&#x11d;" k="20" />
    <hkern u1="&#x157;" u2="&#x11b;" k="20" />
    <hkern u1="&#x157;" u2="&#x119;" k="20" />
    <hkern u1="&#x157;" u2="&#x117;" k="20" />
    <hkern u1="&#x157;" u2="&#x115;" k="20" />
    <hkern u1="&#x157;" u2="&#x113;" k="20" />
    <hkern u1="&#x157;" u2="&#x111;" k="20" />
    <hkern u1="&#x157;" u2="&#x10f;" k="20" />
    <hkern u1="&#x157;" u2="&#x10d;" k="20" />
    <hkern u1="&#x157;" u2="&#x10b;" k="20" />
    <hkern u1="&#x157;" u2="&#x109;" k="20" />
    <hkern u1="&#x157;" u2="&#x107;" k="20" />
    <hkern u1="&#x157;" u2="&#x105;" k="10" />
    <hkern u1="&#x157;" u2="&#x103;" k="10" />
    <hkern u1="&#x157;" u2="&#x101;" k="10" />
    <hkern u1="&#x157;" u2="&#xf8;" k="20" />
    <hkern u1="&#x157;" u2="&#xf6;" k="20" />
    <hkern u1="&#x157;" u2="&#xf5;" k="20" />
    <hkern u1="&#x157;" u2="&#xf4;" k="20" />
    <hkern u1="&#x157;" u2="&#xf3;" k="20" />
    <hkern u1="&#x157;" u2="&#xf2;" k="20" />
    <hkern u1="&#x157;" u2="&#xf0;" k="20" />
    <hkern u1="&#x157;" u2="&#xeb;" k="20" />
    <hkern u1="&#x157;" u2="&#xea;" k="20" />
    <hkern u1="&#x157;" u2="&#xe9;" k="20" />
    <hkern u1="&#x157;" u2="&#xe8;" k="20" />
    <hkern u1="&#x157;" u2="&#xe7;" k="20" />
    <hkern u1="&#x157;" u2="&#xe6;" k="10" />
    <hkern u1="&#x157;" u2="&#xe5;" k="10" />
    <hkern u1="&#x157;" u2="&#xe4;" k="10" />
    <hkern u1="&#x157;" u2="&#xe3;" k="10" />
    <hkern u1="&#x157;" u2="&#xe2;" k="10" />
    <hkern u1="&#x157;" u2="&#xe1;" k="10" />
    <hkern u1="&#x157;" u2="&#xe0;" k="10" />
    <hkern u1="&#x157;" u2="x" k="-10" />
    <hkern u1="&#x157;" u2="w" k="-30" />
    <hkern u1="&#x157;" u2="v" k="-30" />
    <hkern u1="&#x157;" u2="t" k="-20" />
    <hkern u1="&#x157;" u2="q" k="20" />
    <hkern u1="&#x157;" u2="o" k="20" />
    <hkern u1="&#x157;" u2="g" k="20" />
    <hkern u1="&#x157;" u2="f" k="-20" />
    <hkern u1="&#x157;" u2="e" k="20" />
    <hkern u1="&#x157;" u2="d" k="20" />
    <hkern u1="&#x157;" u2="c" k="20" />
    <hkern u1="&#x157;" u2="a" k="10" />
    <hkern u1="&#x157;" u2="T" k="30" />
    <hkern u1="&#x157;" u2="&#x2f;" k="50" />
    <hkern u1="&#x157;" u2="&#x2e;" k="115" />
    <hkern u1="&#x157;" u2="&#x2d;" k="60" />
    <hkern u1="&#x157;" u2="&#x2c;" k="115" />
    <hkern u1="&#x158;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x158;" u2="&#x2014;" k="30" />
    <hkern u1="&#x158;" u2="&#x2013;" k="30" />
    <hkern u1="&#x158;" u2="&#x164;" k="25" />
    <hkern u1="&#x158;" u2="&#x162;" k="25" />
    <hkern u1="&#x158;" u2="T" k="25" />
    <hkern u1="&#x158;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x158;" u2="&#x2d;" k="30" />
    <hkern u1="&#x158;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x159;" g2="fl" k="-20" />
    <hkern u1="&#x159;" g2="fi" k="-20" />
    <hkern u1="&#x159;" u2="&#x2026;" k="115" />
    <hkern u1="&#x159;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x159;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x159;" u2="&#x2014;" k="60" />
    <hkern u1="&#x159;" u2="&#x2013;" k="60" />
    <hkern u1="&#x159;" u2="&#x1e85;" k="-30" />
    <hkern u1="&#x159;" u2="&#x1e83;" k="-30" />
    <hkern u1="&#x159;" u2="&#x1e81;" k="-30" />
    <hkern u1="&#x159;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x159;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x159;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x159;" u2="&#x17f;" k="-20" />
    <hkern u1="&#x159;" u2="&#x175;" k="-30" />
    <hkern u1="&#x159;" u2="&#x167;" k="-20" />
    <hkern u1="&#x159;" u2="&#x165;" k="-20" />
    <hkern u1="&#x159;" u2="&#x164;" k="30" />
    <hkern u1="&#x159;" u2="&#x163;" k="-20" />
    <hkern u1="&#x159;" u2="&#x162;" k="30" />
    <hkern u1="&#x159;" u2="&#x153;" k="20" />
    <hkern u1="&#x159;" u2="&#x151;" k="20" />
    <hkern u1="&#x159;" u2="&#x14f;" k="20" />
    <hkern u1="&#x159;" u2="&#x14d;" k="20" />
    <hkern u1="&#x159;" u2="&#x123;" k="20" />
    <hkern u1="&#x159;" u2="&#x121;" k="20" />
    <hkern u1="&#x159;" u2="&#x11f;" k="20" />
    <hkern u1="&#x159;" u2="&#x11d;" k="20" />
    <hkern u1="&#x159;" u2="&#x11b;" k="20" />
    <hkern u1="&#x159;" u2="&#x119;" k="20" />
    <hkern u1="&#x159;" u2="&#x117;" k="20" />
    <hkern u1="&#x159;" u2="&#x115;" k="20" />
    <hkern u1="&#x159;" u2="&#x113;" k="20" />
    <hkern u1="&#x159;" u2="&#x111;" k="20" />
    <hkern u1="&#x159;" u2="&#x10f;" k="20" />
    <hkern u1="&#x159;" u2="&#x10d;" k="20" />
    <hkern u1="&#x159;" u2="&#x10b;" k="20" />
    <hkern u1="&#x159;" u2="&#x109;" k="20" />
    <hkern u1="&#x159;" u2="&#x107;" k="20" />
    <hkern u1="&#x159;" u2="&#x105;" k="10" />
    <hkern u1="&#x159;" u2="&#x103;" k="10" />
    <hkern u1="&#x159;" u2="&#x101;" k="10" />
    <hkern u1="&#x159;" u2="&#xf8;" k="20" />
    <hkern u1="&#x159;" u2="&#xf6;" k="20" />
    <hkern u1="&#x159;" u2="&#xf5;" k="20" />
    <hkern u1="&#x159;" u2="&#xf4;" k="20" />
    <hkern u1="&#x159;" u2="&#xf3;" k="20" />
    <hkern u1="&#x159;" u2="&#xf2;" k="20" />
    <hkern u1="&#x159;" u2="&#xf0;" k="20" />
    <hkern u1="&#x159;" u2="&#xeb;" k="20" />
    <hkern u1="&#x159;" u2="&#xea;" k="20" />
    <hkern u1="&#x159;" u2="&#xe9;" k="20" />
    <hkern u1="&#x159;" u2="&#xe8;" k="20" />
    <hkern u1="&#x159;" u2="&#xe7;" k="20" />
    <hkern u1="&#x159;" u2="&#xe6;" k="10" />
    <hkern u1="&#x159;" u2="&#xe5;" k="10" />
    <hkern u1="&#x159;" u2="&#xe4;" k="10" />
    <hkern u1="&#x159;" u2="&#xe3;" k="10" />
    <hkern u1="&#x159;" u2="&#xe2;" k="10" />
    <hkern u1="&#x159;" u2="&#xe1;" k="10" />
    <hkern u1="&#x159;" u2="&#xe0;" k="10" />
    <hkern u1="&#x159;" u2="x" k="-10" />
    <hkern u1="&#x159;" u2="w" k="-30" />
    <hkern u1="&#x159;" u2="v" k="-30" />
    <hkern u1="&#x159;" u2="t" k="-20" />
    <hkern u1="&#x159;" u2="q" k="20" />
    <hkern u1="&#x159;" u2="o" k="20" />
    <hkern u1="&#x159;" u2="g" k="20" />
    <hkern u1="&#x159;" u2="f" k="-20" />
    <hkern u1="&#x159;" u2="e" k="20" />
    <hkern u1="&#x159;" u2="d" k="20" />
    <hkern u1="&#x159;" u2="c" k="20" />
    <hkern u1="&#x159;" u2="a" k="10" />
    <hkern u1="&#x159;" u2="T" k="30" />
    <hkern u1="&#x159;" u2="&#x2f;" k="50" />
    <hkern u1="&#x159;" u2="&#x2e;" k="115" />
    <hkern u1="&#x159;" u2="&#x2d;" k="60" />
    <hkern u1="&#x159;" u2="&#x2c;" k="115" />
    <hkern u1="&#x15a;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x15a;" u2="&#x218;" k="25" />
    <hkern u1="&#x15a;" u2="&#x178;" k="40" />
    <hkern u1="&#x15a;" u2="&#x176;" k="40" />
    <hkern u1="&#x15a;" u2="&#x164;" k="10" />
    <hkern u1="&#x15a;" u2="&#x162;" k="10" />
    <hkern u1="&#x15a;" u2="&#x160;" k="25" />
    <hkern u1="&#x15a;" u2="&#x15e;" k="25" />
    <hkern u1="&#x15a;" u2="&#x15c;" k="25" />
    <hkern u1="&#x15a;" u2="&#x15a;" k="25" />
    <hkern u1="&#x15a;" u2="&#xdd;" k="40" />
    <hkern u1="&#x15a;" u2="Y" k="40" />
    <hkern u1="&#x15a;" u2="V" k="20" />
    <hkern u1="&#x15a;" u2="T" k="10" />
    <hkern u1="&#x15a;" u2="S" k="25" />
    <hkern u1="&#x15b;" u2="&#x219;" k="20" />
    <hkern u1="&#x15b;" u2="&#x164;" k="70" />
    <hkern u1="&#x15b;" u2="&#x162;" k="70" />
    <hkern u1="&#x15b;" u2="&#x161;" k="20" />
    <hkern u1="&#x15b;" u2="&#x15f;" k="20" />
    <hkern u1="&#x15b;" u2="&#x15d;" k="20" />
    <hkern u1="&#x15b;" u2="&#x15b;" k="20" />
    <hkern u1="&#x15b;" u2="s" k="20" />
    <hkern u1="&#x15b;" u2="T" k="70" />
    <hkern u1="&#x15c;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x15c;" u2="&#x218;" k="25" />
    <hkern u1="&#x15c;" u2="&#x178;" k="40" />
    <hkern u1="&#x15c;" u2="&#x176;" k="40" />
    <hkern u1="&#x15c;" u2="&#x164;" k="10" />
    <hkern u1="&#x15c;" u2="&#x162;" k="10" />
    <hkern u1="&#x15c;" u2="&#x160;" k="25" />
    <hkern u1="&#x15c;" u2="&#x15e;" k="25" />
    <hkern u1="&#x15c;" u2="&#x15c;" k="25" />
    <hkern u1="&#x15c;" u2="&#x15a;" k="25" />
    <hkern u1="&#x15c;" u2="&#xdd;" k="40" />
    <hkern u1="&#x15c;" u2="Y" k="40" />
    <hkern u1="&#x15c;" u2="V" k="20" />
    <hkern u1="&#x15c;" u2="T" k="10" />
    <hkern u1="&#x15c;" u2="S" k="25" />
    <hkern u1="&#x15d;" u2="&#x219;" k="20" />
    <hkern u1="&#x15d;" u2="&#x164;" k="70" />
    <hkern u1="&#x15d;" u2="&#x162;" k="70" />
    <hkern u1="&#x15d;" u2="&#x161;" k="20" />
    <hkern u1="&#x15d;" u2="&#x15f;" k="20" />
    <hkern u1="&#x15d;" u2="&#x15d;" k="20" />
    <hkern u1="&#x15d;" u2="&#x15b;" k="20" />
    <hkern u1="&#x15d;" u2="s" k="20" />
    <hkern u1="&#x15d;" u2="T" k="70" />
    <hkern u1="&#x15e;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x15e;" u2="&#x218;" k="25" />
    <hkern u1="&#x15e;" u2="&#x178;" k="40" />
    <hkern u1="&#x15e;" u2="&#x176;" k="40" />
    <hkern u1="&#x15e;" u2="&#x164;" k="10" />
    <hkern u1="&#x15e;" u2="&#x162;" k="10" />
    <hkern u1="&#x15e;" u2="&#x160;" k="25" />
    <hkern u1="&#x15e;" u2="&#x15e;" k="25" />
    <hkern u1="&#x15e;" u2="&#x15c;" k="25" />
    <hkern u1="&#x15e;" u2="&#x15a;" k="25" />
    <hkern u1="&#x15e;" u2="&#xdd;" k="40" />
    <hkern u1="&#x15e;" u2="Y" k="40" />
    <hkern u1="&#x15e;" u2="V" k="20" />
    <hkern u1="&#x15e;" u2="T" k="10" />
    <hkern u1="&#x15e;" u2="S" k="25" />
    <hkern u1="&#x15f;" u2="&#x219;" k="20" />
    <hkern u1="&#x15f;" u2="&#x164;" k="70" />
    <hkern u1="&#x15f;" u2="&#x162;" k="70" />
    <hkern u1="&#x15f;" u2="&#x161;" k="20" />
    <hkern u1="&#x15f;" u2="&#x15f;" k="20" />
    <hkern u1="&#x15f;" u2="&#x15d;" k="20" />
    <hkern u1="&#x15f;" u2="&#x15b;" k="20" />
    <hkern u1="&#x15f;" u2="s" k="20" />
    <hkern u1="&#x15f;" u2="T" k="70" />
    <hkern u1="&#x160;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x160;" u2="&#x218;" k="25" />
    <hkern u1="&#x160;" u2="&#x178;" k="40" />
    <hkern u1="&#x160;" u2="&#x176;" k="40" />
    <hkern u1="&#x160;" u2="&#x164;" k="10" />
    <hkern u1="&#x160;" u2="&#x162;" k="10" />
    <hkern u1="&#x160;" u2="&#x160;" k="25" />
    <hkern u1="&#x160;" u2="&#x15e;" k="25" />
    <hkern u1="&#x160;" u2="&#x15c;" k="25" />
    <hkern u1="&#x160;" u2="&#x15a;" k="25" />
    <hkern u1="&#x160;" u2="&#xdd;" k="40" />
    <hkern u1="&#x160;" u2="Y" k="40" />
    <hkern u1="&#x160;" u2="V" k="20" />
    <hkern u1="&#x160;" u2="T" k="10" />
    <hkern u1="&#x160;" u2="S" k="25" />
    <hkern u1="&#x161;" u2="&#x219;" k="20" />
    <hkern u1="&#x161;" u2="&#x164;" k="70" />
    <hkern u1="&#x161;" u2="&#x162;" k="70" />
    <hkern u1="&#x161;" u2="&#x161;" k="20" />
    <hkern u1="&#x161;" u2="&#x15f;" k="20" />
    <hkern u1="&#x161;" u2="&#x15d;" k="20" />
    <hkern u1="&#x161;" u2="&#x15b;" k="20" />
    <hkern u1="&#x161;" u2="s" k="20" />
    <hkern u1="&#x161;" u2="T" k="70" />
    <hkern u1="&#x162;" u2="&#x2026;" k="110" />
    <hkern u1="&#x162;" u2="&#x2014;" k="110" />
    <hkern u1="&#x162;" u2="&#x2013;" k="110" />
    <hkern u1="&#x162;" u2="&#x1ef3;" k="105" />
    <hkern u1="&#x162;" u2="&#x1e85;" k="55" />
    <hkern u1="&#x162;" u2="&#x1e83;" k="55" />
    <hkern u1="&#x162;" u2="&#x1e81;" k="55" />
    <hkern u1="&#x162;" u2="&#x219;" k="90" />
    <hkern u1="&#x162;" u2="&#x1ff;" k="75" />
    <hkern u1="&#x162;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x162;" u2="&#x1fd;" k="95" />
    <hkern u1="&#x162;" u2="&#x1fb;" k="95" />
    <hkern u1="&#x162;" u2="&#x177;" k="105" />
    <hkern u1="&#x162;" u2="&#x175;" k="55" />
    <hkern u1="&#x162;" u2="&#x173;" k="105" />
    <hkern u1="&#x162;" u2="&#x171;" k="105" />
    <hkern u1="&#x162;" u2="&#x16f;" k="105" />
    <hkern u1="&#x162;" u2="&#x16d;" k="105" />
    <hkern u1="&#x162;" u2="&#x16b;" k="105" />
    <hkern u1="&#x162;" u2="&#x169;" k="105" />
    <hkern u1="&#x162;" u2="&#x161;" k="90" />
    <hkern u1="&#x162;" u2="&#x15f;" k="90" />
    <hkern u1="&#x162;" u2="&#x15d;" k="90" />
    <hkern u1="&#x162;" u2="&#x15b;" k="90" />
    <hkern u1="&#x162;" u2="&#x159;" k="105" />
    <hkern u1="&#x162;" u2="&#x157;" k="105" />
    <hkern u1="&#x162;" u2="&#x155;" k="105" />
    <hkern u1="&#x162;" u2="&#x153;" k="75" />
    <hkern u1="&#x162;" u2="&#x152;" k="15" />
    <hkern u1="&#x162;" u2="&#x151;" k="75" />
    <hkern u1="&#x162;" u2="&#x150;" k="15" />
    <hkern u1="&#x162;" u2="&#x14f;" k="75" />
    <hkern u1="&#x162;" u2="&#x14e;" k="15" />
    <hkern u1="&#x162;" u2="&#x14d;" k="75" />
    <hkern u1="&#x162;" u2="&#x14c;" k="15" />
    <hkern u1="&#x162;" u2="&#x14b;" k="105" />
    <hkern u1="&#x162;" u2="&#x148;" k="105" />
    <hkern u1="&#x162;" u2="&#x146;" k="105" />
    <hkern u1="&#x162;" u2="&#x144;" k="105" />
    <hkern u1="&#x162;" u2="&#x123;" k="75" />
    <hkern u1="&#x162;" u2="&#x122;" k="15" />
    <hkern u1="&#x162;" u2="&#x121;" k="75" />
    <hkern u1="&#x162;" u2="&#x120;" k="15" />
    <hkern u1="&#x162;" u2="&#x11f;" k="75" />
    <hkern u1="&#x162;" u2="&#x11e;" k="15" />
    <hkern u1="&#x162;" u2="&#x11d;" k="75" />
    <hkern u1="&#x162;" u2="&#x11c;" k="15" />
    <hkern u1="&#x162;" u2="&#x11b;" k="75" />
    <hkern u1="&#x162;" u2="&#x119;" k="75" />
    <hkern u1="&#x162;" u2="&#x117;" k="75" />
    <hkern u1="&#x162;" u2="&#x115;" k="75" />
    <hkern u1="&#x162;" u2="&#x113;" k="75" />
    <hkern u1="&#x162;" u2="&#x111;" k="75" />
    <hkern u1="&#x162;" u2="&#x10f;" k="75" />
    <hkern u1="&#x162;" u2="&#x10d;" k="75" />
    <hkern u1="&#x162;" u2="&#x10c;" k="15" />
    <hkern u1="&#x162;" u2="&#x10b;" k="75" />
    <hkern u1="&#x162;" u2="&#x10a;" k="15" />
    <hkern u1="&#x162;" u2="&#x109;" k="75" />
    <hkern u1="&#x162;" u2="&#x108;" k="15" />
    <hkern u1="&#x162;" u2="&#x107;" k="75" />
    <hkern u1="&#x162;" u2="&#x106;" k="15" />
    <hkern u1="&#x162;" u2="&#x105;" k="95" />
    <hkern u1="&#x162;" u2="&#x103;" k="95" />
    <hkern u1="&#x162;" u2="&#x101;" k="95" />
    <hkern u1="&#x162;" u2="&#xff;" k="105" />
    <hkern u1="&#x162;" u2="&#xfd;" k="105" />
    <hkern u1="&#x162;" u2="&#xfc;" k="105" />
    <hkern u1="&#x162;" u2="&#xfb;" k="105" />
    <hkern u1="&#x162;" u2="&#xfa;" k="105" />
    <hkern u1="&#x162;" u2="&#xf9;" k="105" />
    <hkern u1="&#x162;" u2="&#xf8;" k="75" />
    <hkern u1="&#x162;" u2="&#xf6;" k="75" />
    <hkern u1="&#x162;" u2="&#xf5;" k="75" />
    <hkern u1="&#x162;" u2="&#xf4;" k="75" />
    <hkern u1="&#x162;" u2="&#xf3;" k="75" />
    <hkern u1="&#x162;" u2="&#xf2;" k="75" />
    <hkern u1="&#x162;" u2="&#xf1;" k="105" />
    <hkern u1="&#x162;" u2="&#xf0;" k="75" />
    <hkern u1="&#x162;" u2="&#xeb;" k="75" />
    <hkern u1="&#x162;" u2="&#xea;" k="75" />
    <hkern u1="&#x162;" u2="&#xe9;" k="75" />
    <hkern u1="&#x162;" u2="&#xe8;" k="75" />
    <hkern u1="&#x162;" u2="&#xe7;" k="75" />
    <hkern u1="&#x162;" u2="&#xe6;" k="95" />
    <hkern u1="&#x162;" u2="&#xe5;" k="95" />
    <hkern u1="&#x162;" u2="&#xe4;" k="95" />
    <hkern u1="&#x162;" u2="&#xe3;" k="95" />
    <hkern u1="&#x162;" u2="&#xe2;" k="95" />
    <hkern u1="&#x162;" u2="&#xe1;" k="95" />
    <hkern u1="&#x162;" u2="&#xe0;" k="95" />
    <hkern u1="&#x162;" u2="&#xd8;" k="15" />
    <hkern u1="&#x162;" u2="&#xd6;" k="15" />
    <hkern u1="&#x162;" u2="&#xd5;" k="15" />
    <hkern u1="&#x162;" u2="&#xd4;" k="15" />
    <hkern u1="&#x162;" u2="&#xd3;" k="15" />
    <hkern u1="&#x162;" u2="&#xd2;" k="15" />
    <hkern u1="&#x162;" u2="&#xc7;" k="15" />
    <hkern u1="&#x162;" u2="&#xa9;" k="15" />
    <hkern u1="&#x162;" u2="z" k="80" />
    <hkern u1="&#x162;" u2="y" k="105" />
    <hkern u1="&#x162;" u2="x" k="65" />
    <hkern u1="&#x162;" u2="w" k="55" />
    <hkern u1="&#x162;" u2="v" k="55" />
    <hkern u1="&#x162;" u2="u" k="105" />
    <hkern u1="&#x162;" u2="s" k="90" />
    <hkern u1="&#x162;" u2="r" k="105" />
    <hkern u1="&#x162;" u2="q" k="75" />
    <hkern u1="&#x162;" u2="p" k="105" />
    <hkern u1="&#x162;" u2="o" k="75" />
    <hkern u1="&#x162;" u2="n" k="105" />
    <hkern u1="&#x162;" u2="m" k="105" />
    <hkern u1="&#x162;" u2="g" k="75" />
    <hkern u1="&#x162;" u2="e" k="75" />
    <hkern u1="&#x162;" u2="d" k="75" />
    <hkern u1="&#x162;" u2="c" k="75" />
    <hkern u1="&#x162;" u2="a" k="95" />
    <hkern u1="&#x162;" u2="_" k="45" />
    <hkern u1="&#x162;" u2="Q" k="15" />
    <hkern u1="&#x162;" u2="O" k="15" />
    <hkern u1="&#x162;" u2="G" k="15" />
    <hkern u1="&#x162;" u2="C" k="15" />
    <hkern u1="&#x162;" u2="A" k="35" />
    <hkern u1="&#x162;" u2="&#x2f;" k="85" />
    <hkern u1="&#x162;" u2="&#x2e;" k="110" />
    <hkern u1="&#x162;" u2="&#x2d;" k="110" />
    <hkern u1="&#x162;" u2="&#x2c;" k="110" />
    <hkern u1="&#x164;" u2="&#x2026;" k="110" />
    <hkern u1="&#x164;" u2="&#x2014;" k="110" />
    <hkern u1="&#x164;" u2="&#x2013;" k="110" />
    <hkern u1="&#x164;" u2="&#x1ef3;" k="105" />
    <hkern u1="&#x164;" u2="&#x1e85;" k="55" />
    <hkern u1="&#x164;" u2="&#x1e83;" k="55" />
    <hkern u1="&#x164;" u2="&#x1e81;" k="55" />
    <hkern u1="&#x164;" u2="&#x219;" k="90" />
    <hkern u1="&#x164;" u2="&#x1ff;" k="75" />
    <hkern u1="&#x164;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x164;" u2="&#x1fd;" k="95" />
    <hkern u1="&#x164;" u2="&#x1fb;" k="95" />
    <hkern u1="&#x164;" u2="&#x177;" k="105" />
    <hkern u1="&#x164;" u2="&#x175;" k="55" />
    <hkern u1="&#x164;" u2="&#x173;" k="105" />
    <hkern u1="&#x164;" u2="&#x171;" k="105" />
    <hkern u1="&#x164;" u2="&#x16f;" k="105" />
    <hkern u1="&#x164;" u2="&#x16d;" k="105" />
    <hkern u1="&#x164;" u2="&#x16b;" k="105" />
    <hkern u1="&#x164;" u2="&#x169;" k="105" />
    <hkern u1="&#x164;" u2="&#x161;" k="90" />
    <hkern u1="&#x164;" u2="&#x15f;" k="90" />
    <hkern u1="&#x164;" u2="&#x15d;" k="90" />
    <hkern u1="&#x164;" u2="&#x15b;" k="90" />
    <hkern u1="&#x164;" u2="&#x159;" k="105" />
    <hkern u1="&#x164;" u2="&#x157;" k="105" />
    <hkern u1="&#x164;" u2="&#x155;" k="105" />
    <hkern u1="&#x164;" u2="&#x153;" k="75" />
    <hkern u1="&#x164;" u2="&#x152;" k="15" />
    <hkern u1="&#x164;" u2="&#x151;" k="75" />
    <hkern u1="&#x164;" u2="&#x150;" k="15" />
    <hkern u1="&#x164;" u2="&#x14f;" k="75" />
    <hkern u1="&#x164;" u2="&#x14e;" k="15" />
    <hkern u1="&#x164;" u2="&#x14d;" k="75" />
    <hkern u1="&#x164;" u2="&#x14c;" k="15" />
    <hkern u1="&#x164;" u2="&#x14b;" k="105" />
    <hkern u1="&#x164;" u2="&#x148;" k="105" />
    <hkern u1="&#x164;" u2="&#x146;" k="105" />
    <hkern u1="&#x164;" u2="&#x144;" k="105" />
    <hkern u1="&#x164;" u2="&#x123;" k="75" />
    <hkern u1="&#x164;" u2="&#x122;" k="15" />
    <hkern u1="&#x164;" u2="&#x121;" k="75" />
    <hkern u1="&#x164;" u2="&#x120;" k="15" />
    <hkern u1="&#x164;" u2="&#x11f;" k="75" />
    <hkern u1="&#x164;" u2="&#x11e;" k="15" />
    <hkern u1="&#x164;" u2="&#x11d;" k="75" />
    <hkern u1="&#x164;" u2="&#x11c;" k="15" />
    <hkern u1="&#x164;" u2="&#x11b;" k="75" />
    <hkern u1="&#x164;" u2="&#x119;" k="75" />
    <hkern u1="&#x164;" u2="&#x117;" k="75" />
    <hkern u1="&#x164;" u2="&#x115;" k="75" />
    <hkern u1="&#x164;" u2="&#x113;" k="75" />
    <hkern u1="&#x164;" u2="&#x111;" k="75" />
    <hkern u1="&#x164;" u2="&#x10f;" k="75" />
    <hkern u1="&#x164;" u2="&#x10d;" k="75" />
    <hkern u1="&#x164;" u2="&#x10c;" k="15" />
    <hkern u1="&#x164;" u2="&#x10b;" k="75" />
    <hkern u1="&#x164;" u2="&#x10a;" k="15" />
    <hkern u1="&#x164;" u2="&#x109;" k="75" />
    <hkern u1="&#x164;" u2="&#x108;" k="15" />
    <hkern u1="&#x164;" u2="&#x107;" k="75" />
    <hkern u1="&#x164;" u2="&#x106;" k="15" />
    <hkern u1="&#x164;" u2="&#x105;" k="95" />
    <hkern u1="&#x164;" u2="&#x103;" k="95" />
    <hkern u1="&#x164;" u2="&#x101;" k="95" />
    <hkern u1="&#x164;" u2="&#xff;" k="105" />
    <hkern u1="&#x164;" u2="&#xfd;" k="105" />
    <hkern u1="&#x164;" u2="&#xfc;" k="105" />
    <hkern u1="&#x164;" u2="&#xfb;" k="105" />
    <hkern u1="&#x164;" u2="&#xfa;" k="105" />
    <hkern u1="&#x164;" u2="&#xf9;" k="105" />
    <hkern u1="&#x164;" u2="&#xf8;" k="75" />
    <hkern u1="&#x164;" u2="&#xf6;" k="75" />
    <hkern u1="&#x164;" u2="&#xf5;" k="75" />
    <hkern u1="&#x164;" u2="&#xf4;" k="75" />
    <hkern u1="&#x164;" u2="&#xf3;" k="75" />
    <hkern u1="&#x164;" u2="&#xf2;" k="75" />
    <hkern u1="&#x164;" u2="&#xf1;" k="105" />
    <hkern u1="&#x164;" u2="&#xf0;" k="75" />
    <hkern u1="&#x164;" u2="&#xeb;" k="75" />
    <hkern u1="&#x164;" u2="&#xea;" k="75" />
    <hkern u1="&#x164;" u2="&#xe9;" k="75" />
    <hkern u1="&#x164;" u2="&#xe8;" k="75" />
    <hkern u1="&#x164;" u2="&#xe7;" k="75" />
    <hkern u1="&#x164;" u2="&#xe6;" k="95" />
    <hkern u1="&#x164;" u2="&#xe5;" k="95" />
    <hkern u1="&#x164;" u2="&#xe4;" k="95" />
    <hkern u1="&#x164;" u2="&#xe3;" k="95" />
    <hkern u1="&#x164;" u2="&#xe2;" k="95" />
    <hkern u1="&#x164;" u2="&#xe1;" k="95" />
    <hkern u1="&#x164;" u2="&#xe0;" k="95" />
    <hkern u1="&#x164;" u2="&#xd8;" k="15" />
    <hkern u1="&#x164;" u2="&#xd6;" k="15" />
    <hkern u1="&#x164;" u2="&#xd5;" k="15" />
    <hkern u1="&#x164;" u2="&#xd4;" k="15" />
    <hkern u1="&#x164;" u2="&#xd3;" k="15" />
    <hkern u1="&#x164;" u2="&#xd2;" k="15" />
    <hkern u1="&#x164;" u2="&#xc7;" k="15" />
    <hkern u1="&#x164;" u2="&#xa9;" k="15" />
    <hkern u1="&#x164;" u2="z" k="80" />
    <hkern u1="&#x164;" u2="y" k="105" />
    <hkern u1="&#x164;" u2="x" k="65" />
    <hkern u1="&#x164;" u2="w" k="55" />
    <hkern u1="&#x164;" u2="v" k="55" />
    <hkern u1="&#x164;" u2="u" k="105" />
    <hkern u1="&#x164;" u2="s" k="90" />
    <hkern u1="&#x164;" u2="r" k="105" />
    <hkern u1="&#x164;" u2="q" k="75" />
    <hkern u1="&#x164;" u2="p" k="105" />
    <hkern u1="&#x164;" u2="o" k="75" />
    <hkern u1="&#x164;" u2="n" k="105" />
    <hkern u1="&#x164;" u2="m" k="105" />
    <hkern u1="&#x164;" u2="g" k="75" />
    <hkern u1="&#x164;" u2="e" k="75" />
    <hkern u1="&#x164;" u2="d" k="75" />
    <hkern u1="&#x164;" u2="c" k="75" />
    <hkern u1="&#x164;" u2="a" k="95" />
    <hkern u1="&#x164;" u2="_" k="45" />
    <hkern u1="&#x164;" u2="Q" k="15" />
    <hkern u1="&#x164;" u2="O" k="15" />
    <hkern u1="&#x164;" u2="G" k="15" />
    <hkern u1="&#x164;" u2="C" k="15" />
    <hkern u1="&#x164;" u2="A" k="35" />
    <hkern u1="&#x164;" u2="&#x2f;" k="85" />
    <hkern u1="&#x164;" u2="&#x2e;" k="110" />
    <hkern u1="&#x164;" u2="&#x2d;" k="110" />
    <hkern u1="&#x164;" u2="&#x2c;" k="110" />
    <hkern u1="&#x174;" u2="&#x2026;" k="55" />
    <hkern u1="&#x174;" u2="&#x2014;" k="20" />
    <hkern u1="&#x174;" u2="&#x2013;" k="20" />
    <hkern u1="&#x174;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x174;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x174;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x174;" u2="&#x153;" k="10" />
    <hkern u1="&#x174;" u2="&#x151;" k="10" />
    <hkern u1="&#x174;" u2="&#x14f;" k="10" />
    <hkern u1="&#x174;" u2="&#x14d;" k="10" />
    <hkern u1="&#x174;" u2="&#x123;" k="10" />
    <hkern u1="&#x174;" u2="&#x121;" k="10" />
    <hkern u1="&#x174;" u2="&#x11f;" k="10" />
    <hkern u1="&#x174;" u2="&#x11d;" k="10" />
    <hkern u1="&#x174;" u2="&#x11b;" k="10" />
    <hkern u1="&#x174;" u2="&#x119;" k="10" />
    <hkern u1="&#x174;" u2="&#x117;" k="10" />
    <hkern u1="&#x174;" u2="&#x115;" k="10" />
    <hkern u1="&#x174;" u2="&#x113;" k="10" />
    <hkern u1="&#x174;" u2="&#x111;" k="10" />
    <hkern u1="&#x174;" u2="&#x10f;" k="10" />
    <hkern u1="&#x174;" u2="&#x10d;" k="10" />
    <hkern u1="&#x174;" u2="&#x10b;" k="10" />
    <hkern u1="&#x174;" u2="&#x109;" k="10" />
    <hkern u1="&#x174;" u2="&#x107;" k="10" />
    <hkern u1="&#x174;" u2="&#x105;" k="10" />
    <hkern u1="&#x174;" u2="&#x103;" k="10" />
    <hkern u1="&#x174;" u2="&#x101;" k="10" />
    <hkern u1="&#x174;" u2="&#xf8;" k="10" />
    <hkern u1="&#x174;" u2="&#xf6;" k="10" />
    <hkern u1="&#x174;" u2="&#xf5;" k="10" />
    <hkern u1="&#x174;" u2="&#xf4;" k="10" />
    <hkern u1="&#x174;" u2="&#xf3;" k="10" />
    <hkern u1="&#x174;" u2="&#xf2;" k="10" />
    <hkern u1="&#x174;" u2="&#xf0;" k="10" />
    <hkern u1="&#x174;" u2="&#xef;" k="-20" />
    <hkern u1="&#x174;" u2="&#xeb;" k="10" />
    <hkern u1="&#x174;" u2="&#xea;" k="10" />
    <hkern u1="&#x174;" u2="&#xe9;" k="10" />
    <hkern u1="&#x174;" u2="&#xe8;" k="10" />
    <hkern u1="&#x174;" u2="&#xe7;" k="10" />
    <hkern u1="&#x174;" u2="&#xe6;" k="10" />
    <hkern u1="&#x174;" u2="&#xe5;" k="10" />
    <hkern u1="&#x174;" u2="&#xe4;" k="10" />
    <hkern u1="&#x174;" u2="&#xe3;" k="10" />
    <hkern u1="&#x174;" u2="&#xe2;" k="10" />
    <hkern u1="&#x174;" u2="&#xe1;" k="10" />
    <hkern u1="&#x174;" u2="&#xe0;" k="10" />
    <hkern u1="&#x174;" u2="v" k="-20" />
    <hkern u1="&#x174;" u2="q" k="10" />
    <hkern u1="&#x174;" u2="o" k="10" />
    <hkern u1="&#x174;" u2="g" k="10" />
    <hkern u1="&#x174;" u2="e" k="10" />
    <hkern u1="&#x174;" u2="d" k="10" />
    <hkern u1="&#x174;" u2="c" k="10" />
    <hkern u1="&#x174;" u2="a" k="10" />
    <hkern u1="&#x174;" u2="_" k="45" />
    <hkern u1="&#x174;" u2="V" k="-20" />
    <hkern u1="&#x174;" u2="&#x2f;" k="55" />
    <hkern u1="&#x174;" u2="&#x2e;" k="55" />
    <hkern u1="&#x174;" u2="&#x2d;" k="20" />
    <hkern u1="&#x174;" u2="&#x2c;" k="55" />
    <hkern u1="&#x175;" u2="&#x2026;" k="65" />
    <hkern u1="&#x175;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x175;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x175;" u2="&#x2014;" k="10" />
    <hkern u1="&#x175;" u2="&#x2013;" k="10" />
    <hkern u1="&#x175;" u2="&#x164;" k="55" />
    <hkern u1="&#x175;" u2="&#x162;" k="55" />
    <hkern u1="&#x175;" u2="_" k="35" />
    <hkern u1="&#x175;" u2="T" k="55" />
    <hkern u1="&#x175;" u2="&#x2f;" k="20" />
    <hkern u1="&#x175;" u2="&#x2e;" k="65" />
    <hkern u1="&#x175;" u2="&#x2d;" k="10" />
    <hkern u1="&#x175;" u2="&#x2c;" k="65" />
    <hkern u1="&#x176;" u2="V" k="-20" />
    <hkern u1="&#x177;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x177;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x177;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x177;" u2="&#x174;" k="10" />
    <hkern u1="&#x177;" u2="&#x164;" k="70" />
    <hkern u1="&#x177;" u2="&#x162;" k="70" />
    <hkern u1="&#x177;" u2="W" k="10" />
    <hkern u1="&#x177;" u2="V" k="20" />
    <hkern u1="&#x177;" u2="T" k="70" />
    <hkern u1="&#x178;" u2="V" k="-20" />
    <hkern u1="&#x179;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x179;" u2="&#x2014;" k="120" />
    <hkern u1="&#x179;" u2="&#x2013;" k="120" />
    <hkern u1="&#x179;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x179;" u2="&#x152;" k="10" />
    <hkern u1="&#x179;" u2="&#x150;" k="10" />
    <hkern u1="&#x179;" u2="&#x14e;" k="10" />
    <hkern u1="&#x179;" u2="&#x14c;" k="10" />
    <hkern u1="&#x179;" u2="&#x122;" k="10" />
    <hkern u1="&#x179;" u2="&#x120;" k="10" />
    <hkern u1="&#x179;" u2="&#x11e;" k="10" />
    <hkern u1="&#x179;" u2="&#x11c;" k="10" />
    <hkern u1="&#x179;" u2="&#x10c;" k="10" />
    <hkern u1="&#x179;" u2="&#x10a;" k="10" />
    <hkern u1="&#x179;" u2="&#x108;" k="10" />
    <hkern u1="&#x179;" u2="&#x106;" k="10" />
    <hkern u1="&#x179;" u2="&#xd8;" k="10" />
    <hkern u1="&#x179;" u2="&#xd6;" k="10" />
    <hkern u1="&#x179;" u2="&#xd5;" k="10" />
    <hkern u1="&#x179;" u2="&#xd4;" k="10" />
    <hkern u1="&#x179;" u2="&#xd3;" k="10" />
    <hkern u1="&#x179;" u2="&#xd2;" k="10" />
    <hkern u1="&#x179;" u2="&#xc7;" k="10" />
    <hkern u1="&#x179;" u2="&#xa9;" k="10" />
    <hkern u1="&#x179;" u2="v" k="30" />
    <hkern u1="&#x179;" u2="Q" k="10" />
    <hkern u1="&#x179;" u2="O" k="10" />
    <hkern u1="&#x179;" u2="G" k="10" />
    <hkern u1="&#x179;" u2="C" k="10" />
    <hkern u1="&#x179;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x179;" u2="&#x2d;" k="120" />
    <hkern u1="&#x179;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17a;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17a;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17a;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x17a;" u2="&#x164;" k="50" />
    <hkern u1="&#x17a;" u2="&#x162;" k="50" />
    <hkern u1="&#x17a;" u2="&#x153;" k="10" />
    <hkern u1="&#x17a;" u2="&#x151;" k="10" />
    <hkern u1="&#x17a;" u2="&#x14f;" k="10" />
    <hkern u1="&#x17a;" u2="&#x14d;" k="10" />
    <hkern u1="&#x17a;" u2="&#x123;" k="10" />
    <hkern u1="&#x17a;" u2="&#x121;" k="10" />
    <hkern u1="&#x17a;" u2="&#x11f;" k="10" />
    <hkern u1="&#x17a;" u2="&#x11d;" k="10" />
    <hkern u1="&#x17a;" u2="&#x11b;" k="10" />
    <hkern u1="&#x17a;" u2="&#x119;" k="10" />
    <hkern u1="&#x17a;" u2="&#x117;" k="10" />
    <hkern u1="&#x17a;" u2="&#x115;" k="10" />
    <hkern u1="&#x17a;" u2="&#x113;" k="10" />
    <hkern u1="&#x17a;" u2="&#x111;" k="10" />
    <hkern u1="&#x17a;" u2="&#x10f;" k="10" />
    <hkern u1="&#x17a;" u2="&#x10d;" k="10" />
    <hkern u1="&#x17a;" u2="&#x10b;" k="10" />
    <hkern u1="&#x17a;" u2="&#x109;" k="10" />
    <hkern u1="&#x17a;" u2="&#x107;" k="10" />
    <hkern u1="&#x17a;" u2="&#xf8;" k="10" />
    <hkern u1="&#x17a;" u2="&#xf6;" k="10" />
    <hkern u1="&#x17a;" u2="&#xf5;" k="10" />
    <hkern u1="&#x17a;" u2="&#xf4;" k="10" />
    <hkern u1="&#x17a;" u2="&#xf3;" k="10" />
    <hkern u1="&#x17a;" u2="&#xf2;" k="10" />
    <hkern u1="&#x17a;" u2="&#xf0;" k="10" />
    <hkern u1="&#x17a;" u2="&#xeb;" k="10" />
    <hkern u1="&#x17a;" u2="&#xea;" k="10" />
    <hkern u1="&#x17a;" u2="&#xe9;" k="10" />
    <hkern u1="&#x17a;" u2="&#xe8;" k="10" />
    <hkern u1="&#x17a;" u2="&#xe7;" k="10" />
    <hkern u1="&#x17a;" u2="q" k="10" />
    <hkern u1="&#x17a;" u2="o" k="10" />
    <hkern u1="&#x17a;" u2="g" k="10" />
    <hkern u1="&#x17a;" u2="e" k="10" />
    <hkern u1="&#x17a;" u2="d" k="10" />
    <hkern u1="&#x17a;" u2="c" k="10" />
    <hkern u1="&#x17a;" u2="T" k="50" />
    <hkern u1="&#x17a;" u2="&#x2d;" k="35" />
    <hkern u1="&#x17b;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x17b;" u2="&#x2014;" k="120" />
    <hkern u1="&#x17b;" u2="&#x2013;" k="120" />
    <hkern u1="&#x17b;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x17b;" u2="&#x152;" k="10" />
    <hkern u1="&#x17b;" u2="&#x150;" k="10" />
    <hkern u1="&#x17b;" u2="&#x14e;" k="10" />
    <hkern u1="&#x17b;" u2="&#x14c;" k="10" />
    <hkern u1="&#x17b;" u2="&#x122;" k="10" />
    <hkern u1="&#x17b;" u2="&#x120;" k="10" />
    <hkern u1="&#x17b;" u2="&#x11e;" k="10" />
    <hkern u1="&#x17b;" u2="&#x11c;" k="10" />
    <hkern u1="&#x17b;" u2="&#x10c;" k="10" />
    <hkern u1="&#x17b;" u2="&#x10a;" k="10" />
    <hkern u1="&#x17b;" u2="&#x108;" k="10" />
    <hkern u1="&#x17b;" u2="&#x106;" k="10" />
    <hkern u1="&#x17b;" u2="&#xd8;" k="10" />
    <hkern u1="&#x17b;" u2="&#xd6;" k="10" />
    <hkern u1="&#x17b;" u2="&#xd5;" k="10" />
    <hkern u1="&#x17b;" u2="&#xd4;" k="10" />
    <hkern u1="&#x17b;" u2="&#xd3;" k="10" />
    <hkern u1="&#x17b;" u2="&#xd2;" k="10" />
    <hkern u1="&#x17b;" u2="&#xc7;" k="10" />
    <hkern u1="&#x17b;" u2="&#xa9;" k="10" />
    <hkern u1="&#x17b;" u2="v" k="30" />
    <hkern u1="&#x17b;" u2="Q" k="10" />
    <hkern u1="&#x17b;" u2="O" k="10" />
    <hkern u1="&#x17b;" u2="G" k="10" />
    <hkern u1="&#x17b;" u2="C" k="10" />
    <hkern u1="&#x17b;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x17b;" u2="&#x2d;" k="120" />
    <hkern u1="&#x17b;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17c;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17c;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x17c;" u2="&#x164;" k="50" />
    <hkern u1="&#x17c;" u2="&#x162;" k="50" />
    <hkern u1="&#x17c;" u2="&#x153;" k="10" />
    <hkern u1="&#x17c;" u2="&#x151;" k="10" />
    <hkern u1="&#x17c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x17c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x17c;" u2="&#x123;" k="10" />
    <hkern u1="&#x17c;" u2="&#x121;" k="10" />
    <hkern u1="&#x17c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x17c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x17c;" u2="&#x11b;" k="10" />
    <hkern u1="&#x17c;" u2="&#x119;" k="10" />
    <hkern u1="&#x17c;" u2="&#x117;" k="10" />
    <hkern u1="&#x17c;" u2="&#x115;" k="10" />
    <hkern u1="&#x17c;" u2="&#x113;" k="10" />
    <hkern u1="&#x17c;" u2="&#x111;" k="10" />
    <hkern u1="&#x17c;" u2="&#x10f;" k="10" />
    <hkern u1="&#x17c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x17c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x17c;" u2="&#x109;" k="10" />
    <hkern u1="&#x17c;" u2="&#x107;" k="10" />
    <hkern u1="&#x17c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x17c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x17c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x17c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x17c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x17c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x17c;" u2="&#xf0;" k="10" />
    <hkern u1="&#x17c;" u2="&#xeb;" k="10" />
    <hkern u1="&#x17c;" u2="&#xea;" k="10" />
    <hkern u1="&#x17c;" u2="&#xe9;" k="10" />
    <hkern u1="&#x17c;" u2="&#xe8;" k="10" />
    <hkern u1="&#x17c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x17c;" u2="q" k="10" />
    <hkern u1="&#x17c;" u2="o" k="10" />
    <hkern u1="&#x17c;" u2="g" k="10" />
    <hkern u1="&#x17c;" u2="e" k="10" />
    <hkern u1="&#x17c;" u2="d" k="10" />
    <hkern u1="&#x17c;" u2="c" k="10" />
    <hkern u1="&#x17c;" u2="T" k="50" />
    <hkern u1="&#x17c;" u2="&#x2d;" k="35" />
    <hkern u1="&#x17d;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x17d;" u2="&#x2014;" k="120" />
    <hkern u1="&#x17d;" u2="&#x2013;" k="120" />
    <hkern u1="&#x17d;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x17d;" u2="&#x152;" k="10" />
    <hkern u1="&#x17d;" u2="&#x150;" k="10" />
    <hkern u1="&#x17d;" u2="&#x14e;" k="10" />
    <hkern u1="&#x17d;" u2="&#x14c;" k="10" />
    <hkern u1="&#x17d;" u2="&#x122;" k="10" />
    <hkern u1="&#x17d;" u2="&#x120;" k="10" />
    <hkern u1="&#x17d;" u2="&#x11e;" k="10" />
    <hkern u1="&#x17d;" u2="&#x11c;" k="10" />
    <hkern u1="&#x17d;" u2="&#x10c;" k="10" />
    <hkern u1="&#x17d;" u2="&#x10a;" k="10" />
    <hkern u1="&#x17d;" u2="&#x108;" k="10" />
    <hkern u1="&#x17d;" u2="&#x106;" k="10" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="10" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="10" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="10" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="10" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="10" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="10" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="10" />
    <hkern u1="&#x17d;" u2="&#xa9;" k="10" />
    <hkern u1="&#x17d;" u2="v" k="30" />
    <hkern u1="&#x17d;" u2="Q" k="10" />
    <hkern u1="&#x17d;" u2="O" k="10" />
    <hkern u1="&#x17d;" u2="G" k="10" />
    <hkern u1="&#x17d;" u2="C" k="10" />
    <hkern u1="&#x17d;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x17d;" u2="&#x2d;" k="120" />
    <hkern u1="&#x17d;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17e;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17e;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x17e;" u2="&#x164;" k="50" />
    <hkern u1="&#x17e;" u2="&#x162;" k="50" />
    <hkern u1="&#x17e;" u2="&#x153;" k="10" />
    <hkern u1="&#x17e;" u2="&#x151;" k="10" />
    <hkern u1="&#x17e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x17e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x17e;" u2="&#x123;" k="10" />
    <hkern u1="&#x17e;" u2="&#x121;" k="10" />
    <hkern u1="&#x17e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x17e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x17e;" u2="&#x11b;" k="10" />
    <hkern u1="&#x17e;" u2="&#x119;" k="10" />
    <hkern u1="&#x17e;" u2="&#x117;" k="10" />
    <hkern u1="&#x17e;" u2="&#x115;" k="10" />
    <hkern u1="&#x17e;" u2="&#x113;" k="10" />
    <hkern u1="&#x17e;" u2="&#x111;" k="10" />
    <hkern u1="&#x17e;" u2="&#x10f;" k="10" />
    <hkern u1="&#x17e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x17e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x17e;" u2="&#x109;" k="10" />
    <hkern u1="&#x17e;" u2="&#x107;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf0;" k="10" />
    <hkern u1="&#x17e;" u2="&#xeb;" k="10" />
    <hkern u1="&#x17e;" u2="&#xea;" k="10" />
    <hkern u1="&#x17e;" u2="&#xe9;" k="10" />
    <hkern u1="&#x17e;" u2="&#xe8;" k="10" />
    <hkern u1="&#x17e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x17e;" u2="q" k="10" />
    <hkern u1="&#x17e;" u2="o" k="10" />
    <hkern u1="&#x17e;" u2="g" k="10" />
    <hkern u1="&#x17e;" u2="e" k="10" />
    <hkern u1="&#x17e;" u2="d" k="10" />
    <hkern u1="&#x17e;" u2="c" k="10" />
    <hkern u1="&#x17e;" u2="T" k="50" />
    <hkern u1="&#x17e;" u2="&#x2d;" k="35" />
    <hkern u1="&#x1fa;" u2="&#x164;" k="35" />
    <hkern u1="&#x1fa;" u2="&#x162;" k="35" />
    <hkern u1="&#x1fa;" u2="T" k="35" />
    <hkern u1="&#x1fc;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x1fc;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x152;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x150;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x14e;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x14c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x122;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x120;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x11e;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x11c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x10c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x10a;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x108;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x106;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd8;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd6;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd5;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd4;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd3;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd2;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xc7;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xa9;" k="7" />
    <hkern u1="&#x1fc;" u2="Q" k="7" />
    <hkern u1="&#x1fc;" u2="O" k="7" />
    <hkern u1="&#x1fc;" u2="G" k="7" />
    <hkern u1="&#x1fc;" u2="C" k="7" />
    <hkern u1="&#x1fc;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x1fc;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x1fd;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x219;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x174;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x164;" k="70" />
    <hkern u1="&#x1fd;" u2="&#x162;" k="70" />
    <hkern u1="&#x1fd;" u2="&#x161;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x1fd;" u2="s" k="-5" />
    <hkern u1="&#x1fd;" u2="W" k="10" />
    <hkern u1="&#x1fd;" u2="V" k="20" />
    <hkern u1="&#x1fd;" u2="T" k="70" />
    <hkern u1="&#x1fe;" u2="&#x2026;" k="25" />
    <hkern u1="&#x1fe;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x164;" k="15" />
    <hkern u1="&#x1fe;" u2="&#x162;" k="15" />
    <hkern u1="&#x1fe;" u2="&#x152;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x150;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x122;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x120;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x108;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x106;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x1fe;" u2="X" k="10" />
    <hkern u1="&#x1fe;" u2="T" k="15" />
    <hkern u1="&#x1fe;" u2="Q" k="-5" />
    <hkern u1="&#x1fe;" u2="O" k="-5" />
    <hkern u1="&#x1fe;" u2="G" k="-5" />
    <hkern u1="&#x1fe;" u2="C" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x2e;" k="25" />
    <hkern u1="&#x1fe;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x2c;" k="25" />
    <hkern u1="&#x1ff;" u2="&#x2026;" k="20" />
    <hkern u1="&#x1ff;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x174;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x164;" k="75" />
    <hkern u1="&#x1ff;" u2="&#x162;" k="75" />
    <hkern u1="&#x1ff;" u2="z" k="20" />
    <hkern u1="&#x1ff;" u2="x" k="10" />
    <hkern u1="&#x1ff;" u2="W" k="10" />
    <hkern u1="&#x1ff;" u2="V" k="20" />
    <hkern u1="&#x1ff;" u2="T" k="75" />
    <hkern u1="&#x1ff;" u2="&#x2e;" k="20" />
    <hkern u1="&#x1ff;" u2="&#x2c;" k="20" />
    <hkern u1="&#x218;" u2="&#x1ef2;" k="40" />
    <hkern u1="&#x218;" u2="&#x218;" k="25" />
    <hkern u1="&#x218;" u2="&#x178;" k="40" />
    <hkern u1="&#x218;" u2="&#x176;" k="40" />
    <hkern u1="&#x218;" u2="&#x164;" k="10" />
    <hkern u1="&#x218;" u2="&#x162;" k="10" />
    <hkern u1="&#x218;" u2="&#x160;" k="25" />
    <hkern u1="&#x218;" u2="&#x15e;" k="25" />
    <hkern u1="&#x218;" u2="&#x15c;" k="25" />
    <hkern u1="&#x218;" u2="&#x15a;" k="25" />
    <hkern u1="&#x218;" u2="&#xdd;" k="40" />
    <hkern u1="&#x218;" u2="Y" k="40" />
    <hkern u1="&#x218;" u2="V" k="20" />
    <hkern u1="&#x218;" u2="T" k="10" />
    <hkern u1="&#x218;" u2="S" k="25" />
    <hkern u1="&#x219;" u2="&#x219;" k="20" />
    <hkern u1="&#x219;" u2="&#x164;" k="70" />
    <hkern u1="&#x219;" u2="&#x162;" k="70" />
    <hkern u1="&#x219;" u2="&#x161;" k="20" />
    <hkern u1="&#x219;" u2="&#x15f;" k="20" />
    <hkern u1="&#x219;" u2="&#x15d;" k="20" />
    <hkern u1="&#x219;" u2="&#x15b;" k="20" />
    <hkern u1="&#x219;" u2="s" k="20" />
    <hkern u1="&#x219;" u2="T" k="70" />
    <hkern u1="&#x1e80;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xef;" k="-20" />
    <hkern u1="&#x1e80;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e80;" u2="v" k="-20" />
    <hkern u1="&#x1e80;" u2="q" k="10" />
    <hkern u1="&#x1e80;" u2="o" k="10" />
    <hkern u1="&#x1e80;" u2="g" k="10" />
    <hkern u1="&#x1e80;" u2="e" k="10" />
    <hkern u1="&#x1e80;" u2="d" k="10" />
    <hkern u1="&#x1e80;" u2="c" k="10" />
    <hkern u1="&#x1e80;" u2="a" k="10" />
    <hkern u1="&#x1e80;" u2="_" k="45" />
    <hkern u1="&#x1e80;" u2="V" k="-20" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e81;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e81;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e81;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e81;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e81;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e81;" u2="_" k="35" />
    <hkern u1="&#x1e81;" u2="T" k="55" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e81;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e81;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1e82;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xef;" k="-20" />
    <hkern u1="&#x1e82;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e82;" u2="v" k="-20" />
    <hkern u1="&#x1e82;" u2="q" k="10" />
    <hkern u1="&#x1e82;" u2="o" k="10" />
    <hkern u1="&#x1e82;" u2="g" k="10" />
    <hkern u1="&#x1e82;" u2="e" k="10" />
    <hkern u1="&#x1e82;" u2="d" k="10" />
    <hkern u1="&#x1e82;" u2="c" k="10" />
    <hkern u1="&#x1e82;" u2="a" k="10" />
    <hkern u1="&#x1e82;" u2="_" k="45" />
    <hkern u1="&#x1e82;" u2="V" k="-20" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e83;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e83;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e83;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e83;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e83;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e83;" u2="_" k="35" />
    <hkern u1="&#x1e83;" u2="T" k="55" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e83;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e83;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1e84;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xef;" k="-20" />
    <hkern u1="&#x1e84;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e84;" u2="v" k="-20" />
    <hkern u1="&#x1e84;" u2="q" k="10" />
    <hkern u1="&#x1e84;" u2="o" k="10" />
    <hkern u1="&#x1e84;" u2="g" k="10" />
    <hkern u1="&#x1e84;" u2="e" k="10" />
    <hkern u1="&#x1e84;" u2="d" k="10" />
    <hkern u1="&#x1e84;" u2="c" k="10" />
    <hkern u1="&#x1e84;" u2="a" k="10" />
    <hkern u1="&#x1e84;" u2="_" k="45" />
    <hkern u1="&#x1e84;" u2="V" k="-20" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e85;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e85;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e85;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e85;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e85;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e85;" u2="_" k="35" />
    <hkern u1="&#x1e85;" u2="T" k="55" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e85;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e85;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1ef2;" u2="V" k="-20" />
    <hkern u1="&#x1ef3;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x174;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x164;" k="70" />
    <hkern u1="&#x1ef3;" u2="&#x162;" k="70" />
    <hkern u1="&#x1ef3;" u2="W" k="10" />
    <hkern u1="&#x1ef3;" u2="V" k="20" />
    <hkern u1="&#x1ef3;" u2="T" k="70" />
    <hkern u1="&#x2013;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2013;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2013;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2013;" u2="&#x218;" k="95" />
    <hkern u1="&#x2013;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x17d;" k="75" />
    <hkern u1="&#x2013;" u2="&#x17b;" k="75" />
    <hkern u1="&#x2013;" u2="&#x179;" k="75" />
    <hkern u1="&#x2013;" u2="&#x178;" k="10" />
    <hkern u1="&#x2013;" u2="&#x176;" k="10" />
    <hkern u1="&#x2013;" u2="&#x175;" k="10" />
    <hkern u1="&#x2013;" u2="&#x174;" k="20" />
    <hkern u1="&#x2013;" u2="&#x164;" k="110" />
    <hkern u1="&#x2013;" u2="&#x162;" k="110" />
    <hkern u1="&#x2013;" u2="&#x160;" k="95" />
    <hkern u1="&#x2013;" u2="&#x15e;" k="95" />
    <hkern u1="&#x2013;" u2="&#x15c;" k="95" />
    <hkern u1="&#x2013;" u2="&#x15a;" k="95" />
    <hkern u1="&#x2013;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2013;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2013;" u2="z" k="35" />
    <hkern u1="&#x2013;" u2="x" k="20" />
    <hkern u1="&#x2013;" u2="w" k="10" />
    <hkern u1="&#x2013;" u2="Z" k="75" />
    <hkern u1="&#x2013;" u2="Y" k="10" />
    <hkern u1="&#x2013;" u2="X" k="65" />
    <hkern u1="&#x2013;" u2="W" k="20" />
    <hkern u1="&#x2013;" u2="V" k="35" />
    <hkern u1="&#x2013;" u2="T" k="110" />
    <hkern u1="&#x2013;" u2="S" k="95" />
    <hkern u1="&#x2013;" u2="Q" k="-15" />
    <hkern u1="&#x2013;" u2="O" k="-15" />
    <hkern u1="&#x2013;" u2="G" k="-15" />
    <hkern u1="&#x2013;" u2="C" k="-15" />
    <hkern u1="&#x2014;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2014;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2014;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2014;" u2="&#x218;" k="95" />
    <hkern u1="&#x2014;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x17d;" k="75" />
    <hkern u1="&#x2014;" u2="&#x17b;" k="75" />
    <hkern u1="&#x2014;" u2="&#x179;" k="75" />
    <hkern u1="&#x2014;" u2="&#x178;" k="10" />
    <hkern u1="&#x2014;" u2="&#x176;" k="10" />
    <hkern u1="&#x2014;" u2="&#x175;" k="10" />
    <hkern u1="&#x2014;" u2="&#x174;" k="20" />
    <hkern u1="&#x2014;" u2="&#x164;" k="110" />
    <hkern u1="&#x2014;" u2="&#x162;" k="110" />
    <hkern u1="&#x2014;" u2="&#x160;" k="95" />
    <hkern u1="&#x2014;" u2="&#x15e;" k="95" />
    <hkern u1="&#x2014;" u2="&#x15c;" k="95" />
    <hkern u1="&#x2014;" u2="&#x15a;" k="95" />
    <hkern u1="&#x2014;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2014;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2014;" u2="z" k="35" />
    <hkern u1="&#x2014;" u2="x" k="20" />
    <hkern u1="&#x2014;" u2="w" k="10" />
    <hkern u1="&#x2014;" u2="Z" k="75" />
    <hkern u1="&#x2014;" u2="Y" k="10" />
    <hkern u1="&#x2014;" u2="X" k="65" />
    <hkern u1="&#x2014;" u2="W" k="20" />
    <hkern u1="&#x2014;" u2="V" k="35" />
    <hkern u1="&#x2014;" u2="T" k="110" />
    <hkern u1="&#x2014;" u2="S" k="95" />
    <hkern u1="&#x2014;" u2="Q" k="-15" />
    <hkern u1="&#x2014;" u2="O" k="-15" />
    <hkern u1="&#x2014;" u2="G" k="-15" />
    <hkern u1="&#x2014;" u2="C" k="-15" />
    <hkern u1="&#x2018;" u2="&#x2026;" k="140" />
    <hkern u1="&#x2018;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x2018;" u2="&#x153;" k="40" />
    <hkern u1="&#x2018;" u2="&#x151;" k="40" />
    <hkern u1="&#x2018;" u2="&#x14f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x14d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x123;" k="40" />
    <hkern u1="&#x2018;" u2="&#x121;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11b;" k="40" />
    <hkern u1="&#x2018;" u2="&#x119;" k="40" />
    <hkern u1="&#x2018;" u2="&#x117;" k="40" />
    <hkern u1="&#x2018;" u2="&#x115;" k="40" />
    <hkern u1="&#x2018;" u2="&#x113;" k="40" />
    <hkern u1="&#x2018;" u2="&#x111;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10b;" k="40" />
    <hkern u1="&#x2018;" u2="&#x109;" k="40" />
    <hkern u1="&#x2018;" u2="&#x107;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="40" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="40" />
    <hkern u1="&#x2018;" u2="&#xea;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="40" />
    <hkern u1="&#x2018;" u2="q" k="40" />
    <hkern u1="&#x2018;" u2="o" k="40" />
    <hkern u1="&#x2018;" u2="g" k="40" />
    <hkern u1="&#x2018;" u2="e" k="40" />
    <hkern u1="&#x2018;" u2="d" k="40" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="140" />
    <hkern u1="&#x2019;" u2="&#x2026;" k="140" />
    <hkern u1="&#x2019;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x2019;" u2="&#x153;" k="40" />
    <hkern u1="&#x2019;" u2="&#x151;" k="40" />
    <hkern u1="&#x2019;" u2="&#x14f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x14d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x123;" k="40" />
    <hkern u1="&#x2019;" u2="&#x121;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11b;" k="40" />
    <hkern u1="&#x2019;" u2="&#x119;" k="40" />
    <hkern u1="&#x2019;" u2="&#x117;" k="40" />
    <hkern u1="&#x2019;" u2="&#x115;" k="40" />
    <hkern u1="&#x2019;" u2="&#x113;" k="40" />
    <hkern u1="&#x2019;" u2="&#x111;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10b;" k="40" />
    <hkern u1="&#x2019;" u2="&#x109;" k="40" />
    <hkern u1="&#x2019;" u2="&#x107;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="40" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="40" />
    <hkern u1="&#x2019;" u2="&#xea;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="40" />
    <hkern u1="&#x2019;" u2="q" k="40" />
    <hkern u1="&#x2019;" u2="o" k="40" />
    <hkern u1="&#x2019;" u2="g" k="40" />
    <hkern u1="&#x2019;" u2="e" k="40" />
    <hkern u1="&#x2019;" u2="d" k="40" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201c;" u2="&#x2026;" k="140" />
    <hkern u1="&#x201c;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x201c;" u2="&#x153;" k="40" />
    <hkern u1="&#x201c;" u2="&#x151;" k="40" />
    <hkern u1="&#x201c;" u2="&#x14f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x14d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x123;" k="40" />
    <hkern u1="&#x201c;" u2="&#x121;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11b;" k="40" />
    <hkern u1="&#x201c;" u2="&#x119;" k="40" />
    <hkern u1="&#x201c;" u2="&#x117;" k="40" />
    <hkern u1="&#x201c;" u2="&#x115;" k="40" />
    <hkern u1="&#x201c;" u2="&#x113;" k="40" />
    <hkern u1="&#x201c;" u2="&#x111;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10b;" k="40" />
    <hkern u1="&#x201c;" u2="&#x109;" k="40" />
    <hkern u1="&#x201c;" u2="&#x107;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="40" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="40" />
    <hkern u1="&#x201c;" u2="&#xea;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="40" />
    <hkern u1="&#x201c;" u2="q" k="40" />
    <hkern u1="&#x201c;" u2="o" k="40" />
    <hkern u1="&#x201c;" u2="g" k="40" />
    <hkern u1="&#x201c;" u2="e" k="40" />
    <hkern u1="&#x201c;" u2="d" k="40" />
    <hkern u1="&#x201c;" u2="c" k="40" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201c;" u2="&#x2c;" k="140" />
    <hkern u1="&#x201d;" u2="&#x2026;" k="140" />
    <hkern u1="&#x201d;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x201d;" u2="&#x153;" k="40" />
    <hkern u1="&#x201d;" u2="&#x151;" k="40" />
    <hkern u1="&#x201d;" u2="&#x14f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x14d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x123;" k="40" />
    <hkern u1="&#x201d;" u2="&#x121;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11b;" k="40" />
    <hkern u1="&#x201d;" u2="&#x119;" k="40" />
    <hkern u1="&#x201d;" u2="&#x117;" k="40" />
    <hkern u1="&#x201d;" u2="&#x115;" k="40" />
    <hkern u1="&#x201d;" u2="&#x113;" k="40" />
    <hkern u1="&#x201d;" u2="&#x111;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10b;" k="40" />
    <hkern u1="&#x201d;" u2="&#x109;" k="40" />
    <hkern u1="&#x201d;" u2="&#x107;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="40" />
    <hkern u1="&#x201d;" u2="&#xeb;" k="40" />
    <hkern u1="&#x201d;" u2="&#xea;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe9;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe8;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="40" />
    <hkern u1="&#x201d;" u2="q" k="40" />
    <hkern u1="&#x201d;" u2="o" k="40" />
    <hkern u1="&#x201d;" u2="g" k="40" />
    <hkern u1="&#x201d;" u2="e" k="40" />
    <hkern u1="&#x201d;" u2="d" k="40" />
    <hkern u1="&#x201d;" u2="c" k="40" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201d;" u2="&#x2c;" k="140" />
    <hkern u1="&#x2026;" g2="fl" k="10" />
    <hkern u1="&#x2026;" g2="fi" k="10" />
    <hkern u1="&#x2026;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2026;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2026;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2026;" u2="&#x2019;" k="60" />
    <hkern u1="&#x2026;" u2="&#x2018;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2026;" u2="&#x1ef2;" k="135" />
    <hkern u1="&#x2026;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2026;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2026;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2026;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2026;" u2="&#x1fe;" k="25" />
    <hkern u1="&#x2026;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x2026;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2026;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x178;" k="135" />
    <hkern u1="&#x2026;" u2="&#x177;" k="20" />
    <hkern u1="&#x2026;" u2="&#x176;" k="135" />
    <hkern u1="&#x2026;" u2="&#x175;" k="65" />
    <hkern u1="&#x2026;" u2="&#x174;" k="55" />
    <hkern u1="&#x2026;" u2="&#x173;" k="20" />
    <hkern u1="&#x2026;" u2="&#x172;" k="30" />
    <hkern u1="&#x2026;" u2="&#x171;" k="20" />
    <hkern u1="&#x2026;" u2="&#x170;" k="30" />
    <hkern u1="&#x2026;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16e;" k="30" />
    <hkern u1="&#x2026;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16c;" k="30" />
    <hkern u1="&#x2026;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16a;" k="30" />
    <hkern u1="&#x2026;" u2="&#x169;" k="20" />
    <hkern u1="&#x2026;" u2="&#x168;" k="30" />
    <hkern u1="&#x2026;" u2="&#x167;" k="20" />
    <hkern u1="&#x2026;" u2="&#x165;" k="20" />
    <hkern u1="&#x2026;" u2="&#x164;" k="110" />
    <hkern u1="&#x2026;" u2="&#x163;" k="20" />
    <hkern u1="&#x2026;" u2="&#x162;" k="110" />
    <hkern u1="&#x2026;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x153;" k="20" />
    <hkern u1="&#x2026;" u2="&#x152;" k="25" />
    <hkern u1="&#x2026;" u2="&#x151;" k="20" />
    <hkern u1="&#x2026;" u2="&#x150;" k="25" />
    <hkern u1="&#x2026;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x14e;" k="25" />
    <hkern u1="&#x2026;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x14c;" k="25" />
    <hkern u1="&#x2026;" u2="&#x123;" k="20" />
    <hkern u1="&#x2026;" u2="&#x122;" k="25" />
    <hkern u1="&#x2026;" u2="&#x121;" k="20" />
    <hkern u1="&#x2026;" u2="&#x120;" k="25" />
    <hkern u1="&#x2026;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11e;" k="25" />
    <hkern u1="&#x2026;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11c;" k="25" />
    <hkern u1="&#x2026;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2026;" u2="&#x119;" k="20" />
    <hkern u1="&#x2026;" u2="&#x118;" k="20" />
    <hkern u1="&#x2026;" u2="&#x117;" k="20" />
    <hkern u1="&#x2026;" u2="&#x116;" k="20" />
    <hkern u1="&#x2026;" u2="&#x115;" k="20" />
    <hkern u1="&#x2026;" u2="&#x114;" k="20" />
    <hkern u1="&#x2026;" u2="&#x113;" k="20" />
    <hkern u1="&#x2026;" u2="&#x112;" k="20" />
    <hkern u1="&#x2026;" u2="&#x111;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10c;" k="25" />
    <hkern u1="&#x2026;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10a;" k="25" />
    <hkern u1="&#x2026;" u2="&#x109;" k="20" />
    <hkern u1="&#x2026;" u2="&#x108;" k="25" />
    <hkern u1="&#x2026;" u2="&#x107;" k="20" />
    <hkern u1="&#x2026;" u2="&#x106;" k="25" />
    <hkern u1="&#x2026;" u2="&#x105;" k="10" />
    <hkern u1="&#x2026;" u2="&#x103;" k="10" />
    <hkern u1="&#x2026;" u2="&#x101;" k="10" />
    <hkern u1="&#x2026;" u2="&#xff;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2026;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xea;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe5;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe4;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe3;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe1;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe0;" k="10" />
    <hkern u1="&#x2026;" u2="&#xdd;" k="135" />
    <hkern u1="&#x2026;" u2="&#xdc;" k="30" />
    <hkern u1="&#x2026;" u2="&#xdb;" k="30" />
    <hkern u1="&#x2026;" u2="&#xda;" k="30" />
    <hkern u1="&#x2026;" u2="&#xd9;" k="30" />
    <hkern u1="&#x2026;" u2="&#xd8;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd6;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd5;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd4;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd3;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd2;" k="25" />
    <hkern u1="&#x2026;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xca;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc7;" k="25" />
    <hkern u1="&#x2026;" u2="&#xab;" k="50" />
    <hkern u1="&#x2026;" u2="&#xa9;" k="25" />
    <hkern u1="&#x2026;" u2="y" k="20" />
    <hkern u1="&#x2026;" u2="x" k="-20" />
    <hkern u1="&#x2026;" u2="w" k="65" />
    <hkern u1="&#x2026;" u2="v" k="65" />
    <hkern u1="&#x2026;" u2="u" k="20" />
    <hkern u1="&#x2026;" u2="t" k="20" />
    <hkern u1="&#x2026;" u2="q" k="20" />
    <hkern u1="&#x2026;" u2="o" k="20" />
    <hkern u1="&#x2026;" u2="g" k="20" />
    <hkern u1="&#x2026;" u2="f" k="10" />
    <hkern u1="&#x2026;" u2="e" k="20" />
    <hkern u1="&#x2026;" u2="d" k="20" />
    <hkern u1="&#x2026;" u2="c" k="20" />
    <hkern u1="&#x2026;" u2="a" k="10" />
    <hkern u1="&#x2026;" u2="Z" k="-30" />
    <hkern u1="&#x2026;" u2="Y" k="135" />
    <hkern u1="&#x2026;" u2="X" k="-30" />
    <hkern u1="&#x2026;" u2="W" k="55" />
    <hkern u1="&#x2026;" u2="V" k="110" />
    <hkern u1="&#x2026;" u2="U" k="30" />
    <hkern u1="&#x2026;" u2="T" k="110" />
    <hkern u1="&#x2026;" u2="S" k="-20" />
    <hkern u1="&#x2026;" u2="Q" k="25" />
    <hkern u1="&#x2026;" u2="O" k="25" />
    <hkern u1="&#x2026;" u2="G" k="25" />
    <hkern u1="&#x2026;" u2="E" k="20" />
    <hkern u1="&#x2026;" u2="C" k="25" />
    <hkern u1="&#x203a;" u2="&#x2026;" k="50" />
    <hkern u1="&#x203a;" u2="&#x2e;" k="50" />
    <hkern u1="&#x203a;" u2="&#x2c;" k="50" />
    <hkern u1="&#x2122;" u2="&#x2026;" k="120" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="120" />
    <hkern u1="&#x2122;" u2="&#x2c;" k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,Tcommaaccent,Tcaron"
	k="35" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="comma,period,ellipsis"
	k="-30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="hyphen,endash,emdash"
	k="85" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="15" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-5" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,period,ellipsis"
	k="25" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,endash,emdash"
	k="-15" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,period,ellipsis"
	k="-50" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="T,Tcommaaccent,Tcaron"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="comma,period,ellipsis"
	k="-40" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen,endash,emdash"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="80" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="-30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteleft,quotedblleft"
	k="110" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteright,quotedblright"
	k="110" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="comma,period,ellipsis"
	k="-10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="40" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="15" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="comma,period,ellipsis"
	k="110" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="hyphen,endash,emdash"
	k="110" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="95" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="105" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="90" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="105" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,ellipsis"
	k="55" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="-15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,ellipsis"
	k="-30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="120" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="75" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="T,Tcommaaccent,Tcaron"
	k="80" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="-5" />
    <hkern g1="g,q,y,yacute,ydieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,ycircumflex,ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="g,q,y,yacute,ydieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="g,q,y,yacute,ydieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,ycircumflex,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="40" />
    <hkern g1="k,kcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="40" />
    <hkern g1="k,kcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="k,kcommaaccent"
	g2="comma,period,ellipsis"
	k="-30" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen,endash,emdash"
	k="45" />
    <hkern g1="k,kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="-10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,ellipsis"
	k="115" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="60" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="f,longs,fi,fl"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,ellipsis"
	k="65" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="comma,period,ellipsis"
	g2="T,Tcommaaccent,Tcaron"
	k="110" />
    <hkern g1="comma,period,ellipsis"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="25" />
    <hkern g1="comma,period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="65" />
    <hkern g1="comma,period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="135" />
    <hkern g1="comma,period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="comma,period,ellipsis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteleft,quotedblleft"
	k="60" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteright,quotedblright"
	k="60" />
    <hkern g1="comma,period,ellipsis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-20" />
    <hkern g1="comma,period,ellipsis"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="f,longs,fi,fl"
	k="10" />
    <hkern g1="comma,period,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="E,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="comma,period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-30" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron"
	k="110" />
    <hkern g1="hyphen,endash,emdash"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-15" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="95" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="75" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,ellipsis"
	k="140" />
    <hkern g1="quoteright,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,ellipsis"
	k="140" />
  </font>
</defs></svg>
