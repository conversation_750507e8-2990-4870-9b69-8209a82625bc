<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.exalead.apps.plma</groupId>
		<artifactId>plma-pom</artifactId>
		<version>427.5.0-SNAPSHOT</version>
	</parent>

	<groupId>com.exalead.apps.plma</groupId>
	<artifactId>plma-security-bridge</artifactId>
	<packaging>jar</packaging>
	<version>427.5.0-SNAPSHOT</version>

	<name>plma-security-bridge</name>
	<url>http://maven.apache.org</url>

	<properties>
		<!-- Properties used in cvplugin.properties -->
		<cv.plugin.type>core</cv.plugin.type>
		<cv.plugin.description>Generic and modular security source</cv.plugin.description>
 		<cv.plugin.reverse-classloading>true</cv.plugin.reverse-classloading>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

		<!-- External libs -->
		<commons-jexl.version>3.1</commons-jexl.version>
		<!-- JSON dependencies (provided by the kit) -->
		<jackson-version>2.14.1</jackson-version>
	</properties>

	<scm>
        <connection>scm:svn:git:****************************:apps/plmaonpremise/collab-spaces-security-source.git</connection>
        <developerConnection>scm:git:****************************:apps/plmaonpremise/collab-spaces-security-source.git</developerConnection>
        <url>https://gitlab.paris.exalead.com/apps/plmaonpremise/collab-spaces-security-source</url>
	  <tag>HEAD</tag>
  </scm>

	<build>
		<finalName>${project.name}</finalName>

		<plugins>
			<!-- Update classpath -->
			<plugin>
				<artifactId>maven-eclipse-plugin</artifactId>
			</plugin>

			<!-- Compile -->
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>

			<!-- Test -->
			<plugin>
				<artifactId>maven-surefire-plugin</artifactId>
			</plugin>

			<plugin>
				<artifactId>maven-surefire-report-plugin</artifactId>
			</plugin>

			<!-- Code style -->
			<plugin>
				<artifactId>maven-checkstyle-plugin</artifactId>
			</plugin>

			<!-- Code validation -->
			<plugin>
				<artifactId>maven-pmd-plugin</artifactId>
			</plugin>

			<!-- Seems to not be supported in Java 11...
                       Replaced by SpotBugs in reporting plugins
                       https://spotbugs.github.io/spotbugs-maven-plugin/usage.html
           <plugin>
               <groupId>org.codehaus.mojo</groupId>
               <artifactId>findbugs-maven-plugin</artifactId>
           </plugin>
           -->

			<!-- Generate and attach sources jar -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
			</plugin>

			<!-- Site generation -->
			<plugin>
				<artifactId>maven-site-plugin</artifactId>
			</plugin>

			<!-- Javadoc generation - uncomment here and in parent project to generate JavaDoc
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
			</plugin>
			-->

			<!-- Build and install CloudView core plugin
					Documentation: https://docs.factory.exalead.com/exa-cv-package-maven-plugin/plugin-info.html
			-->
			<plugin>
				<groupId>com.exalead.tools</groupId>
				<artifactId>exa-cv-package-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>cv-build</id>
						<goals>
							<!-- Generates 'cvplugin.properties' descriptor file
									Documentation: https://docs.factory.exalead.com/exa-cv-package-maven-plugin/core-descriptor-mojo.html
							-->
							<goal>core-descriptor</goal>
							<!-- Installs CloudView Core plugin on CloudView instance
                                 Mandatory properties :
                                 - cv.instance.host : CloudView host name
                                 - cv.instance.port : CloudView base port
                                 Documentation: https://docs.factory.exalead.com/exa-cv-package-maven-plugin/core-install-mojo.html
                            -->
							<goal>core-install</goal>
						</goals>
						<inherited>true</inherited>
						<configuration />
					</execution>
				</executions>
			</plugin>

			<!-- Generate custom components reports
                 use property "maven.cv-components-doc.skip=true" or "maven.cv-components-doc.report.skip=true" to skip
                 Documentation: https://docs.factory.exalead.com/exa-cv-reports-maven-plugin/plugin-info.html
             -->
			<plugin>
				<groupId>com.exalead.tools</groupId>
				<artifactId>exa-cv-reports-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>cv-report</id>
						<goals>
							<!-- Generate custom components documentation
									Documentation: https://docs.factory.exalead.com/exa-cv-reports-maven-plugin/cv-components-doc-mojo.html
							-->
							<goal>cv-components-doc</goal>
						</goals>
						<inherited>true</inherited>
						<configuration />
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
			</plugin>

			<!-- Release
            <plugin>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>-->
		</plugins>
	</build>

	<dependencies>
		<!-- CORE SDK dependencies -->
		
		<dependency>
			<groupId>${cv.groupid}</groupId>
			<artifactId>core-sdk</artifactId>
			<type>pom</type>
		</dependency>

		<dependency>
			<groupId>${cv.groupid}</groupId>
			<artifactId>access-sdk</artifactId>
			<type>pom</type>
		</dependency>

		<dependency>
			<groupId>${cv.groupid}</groupId>
			<artifactId>consolidation-java-client</artifactId>
			<version>${cloudview.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- ToolBox -->

		<dependency>
			<groupId>com.exalead.tools</groupId>
			<artifactId>CloudViewToolBox</artifactId>
		</dependency>

		<!-- JSON dependencies -->

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>${jackson-version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>${jackson-version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson-version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- Test dependencies -->
		
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>3.1</version>
		</dependency>

	</dependencies>
	
	<distributionManagement>
		<repository>
			<id>releases-repository</id>
			<url>${distribution.releases.url}</url>
		</repository>
		<snapshotRepository>
			<id>snapshots-repository</id>
			<url>${distribution.snapshots.url}</url>
		</snapshotRepository>
		<site>
			<id>nexus-site</id>
			<url>${distribution.site.url}${project.artifactId}</url>
		</site>
	</distributionManagement>
</project>
