<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>plma-tools</artifactId>
		<groupId>com.exalead.apps</groupId>
		<version>427.5.0-SNAPSHOT</version>
	</parent>

	<artifactId>plma-tools-core</artifactId>
	<packaging>jar</packaging>

	<name>plma-tools-core</name>
	<description>Custom CV core components (Document processors,Connectors...)</description>
	<url>https://nexus.exalead.com</url>

	<properties>
		<!-- Properties used in cvplugin.properties -->
		<cv.plugin.type>core</cv.plugin.type>
		<cv.plugin.description>${project.description}</cv.plugin.description>
		<cv.plugin.reverse-classloading>true</cv.plugin.reverse-classloading>
		<maven.cv-components-doc.overview.index>true</maven.cv-components-doc.overview.index>
	</properties>

	<build>
		<finalName>${project.name}</finalName>

		<plugins>
			<!-- Compile -->
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>

			<!-- Test -->
			<plugin>
				<artifactId>maven-surefire-plugin</artifactId>
			</plugin>

			<plugin>
				<artifactId>maven-surefire-report-plugin</artifactId>
			</plugin>

			<!-- Code style -->
			<plugin>
				<artifactId>maven-checkstyle-plugin</artifactId>
			</plugin>

			<!-- Code validation -->
			<plugin>
				<artifactId>maven-pmd-plugin</artifactId>
			</plugin>

			<!-- Seems to not be supported in Java 11...
                           Replaced by SpotBugs
                           https://spotbugs.github.io/spotbugs-maven-plugin/usage.html
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>findbugs-maven-plugin</artifactId>
			</plugin>
			-->

			<!-- Build doc -->
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
			</plugin>

			<!-- Build and install CloudView core plugin
					Documentation: https://docs.factory.exalead.com/exa-cv-package-maven-plugin/plugin-info.html
			-->
			<plugin>
				<groupId>com.exalead.tools</groupId>
				<artifactId>exa-cv-package-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>cv-build</id>
						<goals>
							<!-- Generates 'cvplugin.properties' descriptor file
									Documentation: https://docs.factory.exalead.com/exa-cv-package-maven-plugin/core-descriptor-mojo.html
							-->
							<goal>core-descriptor</goal>
							<!-- Installs CloudView Core plugin on CloudView instance
                                 Mandatory properties :
                                 - cv.instance.host : CloudView host name
                                 - cv.instance.port : CloudView base port
                                 Documentation: https://docs.factory.exalead.com/exa-cv-package-maven-plugin/core-install-mojo.html
                            -->
							<goal>core-install</goal>
						</goals>
						<inherited>true</inherited>
						<configuration />
					</execution>
				</executions>
			</plugin>

			<!-- Generate custom components reports
                 use property "maven.cv-components-doc.skip=true" or "maven.cv-components-doc.report.skip=true" to skip
                 Documentation: https://docs.factory.exalead.com/exa-cv-reports-maven-plugin/plugin-info.html
             -->
			<plugin>
				<groupId>com.exalead.tools</groupId>
				<artifactId>exa-cv-reports-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>cv-report</id>
						<goals>
							<!-- Generate custom components documentation
									Documentation: https://docs.factory.exalead.com/exa-cv-reports-maven-plugin/cv-components-doc-mojo.html
							-->
							<goal>cv-components-doc</goal>
						</goals>
						<inherited>true</inherited>
						<configuration />
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pdf-plugin</artifactId>
			</plugin>

			<!-- Release -->
			<plugin>
				<artifactId>maven-release-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

	<dependencies>
		<!-- CloudView Core dependencies  -->
		<dependency>
			<groupId>${cv.groupid}</groupId>
			<artifactId>core-sdk</artifactId>
			<type>pom</type>
		</dependency>
		
<!-- Jackson JSON parser -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.fasterxml.jackson.core</groupId>-->
<!--			<artifactId>jackson-annotations</artifactId>-->
<!--		</dependency>-->

		<!-- PLMA dependencies -->
		<dependency>
			<groupId>com.exalead.apps</groupId>
			<artifactId>plma-tools-commons</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- PLMA dependencies -->
		<dependency>
			<groupId>com.exalead.apps</groupId>
			<artifactId>apps-commons-core</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.google.guava</groupId>-->
<!--			<artifactId>guava</artifactId>-->
<!--			<version>21.0</version>-->
<!--			<scope>provided</scope>-->
<!--		</dependency>-->

<!--		<dependency>-->
<!--			<groupId>com.google.guava</groupId>-->
<!--			<artifactId>listenablefuture</artifactId>-->
<!--			<version>9999.0-empty-to-avoid-conflict-with-guava</version>-->
<!--			<scope>provided</scope>-->
<!--		</dependency>-->

<!--		<dependency>-->
<!--			<groupId>org.mapdb</groupId>-->
<!--			<artifactId>mapdb</artifactId>-->
<!--		</dependency>-->

		<!-- Diagnostics components, from third party project because
				not exclusively PLMA and included in external tools -->

		<dependency>
			<groupId>com.exalead.tools</groupId>
			<artifactId>cv-diagnostics-commons</artifactId>
			<version>${cv-diag-commons.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.exalead.plugin</groupId>
					<artifactId>exalead-plugin-commons</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

	</dependencies>
</project>
