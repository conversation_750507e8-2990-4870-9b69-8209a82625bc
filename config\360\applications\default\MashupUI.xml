<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<MashupUI xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" lastModifiedDate="0" appLastModifiedDate="1751968237065" type="web" xsi:noNamespaceSchemaLocation="">
    <MashupPage id="index" lastModifiedDate="1751968237065" migratorVersion="-1">
        <Options/>
        <Layout width="1000" widthFormat="px">
            <Table>
                <ColsConfig>
                    <ColConfig idx="1000000.0" width="1000"/>
                </ColsConfig>
                <Rows>
                    <Row rowIdx="1000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="1000000.0" rowEnd="2000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="center"/>
                            </Options>
                            <Widget useWidgetId="emptyBlock" enable="true" wuid="sCVdLwzh" compositeWidget="false">
<Options>
    <Option name="height" secured="false" value="80"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                            <Widget useWidgetId="logo" enable="true" wuid="u1mX3nqs" compositeWidget="false">
<Options>
    <Option name="urlLink" secured="false" value="/"/>
    <Option name="urlImage" secured="false" value="/resources/logo/images/cvlogo_big.png"/>
    <Option name="alt" secured="false" value="${i18n['logo.alt']}"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="2000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="2000000.0" rowEnd="3000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="center"/>
                            </Options>
                            <Widget useWidgetId="searchForm" enable="true" wuid="z3Suu6Mr" compositeWidget="false">
<Options>
    <Option name="action" secured="false" value="##search"/>
    <Option name="enableSuggest" secured="false" value="false"/>
    <Option name="suggestApiAction" secured="false" value="service"/>
    <Option name="suggestApiConfig" secured="false" value="sapi0"/>
    <Option name="suggestApiCommand" secured="false" value="suggest"/>
    <Option name="inputSize" secured="false" value="350"/>
    <Option name="inputName" secured="false" value="q"/>
    <Option name="buttonName" secured="false" value="${i18n['search']}"/>
    <Option name="advancedSearchEnabled" secured="false" value="true"/>
    <Option name="expandQueryAfter" secured="false" value="3"/>
    <Option name="geoLatitude" secured="false" value="lat"/>
    <Option name="geoLongitude" secured="false" value="lon"/>
    <Option name="geoMaximumAge" secured="false" value="5000"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                        </Cell>
                    </Row>
                </Rows>
            </Table>
        </Layout>
    </MashupPage>
    <MashupPage id="search" lastModifiedDate="1751968237065" migratorVersion="-1">
        <Options/>
        <Layout width="1000" widthFormat="px">
            <Table>
                <ColsConfig>
                    <ColConfig idx="1000000.0" width="300"/>
                    <ColConfig idx="2000000.0" width="400"/>
                    <ColConfig idx="3000000.0" width="300"/>
                </ColsConfig>
                <Rows>
                    <Row rowIdx="1000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="1000000.0" rowEnd="2000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="center"/>
                            </Options>
                            <Widget useWidgetId="logo" enable="true" wuid="sfFtwzlS" compositeWidget="false">
<Options>
    <Option name="urlLink" secured="false" value="/"/>
    <Option name="urlImage" secured="false" value="/resources/logo/images/cvlogo_medium.png"/>
    <Option name="alt" secured="false" value="${i18n['logo.alt']}"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                        </Cell>
                        <Cell colSpan="2" rowSpan="1" colStart="1000000.0" colEnd="3000000.0" rowStart="1000000.0" rowEnd="2000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="emptyBlock" enable="true" wuid="2MFVMET1" compositeWidget="false">
<Options>
    <Option name="height" secured="false" value="20"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                            <Widget useWidgetId="searchForm" enable="true" wuid="uDKgyePp" compositeWidget="false">
<Options>
    <Option name="action" secured="false" value="##search"/>
    <Option name="enableSuggest" secured="false" value="false"/>
    <Option name="suggestApiAction" secured="false" value="service"/>
    <Option name="suggestApiConfig" secured="false" value="sapi0"/>
    <Option name="suggestApiCommand" secured="false" value="suggest"/>
    <Option name="inputSize" secured="false" value="350"/>
    <Option name="inputName" secured="false" value="q"/>
    <Option name="buttonName" secured="false" value="${i18n['search']}"/>
    <Option name="advancedSearchEnabled" secured="false" value="true"/>
    <Option name="expandQueryAfter" secured="false" value="3"/>
    <Option name="geoLatitude" secured="false" value="lat"/>
    <Option name="geoLongitude" secured="false" value="lon"/>
    <Option name="geoMaximumAge" secured="false" value="5000"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="2000000.0">
                        <Options/>
                        <Cell colSpan="2" rowSpan="1" colStart="0.0" colEnd="2000000.0" rowStart="2000000.0" rowEnd="3000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="navigationHeader" enable="true" wuid="IL3SltMK" compositeWidget="false">
<Options>
    <Option name="jspPath" secured="false" value="default.jsp"/>
    <Option name="templateBasePath" secured="false" value="templates/"/>
    <Option name="sortableMetas" secured="false" value="${i18n['relevance']}##text_relevance##desc"/>
    <Option name="sortableMetas" secured="false" value="${i18n['date']}##document_lastmodifieddate##desc"/>
    <Option name="sortableMetas" secured="false" value="${i18n['size']}##document_file_size##desc"/>
    <Option name="spellSuggestionEnable" secured="false" value="true"/>
    <Option name="spellSuggestionQueryParameter" secured="false" value="q"/>
    <Option name="spellSuggestionText" secured="false" value="${i18n['suggestion.didYouMean']}"/>
</Options>
<UseFeeds useParentEntry="false">
    <FeedId>cloudview</FeedId>
</UseFeeds>
                            </Widget>
                            <Widget useWidgetId="displayHits" enable="true" wuid="TF95vTxZ" compositeWidget="false">
<Options>
    <Option name="filterMetas" secured="false" value="Exclude"/>
    <Option name="metas" secured="false" value="url, source, language, title, lastmodifieddate, displayurl, text, keyword, similardoc_query"/>
    <Option name="showHitMetas" secured="false" value="true"/>
    <Option name="customDisplay" secured="false" value="false"/>
    <Option name="showHitId" secured="false" value="true"/>
    <Option name="hitUrl" secured="false" value="${entry.metas['publicurl']!uh}"/>
    <Option name="showEmptyMetas" secured="false" value="false"/>
    <Option name="similarUrl" secured="false" value="?{${entry.metas['similardoc_query']}?\?q=${entry.metas['similardoc_query']}}"/>
    <Option name="showHitFacets" secured="false" value="true"/>
    <Option name="showEmptyFacets" secured="false" value="false"/>
    <Option name="showThumbnail" secured="false" value="on the right"/>
    <Option name="hitFilterFacetsType" secured="false" value="No filtering"/>
    <Option name="showPreview" secured="false" value="true"/>
    <Option name="showDownload" secured="false" value="true"/>
    <Option name="showTextOnTop" secured="false" value="true"/>
    <Option name="showTextOnTopTruncate" secured="false" value="500"/>
    <Option name="useThumbnailPreview" secured="false" value="true"/>
    <Option name="showHitIcon" secured="false" value="true"/>
    <Option name="customJspPathHit" secured="false" value="tabulated/default.jsp"/>
    <Option name="defaultJspPathHit" secured="false" value="default/default.jsp"/>
    <Option name="noResultsJspPathHit" secured="false" value="/WEB-INF/jsp/commons/noResults.jsp"/>
    <Option name="templateBasePath" secured="false" value="templates/"/>
    <Option name="hitUrlTarget" secured="false" value="Current Page"/>
    <Option name="metaUrlTarget" secured="false" value="Current Page"/>
    <Option name="sortModeFacets" secured="false" value="default"/>
    <Option name="hitFacetSortStrategy" secured="false" value="default"/>
</Options>
<UseFeeds useParentEntry="false">
    <FeedId>cloudview</FeedId>
</UseFeeds>
                            </Widget>
                            <Widget useWidgetId="pagination" enable="true" wuid="XgjS91jn" compositeWidget="false">
<Trigger className="com.exalead.cv360.searchui.triggers.impl.RemoveIfNoEntries" enable="true"/>
<Options>
    <Option name="jspPath" secured="false" value="default.jsp"/>
    <Option name="nbPageToShowInPagination" secured="false" value="9"/>
    <Option name="templateBasePath" secured="false" value="templates/"/>
</Options>
<UseFeeds useParentEntry="false">
    <FeedId>cloudview</FeedId>
</UseFeeds>
                            </Widget>
                        </Cell>
                        <Cell colSpan="1" rowSpan="1" colStart="2000000.0" colEnd="3000000.0" rowStart="2000000.0" rowEnd="3000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="refines" enable="true" wuid="GY9TIcdK" compositeWidget="false">
<Options>
    <Option name="title" secured="false" value="${i18n['refines.title']}"/>
    <Option name="filter" secured="false" value="No filtering"/>
    <Option name="values" secured="false" value="count"/>
    <Option name="facetTemplateDirectory" secured="false" value="default"/>
    <Option name="nbSubFacets" secured="false" value="1000"/>
    <Option name="templateBasePath" secured="false" value="templates/"/>
    <Option name="noResultsJspPathHit" secured="false" value="/WEB-INF/jsp/commons/noFacets.jsp"/>
</Options>
<UseFeeds useParentEntry="false">
    <FeedId>cloudview</FeedId>
</UseFeeds>
                            </Widget>
                        </Cell>
                    </Row>
                </Rows>
            </Table>
        </Layout>
    </MashupPage>
    <SpringControllers/>
    <Options>
        <Option name="reporterName" secured="false" value="mashup-ui-reporting"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/jquery-3.3.1.min.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/jquery-migrate-3.0.1.min.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/jquery.browser-detection-compat.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/jquery.cookie.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/jquery.spinner.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/buildUrl.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/oop.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/mashupAjaxClient.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/mashupI18N.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/exa/core.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/timezone.js"/>
        <Option name="cssFile" secured="false" value="/resources/css/fonts/fonts.less"/>
        <Option name="cssFile" secured="false" value="/resources/css/fonts/icons.less"/>
        <Option name="displayStackTrace" secured="false" value="true"/>
        <Option name="sitemapEnabled" secured="false" value="false"/>
        <Option name="sitemapQueryParam" secured="false" value="q"/>
        <Option name="sitemapDefaultPage" secured="false" value="search"/>
        <Option name="sitemapManual" secured="false" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#10;&lt;urlset&gt;&#10;	&lt;url&gt;&#10;		&lt;loc&gt;http://sitename.com/contact&lt;/loc&gt;&#10;		&lt;changefreq&gt;weekly&lt;/changefreq&gt;&#10;		&lt;priority&gt;0.5&lt;/priority&gt;&#10;	&lt;/url&gt;&#10;	&lt;url&gt;&#10;		&lt;loc&gt;http://sitename.com/products&lt;/loc&gt;&#10;		&lt;changefreq&gt;daily&lt;/changefreq&gt;&#10;		&lt;priority&gt;1.0&lt;/priority&gt;&#10;	&lt;/url&gt;&#10;&lt;/urlset&gt;&#10;"/>
        <Option name="robotsRules" secured="false" value="User-agent: *&#10;Disallow: /resources/&#10;"/>
        <Option name="cssTheme" secured="false" value="theme_enterprise"/>
        <Option name="packResources" secured="false" value="true"/>
        <Option name="resourceMaxAgeCache" secured="false" value="86400"/>
        <Option name="usersCheck" secured="false" value="false"/>
    </Options>
    <CustomComponentList>
        <component>com.exalead.gwt.bconsole.recommend.access.RecommendationDebugTrigger</component>
    </CustomComponentList>
    <Trigger className="com.exalead.cv360.searchui.triggers.impl.I18NTrigger" enable="true">
        <Parameter name="parameterName" secured="false" value="lang"/>
    </Trigger>
    <Trigger className="com.exalead.cv360.searchui.triggers.impl.CookieToParameterTrigger" enable="true">
        <Parameter name="parameterName" secured="false" value="timezone"/>
        <Parameter name="cookieName" secured="false" value="mashup-timezone"/>
    </Trigger>
</MashupUI>
