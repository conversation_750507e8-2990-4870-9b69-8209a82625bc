#Plugin properties
#Tue Jul 08 15:22:01 IST 2025
component.0.class=com.exalead.papi.enovia.commons.consolidation.CalculatePathAggregationProcessor
component.0.description=Get path
component.0.interface.0.class=com.exalead.papi.enovia.commons.consolidation.CalculatePathAggregationProcessor
component.0.interface.1.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.0.interface.2.class=java.lang.Object
component.0.label=Get path
component.1.class=com.exalead.papi.enovia.commons.consolidation.CopyMetasAggregationProcessor
component.1.interface.0.class=com.exalead.papi.enovia.commons.consolidation.CopyMetasAggregationProcessor
component.1.interface.1.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.1.interface.2.class=java.lang.Object
component.10.class=com.exalead.papi.enovia.commons.papi.RepRefDumperConfig
component.10.interface.0.class=com.exalead.papi.enovia.commons.papi.RepRefDumperConfig
component.10.interface.1.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig$DefaultImpl
component.10.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig
component.10.interface.3.class=java.lang.Object
component.11.class=com.exalead.papi.enovia.commons.papi.SingleToMultiThreadPushAPI
component.11.description=If your connector pushes only on one thread this papi filter will multi thread papi calls
component.11.interface.0.class=com.exalead.papi.enovia.commons.papi.SingleToMultiThreadPushAPI
component.11.interface.1.class=com.exalead.mercury.component.CVComponent
component.11.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilter
component.11.interface.3.class=com.exalead.papi.helper.pipe.PipedPushAPI
component.11.interface.4.class=com.exalead.papi.helper.PushAPI
component.11.interface.5.class=java.lang.Object
component.11.label=Single to multithread PushAPI
component.12.class=com.exalead.papi.enovia.commons.papi.SingleToMultiThreadPushAPIConfig
component.12.description=If your connector pushes only on one thread this papi filter will multi thread papi calls
component.12.interface.0.class=com.exalead.papi.enovia.commons.papi.SingleToMultiThreadPushAPIConfig
component.12.interface.1.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig$DefaultImpl
component.12.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig
component.12.interface.3.class=java.lang.Object
component.12.label=Single to multithread PushAPI
component.13.class=com.exalead.papi.enovia.commons.papi.VPMV6FamilySetter
component.13.description=VPMV6FamilySetter
component.13.interface.0.class=com.exalead.papi.enovia.commons.papi.VPMV6FamilySetter
component.13.interface.1.class=com.exalead.mercury.component.CVComponent
component.13.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilter
component.13.interface.3.class=com.exalead.papi.helper.pipe.PipedPushAPI
component.13.interface.4.class=com.exalead.papi.helper.PushAPI
component.13.interface.5.class=java.lang.Object
component.13.label=VPMV6FamilySetter
component.14.class=com.exalead.papi.enovia.commons.papi.VPMV6FamilySetterConfig
component.14.description=VPMV6FamilySetter
component.14.interface.0.class=com.exalead.papi.enovia.commons.papi.VPMV6FamilySetterConfig
component.14.interface.1.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig$DefaultImpl
component.14.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig
component.14.interface.3.class=java.lang.Object
component.14.label=VPMV6FamilySetter
component.15.class=com.exalead.papi.enovia.commons.papi.VPMV6Filter
component.15.description=VPMV6Filter
component.15.interface.0.class=com.exalead.papi.enovia.commons.papi.VPMV6Filter
component.15.interface.1.class=com.exalead.mercury.component.CVComponent
component.15.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilter
component.15.interface.3.class=com.exalead.papi.helper.pipe.PipedPushAPI
component.15.interface.4.class=com.exalead.papi.helper.PushAPI
component.15.interface.5.class=java.lang.Object
component.15.label=VPMV6Filter
component.16.class=com.exalead.papi.enovia.commons.papi.VPMV6FilterConfig
component.16.description=VPMV6Filter
component.16.interface.0.class=com.exalead.papi.enovia.commons.papi.VPMV6FilterConfig
component.16.interface.1.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig$DefaultImpl
component.16.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig
component.16.interface.3.class=java.lang.Object
component.16.label=VPMV6Filter
component.17.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorBasicConnectivityHandler
component.17.description=Basic
component.17.interface.0.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorBasicConnectivityHandler
component.17.interface.1.class=com.exalead.enovia.er.connector.sdk.connectivity.ConnectivityHandler
component.17.interface.2.class=java.lang.Object
component.17.label=Basic
component.18.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorBasicConnectivityHandlerConfig
component.18.description=On premise
component.18.interface.0.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorBasicConnectivityHandlerConfig
component.18.interface.1.class=com.exalead.enovia.er.connector.sdk.connectivity.ConnectivityHandlerConfig
component.18.interface.2.class=java.lang.Object
component.18.label=On premise
component.19.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorCloudCASConnectivityHandler
component.19.description=Cloud with CAS
component.19.interface.0.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorCloudCASConnectivityHandler
component.19.interface.1.class=com.exalead.enovia.er.connector.sdk.connectivity.ConnectivityHandler
component.19.interface.2.class=java.lang.Object
component.19.label=Cloud with CAS
component.2.class=com.exalead.papi.enovia.commons.consolidation.CopyMetasTransformationProcessor
component.2.description=Copy metas
component.2.interface.0.class=com.exalead.papi.enovia.commons.consolidation.CopyMetasTransformationProcessor
component.2.interface.1.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.2.interface.2.class=java.lang.Object
component.2.label=Copy metas
component.20.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorCloudCASConnectivityHandlerConfig
component.20.description=Cloud with CAS
component.20.interface.0.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorCloudCASConnectivityHandlerConfig
component.20.interface.1.class=com.exalead.enovia.er.connector.sdk.connectivity.ConnectivityHandlerConfig
component.20.interface.2.class=java.lang.Object
component.20.label=Cloud with CAS
component.21.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorCloudConnectivityHandler
component.21.description=Cloud
component.21.interface.0.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorCloudConnectivityHandler
component.21.interface.1.class=com.exalead.enovia.er.connector.sdk.connectivity.ConnectivityHandler
component.21.interface.2.class=java.lang.Object
component.21.label=Cloud
component.22.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorCloudConnectivityHandlerConfig
component.22.description=Cloud
component.22.interface.0.class=com.exalead.enovia.er.connector.config.connectivity.ERConnectorCloudConnectivityHandlerConfig
component.22.interface.1.class=com.exalead.enovia.er.connector.sdk.connectivity.ConnectivityHandlerConfig
component.22.interface.2.class=java.lang.Object
component.22.label=Cloud
component.23.class=com.exalead.enovia.er.connector.config.impl.DefaultCVGraphManagerConfig
component.23.description=This is the most simple mode as it allows you to simplify the naming of URIs. The Trace tenant option can be used for cloud deployments, to trace the Enovia vault and tenant.
component.23.interface.0.class=com.exalead.enovia.er.connector.config.impl.DefaultCVGraphManagerConfig
component.23.interface.1.class=com.exalead.enovia.er.connector.config.CVGraphManagerConfig
component.23.interface.2.class=java.lang.Object
component.23.label=Default E/R graph manager
component.24.class=com.exalead.enovia.er.connector.config.impl.DefaultCVGraphManagerHolder
component.24.description=Default E/R connector graph manager
component.24.interface.0.class=com.exalead.enovia.er.connector.config.impl.DefaultCVGraphManagerHolder
component.24.interface.1.class=com.exalead.enovia.er.connector.config.CVGraphManagerHolder
component.24.interface.2.class=java.lang.Object
component.24.label=Default E/R graph manager
component.25.class=com.exalead.enovia.er.connector.config.impl.ENOVIASBAConnectorCVGraphManagerConfig
component.25.description=Graphs will be managed as they were when using the legacy SBA connector (URIs and Consolidation Server object graph will be treated as they were using the old SBA connector). 
component.25.interface.0.class=com.exalead.enovia.er.connector.config.impl.ENOVIASBAConnectorCVGraphManagerConfig
component.25.interface.1.class=com.exalead.enovia.er.connector.config.CVGraphManagerConfig
component.25.interface.2.class=java.lang.Object
component.25.label=ENOVIA SBA graph manager
component.26.class=com.exalead.enovia.er.connector.config.impl.ENOVIASBAConnectorCVGraphManagerHolder
component.26.description=ENOVIA SBA graph manager, used for legacy SBA project migration
component.26.interface.0.class=com.exalead.enovia.er.connector.config.impl.ENOVIASBAConnectorCVGraphManagerHolder
component.26.interface.1.class=com.exalead.enovia.er.connector.config.CVGraphManagerHolder
component.26.interface.2.class=java.lang.Object
component.26.label=ENOVIA SBA graph manager
component.27.class=com.exalead.enovia.er.connector.config.impl.http.ConnectTimeoutConfig
component.27.description=Timeout when establishing a connection to an HTTP server
component.27.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.ConnectTimeoutConfig
component.27.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientEntryConfig
component.27.interface.2.class=java.lang.Object
component.27.label=HTTP connection timeout
component.28.class=com.exalead.enovia.er.connector.config.impl.http.SoKeepAlive
component.28.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.SoKeepAlive
component.28.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientConfigHolder
component.28.interface.2.class=java.lang.Object
component.29.class=com.exalead.enovia.er.connector.config.impl.http.SoKeepAliveConfig
component.29.description=If no data has been sent over a socket for duration (2 hours, depends of the implementation) this client sends a probe frame to the server to assert the socket is still valid.
component.29.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.SoKeepAliveConfig
component.29.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientEntryConfig
component.29.interface.2.class=java.lang.Object
component.29.label=Socket keep-alive
component.3.class=com.exalead.papi.enovia.commons.consolidation.SetMetasAggregationProcessor
component.3.interface.0.class=com.exalead.papi.enovia.commons.consolidation.SetMetasAggregationProcessor
component.3.interface.1.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.3.interface.2.class=java.lang.Object
component.30.class=com.exalead.enovia.er.connector.config.impl.http.SoLinger
component.30.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.SoLinger
component.30.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientConfigHolder
component.30.interface.2.class=java.lang.Object
component.31.class=com.exalead.enovia.er.connector.config.impl.http.SoLingerConfig
component.31.description=Disables (if set to 0) the waiting time for an acknowledgment that all the data has been sent and the connection is gracefully closed. When the timeout is reached, a TCP RST is sent to terminate forcefully the connection. If set to 0 a TCP RST is immediately sent upon closure.
component.31.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.SoLingerConfig
component.31.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientEntryConfig
component.31.interface.2.class=java.lang.Object
component.31.label=TCP linger
component.32.class=com.exalead.enovia.er.connector.config.impl.http.SoReuseAddress
component.32.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.SoReuseAddress
component.32.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientConfigHolder
component.32.interface.2.class=java.lang.Object
component.33.class=com.exalead.enovia.er.connector.config.impl.http.SoReuseAddressConfig
component.33.description=Re-uses Socket address. Only useful of multicast sockets
component.33.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.SoReuseAddressConfig
component.33.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientEntryConfig
component.33.interface.2.class=java.lang.Object
component.33.label=Socket reuse address
component.34.class=com.exalead.enovia.er.connector.config.impl.http.SoTimeout
component.34.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.SoTimeout
component.34.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientConfigHolder
component.34.interface.2.class=java.lang.Object
component.35.class=com.exalead.enovia.er.connector.config.impl.http.SoTimeoutConfig
component.35.description=Timeout on blocking Socket operation. Blocking operation here is reading data from the server.
component.35.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.SoTimeoutConfig
component.35.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientEntryConfig
component.35.interface.2.class=java.lang.Object
component.35.label=TCP timeout
component.36.class=com.exalead.enovia.er.connector.config.impl.http.TcpNoDelay
component.36.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.TcpNoDelay
component.36.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientConfigHolder
component.36.interface.2.class=java.lang.Object
component.37.class=com.exalead.enovia.er.connector.config.impl.http.TcpNoDelayConfig
component.37.description=Disable Nagle's algorithm
component.37.interface.0.class=com.exalead.enovia.er.connector.config.impl.http.TcpNoDelayConfig
component.37.interface.1.class=com.exalead.enovia.er.connector.config.impl.http.HttpClientEntryConfig
component.37.interface.2.class=java.lang.Object
component.37.label=TCP no delay
component.38.class=com.exalead.enovia.er.connector.config.query.ERConnectorFileBasedQuery
component.38.description=File
component.38.interface.0.class=com.exalead.enovia.er.connector.config.query.ERConnectorFileBasedQuery
component.38.interface.1.class=com.exalead.enovia.er.connector.sdk.query.QueryHandler
component.38.interface.2.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponent
component.38.interface.3.class=java.lang.Object
component.38.label=File
component.39.class=com.exalead.enovia.er.connector.config.query.ERConnectorFileBasedQueryConfig
component.39.description=File
component.39.interface.0.class=com.exalead.enovia.er.connector.config.query.ERConnectorFileBasedQueryConfig
component.39.interface.1.class=com.exalead.enovia.er.connector.sdk.query.QueryHandlerConfig
component.39.interface.2.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponentConfig
component.39.interface.3.class=java.lang.Object
component.39.label=File
component.4.class=com.exalead.papi.enovia.commons.consolidation.SetMetasTransformationProcessor
component.4.interface.0.class=com.exalead.papi.enovia.commons.consolidation.SetMetasTransformationProcessor
component.4.interface.1.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor
component.4.interface.2.class=java.lang.Object
component.40.class=com.exalead.enovia.er.connector.config.query.ERConnectorUIQuery
component.40.description=UI
component.40.interface.0.class=com.exalead.enovia.er.connector.config.query.ERConnectorUIQuery
component.40.interface.1.class=com.exalead.enovia.er.connector.sdk.query.QueryHandler
component.40.interface.2.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponent
component.40.interface.3.class=java.lang.Object
component.40.label=UI
component.41.class=com.exalead.enovia.er.connector.config.query.ERConnectorUIQueryConfig
component.41.description=UI
component.41.interface.0.class=com.exalead.enovia.er.connector.config.query.ERConnectorUIQueryConfig
component.41.interface.1.class=com.exalead.enovia.er.connector.sdk.query.QueryHandlerConfig
component.41.interface.2.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponentConfig
component.41.interface.3.class=java.lang.Object
component.41.label=UI
component.42.class=com.exalead.enovia.er.connector.config.query.PolicyIncludeExcludeItemProcessor
component.42.interface.0.class=com.exalead.enovia.er.connector.config.query.PolicyIncludeExcludeItemProcessor
component.42.interface.1.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessor
component.42.interface.2.class=java.lang.Object
component.43.class=com.exalead.enovia.er.connector.config.query.PolicyIncludeExcludeItemProcessorConfig
component.43.description=Policy include exclude filter
component.43.interface.0.class=com.exalead.enovia.er.connector.config.query.PolicyIncludeExcludeItemProcessorConfig
component.43.interface.1.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorConfig
component.43.interface.2.class=java.lang.Object
component.43.label=Policy include exclude filter
component.44.class=com.exalead.enovia.er.connector.consolidation.ArcsToMetaProcessor
component.44.interface.0.class=com.exalead.enovia.er.connector.consolidation.ArcsToMetaProcessor
component.44.interface.1.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessor
component.44.interface.2.class=com.exalead.enovia.er.connector.sdk.query.ERCustomSelectComponent
component.44.interface.3.class=java.lang.Object
component.45.class=com.exalead.enovia.er.connector.consolidation.ArcsToMetaProcessorConfig
component.45.interface.0.class=com.exalead.enovia.er.connector.consolidation.ArcsToMetaProcessorConfig
component.45.interface.1.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorConfig
component.45.interface.2.class=java.lang.Object
component.46.class=com.exalead.enovia.er.connector.files.DefaultFileContainerHandler
component.46.interface.0.class=com.exalead.enovia.er.connector.files.DefaultFileContainerHandler
component.46.interface.1.class=com.exalead.enovia.er.connector.sdk.files.FileContainerHandler
component.46.interface.2.class=java.lang.Object
component.47.class=com.exalead.enovia.er.connector.files.SBAConnectorFileContainerHandler
component.47.description=ENOVIA SBA file container handler
component.47.interface.0.class=com.exalead.enovia.er.connector.files.SBAConnectorFileContainerHandler
component.47.interface.1.class=com.exalead.enovia.er.connector.sdk.files.FileContainerHandler
component.47.interface.2.class=java.lang.Object
component.47.label=ENOVIA SBA file handler
component.48.class=com.exalead.enovia.er.connector.files.SBAConnectorFileContainerHandlerConfig
component.48.description=ENOVIA SBA file container handler
component.48.interface.0.class=com.exalead.enovia.er.connector.files.SBAConnectorFileContainerHandlerConfig
component.48.interface.1.class=com.exalead.enovia.er.connector.sdk.files.FileContainerHandlerConfig
component.48.interface.2.class=java.lang.Object
component.48.label=ENOVIA SBA file handler
component.49.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandler
component.49.interface.0.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandler
component.49.interface.1.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandler
component.49.interface.2.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponent
component.49.interface.3.class=java.lang.Object
component.5.class=com.exalead.papi.enovia.commons.papi.MetaAnalyser
component.5.description=MetaAnalyser
component.5.interface.0.class=com.exalead.papi.enovia.commons.papi.MetaAnalyser
component.5.interface.1.class=com.exalead.mercury.component.CVComponent
component.5.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilter
component.5.interface.3.class=com.exalead.papi.helper.pipe.PipedPushAPI
component.5.interface.4.class=com.exalead.papi.helper.PushAPI
component.5.interface.5.class=java.lang.Object
component.5.label=MetaAnalyser
component.50.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandlerConfig
component.50.description=Default Security. Only opts to choose to make visible to everybody or not
component.50.interface.0.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandlerConfig
component.50.interface.1.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandlerConfig
component.50.interface.2.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponentConfig
component.50.interface.3.class=java.lang.Object
component.50.label=Default Security
component.51.class=com.exalead.enovia.er.connector.security.impl.EmptyItemProcessorConfig
component.51.interface.0.class=com.exalead.enovia.er.connector.security.impl.EmptyItemProcessorConfig
component.51.interface.1.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorConfig
component.51.interface.2.class=java.lang.Object
component.52.class=com.exalead.enovia.er.connector.security.impl.ProjectSecurityHandler
component.52.description=Project Based Security
component.52.interface.0.class=com.exalead.enovia.er.connector.security.impl.ProjectSecurityHandler
component.52.interface.1.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandler
component.52.interface.2.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandler
component.52.interface.3.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponent
component.52.interface.4.class=java.lang.Object
component.52.label=Project Based Security
component.53.class=com.exalead.enovia.er.connector.security.impl.ProjectSecurityHandlerConfig
component.53.description=Project Based Security
component.53.interface.0.class=com.exalead.enovia.er.connector.security.impl.ProjectSecurityHandlerConfig
component.53.interface.1.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandlerConfig
component.53.interface.2.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponentConfig
component.53.interface.3.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandlerConfig
component.53.interface.4.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandlerConfig
component.53.interface.5.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponentConfig
component.53.interface.6.class=java.lang.Object
component.53.label=Project Based Security
component.54.class=com.exalead.enovia.er.connector.security.impl.SBAConnectorSecurityHandler
component.54.description=ENOVIA SBA Security
component.54.interface.0.class=com.exalead.enovia.er.connector.security.impl.SBAConnectorSecurityHandler
component.54.interface.1.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandler
component.54.interface.2.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandler
component.54.interface.3.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponent
component.54.interface.4.class=java.lang.Object
component.54.label=ENOVIA SBA Security
component.55.class=com.exalead.enovia.er.connector.security.impl.SBAConnectorSecurityHandlerConfig
component.55.description=ENOVIA SBA Security
component.55.interface.0.class=com.exalead.enovia.er.connector.security.impl.SBAConnectorSecurityHandlerConfig
component.55.interface.1.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandlerConfig
component.55.interface.2.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandlerConfig
component.55.interface.3.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponentConfig
component.55.interface.4.class=java.lang.Object
component.55.label=ENOVIA SBA Security
component.56.class=com.exalead.enovia.er.connector.security.impl.SecurityACLTokenAggregationProcessor
component.56.description=Copies into the security metas the mxentry of the linked ACL
component.56.interface.0.class=com.exalead.enovia.er.connector.security.impl.SecurityACLTokenAggregationProcessor
component.56.interface.1.class=com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor
component.56.interface.2.class=java.lang.Object
component.56.label=ACLs Security Aggregator
component.57.class=com.exalead.enovia.er.connector.security.impl.SecurityV2Handler
component.57.description=Security V2
component.57.interface.0.class=com.exalead.enovia.er.connector.security.impl.SecurityV2Handler
component.57.interface.1.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandler
component.57.interface.2.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandler
component.57.interface.3.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponent
component.57.interface.4.class=java.lang.Object
component.57.label=Security V2
component.58.class=com.exalead.enovia.er.connector.security.impl.SecurityV2HandlerConfig
component.58.description=Security V2
component.58.interface.0.class=com.exalead.enovia.er.connector.security.impl.SecurityV2HandlerConfig
component.58.interface.1.class=com.exalead.enovia.er.connector.security.impl.DefaultSecurityHandlerConfig
component.58.interface.2.class=com.exalead.enovia.er.connector.sdk.security.SecurityHandlerConfig
component.58.interface.3.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorDefinitionComponentConfig
component.58.interface.4.class=java.lang.Object
component.58.label=Security V2
component.59.class=com.exalead.enovia.er.connector.ERConnectorWrapper
component.59.description=ENOVIA ER
component.59.interface.0.class=com.exalead.enovia.er.connector.ERConnectorWrapper
component.59.interface.1.class=com.exalead.enovia.er.connector.AbstractERConnectorWrapper
component.59.interface.2.class=com.exalead.papi.enovia.commons.connector.ContextualizedConnectorWrapper
component.59.interface.3.class=com.exalead.papi.framework.connectors.Connector
component.59.interface.4.class=com.exalead.papi.framework.connectors.v10.ConnectorInterface
component.59.interface.5.class=com.exalead.papi.framework.connectors.v20.ConnectorInterface
component.59.interface.6.class=com.exalead.papi.framework.connectors.v30.ConnectorInterface
component.59.interface.7.class=com.exalead.mercury.component.CVComponent
component.59.interface.8.class=java.lang.Object
component.59.label=ENOVIA ER
component.6.class=com.exalead.papi.enovia.commons.papi.MetaAnalyserConfig
component.6.description=MetaAnalyser
component.6.interface.0.class=com.exalead.papi.enovia.commons.papi.MetaAnalyserConfig
component.6.interface.1.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig$DefaultImpl
component.6.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig
component.6.interface.3.class=java.lang.Object
component.6.label=MetaAnalyser
component.60.class=com.exalead.enovia.er.connector.PolicyFilterItemProcessor
component.60.description=Policy Filter
component.60.interface.0.class=com.exalead.enovia.er.connector.PolicyFilterItemProcessor
component.60.interface.1.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessor
component.60.interface.2.class=java.lang.Object
component.60.label=Policy Filter
component.61.class=com.exalead.enovia.er.connector.PolicyFilterItemProcessorConfig
component.61.description=Policy Filter
component.61.interface.0.class=com.exalead.enovia.er.connector.PolicyFilterItemProcessorConfig
component.61.interface.1.class=com.exalead.enovia.er.connector.sdk.processing.ERItemProcessorConfig
component.61.interface.2.class=java.lang.Object
component.61.label=Policy Filter
component.62.class=com.exalead.enovia.er.securitysource.ERConnectorSecuritySource
component.62.description=ER Connector
component.62.interface.0.class=com.exalead.enovia.er.securitysource.ERConnectorSecuritySource
component.62.interface.1.class=com.exalead.mercury.component.CVComponent
component.62.interface.2.class=com.exalead.security.sources.common.SecuritySource
component.62.interface.3.class=com.exalead.mercury.component.CVComponent
component.62.interface.4.class=java.lang.Object
component.62.label=ER Connector
component.7.class=com.exalead.papi.enovia.commons.papi.PartDump
component.7.description=PartDump
component.7.interface.0.class=com.exalead.papi.enovia.commons.papi.PartDump
component.7.interface.1.class=com.exalead.mercury.component.CVComponent
component.7.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilter
component.7.interface.3.class=com.exalead.papi.framework.connectors.PushAPIFilter$Fetch
component.7.interface.4.class=com.exalead.papi.helper.pipe.PipedPushAPI
component.7.interface.5.class=com.exalead.papi.helper.PushAPI
component.7.interface.6.class=java.lang.Object
component.7.label=PartDump
component.8.class=com.exalead.papi.enovia.commons.papi.PartDumpConfig
component.8.description=PartDump
component.8.interface.0.class=com.exalead.papi.enovia.commons.papi.PartDumpConfig
component.8.interface.1.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig$DefaultImpl
component.8.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilterConfig
component.8.interface.3.class=java.lang.Object
component.8.label=PartDump
component.9.class=com.exalead.papi.enovia.commons.papi.RepRefDumper
component.9.description=RepRefDumper
component.9.interface.0.class=com.exalead.papi.enovia.commons.papi.RepRefDumper
component.9.interface.1.class=com.exalead.mercury.component.CVComponent
component.9.interface.2.class=com.exalead.papi.framework.connectors.PushAPIFilter
component.9.interface.3.class=com.exalead.papi.helper.pipe.PipedPushAPI
component.9.interface.4.class=com.exalead.papi.helper.PushAPI
component.9.interface.5.class=java.lang.Object
component.9.label=RepRefDumper
debug.classes.count=4804
debug.destination.path=D\:\\WS\\15000_PIU\\bin\\..\\tmp\\plugin-install
debug.jar.count=23
lastmodified=1751968317431
plugin.reverse-classloading=false
timestamp=1751968317222
