<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Thu Feb  7 11:12:21 2019
 By Aleksey,,,
Copyright (c) 2010 by Dassault Systmes Company. All rights reserved.
</metadata>
<defs>
<font id="3ds-Bold" horiz-adv-x="605" >
  <font-face 
    font-family="3ds"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 3 2 0 0 2 0 4"
    ascent="800"
    descent="-200"
    x-height="487"
    cap-height="665"
    bbox="-50 -232 1209 1013"
    underline-thickness="50"
    underline-position="-150"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="557" 
d="M474 665v-665h-391v665h391zM202 108h152v450h-152v-450z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="630" 
d="M86 487v53q0 59 22.5 98t60.5 62t86.5 32.5t101.5 9.5q23 0 48.5 -1.5t49.5 -4t45 -6t36 -7.5l-13 -120q-29 5 -70 10.5t-82 5.5q-24 0 -45 -3t-37 -11.5t-25.5 -23.5t-9.5 -38v-56h310v-487h-168v373h-142v-373h-168v373h-73v114h73z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="630" 
d="M86 487v53q0 52 19 90t53.5 63t81.5 37t104 12q60 0 111 -6t109 -16v-720h-168v617h-30q-19 0 -39 -2t-36 -9.5t-26.5 -22.5t-10.5 -40v-56h76v-114h-76v-373h-168v373h-73v114h73z" />
    <glyph glyph-name=".notdef" horiz-adv-x="557" 
d="M474 665v-665h-391v665h391zM202 108h152v450h-152v-450z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="291" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="291" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="304" 
d="M245 665l-27 -470h-127l-29 470h183zM58 65q0 38 26 58t68 20t67.5 -20t25.5 -58t-25.5 -57.5t-67.5 -19.5t-68 19.5t-26 57.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="480" 
d="M232 665l-31 -213h-108l-31 213h170zM435 665l-31 -213h-108l-31 213h170z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="715" 
d="M362 665l-35 -180h110l35 180h124l-35 -180h114l-20 -105h-114l-20 -105h114l-20 -105h-115l-33 -170h-123l33 170h-111l-32 -170h-115l33 170h-114l19 105h115l21 105h-115l20 105h115l35 180h114zM286 275h111l20 105h-110z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M272 9q-61 1 -110 10t-85 19l17 120q33 -8 79 -17.5t97 -9.5q19 0 36 2.5t30 8.5t21 16t8 25q0 23 -22 41t-55 34.5t-72 35t-72 43.5t-55 59t-22 82q0 43 13 74t37.5 51.5t59.5 32t78 16.5v90h104v-85q40 -1 82 -5t76 -11l-11 -117q-18 4 -40 7t-44.5 5t-44 3t-38.5 1
q-47 0 -73 -13t-26 -43q0 -20 22 -35.5t55 -31t72 -33.5t72 -44t55 -63t22 -90q0 -73 -42 -115t-120 -56v-100h-104v93z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="860" 
d="M201 595q-22 0 -38.5 -20.5t-16.5 -74.5q0 -55 16.5 -76t38.5 -21q23 0 38 21t15 75q0 55 -15 75.5t-38 20.5zM201 676q37 0 68.5 -9.5t54 -30.5t35.5 -55t13 -82q0 -47 -13 -80.5t-35.5 -55.5t-54 -32t-68.5 -10t-68 10t-54 32t-36 56t-13 81q0 48 13 81.5t36 54.5
t54 30.5t68 9.5zM658 264q-22 0 -39 -20.5t-17 -74.5t17 -75t39 -21t37.5 21t15.5 74q0 55 -15 75.5t-38 20.5zM658 345q37 0 68.5 -9.5t54 -30.5t35.5 -54.5t13 -82.5q0 -47 -13 -80.5t-35.5 -55.5t-54 -32t-68.5 -10t-68 10t-54 32t-36 56t-13 81q0 48 13 81.5t36 54.5
t54 30.5t68 9.5zM700 666l-431 -666h-111l430 666h112z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="686" 
d="M477 253q14 17 25.5 49.5t16.5 67.5l138 -24q-4 -24 -12.5 -53t-20.5 -57t-26 -52.5t-28 -40.5l128 -143h-210l-35 40q-70 -52 -186 -52q-113 0 -177 46.5t-64 148.5q0 33 10 60t27.5 50t40.5 42t50 36q-30 32 -51 69t-21 79q0 78 53.5 117.5t158.5 39.5q98 0 154 -39
t56 -115q0 -55 -31 -95.5t-80 -75.5zM253 268q-26 -15 -42 -35.5t-16 -52.5q0 -39 23.5 -57t61.5 -18q25 0 49.5 7.5t42.5 24.5zM294 438q21 18 38 38.5t17 47.5t-16 38.5t-39 11.5q-22 0 -36.5 -12t-14.5 -38t16 -45.5t35 -40.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="250" 
d="M210 665l-31 -213h-108l-31 213h170z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="352" 
d="M37 271q0 126 39.5 244.5t126.5 214.5h129q-37 -51 -67.5 -102.5t-52.5 -107.5t-34 -117.5t-12 -131.5q0 -71 12 -132t34 -117t52.5 -107.5t67.5 -102.5h-129q-87 96 -126.5 214.5t-39.5 244.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="352" 
d="M315 271q0 -126 -39.5 -244.5t-126.5 -214.5h-129q37 51 67.5 102.5t52.5 107.5t34 117t12 132q0 70 -12 131.5t-34 117.5t-52.5 107.5t-67.5 102.5h129q87 -96 126.5 -214.5t39.5 -244.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="440" 
d="M178 552l-25 113h134l-25 -113l124 53l38 -102l-139 -21l101 -78l-95 -75l-71 104l-71 -104l-94 75l100 78l-139 21l38 102z" />
    <glyph glyph-name="plus" unicode="+" 
d="M238 372v154h128v-154h181v-107h-181v-155h-128v155h-181v107h181z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="275" 
d="M235 152l-110 -285h-114l57 285h167z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="402" 
d="M345 327v-112h-288v112h288z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="275" 
d="M137 159q47 0 74.5 -22t27.5 -63q0 -40 -27.5 -62t-74.5 -22q-46 0 -73.5 22t-27.5 62q0 41 27.5 63t73.5 22z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="479" 
d="M485 730l-358 -783h-128l357 783h129z" />
    <glyph glyph-name="zero" unicode="0" 
d="M570 330q0 -175 -64.5 -259t-203.5 -84t-203 83.5t-64 255.5q0 179 64 265t203 86t203.5 -85.5t64.5 -261.5zM398 329q0 63 -5.5 106.5t-17 70.5t-30 38.5t-43.5 11.5q-26 0 -44 -11.5t-29.5 -38.5t-17 -70.5t-5.5 -107.5q0 -62 5.5 -104t17 -68t29.5 -37t44 -11
q25 0 43.5 11t30 37t17 68.5t5.5 104.5z" />
    <glyph glyph-name="one" unicode="1" 
d="M284 665h131v-665h-162v528l-147 -35v127z" />
    <glyph glyph-name="two" unicode="2" 
d="M70 654q34 10 87 16.5t116 6.5q49 0 94.5 -9t80 -31.5t55 -60t20.5 -94.5q0 -54 -18.5 -97.5t-52 -85t-80 -84t-103.5 -93.5h266v-122h-474v114q70 69 123.5 120t89.5 92.5t54.5 77t18.5 73.5q0 43 -25 61t-73 18q-21 0 -44 -2t-45 -5.5t-41 -7.5t-32 -8z" />
    <glyph glyph-name="three" unicode="3" 
d="M522 665v-107l-172 -163q41 -5 75.5 -17t59 -33.5t38.5 -54t14 -78.5q0 -59 -19 -101t-53.5 -70t-84 -41t-110.5 -13q-65 0 -115.5 7.5t-92.5 20.5l14 120q42 -11 86.5 -19t91.5 -8q48 0 78.5 20.5t30.5 75.5q0 48 -29 67.5t-82 19.5h-83v99l156 157h-240v118h437z" />
    <glyph glyph-name="four" unicode="4" 
d="M496 665v-413h67v-121h-67v-131h-157v131h-329v113l271 421h215zM339 252v272l-174 -272h174z" />
    <glyph glyph-name="five" unicode="5" 
d="M520 539h-272l-11 -130q11 1 23.5 1h23.5q54 0 101 -9t81 -31.5t53.5 -61.5t19.5 -99q0 -58 -18.5 -100t-52.5 -69t-83 -40t-110 -13q-65 0 -111.5 7t-87.5 20l13 120q38 -11 80 -18.5t89 -7.5q48 0 78.5 19.5t30.5 74.5q0 27 -9 44t-25 27.5t-37.5 14.5t-46.5 4
q-30 0 -65 -3.5t-68 -6.5l-28 31l32 352h400v-126z" />
    <glyph glyph-name="six" unicode="6" 
d="M512 540q-35 8 -77.5 12t-78.5 4q-34 0 -59.5 -8t-43 -26.5t-26.5 -49t-9 -75.5q23 8 58 13t66 5q110 0 165.5 -48.5t55.5 -153.5t-62 -165.5t-185 -60.5q-62 0 -111 17.5t-83.5 57.5t-53 105t-18.5 159q0 99 21.5 166t61 108.5t94.5 59t122 17.5q21 0 45.5 -1.5t48.5 -4
t45.5 -5.5t38.5 -7zM219 275q0 -35 3.5 -65.5t14 -53t29.5 -35.5t50 -13q42 0 62.5 27.5t20.5 69.5q0 48 -20.5 68.5t-64.5 20.5q-24 0 -49.5 -5.5t-45.5 -13.5z" />
    <glyph glyph-name="seven" unicode="7" 
d="M544 665v-107l-259 -558h-196l274 539h-304v126h485z" />
    <glyph glyph-name="eight" unicode="8" 
d="M302 277q-45 0 -72.5 -23t-27.5 -67q0 -37 24 -60t76 -23t76 23t24 60q0 44 -27.5 67t-72.5 23zM471 337q21 -11 38.5 -25t30 -32.5t20 -42.5t7.5 -55q0 -48 -19.5 -84.5t-54.5 -61t-83.5 -37t-107.5 -12.5t-107.5 12.5t-83.5 37t-54.5 61t-19.5 84.5q0 31 7.5 55
t20 42.5t30 32.5t38.5 25q-41 18 -63 52.5t-22 97.5q0 51 19.5 87.5t54 59t81 33t99.5 10.5t99.5 -10.5t81 -33t54 -59t19.5 -87.5q0 -63 -22 -97.5t-63 -52.5zM302 562q-42 0 -65 -19.5t-23 -56.5q0 -42 19 -65t69 -23t69 23t19 65q0 37 -23 56.5t-65 19.5z" />
    <glyph glyph-name="nine" unicode="9" 
d="M93 126q32 -7 70 -13t74 -6q79 0 114.5 38.5t35.5 122.5q-23 -8 -55.5 -13t-63.5 -5q-110 0 -169.5 48.5t-59.5 153.5t65 165t188 60q62 0 111 -17.5t82.5 -57.5t51.5 -104.5t18 -158.5q0 -102 -23.5 -170t-65 -108.5t-97.5 -57t-121 -16.5q-20 0 -42.5 1.5t-45 4
t-43.5 6t-39 7.5zM386 390q0 35 -3 65.5t-13 53t-28.5 35.5t-49.5 13q-42 0 -64 -27.5t-22 -69.5q0 -48 20.5 -68.5t64.5 -20.5q24 0 49.5 5.5t45.5 13.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="275" 
d="M137 499q47 0 74.5 -22t27.5 -63q0 -40 -27.5 -62t-74.5 -22q-46 0 -73.5 22t-27.5 62q0 41 27.5 63t73.5 22zM137 159q47 0 74.5 -22t27.5 -63q0 -40 -27.5 -62t-74.5 -22q-46 0 -73.5 22t-27.5 62q0 41 27.5 63t73.5 22z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="275" 
d="M216 152l-110 -285h-114l57 285h167zM137 499q47 0 74.5 -22t27.5 -63q0 -40 -27.5 -62t-74.5 -22q-46 0 -73.5 22t-27.5 62q0 41 27.5 63t73.5 22z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M547 537v-116l-331 -103l331 -103v-116l-490 167v104z" />
    <glyph glyph-name="equal" unicode="=" 
d="M547 274v-107h-490v107h490zM547 472v-107h-490v107h490z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M547 370v-104l-490 -167v116l331 103l-331 103v116z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="458" 
d="M39 650q27 11 74 18.5t103 7.5q46 0 85 -10t67.5 -30t44.5 -50.5t16 -72.5q0 -39 -10.5 -67t-27 -49.5t-35 -38t-35 -32.5t-27 -33.5t-10.5 -41.5v-58h-138v68q0 39 9 68t22 50.5t29 37t29 29t22 26.5t9 29q0 32 -19 43t-54 12q-35 0 -71.5 -6t-60.5 -15zM121 65
q0 38 25.5 58t68.5 20q42 0 67.5 -20t25.5 -58t-25.5 -57.5t-67.5 -19.5q-43 0 -68.5 19.5t-25.5 57.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="896" 
d="M628 183q0 -30 12.5 -47t37.5 -17q20 0 33 8.5t21 28t11.5 52t3.5 80.5q0 128 -76 191.5t-222 63.5q-81 0 -138 -21.5t-93 -61t-53 -96.5t-17 -128q0 -86 16 -148.5t52 -103t95 -60t145 -19.5q64 0 123.5 10t111.5 29l24 -89q-52 -19 -120.5 -31t-139.5 -12
q-114 0 -193.5 30t-128.5 85.5t-71 134t-22 174.5q0 82 23 154t72 126t126.5 85t187.5 31q111 0 188 -27.5t125.5 -75t70.5 -111t22 -134.5q0 -56 -8.5 -104.5t-29.5 -84.5t-56 -56.5t-87 -20.5q-51 0 -84.5 15.5t-45.5 35.5q-18 -17 -51 -34t-78 -17q-33 0 -62.5 11t-52 36
t-35.5 66t-13 100q0 101 54 150t145 49q46 0 93 -8t84 -21v-218zM490 331q-8 1 -16.5 1.5t-14.5 0.5q-35 0 -49 -26t-14 -80q0 -59 12 -81.5t38 -22.5q24 0 44 18v190z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="642" 
d="M255 105q16 -2 34.5 -3t30.5 -1q63 0 83.5 23t20.5 69q0 42 -21 63t-73 21h-75v-172zM255 393h73q39 0 61 18.5t22 62.5q0 45 -20.5 61.5t-64.5 16.5h-71v-159zM320 665q60 0 109.5 -9t84.5 -29t54.5 -53t19.5 -81q0 -64 -28 -100t-83 -54q26 -5 50.5 -16t43 -30
t30 -45.5t11.5 -63.5q0 -55 -21.5 -92.5t-60 -61t-91 -33.5t-113.5 -10q-51 0 -114 6.5t-129 20.5v651h237z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="572" 
d="M517 535q-30 9 -74 15t-84 6q-69 0 -104 -51t-35 -169q0 -69 9 -112.5t27.5 -68t48 -34t70.5 -9.5q38 0 78 7t72 15l12 -120q-31 -10 -77 -18.5t-94 -8.5q-78 0 -139 17.5t-102.5 58.5t-63 107.5t-21.5 165.5q0 94 21 159t62.5 105.5t102.5 58.5t141 18q48 0 94 -6.5
t73 -13.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="688" 
d="M340 665q73 0 130.5 -19t97 -58t60.5 -100t21 -145q0 -75 -14.5 -140t-52.5 -113t-105.5 -75.5t-173.5 -27.5q-48 0 -103 6t-120 19v653h260zM252 112q16 -2 29.5 -3t27.5 -1q49 0 80.5 15t50 44.5t25.5 72.5t7 100q0 58 -8.5 97.5t-25.5 64t-41.5 35t-56.5 10.5h-88
v-435z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="562" 
d="M532 665v-118h-277v-174h241v-118h-241v-255h-172v665h449z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="702" 
d="M613 534q-38 8 -93.5 15t-111.5 7q-49 0 -84.5 -12t-58.5 -38t-34 -68t-11 -103q0 -69 10.5 -113.5t29.5 -69.5t46.5 -34.5t61.5 -9.5q22 0 48.5 2.5t47.5 5.5v144h-115v123h286v-356q-22 -6 -51.5 -13t-63 -13t-70 -10t-72.5 -4q-79 0 -141.5 15.5t-106 54.5t-67 106.5
t-23.5 171.5q0 97 26 162.5t73.5 105.5t114 57t147.5 17q27 0 58.5 -2t62 -5t57.5 -7t45 -8z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="748" 
d="M255 665v-262h238v262h172v-665h-172v278h-238v-278h-172v665h172z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="328" 
d="M255 665v-559q0 -61 -20.5 -108t-53 -83.5t-72.5 -64.5t-78 -50l-73 92q28 24 51 46t39.5 46t25.5 52t9 64v565h172z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="678" 
d="M255 665v-329l250 329h180l-244 -311q42 -11 74.5 -45t60 -82.5t53 -107.5t53.5 -119h-192q-21 50 -38.5 93.5t-36.5 75.5t-41 50.5t-53 18.5l-66 -83v-155h-172v665h172z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="525" 
d="M255 665v-547h251v-118h-423v665h172z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="929" 
d="M404 200l-149 223v-423h-172v665h171l211 -312l210 312h171v-665h-172v423l-149 -223h-121z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="757" 
d="M215 665l291 -328v328h168v-665h-168v133l-256 293v-426h-167v665h132z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="622" 
d="M255 347q16 -2 29.5 -3.5t25.5 -1.5q55 0 79.5 24.5t24.5 79.5q0 30 -6 50t-19 31t-32 15.5t-45 4.5h-57v-200zM312 665q68 0 119 -11t85 -37t51 -68t17 -103q0 -64 -18.5 -107t-51.5 -69.5t-78 -37.5t-98 -11q-20 0 -41 1.5t-42 5.5v-228h-172v665h229z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="692" 
d="M296 -52q28 0 67 -7t77.5 -16t71 -16t46.5 -7q27 0 44.5 10.5t33.5 22.5l54 -110q-10 -9 -22 -18t-27 -16.5t-34.5 -12t-43.5 -4.5q-32 0 -69.5 8.5t-74.5 19t-69.5 19.5t-53.5 9v118zM652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263
t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="638" 
d="M312 665q68 0 119 -10.5t85 -36t51 -67t17 -102.5q0 -38 -10 -64.5t-26.5 -45t-36.5 -30t-40 -19.5q38 -15 63.5 -41.5t40.5 -79.5l49 -169h-179l-38 131q-7 25 -15 44t-20 31t-29.5 18t-42.5 6q-11 0 -23.5 0.5t-21.5 1.5v-232h-172v665h229zM255 352q14 -2 28.5 -2.5
t26.5 -0.5q55 0 79.5 22.5t24.5 77.5q0 30 -6 49.5t-19 30t-32 14.5t-45 4h-57v-195z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="559" 
d="M483 539q-17 3 -38 6t-43 5.5t-43 4t-39 1.5q-54 0 -81.5 -11.5t-27.5 -45.5q0 -21 22.5 -39t56.5 -36.5t73.5 -40.5t73.5 -51.5t56.5 -68.5t22.5 -92q0 -96 -67.5 -140t-194.5 -44q-65 0 -116 9.5t-89 19.5l14 120q43 -11 85.5 -19.5t98.5 -8.5q19 0 36 2t30 8t20 16.5
t7 27.5q0 24 -22.5 44.5t-56.5 41t-73.5 43.5t-73.5 51.5t-56.5 64.5t-22.5 84q0 56 20.5 92t58.5 56.5t90.5 28.5t117.5 8q44 0 91.5 -5.5t81.5 -11.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="592" 
d="M579 665v-118h-197v-547h-172v547h-197v118h566z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="667" 
d="M245 0l-245 665h183l165 -482q43 87 82.5 204t65.5 278h166q-13 -101 -38.5 -192t-57 -174t-67 -157.5t-69.5 -141.5h-185z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="941" 
d="M183 0l-179 665h178l104 -472l112 356h141l116 -361q39 88 69 205.5t52 271.5h161q-29 -200 -80 -365t-119 -300h-159l-110 358l-120 -358h-166z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="640" 
d="M5 665h197l124 -205l135 205h179l-221 -326l220 -339h-199l-128 210l-134 -210h-178l219 329z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="677" 
d="M258 232l-273 433h187l184 -298q20 18 43.5 52t45.5 75.5t39 86t25 84.5h173q-18 -60 -43.5 -122t-57 -118.5t-69 -104.5t-79.5 -82v-238h-175v232z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="611" 
d="M566 665v-113l-305 -433h315v-119h-528v113l305 434h-296v118h509z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="341" 
d="M320 733v-103h-106v-714h106v-104h-237v921h237z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="479" 
d="M122 730l357 -783h-128l-358 783h129z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="342" 
d="M259 733v-921h-237v104h106v714h-106v103h237z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M403 361q-26 46 -50.5 92.5t-50.5 93.5l-101 -186h-132l175 304h116l175 -304h-132v0z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="694" 
d="M694 -10v-101h-694v101h694z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="550" 
d="M277 730l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="577" 
d="M63 730h168v-238q20 4 39 5.5t35 1.5q54 0 98 -13.5t75 -42.5t48 -76t17 -114q0 -75 -18.5 -126t-51 -81.5t-77 -44t-97.5 -13.5q-24 0 -56 2.5t-65 7t-63.5 10t-51.5 11.5v711zM230 110q18 -5 33 -5.5t32 -0.5q40 0 57.5 31.5t17.5 115.5q0 40 -5 65.5t-15 40.5
t-24.5 20.5t-33.5 5.5q-15 0 -32 -2t-30 -5v-266z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="431" 
d="M387 371q-22 5 -44.5 8.5t-47.5 3.5q-23 0 -40 -6t-27.5 -21.5t-15.5 -42.5t-5 -69q0 -38 4 -64.5t14 -43.5t28 -24.5t47 -7.5q27 0 49.5 5t45.5 11l14 -114q-24 -8 -62 -13t-72 -5q-56 0 -100.5 14.5t-76 45.5t-48 80t-16.5 118q0 72 18 120.5t51.5 78t80.5 42t104 12.5
q29 0 61.5 -3.5t53.5 -8.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="577" 
d="M514 19q-21 -6 -51.5 -11.5t-63.5 -10t-65 -7t-56 -2.5q-53 0 -97.5 13.5t-77 44t-51 81.5t-18.5 126q0 67 17 114t48 76t75 42.5t98 13.5q16 0 35 -1.5t39 -5.5v238h168v-711zM347 376q-13 3 -30 5t-32 2q-19 0 -33.5 -5.5t-24.5 -20.5t-15 -40.5t-5 -65.5q0 -42 4 -70
t13 -45.5t23.5 -24.5t34.5 -7q17 0 32 0.5t33 5.5v266z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="394" 
d="M87 487v74q0 93 57.5 137t154.5 44q27 0 55 -2.5t50 -7.5l-14 -112q-18 2 -35.5 3.5t-34.5 1.5q-29 0 -47.5 -13t-18.5 -51v-74h127v-114h-127v-373h-167v373h-74v114h74z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="575" 
d="M514 25q0 -69 -19.5 -113t-54 -68.5t-82 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q48 0 73.5 17.5t25.5 67.5v11q-18 -4 -42 -7t-43 -3q-49 0 -90.5 12.5t-72 42t-48 79.5t-17.5 125q0 67 19 113t53 74
t80.5 40.5t100.5 12.5q60 0 120.5 -12t106.5 -29v-434zM347 376q-13 3 -27.5 5t-29.5 2q-19 0 -34.5 -4.5t-26 -18t-16.5 -38t-6 -64.5q0 -42 4 -69.5t13 -43t23.5 -22t34.5 -6.5q17 0 32 2.5t33 7.5v249z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="588" 
d="M233 730v-239q46 8 87 8q48 0 87 -9.5t66.5 -30.5t43 -56t15.5 -86v-317h-167v311q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v730h167z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="299" 
d="M233 487v-487h-167v487h167zM245 657q0 -42 -26 -63.5t-70 -21.5t-70 21.5t-26 63.5t26 63.5t70 21.5t70 -21.5t26 -63.5z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="299" 
d="M233 487v-401q0 -50 -12 -90.5t-37 -74.5t-62 -64t-88 -58l-68 81q30 26 49.5 48.5t30.5 46t15.5 50t4.5 60.5v402h167zM245 657q0 -42 -26 -63.5t-70 -21.5t-70 21.5t-26 63.5t26 63.5t70 21.5t70 -21.5t26 -63.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="558" 
d="M234 730v-434l141 191h169l-160 -196q53 -9 90.5 -45.5t51.5 -107.5l29 -138h-178l-23 125q-4 21 -12.5 33t-19.5 19t-23.5 9.5t-24.5 2.5l-40 -44v-145h-168v730h168z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="300" 
d="M234 730v-730h-168v730h168z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="865" 
d="M64 459q18 6 43 13t54 13t62 10t67 4q99 0 159 -40q37 19 79.5 29.5t84.5 10.5q46 0 82 -10t61.5 -31.5t39 -56.5t13.5 -84v-317h-167v311q0 42 -17 57t-49 15q-16 0 -33 -4t-30 -10q7 -22 7 -52v-317h-167v311q0 42 -17 57t-49 15q-14 0 -29.5 -2t-26.5 -5v-376h-167
v459v0z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="586" 
d="M64 459q18 6 43.5 13t56 13t64 10t67.5 4q54 0 97 -9.5t74 -30.5t47.5 -56t16.5 -86v-317h-167v311q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v459v0z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="576" 
d="M62 459q46 16 109 28t123 12q54 0 99.5 -13.5t78.5 -42.5t51.5 -76t18.5 -114q0 -75 -17.5 -126t-48 -81.5t-72 -44t-90.5 -13.5q-19 0 -43 3t-42 7v-186h-167v647zM229 114q18 -5 33 -7.5t32 -2.5q40 0 57.5 31.5t17.5 115.5q0 40 -5 65.5t-15 40.5t-24.5 20.5
t-33.5 5.5q-15 0 -32 -2t-30 -5v-262z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="576" 
d="M514 -188h-167v186q-18 -4 -42 -7t-43 -3q-49 0 -90.5 13.5t-72 44t-48 81.5t-17.5 126q0 67 19 114t53 76t80.5 42.5t100.5 13.5q60 0 120.5 -11.5t106.5 -28.5v-647zM347 376q-13 3 -27.5 5t-29.5 2q-19 0 -34.5 -5.5t-26 -20.5t-16.5 -40.5t-6 -65.5q0 -42 4 -70
t13 -45.5t23.5 -24.5t34.5 -7q17 0 32 2.5t33 7.5v262z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="381" 
d="M365 383q-34 0 -67.5 -1.5t-68.5 -6.5v-375h-167v459q74 24 152.5 32t150.5 8v-116z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="478" 
d="M416 374q-30 6 -60 9.5t-70 3.5q-42 0 -60 -6t-18 -26q0 -13 18 -23.5t44.5 -22t58 -26t58 -34.5t44.5 -48t18 -67q0 -41 -18 -69.5t-48.5 -45.5t-72 -24.5t-87.5 -7.5q-59 0 -102 7.5t-71 15.5l13 110q27 -6 66 -12.5t74 -6.5q42 0 56 7t14 27q0 14 -17.5 25t-43 22.5
t-56 25.5t-56 33t-43 45.5t-17.5 63.5q0 45 18 74t50 45.5t77 23t99 6.5q44 0 78 -4t63 -9z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="403" 
d="M253 621v-134h128v-114h-128v-222q0 -24 15 -32.5t35 -8.5q19 0 38 3t33 7l4 -115q-27 -9 -55.5 -13t-57.5 -4q-85 0 -132 41.5t-47 124.5v219h-74v114h74l29 134h138z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="540" 
d="M5 487h173l97 -342q14 33 27.5 68.5t26.5 76.5t25 89.5t22 107.5h154q-19 -122 -64 -243.5t-109 -243.5h-185z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="884" 
d="M7 487h180l89 -342l91 342h163l98 -342q14 33 27 69t25 77t22.5 89.5t19.5 106.5h155q-34 -242 -166 -487h-181l-83 319l-84 -319h-195z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="520" 
d="M197 487l74 -146l83 146h162l-150 -238l154 -249h-190l-85 143l-83 -143h-162l152 239l-144 248h189z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="585" 
d="M223 487v-307q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v363h168v-462q0 -69 -19.5 -113t-54.5 -68.5t-82.5 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q24 0 42.5 3t31 12t19 24.5t6.5 40.5v11
q-14 -3 -35.5 -4.5t-36.5 -1.5q-47 0 -87.5 8.5t-70.5 29.5t-47.5 57t-17.5 91v301h167z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="505" 
d="M459 487v-101l-215 -272h227v-114h-435v101l221 272h-210v114h412z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="391" 
d="M31 329q45 2 66.5 9.5t21.5 39.5q0 17 -3 39t-6 46.5t-6 51t-3 51.5q0 92 47.5 134t126.5 42q25 0 46 -2t50 -8l-11 -104q-12 3 -26.5 5t-28.5 2q-32 0 -51 -13.5t-19 -44.5q0 -45 8 -99t8 -102q0 -45 -22 -69t-74 -35q50 -13 73 -36t23 -74q0 -23 -2.5 -49t-5.5 -52
t-5.5 -51t-2.5 -46q0 -31 19.5 -43.5t49.5 -12.5q14 0 29 2t27 5l11 -105q-29 -6 -49.5 -8t-46.5 -2q-80 0 -127 41.5t-47 133.5q0 25 3 51.5t6 51.5t6 47.5t3 39.5q0 16 -5.5 25t-16.5 14.5t-27.5 7t-38.5 2.5v115z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="296" 
d="M212 800v-1000h-128v1000h128z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="391" 
d="M360 214q-22 -1 -38.5 -2.5t-27.5 -7t-16.5 -14.5t-5.5 -25q0 -17 3 -39.5t6 -47.5t6 -51.5t3 -51.5q0 -92 -47 -133.5t-127 -41.5q-26 0 -46.5 2t-49.5 8l11 105q12 -3 27 -5t29 -2q30 0 49.5 12.5t19.5 43.5q0 21 -2.5 46t-5.5 51t-5.5 52t-2.5 49q0 51 23 74t73 36
q-52 11 -74 35t-22 69q0 48 8 102t8 99q0 31 -19 44.5t-51 13.5q-14 0 -28.5 -2t-26.5 -5l-11 104q29 6 50 8t46 2q79 0 126.5 -42t47.5 -134q0 -25 -3 -51.5t-6 -51t-6 -46.5t-3 -39q0 -32 21.5 -39.5t66.5 -9.5v-115z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M564 360q-10 -27 -24 -48.5t-34 -37.5t-48 -24.5t-65 -8.5q-27 1 -54 8.5t-51 16.5t-44.5 16.5t-36.5 7.5q-26 0 -40 -12.5t-25 -40.5q-26 10 -51 19.5t-51 18.5q9 25 21.5 47t31 38t44.5 25t61 9q27 0 54.5 -7.5t53.5 -16.5t49.5 -16.5t41.5 -7.5q26 0 40 12.5t25 40.5z
" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="291" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="304" 
d="M62 -188l29 470h127l27 -470h-183zM58 421q0 38 26 57.5t68 19.5t67.5 -19.5t25.5 -57.5t-25.5 -58t-67.5 -20t-68 20t-26 58z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M275 56q-45 10 -80 30.5t-59 54t-36 81.5t-12 113q0 121 45.5 184.5t134.5 84.5v138h104v-127q50 0 89.5 -5.5t65.5 -10.5l-15 -117q-26 6 -65.5 10.5t-74.5 4.5q-30 0 -53.5 -7t-39 -25.5t-23.5 -50t-8 -79.5q0 -57 9 -90t26 -50.5t43 -22.5t60 -5t69.5 6t63.5 12
l11 -115q-32 -9 -70.5 -16t-80.5 -7v-131h-104v140z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M145 390l8 84q5 56 23.5 94.5t49.5 62.5t73 35t95 11q51 0 94 -5.5t72 -10.5l-17 -121q-32 6 -66.5 11t-61.5 5q-38 0 -60 -16t-27 -57l-10 -93h202v-99h-214l-6 -58q-4 -35 -22.5 -66t-52.5 -49h333v-118h-520v96q24 12 40.5 26.5t26.5 31.5t15 37.5t8 45.5l6 54h-97v99
h108z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M192 665l127 -250l114 250h164l-120 -227h89v-80h-122l-32 -72h154v-80h-163v-206h-165v206h-177v80h176l-44 72h-132v80h83l-137 227h185z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="296" 
d="M212 800v-401h-128v401h128zM212 201v-401h-128v401h128z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="585" 
d="M63 255q0 54 20.5 85t58.5 48q-26 23 -41.5 53t-15.5 68q0 51 18.5 83.5t53 51t82 25.5t106.5 7q39 0 80 -4t77 -11l-11 -110q-39 7 -76 11t-69 4q-49 0 -75 -11.5t-26 -39.5q0 -19 22 -34.5t55 -32t71 -36t71 -46.5t55 -63t22 -85q0 -57 -27 -91t-69 -50
q16 -23 25.5 -50.5t9.5 -62.5q0 -87 -61.5 -125.5t-176.5 -38.5q-59 0 -107 9t-82 18l16 113q31 -8 73 -16.5t89 -8.5q36 0 62 10t26 39q0 22 -19 39.5t-47.5 34.5t-61.5 36.5t-61.5 44.5t-47.5 58t-19 78zM210 266q0 -13 14 -26t34.5 -25.5t44.5 -25t45 -25.5
q19 5 29.5 15.5t10.5 31.5q0 13 -12.5 26.5t-31.5 26.5t-43 25t-46 24q-20 -5 -32.5 -17t-12.5 -30z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="550" 
d="M254 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM453 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="732" 
d="M488 154q-22 -7 -49 -12.5t-58 -5.5q-44 0 -79.5 10.5t-60.5 34t-38 61t-13 91.5q0 102 48.5 147t137.5 45q31 0 59.5 -5t47.5 -10l-9 -87q-20 5 -43.5 8.5t-44.5 3.5q-33 0 -48.5 -21.5t-15.5 -80.5q0 -33 4.5 -54t13.5 -32.5t22.5 -16t31.5 -4.5q42 0 85 16zM698 332
q0 -170 -86 -257t-253 -87t-253 87t-86 257t86 257t253 87t253 -87t86 -257zM626 332q0 138 -65 206.5t-202 68.5t-202 -68.5t-65 -206.5t65 -206.5t202 -68.5t202 68.5t65 206.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="358" 
d="M37 658q28 8 60 13t74 5q60 0 97 -27.5t37 -79.5v-176q-9 -4 -25 -9t-36.5 -9.5t-44 -8t-47.5 -3.5q-63 0 -99 23.5t-36 76.5q0 28 12 45.5t31 27.5t42 13t45 3q31 0 43 -4v16q0 17 -9 26.5t-35 9.5q-28 0 -51 -5t-44 -12zM190 487q-8 2 -15.5 3t-18.5 1q-13 0 -21.5 -7
t-8.5 -24q0 -19 9 -26t21 -7q19 0 34 5v55z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="619" 
d="M341 452l-129 -181l129 -181h-142l-164 181l164 181h142zM584 452l-94 -181l94 -181h-142l-129 181l129 181h142z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M547 472v-305h-118v198h-372v107h490z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="402" 
d="M345 327v-112h-288v112h288z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="627" 
d="M284 442q5 -1 11 -1.5t13 -0.5q22 0 33.5 6t11.5 29q0 20 -10.5 27.5t-33.5 7.5h-25v-68zM313 566q54 0 90.5 -20.5t36.5 -64.5q0 -32 -12 -46.5t-35 -24.5q16 -8 24 -22t14 -31l28 -74h-92l-23 63q-7 18 -14.5 26.5t-24.5 8.5h-16q-4 0 -5 1v-99h-86v283h115zM598 416
q0 -128 -73.5 -194t-211.5 -66t-211.5 66t-73.5 194t73.5 194t211.5 66t211.5 -66t73.5 -194zM526 416q0 105 -51.5 152t-161.5 47t-161.5 -47t-51.5 -152t51.5 -152t161.5 -47t161.5 47t51.5 152z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="550" 
d="M426 694v-90h-302v90h302z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="385" 
d="M193 618q-28 0 -44 -16t-16 -40t16 -40t44 -16q26 0 42.5 16t16.5 40t-16.5 40t-42.5 16zM193 677q26 0 49 -9t40 -24.5t27 -36.5t10 -45t-10 -45t-27 -36.5t-40 -24.5t-49 -9q-27 0 -50 9t-40 24.5t-27 36.5t-10 45t10 45t27 36.5t40 24.5t50 9z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M238 392v134h128v-134h181v-107h-181v-125h-128v125h-181v107h181zM547 107v-107h-490v107h490z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="330" 
d="M36 662q23 7 53 11t64 4q57 0 94.5 -26t37.5 -79q0 -39 -16.5 -67t-41.5 -49l-54 -46h118v-78h-255v68l86 81q27 26 41.5 44.5t14.5 38.5q0 18 -11 25.5t-34 7.5t-45.5 -4.5t-39.5 -9.5z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="330" 
d="M284 665v-67l-91 -74h8q40 0 69.5 -24.5t29.5 -66.5q0 -64 -40 -90.5t-105 -26.5q-35 0 -67.5 6.5t-49.5 12.5l13 77q8 -2 19.5 -5t24.5 -5.5t25.5 -4t22.5 -1.5q20 0 33 7.5t13 26.5t-12.5 25.5t-35.5 6.5h-50v59l71 69h-128v75h250z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="550" 
d="M445 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="589" 
d="M231 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-17 0 -33.5 1t-32.5 3v-180h-167v675h167z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" horiz-adv-x="589" 
d="M231 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-17 0 -33.5 1t-32.5 3v-180h-167v675h167z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="714" 
d="M447 -158h-105v382q-11 0 -23 -1t-31 -1q-65 0 -114 11.5t-81.5 37.5t-49 69t-16.5 105q0 64 15 106.5t47 68t82.5 35.5t122.5 10h153v-823zM631 665v-823h-105v823h105z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="275" 
d="M137 355q47 0 74.5 -22t27.5 -63q0 -40 -27.5 -62t-74.5 -22q-46 0 -73.5 22t-27.5 62q0 41 27.5 63t73.5 22z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="550" 
d="M338 -35q14 -17 24.5 -36t10.5 -41q0 -42 -26.5 -67t-92.5 -25q-26 0 -54 4t-51 13l17 87q10 -3 25.5 -6t26.5 -3q19 0 25 6.5t6 20.5q0 11 -5.5 23t-13.5 24h108z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="330" 
d="M137 665h82v-334h-105v244l-71 -15v82z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="358" 
d="M339 516q0 -85 -41 -122t-119 -37t-119 37t-41 122t41 122.5t119 37.5t119 -37.5t41 -122.5zM220 516q0 44 -10.5 63.5t-30.5 19.5t-30.5 -19.5t-10.5 -63.5q0 -83 41 -83t41 83z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="619" 
d="M420 452l164 -181l-164 -181h-142l129 181l-129 181h142zM177 452l129 -181l-129 -181h-142l94 181l-94 181h142z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="814" 
d="M743 343v-202h42v-73h-42v-68h-100v68h-153v60l111 215h142zM643 141v122l-63 -122h63zM685 665l-429 -665h-111l429 665h111zM129 665h82v-334h-105v244l-71 -15v82z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="814" 
d="M677 665l-429 -665h-111l429 665h111zM133 665h82v-334h-105v244l-71 -15v82zM511 330q23 7 53 11t64 4q57 0 94.5 -26t37.5 -79q0 -39 -16.5 -67t-41.5 -49l-54 -46h118v-78h-255v68l86 81q27 26 41.5 44.5t14.5 38.5q0 18 -11 25.5t-34 7.5t-45.5 -4.5t-39.5 -9.5z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="814" 
d="M743 343v-202h42v-73h-42v-68h-100v68h-153v60l111 215h142zM643 141v122l-63 -122h63zM298 665v-67l-91 -74h8q40 0 69.5 -24.5t29.5 -66.5q0 -64 -40 -90.5t-105 -26.5q-35 0 -67.5 6.5t-49.5 12.5l13 77q8 -2 19.5 -5t24.5 -5.5t25.5 -4t22.5 -1.5q20 0 33 7.5
t13 26.5t-12.5 25.5t-35.5 6.5h-50v59l71 69h-128v75h250zM702 665l-429 -665h-111l429 665h111z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="416" 
d="M406 -169q-27 -11 -74 -18.5t-103 -7.5q-46 0 -85 10t-67.5 30t-44.5 50.5t-16 72.5q0 39 10.5 67t27 49.5t35 38t35 32.5t27 33.5t10.5 41.5v58h138v-68q0 -39 -9 -68t-22 -50.5t-29 -37t-29 -29t-22 -26.5t-9 -29q0 -32 19 -43t54 -12q35 0 71.5 6t60.5 15zM324 416
q0 -38 -25.5 -58t-68.5 -20q-42 0 -67.5 20t-25.5 58t25.5 57.5t67.5 19.5q43 0 68.5 -19.5t25.5 -57.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204zM312 916l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204zM515 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204zM403 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204zM159 800q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5
q17 0 23.5 10t11.5 25l95 -28q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204zM313 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5
t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM512 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204zM459 835q0 -51 -34.5 -72t-91.5 -21t-91.5 21t-34.5 72t34.5 72
t91.5 21t91.5 -21t34.5 -72zM370 835q0 32 -37 32t-37 -32t37 -32t37 32z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="934" 
d="M869 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v78h-205v-268h-169v432q0 56 13.5 101.5t43.5 77t78.5 48.5t117.5 17q43 0 78.5 -10t54.5 -31v30h423zM433 386v58q0 55 -24.5 83.5
t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-58h204z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="572" 
d="M517 535q-30 9 -74 15t-84 6q-69 0 -104 -51t-35 -169q0 -69 9 -112.5t27.5 -68t48 -34t70.5 -9.5q38 0 78 7t72 15l12 -120q-31 -10 -77 -18.5t-94 -8.5q-78 0 -139 17.5t-102.5 58.5t-63 107.5t-21.5 165.5q0 94 21 159t62.5 105.5t102.5 58.5t141 18q48 0 94 -6.5
t73 -13.5zM386 -35q14 -17 24.5 -36t10.5 -41q0 -42 -26.5 -67t-92.5 -25q-26 0 -54 4t-51 13l17 87q10 -3 25.5 -6t26.5 -3q19 0 25 6.5t6 20.5q0 11 -5.5 23t-13.5 24h108z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435zM301 916l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435zM462 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435zM363 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435zM274 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM473 835
q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172zM143 916l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172zM363 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172zM239 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172zM149 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM348 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="712" 
d="M364 665q73 0 130.5 -19t97 -58t60.5 -100t21 -145q0 -75 -14.5 -140t-52.5 -113t-105.5 -75.5t-173.5 -27.5q-48 0 -103 6t-120 19v264h-84v117h84v272h260zM276 393h131v-117h-131v-164q16 -2 29.5 -3t27.5 -1q49 0 80.5 15t50 44.5t25.5 72.5t7 100q0 58 -8.5 97.5
t-25.5 64t-41.5 35t-56.5 10.5h-88v-154z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="757" 
d="M215 665l291 -328v328h168v-665h-168v133l-256 293v-426h-167v665h132zM204 799q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11
t-11.5 -23z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106zM324 916l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106zM533 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106zM416 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106zM172 799q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106zM326 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM525 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M536 185l-87 -84l-148 136l-148 -136l-87 84l147 132l-147 132l87 84l148 -136l148 136l87 -84l-147 -132z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-48 0 -88.5 8.5t-72.5 23.5l-60 -90l-67 45l61 93q-79 83 -79 264q0 180 75.5 263t230.5 83q91 0 158 -31l60 91l67 -45l-61 -92q82 -83 82 -269zM472 332q0 62 -8 108l-200 -301q15 -17 36 -24t47 -7q31 0 54.5 11t39.5 37.5
t23.5 69.5t7.5 106zM220 332q0 -31 2 -56.5t5 -46.5l198 299q-14 15 -34 21.5t-44 6.5q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM324 916l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM545 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM423 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM333 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5
t20.5 -47.5zM532 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="677" 
d="M531 916l-135 -161h-122l82 161h175zM258 232l-273 433h187l184 -298q20 18 43.5 52t45.5 75.5t39 86t25 84.5h173q-18 -60 -43.5 -122t-57 -118.5t-69 -104.5t-79.5 -82v-238h-175v232z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="622" 
d="M255 665v-109h61q72 0 123 -10t83.5 -35t47 -67.5t14.5 -106.5q0 -62 -17.5 -104t-51.5 -68t-83.5 -37.5t-112.5 -11.5q-20 0 -35.5 1t-28.5 3v-120h-172v665h172zM255 236q8 -1 23.5 -2t29.5 -1q53 0 79.5 23.5t26.5 80.5q0 60 -23 80t-75 20h-61v-201z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="586" 
d="M61 0v561q0 86 55 133.5t175 47.5q59 0 100.5 -15t68 -39.5t38.5 -55.5t12 -63q0 -39 -11.5 -66t-25 -48t-24.5 -38t-11 -36q0 -17 19.5 -40t43 -53t43 -67t19.5 -82q0 -73 -40.5 -112.5t-121.5 -39.5q-30 0 -59 4t-54 10l5 110q17 -4 36.5 -7t36.5 -3q20 0 29.5 7.5
t9.5 30.5q0 24 -16.5 43.5t-36.5 43.5t-36.5 55.5t-16.5 78.5q0 36 9 61t20 46t20 42.5t9 51.5q0 65 -64 65t-64 -65v-561h-168z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5zM262 730l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5zM452 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5zM340 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5zM98 614q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28q-17 -47 -44.5 -75t-71.5 -28
q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5zM252 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM451 651
q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5zM398 649q0 -51 -34.5 -72t-91.5 -21t-91.5 21t-34.5 72t34.5 72t91.5 21t91.5 -21t34.5 -72zM309 649q0 32 -37 32t-37 -32t37 -32
t37 32z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="796" 
d="M481 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-45 0 -83 6.5t-68 22.5q-35 -12 -77 -20.5t-85 -8.5q-47 0 -87.5 7.5t-70 26.5t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5
q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5l-7 112q35 9 82.5 15.5t90.5 6.5q48 0 89 -7.5t73 -25.5q26 18 60.5 25.5t79.5 7.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5v-86h-277zM315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5
t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM603 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="431" 
d="M387 371q-22 5 -44.5 8.5t-47.5 3.5q-23 0 -40 -6t-27.5 -21.5t-15.5 -42.5t-5 -69q0 -38 4 -64.5t14 -43.5t28 -24.5t47 -7.5q27 0 49.5 5t45.5 11l14 -114q-24 -8 -62 -13t-72 -5q-56 0 -100.5 14.5t-76 45.5t-48 80t-16.5 118q0 72 18 120.5t51.5 78t80.5 42t104 12.5
q29 0 61.5 -3.5t53.5 -8.5zM304 -35q14 -17 24.5 -36t10.5 -41q0 -42 -26.5 -67t-92.5 -25q-26 0 -54 4t-51 13l17 87q10 -3 25.5 -6t26.5 -3q19 0 25 6.5t6 20.5q0 11 -5.5 23t-13.5 24h108z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM248 730l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM466 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM342 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM252 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM451 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5
t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="299" 
d="M125 730l81 -161h-120l-136 161h175zM233 487v-487h-167v487h167z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="299" 
d="M339 730l-135 -161h-122l82 161h175zM233 487v-487h-167v487h167z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="299" 
d="M233 487v-487h-167v487h167zM219 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="299" 
d="M129 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM328 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM233 487v-487h-167v487h167z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="572" 
d="M364 730q17 -17 30 -33t26 -34l124 22v-90l-74 -13q20 -42 32.5 -80t20 -75t10 -74.5t2.5 -77.5q0 -133 -62 -210t-189 -77q-122 0 -186 60.5t-64 195.5q0 125 58 189.5t170 64.5q34 0 64.5 -9t47.5 -23q-3 9 -8 21t-10.5 24.5t-11.5 24.5t-11 20l-189 -33v90l141 25
q-16 22 -31.5 42.5t-40.5 49.5h151zM361 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="586" 
d="M64 459q18 6 43.5 13t56 13t64 10t67.5 4q54 0 97 -9.5t74 -30.5t47.5 -56t16.5 -86v-317h-167v311q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v459zM123 614q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28
q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM269 730l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM478 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM355 730l111 -161h-127l-53 89l-54 -89h-127
l111 161h139z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM111 614q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11
t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM265 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5
t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM464 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M385 502q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5zM547 372v-107h-490v107h490zM385 135q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61q-64 0 -115 17l-59 -91l-62 38l58 89q-36 31 -54.5 81t-18.5 122q0 134 64.5 194.5t186.5 60.5q32 0 59.5 -4t51.5 -12l60 91l62 -38l-58 -88q38 -31 57 -81t19 -123zM363 244q0 28 -3 51l-118 -180q15 -11 43 -11q43 0 60.5 31
t17.5 109zM207 244v-25.5t2 -22.5l116 178q-17 9 -40 9q-43 0 -60.5 -30.5t-17.5 -108.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM271 730l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM485 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM360 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM270 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5
t58.5 -16.5t20.5 -47.5zM469 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="585" 
d="M223 487v-307q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v363h168v-462q0 -69 -19.5 -113t-54.5 -68.5t-82.5 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q24 0 42.5 3t31 12t19 24.5t6.5 40.5v11
q-14 -3 -35.5 -4.5t-36.5 -1.5q-47 0 -87.5 8.5t-70.5 29.5t-47.5 57t-17.5 91v301h167zM485 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="580" 
d="M66 730h167v-235q17 2 33 3t32 1q54 0 99.5 -13.5t78.5 -42.5t51.5 -76t18.5 -114q0 -75 -17.5 -126t-48 -81.5t-72 -44t-90.5 -13.5q-19 0 -43 3t-42 7v-186h-167v918zM233 114q18 -5 33 -7.5t32 -2.5q40 0 57.5 31.5t17.5 115.5q0 40 -5 65.5t-15 40.5t-24.5 20.5
t-33.5 5.5q-15 0 -32 -2t-30 -5v-262z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="585" 
d="M223 487v-307q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v363h168v-462q0 -69 -19.5 -113t-54.5 -68.5t-82.5 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q24 0 42.5 3t31 12t19 24.5t6.5 40.5v11
q-14 -3 -35.5 -4.5t-36.5 -1.5q-47 0 -87.5 8.5t-70.5 29.5t-47.5 57t-17.5 91v301h167zM271 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM470 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5
t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204zM484 880v-90h-302v90h302z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5zM422 694v-90h-302v90h302z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="666" 
d="M229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204zM284 908q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114
q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="532" 
d="M315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5
t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5zM222 722q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5
t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="666" 
d="M647 -180q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 37 20.5 66.5t48.5 45.5h-63v222h-204v-222h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432q-21 -17 -38.5 -39.5t-17.5 -42.5q0 -23 32 -23
q24 0 49 11zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="532" 
d="M523 -155q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 23 10.5 43.5t26.5 37.5q-41 -6 -82 -6q-47 0 -87.5 7.5t-70 26.5t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5
t-75.5 -14.5l-7 112q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-21 -17 -38.5 -39.5t-17.5 -42.5q0 -23 32 -23q24 0 49 11zM315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="572" 
d="M517 535q-30 9 -74 15t-84 6q-69 0 -104 -51t-35 -169q0 -69 9 -112.5t27.5 -68t48 -34t70.5 -9.5q38 0 78 7t72 15l12 -120q-31 -10 -77 -18.5t-94 -8.5q-78 0 -139 17.5t-102.5 58.5t-63 107.5t-21.5 165.5q0 94 21 159t62.5 105.5t102.5 58.5t141 18q48 0 94 -6.5
t73 -13.5zM504 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="431" 
d="M387 371q-22 5 -44.5 8.5t-47.5 3.5q-23 0 -40 -6t-27.5 -21.5t-15.5 -42.5t-5 -69q0 -38 4 -64.5t14 -43.5t28 -24.5t47 -7.5q27 0 49.5 5t45.5 11l14 -114q-24 -8 -62 -13t-72 -5q-56 0 -100.5 14.5t-76 45.5t-48 80t-16.5 118q0 72 18 120.5t51.5 78t80.5 42t104 12.5
q29 0 61.5 -3.5t53.5 -8.5zM440 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="572" 
d="M517 535q-30 9 -74 15t-84 6q-69 0 -104 -51t-35 -169q0 -69 9 -112.5t27.5 -68t48 -34t70.5 -9.5q38 0 78 7t72 15l12 -120q-31 -10 -77 -18.5t-94 -8.5q-78 0 -139 17.5t-102.5 58.5t-63 107.5t-21.5 165.5q0 94 21 159t62.5 105.5t102.5 58.5t141 18q48 0 94 -6.5
t73 -13.5zM382 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="431" 
d="M387 371q-22 5 -44.5 8.5t-47.5 3.5q-23 0 -40 -6t-27.5 -21.5t-15.5 -42.5t-5 -69q0 -38 4 -64.5t14 -43.5t28 -24.5t47 -7.5q27 0 49.5 5t45.5 11l14 -114q-24 -8 -62 -13t-72 -5q-56 0 -100.5 14.5t-76 45.5t-48 80t-16.5 118q0 72 18 120.5t51.5 78t80.5 42t104 12.5
q29 0 61.5 -3.5t53.5 -8.5zM311 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="572" 
d="M517 535q-30 9 -74 15t-84 6q-69 0 -104 -51t-35 -169q0 -69 9 -112.5t27.5 -68t48 -34t70.5 -9.5q38 0 78 7t72 15l12 -120q-31 -10 -77 -18.5t-94 -8.5q-78 0 -139 17.5t-102.5 58.5t-63 107.5t-21.5 165.5q0 94 21 159t62.5 105.5t102.5 58.5t141 18q48 0 94 -6.5
t73 -13.5zM395 835q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="431" 
d="M387 371q-22 5 -44.5 8.5t-47.5 3.5q-23 0 -40 -6t-27.5 -21.5t-15.5 -42.5t-5 -69q0 -38 4 -64.5t14 -43.5t28 -24.5t47 -7.5q27 0 49.5 5t45.5 11l14 -114q-24 -8 -62 -13t-72 -5q-56 0 -100.5 14.5t-76 45.5t-48 80t-16.5 118q0 72 18 120.5t51.5 78t80.5 42t104 12.5
q29 0 61.5 -3.5t53.5 -8.5zM325 651q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="572" 
d="M517 535q-30 9 -74 15t-84 6q-69 0 -104 -51t-35 -169q0 -69 9 -112.5t27.5 -68t48 -34t70.5 -9.5q38 0 78 7t72 15l12 -120q-31 -10 -77 -18.5t-94 -8.5q-78 0 -139 17.5t-102.5 58.5t-63 107.5t-21.5 165.5q0 94 21 159t62.5 105.5t102.5 58.5t141 18q48 0 94 -6.5
t73 -13.5zM243 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="431" 
d="M387 371q-22 5 -44.5 8.5t-47.5 3.5q-23 0 -40 -6t-27.5 -21.5t-15.5 -42.5t-5 -69q0 -38 4 -64.5t14 -43.5t28 -24.5t47 -7.5q27 0 49.5 5t45.5 11l14 -114q-24 -8 -62 -13t-72 -5q-56 0 -100.5 14.5t-76 45.5t-48 80t-16.5 118q0 72 18 120.5t51.5 78t80.5 42t104 12.5
q29 0 61.5 -3.5t53.5 -8.5zM173 569l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="689" 
d="M340 665q73 0 130.5 -19t97 -58t60.5 -100t21 -145q0 -75 -14.5 -140t-52.5 -113t-105.5 -75.5t-173.5 -27.5q-48 0 -103 6t-120 19v653h260zM252 112q16 -2 29.5 -3t27.5 -1q49 0 80.5 15t50 44.5t25.5 72.5t7 100q0 58 -8.5 97.5t-25.5 64t-41.5 35t-56.5 10.5h-88
v-435zM273 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="744" 
d="M764 665l-82 -213h-113l37 213h158zM514 19q-21 -6 -51.5 -11.5t-63.5 -10t-65 -7t-56 -2.5q-53 0 -97.5 13.5t-77 44t-51 81.5t-18.5 126q0 67 17 114t48 76t75 42.5t98 13.5q16 0 35 -1.5t39 -5.5v238h168v-711zM347 376q-13 3 -30 5t-32 2q-19 0 -33.5 -5.5
t-24.5 -20.5t-15 -40.5t-5 -65.5q0 -42 4 -70t13 -45.5t23.5 -24.5t34.5 -7q17 0 32 0.5t33 5.5v266z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="713" 
d="M364 665q73 0 130.5 -19t97 -58t60.5 -100t21 -145q0 -75 -14.5 -140t-52.5 -113t-105.5 -75.5t-173.5 -27.5q-48 0 -103 6t-120 19v264h-84v117h84v272h260zM276 393h131v-117h-131v-164q16 -2 29.5 -3t27.5 -1q49 0 80.5 15t50 44.5t25.5 72.5t7 100q0 58 -8.5 97.5
t-25.5 64t-41.5 35t-56.5 10.5h-88v-154z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="636" 
d="M514 19q-21 -6 -51.5 -11.5t-63.5 -10t-65 -7t-56 -2.5q-53 0 -97.5 13.5t-77 44t-51 81.5t-18.5 126q0 67 17 114t48 76t75 42.5t98 13.5q16 0 35 -1.5t39 -5.5v78h-158v95h158v65h168v-65h102v-95h-102v-551zM347 376q-13 3 -30 5t-32 2q-19 0 -33.5 -5.5t-24.5 -20.5
t-15 -40.5t-5 -65.5q0 -42 4 -70t13 -45.5t23.5 -24.5t34.5 -7q17 0 32 0.5t33 5.5v266z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435zM445 880v-90h-302v90h302z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM423 694v-90h-302v90h302z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435zM245 907q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5
t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM223 722q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435zM377 835q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM355 651q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="573" 
d="M535 -180q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 37 20.5 66.5t48.5 45.5h-62q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-25q-21 -17 -38.5 -39.5t-17.5 -42.5
q0 -23 32 -23q24 0 49 11z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="524" 
d="M519 -168q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 26 13.5 49.5t33.5 40.5q-11 -1 -23 -1.5t-24 -0.5q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-21 -17 -38.5 -40t-17.5 -43q0 -23 32 -23q24 0 49 11zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="573" 
d="M508 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v475h435zM225 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="524" 
d="M209 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-57 0 -105.5 11.5t-84 40.5t-55.5 78.5t-20 125.5q0 75 17.5 124t49 78.5t76.5 41t101 11.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5
v-86h-277zM331 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM203 569l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="702" 
d="M613 534q-38 8 -93.5 15t-111.5 7q-49 0 -84.5 -12t-58.5 -38t-34 -68t-11 -103q0 -69 10.5 -113.5t29.5 -69.5t46.5 -34.5t61.5 -9.5q22 0 48.5 2.5t47.5 5.5v144h-115v123h286v-356q-22 -6 -51.5 -13t-63 -13t-70 -10t-72.5 -4q-79 0 -141.5 15.5t-106 54.5t-67 106.5
t-23.5 171.5q0 97 26 162.5t73.5 105.5t114 57t147.5 17q27 0 58.5 -2t62 -5t57.5 -7t45 -8zM419 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="575" 
d="M514 25q0 -69 -19.5 -113t-54 -68.5t-82 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q48 0 73.5 17.5t25.5 67.5v11q-18 -4 -42 -7t-43 -3q-49 0 -90.5 12.5t-72 42t-48 79.5t-17.5 125q0 67 19 113t53 74
t80.5 40.5t100.5 12.5q60 0 120.5 -12t106.5 -29v-434zM347 376q-13 3 -27.5 5t-29.5 2q-19 0 -34.5 -4.5t-26 -18t-16.5 -38t-6 -64.5q0 -42 4 -69.5t13 -43t23.5 -22t34.5 -6.5q17 0 32 2.5t33 7.5v249zM358 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="702" 
d="M613 534q-38 8 -93.5 15t-111.5 7q-49 0 -84.5 -12t-58.5 -38t-34 -68t-11 -103q0 -69 10.5 -113.5t29.5 -69.5t46.5 -34.5t61.5 -9.5q22 0 48.5 2.5t47.5 5.5v144h-115v123h286v-356q-22 -6 -51.5 -13t-63 -13t-70 -10t-72.5 -4q-79 0 -141.5 15.5t-106 54.5t-67 106.5
t-23.5 171.5q0 97 26 162.5t73.5 105.5t114 57t147.5 17q27 0 58.5 -2t62 -5t57.5 -7t45 -8zM301 907q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="575" 
d="M514 25q0 -69 -19.5 -113t-54 -68.5t-82 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q48 0 73.5 17.5t25.5 67.5v11q-18 -4 -42 -7t-43 -3q-49 0 -90.5 12.5t-72 42t-48 79.5t-17.5 125q0 67 19 113t53 74
t80.5 40.5t100.5 12.5q60 0 120.5 -12t106.5 -29v-434zM347 376q-13 3 -27.5 5t-29.5 2q-19 0 -34.5 -4.5t-26 -18t-16.5 -38t-6 -64.5q0 -42 4 -69.5t13 -43t23.5 -22t34.5 -6.5q17 0 32 2.5t33 7.5v249zM239 722q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114
q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="702" 
d="M613 534q-38 8 -93.5 15t-111.5 7q-49 0 -84.5 -12t-58.5 -38t-34 -68t-11 -103q0 -69 10.5 -113.5t29.5 -69.5t46.5 -34.5t61.5 -9.5q22 0 48.5 2.5t47.5 5.5v144h-115v123h286v-356q-22 -6 -51.5 -13t-63 -13t-70 -10t-72.5 -4q-79 0 -141.5 15.5t-106 54.5t-67 106.5
t-23.5 171.5q0 97 26 162.5t73.5 105.5t114 57t147.5 17q27 0 58.5 -2t62 -5t57.5 -7t45 -8zM433 836q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="575" 
d="M514 25q0 -69 -19.5 -113t-54 -68.5t-82 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q48 0 73.5 17.5t25.5 67.5v11q-18 -4 -42 -7t-43 -3q-49 0 -90.5 12.5t-72 42t-48 79.5t-17.5 125q0 67 19 113t53 74
t80.5 40.5t100.5 12.5q60 0 120.5 -12t106.5 -29v-434zM347 376q-13 3 -27.5 5t-29.5 2q-19 0 -34.5 -4.5t-26 -18t-16.5 -38t-6 -64.5q0 -42 4 -69.5t13 -43t23.5 -22t34.5 -6.5q17 0 32 2.5t33 7.5v249zM372 651q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5
t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="702" 
d="M613 534q-38 8 -93.5 15t-111.5 7q-49 0 -84.5 -12t-58.5 -38t-34 -68t-11 -103q0 -69 10.5 -113.5t29.5 -69.5t46.5 -34.5t61.5 -9.5q22 0 48.5 2.5t47.5 5.5v144h-115v123h286v-356q-22 -6 -51.5 -13t-63 -13t-70 -10t-72.5 -4q-79 0 -141.5 15.5t-106 54.5t-67 106.5
t-23.5 171.5q0 97 26 162.5t73.5 105.5t114 57t147.5 17q27 0 58.5 -2t62 -5t57.5 -7t45 -8zM479 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="575" 
d="M514 25q0 -69 -19.5 -113t-54 -68.5t-82 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q48 0 73.5 17.5t25.5 67.5v11q-18 -4 -42 -7t-43 -3q-49 0 -90.5 12.5t-72 42t-48 79.5t-17.5 125q0 67 19 113t53 74
t80.5 40.5t100.5 12.5q60 0 120.5 -12t106.5 -29v-434zM347 376q-13 3 -27.5 5t-29.5 2q-19 0 -34.5 -4.5t-26 -18t-16.5 -38t-6 -64.5q0 -42 4 -69.5t13 -43t23.5 -22t34.5 -6.5q17 0 32 2.5t33 7.5v249zM191 569l135 161h122l-82 -161h-175z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="748" 
d="M255 665v-262h238v262h172v-665h-172v278h-238v-278h-172v665h172zM443 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="588" 
d="M233 730v-239q46 8 87 8q48 0 87 -9.5t66.5 -30.5t43 -56t15.5 -86v-317h-167v311q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v730h167zM216 945l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="790" 
d="M276 665v-80h238v80h172v-80h84v-86h-84v-499h-172v278h-238v-278h-172v499h-84v86h84v80h172zM514 403v96h-238v-96h238z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="654" 
d="M299 730v-65h159v-96h-159v-78q46 8 87 8q48 0 87 -9.5t66.5 -30.5t43 -56t15.5 -86v-317h-167v311q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v569h-112v96h112v65h167z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172zM-5 799q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="299" 
d="M233 487v-487h-167v487h167zM-25 614q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172zM320 881v-90h-302v90h302z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="299" 
d="M233 487v-487h-167v487h167zM300 694v-90h-302v90h302z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172zM120 908q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="299" 
d="M233 487v-487h-167v487h167zM100 722q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="338" 
d="M296 -180q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 37 20.5 66.5t48.5 45.5h-62v665h172v-665q-21 -17 -38.5 -39.5t-17.5 -42.5q0 -23 32 -23q24 0 49 11z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="299" 
d="M274 -180q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 37 20.5 66.5t48.5 45.5h-57v487h167v-487q-21 -17 -38.5 -39.5t-17.5 -42.5q0 -23 32 -23q24 0 49 11zM245 657q0 -42 -26 -63.5t-70 -21.5t-70 21.5t-26 63.5t26 63.5t70 21.5t70 -21.5t26 -63.5
z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="338" 
d="M255 665v-665h-172v665h172zM252 836q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="299" 
d="M233 487v-487h-167v487h167z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="328" 
d="M255 665v-559q0 -61 -20.5 -108t-53 -83.5t-72.5 -64.5t-78 -50l-73 92q28 24 51 46t39.5 46t25.5 52t9 64v565h172zM234 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="299" 
d="M233 487v-401q0 -50 -12 -90.5t-37 -74.5t-62 -64t-88 -58l-68 81q30 26 49.5 48.5t30.5 46t15.5 50t4.5 60.5v402h167zM217 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="678" 
d="M255 665v-329l250 329h180l-244 -311q42 -11 74.5 -45t60 -82.5t53 -107.5t53.5 -119h-192q-21 50 -38.5 93.5t-36.5 75.5t-41 50.5t-53 18.5l-66 -83v-155h-172v665h172zM475 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="558" 
d="M234 730v-434l141 191h169l-160 -196q53 -9 90.5 -45.5t51.5 -107.5l29 -138h-178l-23 125q-4 21 -12.5 33t-19.5 19t-23.5 9.5t-24.5 2.5l-40 -44v-145h-168v730h168zM412 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="525" 
d="M255 665v-547h251v-118h-423v665h172zM362 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="298" 
d="M234 730v-730h-168v730h168zM345 954l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="525" 
d="M255 665v-547h251v-118h-423v665h172zM407 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="296" 
d="M232 730v-730h-168v730h168zM236 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="525" 
d="M255 665v-547h251v-118h-423v665h172zM98 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="464" 
d="M234 730v-730h-168v730h168zM484 665l-82 -213h-113l37 213h158z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="545" 
d="M276 665v-244l168 66v-120l-168 -66v-183h251v-118h-423v234l-84 -33v120l84 33v311h172z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="346" 
d="M257 730v-270l69 28v-105l-69 -28v-355h-168v290l-69 -27v105l69 27v335h168z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="757" 
d="M215 665l291 -328v328h168v-665h-168v133l-256 293v-426h-167v665h132zM552 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="586" 
d="M64 459q18 6 43.5 13t56 13t64 10t67.5 4q54 0 97 -9.5t74 -30.5t47.5 -56t16.5 -86v-317h-167v311q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v459zM490 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="757" 
d="M215 665l291 -328v328h168v-665h-168v133l-256 293v-426h-167v665h132zM492 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="586" 
d="M64 459q18 6 43.5 13t56 13t64 10t67.5 4q54 0 97 -9.5t74 -30.5t47.5 -56t16.5 -86v-317h-167v311q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v459zM397 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="757" 
d="M215 665l291 -328v328h168v-665h-168v133l-256 293v-426h-167v665h132zM309 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="586" 
d="M64 459q18 6 43.5 13t56 13t64 10t67.5 4q54 0 97 -9.5t74 -30.5t47.5 -56t16.5 -86v-317h-167v311q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v459zM227 569l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="757" 
d="M674 665v-561q0 -61 -20.5 -107.5t-53 -82.5t-72.5 -64t-78 -50l-73 92q28 24 51.5 46t40.5 45.5t26.5 51t9.5 63.5v36l-255 292v-426h-167v665h132l290 -328v328h169z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="586" 
d="M64 459q18 6 43.5 13t56 13t64 10t67.5 4q54 0 97 -9.5t74 -30.5t47.5 -56t16.5 -86v-233q0 -50 -12 -90t-37 -73.5t-62 -63t-88 -57.5l-68 81q30 26 49.5 48t30.5 45t15.5 49t4.5 60v228q0 42 -19.5 57t-51.5 15q-14 0 -32 -2t-29 -5v-376h-167v459z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106zM497 880v-90h-302v90h302z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM436 694v-90h-302v90h302z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106zM297 907q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM236 722q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5
h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-155 0 -230.5 83t-75.5 261q0 180 75.5 263t230.5 83t230.5 -83t75.5 -263zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11
q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106zM561 916l-84 -161h-107l40 161h151zM360 916l-46 -161h-104l21 161h129z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="570" 
d="M536 244q0 -134 -64.5 -195t-186.5 -61t-186.5 61t-64.5 195t64.5 194.5t186.5 60.5t186.5 -60.5t64.5 -194.5zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM511 730l-84 -161h-107l40 161h151zM310 730
l-46 -161h-104l21 161h129z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="974" 
d="M909 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-67 0 -113 11t-74 40q-57 -63 -190 -63q-155 0 -230.5 82.5t-75.5 260.5q0 180 75.5 263t230.5 83q92 0 148 -32v20h415zM472 332q0 63 -7.5 106t-23.5 69.5t-39.5 37.5t-54.5 11
q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106t7.5 -106t23.5 -69.5t40 -37.5t56 -11q31 0 54.5 11t39.5 37.5t23.5 69.5t7.5 106z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="853" 
d="M538 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-56 0 -99.5 10.5t-75.5 37.5q-58 -48 -168 -48q-122 0 -186.5 61t-64.5 195t64.5 194.5t186.5 60.5q104 0 162 -43q28 24 67.5 33.5t92.5 9.5
q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5v-86h-277zM363 244q0 78 -17.5 108.5t-60.5 30.5t-60.5 -30.5t-17.5 -108.5t17.5 -109t60.5 -31t60.5 31t17.5 109zM660 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="638" 
d="M312 665q68 0 119 -10.5t85 -36t51 -67t17 -102.5q0 -38 -10 -64.5t-26.5 -45t-36.5 -30t-40 -19.5q38 -15 63.5 -41.5t40.5 -79.5l49 -169h-179l-38 131q-7 25 -15 44t-20 31t-29.5 18t-42.5 6q-11 0 -23.5 0.5t-21.5 1.5v-232h-172v665h229zM255 352q14 -2 28.5 -2.5
t26.5 -0.5q55 0 79.5 22.5t24.5 77.5q0 30 -6 49.5t-19 30t-32 14.5t-45 4h-57v-195zM516 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="381" 
d="M365 383q-34 0 -67.5 -1.5t-68.5 -6.5v-375h-167v459q74 24 152.5 32t150.5 8v-116zM394 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="638" 
d="M312 665q68 0 119 -10.5t85 -36t51 -67t17 -102.5q0 -38 -10 -64.5t-26.5 -45t-36.5 -30t-40 -19.5q38 -15 63.5 -41.5t40.5 -79.5l49 -169h-179l-38 131q-7 25 -15 44t-20 31t-29.5 18t-42.5 6q-11 0 -23.5 0.5t-21.5 1.5v-232h-172v665h229zM255 352q14 -2 28.5 -2.5
t26.5 -0.5q55 0 79.5 22.5t24.5 77.5q0 30 -6 49.5t-19 30t-32 14.5t-45 4h-57v-195zM448 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="381" 
d="M365 383q-34 0 -67.5 -1.5t-68.5 -6.5v-375h-167v459q74 24 152.5 32t150.5 8v-116zM234 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="638" 
d="M312 665q68 0 119 -10.5t85 -36t51 -67t17 -102.5q0 -38 -10 -64.5t-26.5 -45t-36.5 -30t-40 -19.5q38 -15 63.5 -41.5t40.5 -79.5l49 -169h-179l-38 131q-7 25 -15 44t-20 31t-29.5 18t-42.5 6q-11 0 -23.5 0.5t-21.5 1.5v-232h-172v665h229zM255 352q14 -2 28.5 -2.5
t26.5 -0.5q55 0 79.5 22.5t24.5 77.5q0 30 -6 49.5t-19 30t-32 14.5t-45 4h-57v-195zM258 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="381" 
d="M365 383q-34 0 -67.5 -1.5t-68.5 -6.5v-375h-167v459q74 24 152.5 32t150.5 8v-116zM144 569l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="559" 
d="M483 539q-17 3 -38 6t-43 5.5t-43 4t-39 1.5q-54 0 -81.5 -11.5t-27.5 -45.5q0 -21 22.5 -39t56.5 -36.5t73.5 -40.5t73.5 -51.5t56.5 -68.5t22.5 -92q0 -96 -67.5 -140t-194.5 -44q-65 0 -116 9.5t-89 19.5l14 120q43 -11 85.5 -19.5t98.5 -8.5q19 0 36 2t30 8t20 16.5
t7 27.5q0 24 -22.5 44.5t-56.5 41t-73.5 43.5t-73.5 51.5t-56.5 64.5t-22.5 84q0 56 20.5 92t58.5 56.5t90.5 28.5t117.5 8q44 0 91.5 -5.5t81.5 -11.5zM440 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="478" 
d="M416 374q-30 6 -60 9.5t-70 3.5q-42 0 -60 -6t-18 -26q0 -13 18 -23.5t44.5 -22t58 -26t58 -34.5t44.5 -48t18 -67q0 -41 -18 -69.5t-48.5 -45.5t-72 -24.5t-87.5 -7.5q-59 0 -102 7.5t-71 15.5l13 110q27 -6 66 -12.5t74 -6.5q42 0 56 7t14 27q0 14 -17.5 25t-43 22.5
t-56 25.5t-56 33t-43 45.5t-17.5 63.5q0 45 18 74t50 45.5t77 23t99 6.5q44 0 78 -4t63 -9zM417 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="559" 
d="M483 539q-17 3 -38 6t-43 5.5t-43 4t-39 1.5q-54 0 -81.5 -11.5t-27.5 -45.5q0 -21 22.5 -39t56.5 -36.5t73.5 -40.5t73.5 -51.5t56.5 -68.5t22.5 -92q0 -96 -67.5 -140t-194.5 -44q-65 0 -116 9.5t-89 19.5l14 120q43 -11 85.5 -19.5t98.5 -8.5q19 0 36 2t30 8t20 16.5
t7 27.5q0 24 -22.5 44.5t-56.5 41t-73.5 43.5t-73.5 51.5t-56.5 64.5t-22.5 84q0 56 20.5 92t58.5 56.5t90.5 28.5t117.5 8q44 0 91.5 -5.5t81.5 -11.5zM330 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="478" 
d="M416 374q-30 6 -60 9.5t-70 3.5q-42 0 -60 -6t-18 -26q0 -13 18 -23.5t44.5 -22t58 -26t58 -34.5t44.5 -48t18 -67q0 -41 -18 -69.5t-48.5 -45.5t-72 -24.5t-87.5 -7.5q-59 0 -102 7.5t-71 15.5l13 110q27 -6 66 -12.5t74 -6.5q42 0 56 7t14 27q0 14 -17.5 25t-43 22.5
t-56 25.5t-56 33t-43 45.5t-17.5 63.5q0 45 18 74t50 45.5t77 23t99 6.5q44 0 78 -4t63 -9zM317 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="559" 
d="M483 539q-17 3 -38 6t-43 5.5t-43 4t-39 1.5q-54 0 -81.5 -11.5t-27.5 -45.5q0 -21 22.5 -39t56.5 -36.5t73.5 -40.5t73.5 -51.5t56.5 -68.5t22.5 -92q0 -96 -67.5 -140t-194.5 -44q-65 0 -116 9.5t-89 19.5l14 120q43 -11 85.5 -19.5t98.5 -8.5q19 0 36 2t30 8t20 16.5
t7 27.5q0 24 -22.5 44.5t-56.5 41t-73.5 43.5t-73.5 51.5t-56.5 64.5t-22.5 84q0 56 20.5 92t58.5 56.5t90.5 28.5t117.5 8q44 0 91.5 -5.5t81.5 -11.5zM343 -35q14 -17 24.5 -36t10.5 -41q0 -42 -26.5 -67t-92.5 -25q-26 0 -54 4t-51 13l17 87q10 -3 25.5 -6t26.5 -3
q19 0 25 6.5t6 20.5q0 11 -5.5 23t-13.5 24h108z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="478" 
d="M416 374q-30 6 -60 9.5t-70 3.5q-42 0 -60 -6t-18 -26q0 -13 18 -23.5t44.5 -22t58 -26t58 -34.5t44.5 -48t18 -67q0 -41 -18 -69.5t-48.5 -45.5t-72 -24.5t-87.5 -7.5q-59 0 -102 7.5t-71 15.5l13 110q27 -6 66 -12.5t74 -6.5q42 0 56 7t14 27q0 14 -17.5 25t-43 22.5
t-56 25.5t-56 33t-43 45.5t-17.5 63.5q0 45 18 74t50 45.5t77 23t99 6.5q44 0 78 -4t63 -9zM331 -35q14 -17 24.5 -36t10.5 -41q0 -42 -26.5 -67t-92.5 -25q-26 0 -54 4t-51 13l17 87q10 -3 25.5 -6t26.5 -3q19 0 25 6.5t6 20.5q0 11 -5.5 23t-13.5 24h108z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="559" 
d="M483 539q-17 3 -38 6t-43 5.5t-43 4t-39 1.5q-54 0 -81.5 -11.5t-27.5 -45.5q0 -21 22.5 -39t56.5 -36.5t73.5 -40.5t73.5 -51.5t56.5 -68.5t22.5 -92q0 -96 -67.5 -140t-194.5 -44q-65 0 -116 9.5t-89 19.5l14 120q43 -11 85.5 -19.5t98.5 -8.5q19 0 36 2t30 8t20 16.5
t7 27.5q0 24 -22.5 44.5t-56.5 41t-73.5 43.5t-73.5 51.5t-56.5 64.5t-22.5 84q0 56 20.5 92t58.5 56.5t90.5 28.5t117.5 8q44 0 91.5 -5.5t81.5 -11.5zM184 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="478" 
d="M416 374q-30 6 -60 9.5t-70 3.5q-42 0 -60 -6t-18 -26q0 -13 18 -23.5t44.5 -22t58 -26t58 -34.5t44.5 -48t18 -67q0 -41 -18 -69.5t-48.5 -45.5t-72 -24.5t-87.5 -7.5q-59 0 -102 7.5t-71 15.5l13 110q27 -6 66 -12.5t74 -6.5q42 0 56 7t14 27q0 14 -17.5 25t-43 22.5
t-56 25.5t-56 33t-43 45.5t-17.5 63.5q0 45 18 74t50 45.5t77 23t99 6.5q44 0 78 -4t63 -9zM178 569l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="592" 
d="M579 665v-118h-197v-547h-172v547h-197v118h566zM384 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="403" 
d="M253 621v-134h128v-114h-128v-222q0 -24 15 -32.5t35 -8.5q19 0 38 3t33 7l4 -115q-27 -9 -55.5 -13t-57.5 -4q-85 0 -132 41.5t-47 124.5v219h-74v114h74l29 134h138zM322 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="592" 
d="M579 665v-118h-197v-547h-172v547h-197v118h566zM226 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="403" 
d="M450 665l-73 -140h-93l37 140h129zM253 621v-134h128v-114h-128v-222q0 -24 15 -32.5t35 -8.5q19 0 38 3t33 7l4 -115q-27 -9 -55.5 -13t-57.5 -4q-85 0 -132 41.5t-47 124.5v219h-74v114h74l29 134h138z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="592" 
d="M210 361v186h-197v118h566v-118h-197v-186h145v-117h-145v-244h-172v244h-145v117h145z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="403" 
d="M253 621v-134h128v-114h-128v-73h104v-103h-104v-46q0 -24 15 -32.5t35 -8.5q19 0 38 3t33 7l4 -115q-27 -9 -55.5 -13t-57.5 -4q-85 0 -132 41.5t-47 124.5v43h-74v103h74v73h-74v114h74l29 134h138z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM179 799q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28
q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM116 614q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25
l95 -28q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM504 880v-90h-302v90h302z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM441 694v-90h-302v90h302z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM304 907q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5
t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM241 722q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5
t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM479 835q0 -51 -34.5 -72t-91.5 -21t-91.5 21t-34.5 72t34.5 72t91.5 21t91.5 -21t34.5 -72zM390 835
q0 32 -37 32t-37 -32t37 -32t37 32z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM416 649q0 -51 -34.5 -72t-91.5 -21t-91.5 21t-34.5 72t34.5 72t91.5 21t91.5 -21t34.5 -72z
M327 649q0 32 -37 32t-37 -32t37 -32t37 32z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="707" 
d="M240 665v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-636q-47 -12 -119 -27t-161 -15q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173zM558 916l-84 -161h-107l40 161h151zM357 916l-46 -161h-104l21 161h129z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="581" 
d="M223 487v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -7 -48 -14t-57 -12t-62 -8.5t-63 -3.5q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167zM504 730l-84 -161h-107l40 161h151zM303 730l-46 -161h-104l21 161h129z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="707" 
d="M668 -152q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 23 8.5 43.5t22.5 37.5q-30 -5 -63.5 -7.5t-68.5 -2.5q-78 0 -131.5 15.5t-86.5 45t-47.5 73t-14.5 99.5v445h173v-456q0 -55 30 -78t83 -23q31 0 58.5 3.5t43.5 7.5v546h172v-637
q-21 -17 -38.5 -39.5t-17.5 -42.5q0 -23 32 -23q24 0 49 11z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="581" 
d="M560 -154q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 23 10.5 43.5t26.5 36.5q-44 -6 -88 -6q-50 0 -92.5 8.5t-73.5 29.5t-49 57t-18 91v313h167v-319q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v375h168v-461q-21 -17 -38.5 -39.5t-17.5 -42.5
q0 -23 32 -23q24 0 49 11z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="941" 
d="M535 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139zM183 0l-179 665h178l104 -472l112 356h141l116 -361q39 88 69 205.5t52 271.5h161q-29 -200 -80 -365t-119 -300h-159l-110 358l-120 -358h-166z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="884" 
d="M510 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139zM7 487h180l89 -342l91 342h163l98 -342q14 33 27 69t25 77t22.5 89.5t19.5 106.5h155q-34 -242 -166 -487h-181l-83 319l-84 -319h-195z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="677" 
d="M414 916l111 -161h-127l-53 89l-54 -89h-127l111 161h139zM258 232l-273 433h187l184 -298q20 18 43.5 52t45.5 75.5t39 86t25 84.5h173q-18 -60 -43.5 -122t-57 -118.5t-69 -104.5t-79.5 -82v-238h-175v232z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="585" 
d="M223 487v-307q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v363h168v-462q0 -69 -19.5 -113t-54.5 -68.5t-82.5 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q24 0 42.5 3t31 12t19 24.5t6.5 40.5v11
q-14 -3 -35.5 -4.5t-36.5 -1.5q-47 0 -87.5 8.5t-70.5 29.5t-47.5 57t-17.5 91v301h167zM349 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="677" 
d="M340 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM539 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM258 232l-273 433h187l184 -298q20 18 43.5 52
t45.5 75.5t39 86t25 84.5h173q-18 -60 -43.5 -122t-57 -118.5t-69 -104.5t-79.5 -82v-238h-175v232z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="611" 
d="M566 665v-113l-305 -433h315v-119h-528v113l305 434h-296v118h509zM489 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="505" 
d="M459 487v-101l-215 -272h227v-114h-435v101l221 272h-210v114h412zM442 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="611" 
d="M566 665v-113l-305 -433h315v-119h-528v113l305 434h-296v118h509zM399 835q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="505" 
d="M459 487v-101l-215 -272h227v-114h-435v101l221 272h-210v114h412zM345 651q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="611" 
d="M566 665v-113l-305 -433h315v-119h-528v113l305 434h-296v118h509zM246 755l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="505" 
d="M459 487v-101l-215 -272h227v-114h-435v101l221 272h-210v114h412zM190 569l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="375" 
d="M87 487v74q0 93 57.5 137t154.5 44q27 0 58 -3t53 -8l-14 -112q-18 2 -38.5 4t-37.5 2q-29 0 -47.5 -13t-18.5 -51v-561h-167v373h-74v114h74z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M170 394v80q0 59 19.5 98t52.5 62.5t76 33t90 9.5q50 0 89 -4.5t70 -11.5l-17 -120q-28 6 -59 10.5t-60 4.5q-38 0 -64 -17.5t-26 -63.5v-81h195v-99h-195v-295h-171v295h-118v99h118z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="666" 
d="M459 835q0 -51 -34.5 -72t-91.5 -21t-91.5 21t-34.5 72q0 38 19 59t54 29l57 90h137l-87 -91q34 -8 53 -28.5t19 -58.5zM370 835q0 32 -37 32t-37 -32t37 -32t37 32zM229 0h-169v432q0 56 13 101.5t44.5 77.5t83.5 49t130 17t130.5 -17t84.5 -49t46 -77.5t14 -101.5v-432
h-173v222h-204v-222zM433 340v104q0 55 -24.5 83.5t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-104h204z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="532" 
d="M387 649q0 -51 -34.5 -72t-91.5 -21t-91.5 21t-34.5 72q0 38 19 59t54 29l57 90h137l-87 -91q34 -8 53 -28.5t19 -58.5zM298 649q0 32 -37 32t-37 -32t37 -32t37 32zM315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5
v113zM75 477q35 9 82.5 15.5t90.5 6.5q50 0 92.5 -8.5t74 -28t49.5 -51t18 -77.5v-309q-18 -6 -43.5 -13t-55.5 -12t-62 -8.5t-63 -3.5q-47 0 -87.5 7.5t-70 26.5t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11
q-37 0 -79.5 -6.5t-75.5 -14.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="934" 
d="M869 665v-118h-263v-151h246v-118h-246v-92q0 -42 25 -55t61 -13h188v-118h-197q-59 0 -105.5 9t-78.5 31t-48.5 58.5t-16.5 91.5v78h-205v-268h-169v432q0 56 13.5 101.5t43.5 77t78.5 48.5t117.5 17q43 0 78.5 -10t54.5 -31v30h423zM433 386v58q0 55 -24.5 83.5
t-77.5 28.5t-77.5 -28.5t-24.5 -83.5v-58h204zM726 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="796" 
d="M481 199q1 -28 8 -46t20.5 -29t35 -15.5t53.5 -4.5q15 0 34 1.5t38 4.5t36 6.5t29 7.5l15 -111q-34 -11 -81.5 -18t-97.5 -7q-45 0 -83 6.5t-68 22.5q-35 -12 -77 -20.5t-85 -8.5q-47 0 -87.5 7.5t-70 26.5t-46 51.5t-16.5 81.5q0 44 17 73t44.5 46.5t63 25t72.5 7.5
q21 0 41 -2t39 -5v29q0 35 -18.5 46t-59.5 11q-37 0 -79.5 -6.5t-75.5 -14.5l-7 112q35 9 82.5 15.5t90.5 6.5q48 0 89 -7.5t73 -25.5q26 18 60.5 25.5t79.5 7.5q57 0 96.5 -14.5t64 -42.5t36 -67.5t11.5 -89.5v-86h-277zM315 207q-12 2 -24.5 3t-23.5 1q-32 0 -52 -13.5
t-20 -49.5q0 -34 20.5 -46.5t49.5 -12.5q25 0 50 5v113zM603 287q0 57 -11.5 83.5t-48.5 26.5q-36 0 -49 -28t-13 -82h122zM604 730l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="692" 
d="M652 331q0 -178 -76 -261t-230 -83q-48 0 -88.5 8.5t-72.5 23.5l-60 -90l-67 45l61 93q-79 83 -79 264q0 180 75.5 263t230.5 83q91 0 158 -31l60 91l67 -45l-61 -92q82 -83 82 -269zM472 332q0 62 -8 108l-200 -301q15 -17 36 -24t47 -7q31 0 54.5 11t39.5 37.5
t23.5 69.5t7.5 106zM220 332q0 -31 2 -56.5t5 -46.5l198 299q-14 15 -34 21.5t-44 6.5q-32 0 -56 -11t-40 -37.5t-23.5 -69.5t-7.5 -106zM533 916l-135 -161h-122l82 161h175z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="570" 
d="M478 730l-135 -161h-122l82 161h175zM536 244q0 -134 -64.5 -195t-186.5 -61q-64 0 -115 17l-59 -91l-62 38l58 89q-36 31 -54.5 81t-18.5 122q0 134 64.5 194.5t186.5 60.5q32 0 59.5 -4t51.5 -12l60 91l62 -38l-58 -88q38 -31 57 -81t19 -123zM363 244q0 28 -3 51
l-118 -180q15 -11 43 -11q43 0 60.5 31t17.5 109zM207 244v-25.5t2 -22.5l116 178q-17 9 -40 9q-43 0 -60.5 -30.5t-17.5 -108.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="559" 
d="M483 539q-17 3 -38 6t-43 5.5t-43 4t-39 1.5q-54 0 -81.5 -11.5t-27.5 -45.5q0 -21 22.5 -39t56.5 -36.5t73.5 -40.5t73.5 -51.5t56.5 -68.5t22.5 -92q0 -96 -67.5 -140t-194.5 -44q-65 0 -116 9.5t-89 19.5l14 120q43 -11 85.5 -19.5t98.5 -8.5q19 0 36 2t30 8t20 16.5
t7 27.5q0 24 -22.5 44.5t-56.5 41t-73.5 43.5t-73.5 51.5t-56.5 64.5t-22.5 84q0 56 20.5 92t58.5 56.5t90.5 28.5t117.5 8q44 0 91.5 -5.5t81.5 -11.5zM399 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="478" 
d="M416 374q-30 6 -60 9.5t-70 3.5q-42 0 -60 -6t-18 -26q0 -13 18 -23.5t44.5 -22t58 -26t58 -34.5t44.5 -48t18 -67q0 -41 -18 -69.5t-48.5 -45.5t-72 -24.5t-87.5 -7.5q-59 0 -102 7.5t-71 15.5l13 110q27 -6 66 -12.5t74 -6.5q42 0 56 7t14 27q0 14 -17.5 25t-43 22.5
t-56 25.5t-56 33t-43 45.5t-17.5 63.5q0 45 18 74t50 45.5t77 23t99 6.5q44 0 78 -4t63 -9zM360 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="550" 
d="M344 730l111 -161h-127l-53 89l-54 -89h-127l111 161h139z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="550" 
d="M205 569l-111 161h127l54 -89l53 89h127l-111 -161h-139z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="550" 
d="M225 722q1 -29 14 -41.5t36 -12.5t36 12.5t14 41.5h114q-2 -41 -14.5 -68.5t-34 -44.5t-50.5 -24.5t-65 -7.5t-65 7.5t-50.5 24.5t-34.5 44.5t-15 68.5h115z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="550" 
d="M358 651q0 -32 -22 -49.5t-61 -17.5q-40 0 -61.5 17.5t-21.5 49.5t21.5 49.5t61.5 17.5q39 0 61 -17.5t22 -49.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="550" 
d="M401 649q0 -51 -34.5 -72t-91.5 -21t-91.5 21t-34.5 72t34.5 72t91.5 21t91.5 -21t34.5 -72zM312 649q0 32 -37 32t-37 -32t37 -32t37 32z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="550" 
d="M381 -180q-48 -20 -100 -20q-25 0 -47 4.5t-38 15t-25.5 27.5t-9.5 41q0 37 20.5 66.5t48.5 45.5h110q-21 -17 -38.5 -39.5t-17.5 -42.5q0 -23 32 -23q24 0 49 11z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="550" 
d="M100 614q10 41 37 69.5t75 28.5q18 0 34 -5t29 -11t24 -11t20 -5q17 0 23.5 10t11.5 25l95 -28q-17 -47 -44.5 -75t-71.5 -28q-17 0 -32 5.5t-28 11.5t-23.5 11.5t-19.5 5.5q-17 0 -24.5 -11t-11.5 -23z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="550" 
d="M490 730l-84 -161h-107l40 161h151zM289 730l-46 -161h-104l21 161h129z" />
    <glyph glyph-name="Delta" unicode="&#x394;" 
d="M226 666h152l211 -666h-574zM302 530q-32 -109 -63.5 -217t-62.5 -217h252z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" 
d="M226 666h152l211 -666h-574zM302 530q-32 -109 -63.5 -217t-62.5 -217h252z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="693" 
d="M154 104q-23 20 -43 42t-36 50.5t-25 64.5t-9 84q0 89 21.5 152t61 103t96 58.5t126.5 18.5t126.5 -18.5t96.5 -58.5t61.5 -103t21.5 -152q0 -48 -9 -84t-25 -64.5t-36.5 -50.5t-43.5 -42h123v-104h-277v107q30 19 52.5 41.5t38 51t23.5 64t8 81.5q0 62 -9.5 105
t-29.5 69.5t-50 38t-71 11.5t-70.5 -11.5t-49.5 -38t-29.5 -69.5t-9.5 -105q0 -46 8 -81.5t23.5 -64t38.5 -51t52 -41.5v-107h-277v104h122z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="693" 
d="M154 104q-23 20 -43 42t-36 50.5t-25 64.5t-9 84q0 89 21.5 152t61 103t96 58.5t126.5 18.5t126.5 -18.5t96.5 -58.5t61.5 -103t21.5 -152q0 -48 -9 -84t-25 -64.5t-36.5 -50.5t-43.5 -42h123v-104h-277v107q30 19 52.5 41.5t38 51t23.5 64t8 81.5q0 62 -9.5 105
t-29.5 69.5t-50 38t-71 11.5t-70.5 -11.5t-49.5 -38t-29.5 -69.5t-9.5 -105q0 -46 8 -81.5t23.5 -64t38.5 -51t52 -41.5v-107h-277v104h122z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="646" 
d="M631 487v-114h-78v-373h-175v373h-110v-373h-175v373h-78v114h616z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="941" 
d="M446 916l81 -161h-120l-136 161h175zM183 0l-179 665h178l104 -472l112 356h141l116 -361q39 88 69 205.5t52 271.5h161q-29 -200 -80 -365t-119 -300h-159l-110 358l-120 -358h-166z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="884" 
d="M416 730l81 -161h-120l-136 161h175zM7 487h180l89 -342l91 342h163l98 -342q14 33 27 69t25 77t22.5 89.5t19.5 106.5h155q-34 -242 -166 -487h-181l-83 319l-84 -319h-195z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="941" 
d="M663 916l-135 -161h-122l82 161h175zM183 0l-179 665h178l104 -472l112 356h141l116 -361q39 88 69 205.5t52 271.5h161q-29 -200 -80 -365t-119 -300h-159l-110 358l-120 -358h-166z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="884" 
d="M640 730l-135 -161h-122l82 161h175zM7 487h180l89 -342l91 342h163l98 -342q14 33 27 69t25 77t22.5 89.5t19.5 106.5h155q-34 -242 -166 -487h-181l-83 319l-84 -319h-195z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="941" 
d="M445 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM644 835q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM183 0l-179 665h178l104 -472l112 356h141
l116 -361q39 88 69 205.5t52 271.5h161q-29 -200 -80 -365t-119 -300h-159l-110 358l-120 -358h-166z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="884" 
d="M420 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM619 651q0 -31 -20.5 -47.5t-58.5 -16.5t-58.5 16.5t-20.5 47.5t20.5 47.5t58.5 16.5t58.5 -16.5t20.5 -47.5zM7 487h180l89 -342l91 342h163l98 -342
q14 33 27 69t25 77t22.5 89.5t19.5 106.5h155q-34 -242 -166 -487h-181l-83 319l-84 -319h-195z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="677" 
d="M342 916l81 -161h-120l-136 161h175zM258 232l-273 433h187l184 -298q20 18 43.5 52t45.5 75.5t39 86t25 84.5h173q-18 -60 -43.5 -122t-57 -118.5t-69 -104.5t-79.5 -82v-238h-175v232z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="585" 
d="M223 487v-307q0 -35 17.5 -49.5t51.5 -14.5q15 0 31.5 2t27.5 6v363h168v-462q0 -69 -19.5 -113t-54.5 -68.5t-82.5 -34t-103.5 -9.5q-25 0 -50.5 2t-49 5.5t-42.5 8t-31 8.5l14 113q12 -4 30 -8t38.5 -7t42 -5t41.5 -2q24 0 42.5 3t31 12t19 24.5t6.5 40.5v11
q-14 -3 -35.5 -4.5t-36.5 -1.5q-47 0 -87.5 8.5t-70.5 29.5t-47.5 57t-17.5 91v301h167zM275 730l81 -161h-120l-136 161h175z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="604" 
d="M547 324v-106h-490v106h490z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="811" 
d="M755 324v-106h-698v106h698z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="250" 
d="M13 452l82 213h113l-37 -213h-158z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="250" 
d="M237 665l-82 -213h-113l37 213h158z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="250" 
d="M219 52l-82 -213h-113l37 213h158z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="485" 
d="M251 452l52 213h113l-14 -213h-151zM22 452l96 213h113l-51 -213h-158z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="485" 
d="M234 665l-52 -213h-113l14 213h151zM463 665l-96 -213h-113l51 213h158z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="470" 
d="M238 54l-52 -213h-113l14 213h151zM467 54l-96 -213h-113l51 213h158z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="466" 
d="M302 665v-157h131v-105h-131v-255h-140v255h-129v105h129v157h140z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="466" 
d="M162 547v118h140v-118h131v-105h-131v-71h131v-106h-131v-117h-140v117h-129v106h129v71h-129v105h129z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="385" 
d="M193 401q65 0 105.5 -31.5t40.5 -98.5q0 -66 -40.5 -98.5t-105.5 -32.5q-63 0 -105.5 33t-42.5 98q0 33 11.5 57.5t31 40.5t46.5 24t59 8z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="825" 
d="M137 159q47 0 74.5 -22t27.5 -63q0 -40 -27.5 -62t-74.5 -22q-46 0 -73.5 22t-27.5 62q0 41 27.5 63t73.5 22zM412 159q47 0 74.5 -22t27.5 -63q0 -40 -27.5 -62t-74.5 -22q-46 0 -73.5 22t-27.5 62q0 41 27.5 63t73.5 22zM687 159q47 0 74.5 -22t27.5 -63
q0 -40 -27.5 -62t-74.5 -22q-46 0 -73.5 22t-27.5 62q0 41 27.5 63t73.5 22z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1241" 
d="M1038 264q-22 0 -38.5 -20.5t-16.5 -74.5t16.5 -75t38.5 -21t38 21t16 74q0 55 -15.5 75.5t-38.5 20.5zM1038 345q37 0 68.5 -9.5t54.5 -30.5t35.5 -54.5t12.5 -82.5q0 -47 -12.5 -80.5t-35.5 -55.5t-54.5 -32t-68.5 -10t-68 10t-53.5 32t-35.5 56t-13 81q0 48 13 81.5
t35.5 54.5t53.5 30.5t68 9.5zM201 595q-22 0 -38.5 -20.5t-16.5 -74.5q0 -55 16.5 -76t38.5 -21q23 0 38 21t15 75q0 55 -15 75.5t-38 20.5zM201 676q37 0 68.5 -9.5t54 -30.5t35.5 -55t13 -82q0 -47 -13 -80.5t-35.5 -55.5t-54 -32t-68.5 -10t-68 10t-54 32t-36 56t-13 81
q0 48 13 81.5t36 54.5t54 30.5t68 9.5zM658 264q-22 0 -39 -20.5t-17 -74.5t17 -75t39 -21t37.5 21t15.5 74q0 55 -15 75.5t-38 20.5zM658 345q37 0 68.5 -9.5t54 -30.5t35.5 -54.5t13 -82.5q0 -47 -13 -80.5t-35.5 -55.5t-54 -32t-68.5 -10t-68 10t-54 32t-36 56t-13 81
q0 48 13 81.5t36 54.5t54 30.5t68 9.5zM700 666l-431 -666h-111l430 666h112z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="371" 
d="M336 452l-124 -181l124 -181h-142l-159 181l159 181h142z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="371" 
d="M177 452l159 -181l-159 -181h-142l124 181l-124 181h142z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="562" 
d="M551 665l-429 -665h-111l429 665h111z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M111 437q7 56 25 100.5t50 75.5t78.5 47.5t109.5 16.5t111 -5t80 -13l-21 -125q-33 10 -75 16t-73 6q-99 0 -106 -119h229l-28 -79h-209q-1 -8 -1 -15v-15q0 -11 0.5 -21t1.5 -21h184l-27 -79h-144q4 -52 29.5 -75.5t70.5 -23.5q32 0 72 6t77 18l18 -115
q-35 -17 -85 -23.5t-104 -6.5q-60 0 -103.5 15.5t-74.5 44t-50.5 69.5t-30.5 91h-85v79h73q-1 11 -1.5 22.5t-0.5 23.5v26h-71v79h81z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="847" 
d="M333 665v-77h-97v-222h-119v222h-98v77h314zM530 429l-54 117v-180h-104v299h132l69 -138l69 138h132v-299h-104v180l-54 -117h-86z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="614" 
d="M139 637q47 23 92 31.5t102 8.5q51 0 91.5 -13t69 -40.5t44 -71.5t15.5 -105q0 -83 -16 -165t-53 -147.5t-97.5 -106t-148.5 -40.5q-93 0 -147 49.5t-54 132.5q0 51 14 93.5t43 73t72 47.5t102 17q39 0 72 -8.5t56 -23.5q2 17 3.5 41t1.5 36q0 64 -28 90.5t-73 26.5
q-38 0 -73.5 -10.5t-72.5 -26.5zM253 103q28 0 47 14t32 36.5t21 51t14 57.5q-17 11 -38.5 17.5t-46.5 6.5q-20 0 -37.5 -5t-30.5 -17t-21 -33t-8 -52q0 -34 18 -55t50 -21z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="715" 
d="M700 665v-117h-72v-548h-171v548h-181v-548h-169v548h-72v117h665z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M63 93l206 254l-206 225v93h477v-118h-264l191 -200l-200 -229h297v-118h-501v93z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M547 372v-107h-490v107h490z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M208 487l98 -327l162 505h126l-222 -665h-131l-134 384h-97v103h198z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="682" 
d="M278 319q-23 22 -43 33.5t-43 11.5q-19 0 -34.5 -10.5t-15.5 -34.5t15.5 -34.5t34.5 -10.5q23 0 43 11t43 34zM404 319q23 -22 43.5 -33.5t43.5 -11.5q18 0 33 10.5t15 34.5t-15 34.5t-33 10.5q-23 0 -43.5 -11t-43.5 -34zM341 393q34 29 70 46.5t85 17.5q70 0 115 -34.5
t45 -103.5t-45 -103.5t-115 -34.5q-49 0 -85 18t-70 47q-34 -29 -70 -47t-85 -18q-70 0 -115 34.5t-45 103.5t45 103.5t115 34.5q49 0 85 -17.5t70 -46.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="323" 
d="M336 624q-6 1 -10.5 1h-9.5q-75 0 -75 -61v-588q0 -40 -11 -73t-34.5 -56.5t-61 -36.5t-90.5 -13q-29 0 -60 3l5 116q6 -1 10 -1h9q75 0 75 61v588q0 40 11 73.5t34.5 56.5t61 36t90.5 13q27 0 59 -3z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M564 450q-10 -27 -24 -48.5t-34 -37.5t-48 -24.5t-65 -8.5q-27 1 -54 8.5t-51 16.5t-44.5 16.5t-36.5 7.5q-26 0 -40 -12.5t-25 -40.5q-26 10 -51 19.5t-51 18.5q9 25 21.5 47t31 38t44.5 25t61 9q27 0 54.5 -7.5t53.5 -16.5t49.5 -16.5t41.5 -7.5q26 0 40 12.5t25 40.5z
M564 255q-10 -27 -24 -48.5t-34 -37.5t-48 -24.5t-65 -8.5q-27 1 -54 8.5t-51 16.5t-44.5 16.5t-36.5 7.5q-26 0 -40 -12.5t-25 -40.5q-26 10 -51 19.5t-51 18.5q9 25 21.5 47t31 38t44.5 25t61 9q27 0 54.5 -7.5t53.5 -16.5t49.5 -16.5t41.5 -7.5q26 0 40 12.5t25 40.5z
" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M324 472l52 108h95l-51 -108h127v-107h-176l-43 -91h219v-107h-269l-50 -109h-95l50 109h-126v107h176l42 91h-218v107h267z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M547 527v-116l-331 -93l331 -93v-116l-490 157v104zM547 107v-107h-490v107h490z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M547 370v-104l-490 -157v116l331 93l-331 93v116zM547 107v-107h-490v107h490z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M256 666h92l208 -333l-208 -333h-92l-208 333zM302 535l-121 -202l121 -202l121 202z" />
    <glyph glyph-name="commaaccentcomb" horiz-adv-x="550" 
d="M407 -46l-115 -142h-122l62 142h175z" />
    <glyph glyph-name="apple" horiz-adv-x="1100" 
d="M821 784h5q30 0 42 -38l47 -156l-499 59l-56 -72l217 -33l-123 -351l-317 23q-13 1 -26 4.5t-23.5 10t-17 16t-6.5 22.5q0 5 1 12t3 14l153 410q7 16 29 26.5t48 12.5zM169 290q-1 -5 -1.5 -9t-0.5 -7q0 -16 12 -25t31 -11l226 -21l97 265l-188 27l-8 68l71 89l481 -54
l-34 112q-5 14 -17.5 18.5t-24.5 5.5l-452 -34q-16 -1 -33.5 -8.5t-23.5 -26.5zM944 486l80 -279q1 -2 1 -9q0 -19 -16 -29.5t-42 -10.5l-234 13v348zM815 190l151 -12h3q14 0 22.5 8.5t8.5 18.5q0 6 -1 7l-65 211l-119 22v-255zM61 9h25q19 -1 32 10t13 38q0 29 -13 40
t-32 10h-25v-98zM88 122q27 0 44.5 -15.5t18.5 -49.5q-1 -33 -18.5 -48t-44.5 -15h-45v128h45zM233 52q-3 21 -23 21q-12 0 -19 -8t-7 -21zM185 32q1 -13 8.5 -19t18.5 -6q9 -1 15.5 3t11.5 11l12 -6q-6 -10 -15.5 -16t-23.5 -6q-21 0 -34 12.5t-14 35.5q1 19 13 32t33 14
q22 -1 32 -13t11 -32zM280 21q8 -14 25 -14q22 0 19 13q1 7 -7.5 9.5t-19 5.5t-19.5 8t-9 17t10 19t27 8q25 -1 36 -18l-12 -8q-6 12 -25 12q-18 0 -18 -13q0 -7 8.5 -9.5t19 -5t19 -8t8.5 -17.5q0 -13 -9.5 -20t-26.5 -7q-15 0 -25 6t-15 16zM385 113q-2 -8 -11 -8
q-10 0 -10 8q0 9 10 9q9 0 11 -9zM383 86v-92h-18v92h18zM474 69q-7 4 -21 4q-12 0 -20 -8t-8 -24q0 -32 26 -32q17 0 23 13v47zM493 -8q0 -39 -43 -39q-28 0 -41 21l12 9q5 -8 11.5 -12t17.5 -4q26 0 24 24v16q-8 -13 -27 -13q-20 0 -30 12.5t-11 34.5q1 23 14.5 34.5
t33.5 11.5q26 -2 39 -11v-84zM541 86v-17q10 16 32 18q31 -1 32 -31v-62h-19v62q0 17 -18 17q-10 0 -15.5 -4.5t-10.5 -11.5v-63h-17v92h16zM694 52q-3 21 -23 21q-13 0 -20.5 -8t-7.5 -21zM645 32q3 -25 28 -25q18 -2 25 14l14 -6q-5 -10 -15 -16t-24 -6q-47 0 -47 48
q0 19 12 32t33 14q22 -1 32.5 -13t10.5 -32zM796 63q-8 10 -23 10q-13 0 -20 -9t-7 -24t7 -24t20 -9q15 0 23 13v43zM815 126v-108q0 -5 0.5 -12t2.5 -12l-16 -1q0 3 -1 6.5t-1 7.5q-5 -6 -12.5 -10t-18.5 -4q-19 0 -30.5 13t-11.5 35q0 21 13 33t30 13q20 -1 26 -10v49h19z
M914 20q10 -13 24 -13t20.5 9.5t6.5 24.5q0 32 -27 32q-8 0 -13.5 -4.5t-10.5 -10.5v-38zM914 126v-54q9 13 31 15q19 -1 28 -13t9 -33q0 -22 -10.5 -35t-28.5 -13q-23 0 -32 14q0 -8 -3 -14l-16 1q2 6 3.5 12.5t1.5 11.5v108h17zM1009 86l27 -75l27 75h18l-40 -100
q-6 -17 -16 -25t-29 -9l-4 13q23 1 31 21l3 7l-35 93h18zM197 -62l-1 -14h-39v-113h-18v113h-39v14h97zM305 -163q6 -13 24 -13q13 0 19 9t6 24q0 32 -24 32q-16 0 -25 -14v-38zM302 -97v-15q12 14 33 16q20 -1 29 -13.5t9 -33.5q1 -21 -9 -34.5t-32 -13.5q-10 0 -16.5 4
t-10.5 8v-50l-18 -1v132zM433 -96q22 -1 34 -14t13 -33q-1 -21 -13 -34t-34 -14q-21 1 -33 14t-13 34q1 20 13 33t33 14zM433 -111q-13 0 -20 -9t-7 -23q0 -15 7 -24t20 -9t20 9t7 24q0 14 -7 23t-20 9zM520 -163q8 -13 23 -13q14 0 20 9t6 24t-5.5 23.5t-19.5 8.5
q-15 0 -24 -14v-38zM518 -97v-15q10 14 33 16q18 -1 27.5 -13.5t9.5 -33.5q2 -21 -9 -34.5t-31 -13.5q-9 0 -16 4t-12 8v-50l-18 -1v132zM630 -57v-54q10 13 31 15q31 -1 32 -31v-62h-19v62q0 16 -18 16t-26 -16v-62h-18v131zM714 -115q6 7 15.5 12.5t24.5 6.5
q20 -2 27.5 -11t7.5 -21v-37q0 -5 1 -11.5t3 -11.5l-16 -3q-1 4 -1 7.5t-2 7.5q-4 -6 -11 -10.5t-18 -4.5q-32 0 -32 26q-1 17 19 24q5 2 17 4t22 3v7q0 7 -4 11.5t-16 4.5q-10 0 -15 -3.5t-9 -8.5zM771 -146q-8 -1 -17.5 -3t-14.5 -3q-9 -4 -9 -11q0 -13 18 -13q17 0 23 14
v16zM878 -115q-8 6 -21 4q-13 0 -21 -7.5t-8 -24.5q0 -31 27 -32q16 2 23 14v46zM895 -191q0 -39 -41 -39q-31 0 -41 21l12 9q5 -8 11.5 -12t17.5 -4q26 0 24 24v16q-8 -13 -28 -13q-19 0 -29 12.5t-10 33.5q0 23 13.5 35t33.5 12q11 -1 21 -3.5t16 -7.5v-84zM987 -131
q-1 20 -23 20q-12 0 -19 -8t-7 -20zM938 -151q2 -13 9.5 -19t18.5 -6q18 -1 25 13l13 -5q-5 -10 -14 -16.5t-24 -6.5q-21 0 -33.5 12.5t-13.5 35.5q1 20 13 33t32 14q21 -1 31.5 -13t12.5 -32zM197 -98l27 -75l27 75h18l-40 -100q-6 -17 -16 -25t-29 -9l-4 13q23 1 31 21
l4 7l-36 93h18z" />
    <hkern u1="&#x28;" u2="&#x134;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-20" />
    <hkern u1="&#x28;" u2="J" k="-50" />
    <hkern u1="&#x2a;" u2="&#x2026;" k="175" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="175" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="175" />
    <hkern u1="&#x2c;" g2="fl" k="10" />
    <hkern u1="&#x2c;" g2="fi" k="10" />
    <hkern u1="&#x2c;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2c;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2c;" u2="&#x1ef2;" k="135" />
    <hkern u1="&#x2c;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2c;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2c;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2c;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2c;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2c;" u2="&#x1fe;" k="25" />
    <hkern u1="&#x2c;" u2="&#x1fc;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x1fa;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2c;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2c;" u2="&#x178;" k="135" />
    <hkern u1="&#x2c;" u2="&#x177;" k="20" />
    <hkern u1="&#x2c;" u2="&#x176;" k="135" />
    <hkern u1="&#x2c;" u2="&#x175;" k="65" />
    <hkern u1="&#x2c;" u2="&#x174;" k="55" />
    <hkern u1="&#x2c;" u2="&#x173;" k="20" />
    <hkern u1="&#x2c;" u2="&#x172;" k="20" />
    <hkern u1="&#x2c;" u2="&#x171;" k="20" />
    <hkern u1="&#x2c;" u2="&#x170;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16e;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16c;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x16a;" k="20" />
    <hkern u1="&#x2c;" u2="&#x169;" k="20" />
    <hkern u1="&#x2c;" u2="&#x168;" k="20" />
    <hkern u1="&#x2c;" u2="&#x167;" k="20" />
    <hkern u1="&#x2c;" u2="&#x165;" k="20" />
    <hkern u1="&#x2c;" u2="&#x164;" k="110" />
    <hkern u1="&#x2c;" u2="&#x163;" k="20" />
    <hkern u1="&#x2c;" u2="&#x162;" k="110" />
    <hkern u1="&#x2c;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2c;" u2="&#x153;" k="20" />
    <hkern u1="&#x2c;" u2="&#x152;" k="25" />
    <hkern u1="&#x2c;" u2="&#x151;" k="20" />
    <hkern u1="&#x2c;" u2="&#x150;" k="25" />
    <hkern u1="&#x2c;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x14e;" k="25" />
    <hkern u1="&#x2c;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x14c;" k="25" />
    <hkern u1="&#x2c;" u2="&#x134;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x123;" k="20" />
    <hkern u1="&#x2c;" u2="&#x122;" k="25" />
    <hkern u1="&#x2c;" u2="&#x121;" k="20" />
    <hkern u1="&#x2c;" u2="&#x120;" k="25" />
    <hkern u1="&#x2c;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11e;" k="25" />
    <hkern u1="&#x2c;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11c;" k="25" />
    <hkern u1="&#x2c;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2c;" u2="&#x119;" k="20" />
    <hkern u1="&#x2c;" u2="&#x118;" k="20" />
    <hkern u1="&#x2c;" u2="&#x117;" k="20" />
    <hkern u1="&#x2c;" u2="&#x116;" k="20" />
    <hkern u1="&#x2c;" u2="&#x115;" k="20" />
    <hkern u1="&#x2c;" u2="&#x114;" k="20" />
    <hkern u1="&#x2c;" u2="&#x113;" k="20" />
    <hkern u1="&#x2c;" u2="&#x112;" k="20" />
    <hkern u1="&#x2c;" u2="&#x111;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10c;" k="25" />
    <hkern u1="&#x2c;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2c;" u2="&#x10a;" k="25" />
    <hkern u1="&#x2c;" u2="&#x109;" k="20" />
    <hkern u1="&#x2c;" u2="&#x108;" k="25" />
    <hkern u1="&#x2c;" u2="&#x107;" k="20" />
    <hkern u1="&#x2c;" u2="&#x106;" k="25" />
    <hkern u1="&#x2c;" u2="&#x104;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x102;" k="-10" />
    <hkern u1="&#x2c;" u2="&#x100;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xff;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2c;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2c;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xea;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="135" />
    <hkern u1="&#x2c;" u2="&#xdc;" k="20" />
    <hkern u1="&#x2c;" u2="&#xdb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xda;" k="20" />
    <hkern u1="&#x2c;" u2="&#xd9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="25" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="25" />
    <hkern u1="&#x2c;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2c;" u2="&#xca;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="25" />
    <hkern u1="&#x2c;" u2="&#xc6;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc5;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc4;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc3;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc2;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc1;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xc0;" k="-10" />
    <hkern u1="&#x2c;" u2="&#xab;" k="50" />
    <hkern u1="&#x2c;" u2="&#xa9;" k="25" />
    <hkern u1="&#x2c;" u2="y" k="20" />
    <hkern u1="&#x2c;" u2="x" k="-20" />
    <hkern u1="&#x2c;" u2="v" k="65" />
    <hkern u1="&#x2c;" u2="q" k="20" />
    <hkern u1="&#x2c;" u2="o" k="20" />
    <hkern u1="&#x2c;" u2="g" k="20" />
    <hkern u1="&#x2c;" u2="e" k="20" />
    <hkern u1="&#x2c;" u2="d" k="20" />
    <hkern u1="&#x2c;" u2="X" k="-40" />
    <hkern u1="&#x2c;" u2="V" k="110" />
    <hkern u1="&#x2c;" u2="Q" k="25" />
    <hkern u1="&#x2c;" u2="O" k="25" />
    <hkern u1="&#x2c;" u2="G" k="25" />
    <hkern u1="&#x2d;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2d;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2d;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2d;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2d;" u2="&#x218;" k="65" />
    <hkern u1="&#x2d;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x17d;" k="65" />
    <hkern u1="&#x2d;" u2="&#x17b;" k="65" />
    <hkern u1="&#x2d;" u2="&#x179;" k="65" />
    <hkern u1="&#x2d;" u2="&#x178;" k="10" />
    <hkern u1="&#x2d;" u2="&#x176;" k="10" />
    <hkern u1="&#x2d;" u2="&#x175;" k="10" />
    <hkern u1="&#x2d;" u2="&#x174;" k="20" />
    <hkern u1="&#x2d;" u2="&#x164;" k="110" />
    <hkern u1="&#x2d;" u2="&#x162;" k="110" />
    <hkern u1="&#x2d;" u2="&#x160;" k="65" />
    <hkern u1="&#x2d;" u2="&#x15e;" k="65" />
    <hkern u1="&#x2d;" u2="&#x15c;" k="65" />
    <hkern u1="&#x2d;" u2="&#x15a;" k="65" />
    <hkern u1="&#x2d;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2d;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2d;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2d;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2d;" u2="z" k="35" />
    <hkern u1="&#x2d;" u2="x" k="20" />
    <hkern u1="&#x2d;" u2="X" k="65" />
    <hkern u1="&#x2d;" u2="V" k="35" />
    <hkern u1="&#x2d;" u2="Q" k="-15" />
    <hkern u1="&#x2d;" u2="O" k="-15" />
    <hkern u1="&#x2d;" u2="G" k="-15" />
    <hkern u1="&#x2e;" g2="fl" k="10" />
    <hkern u1="&#x2e;" g2="fi" k="10" />
    <hkern u1="&#x2e;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2e;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="60" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2e;" u2="&#x1ef2;" k="135" />
    <hkern u1="&#x2e;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2e;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2e;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2e;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2e;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2e;" u2="&#x1fe;" k="25" />
    <hkern u1="&#x2e;" u2="&#x1fc;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x1fa;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2e;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2e;" u2="&#x178;" k="135" />
    <hkern u1="&#x2e;" u2="&#x177;" k="20" />
    <hkern u1="&#x2e;" u2="&#x176;" k="135" />
    <hkern u1="&#x2e;" u2="&#x175;" k="65" />
    <hkern u1="&#x2e;" u2="&#x174;" k="55" />
    <hkern u1="&#x2e;" u2="&#x173;" k="20" />
    <hkern u1="&#x2e;" u2="&#x172;" k="20" />
    <hkern u1="&#x2e;" u2="&#x171;" k="20" />
    <hkern u1="&#x2e;" u2="&#x170;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16e;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16c;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x16a;" k="20" />
    <hkern u1="&#x2e;" u2="&#x169;" k="20" />
    <hkern u1="&#x2e;" u2="&#x168;" k="20" />
    <hkern u1="&#x2e;" u2="&#x167;" k="20" />
    <hkern u1="&#x2e;" u2="&#x165;" k="20" />
    <hkern u1="&#x2e;" u2="&#x164;" k="110" />
    <hkern u1="&#x2e;" u2="&#x163;" k="20" />
    <hkern u1="&#x2e;" u2="&#x162;" k="110" />
    <hkern u1="&#x2e;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2e;" u2="&#x153;" k="20" />
    <hkern u1="&#x2e;" u2="&#x152;" k="25" />
    <hkern u1="&#x2e;" u2="&#x151;" k="20" />
    <hkern u1="&#x2e;" u2="&#x150;" k="25" />
    <hkern u1="&#x2e;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x14e;" k="25" />
    <hkern u1="&#x2e;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x14c;" k="25" />
    <hkern u1="&#x2e;" u2="&#x134;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x123;" k="20" />
    <hkern u1="&#x2e;" u2="&#x122;" k="25" />
    <hkern u1="&#x2e;" u2="&#x121;" k="20" />
    <hkern u1="&#x2e;" u2="&#x120;" k="25" />
    <hkern u1="&#x2e;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11e;" k="25" />
    <hkern u1="&#x2e;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11c;" k="25" />
    <hkern u1="&#x2e;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2e;" u2="&#x119;" k="20" />
    <hkern u1="&#x2e;" u2="&#x118;" k="20" />
    <hkern u1="&#x2e;" u2="&#x117;" k="20" />
    <hkern u1="&#x2e;" u2="&#x116;" k="20" />
    <hkern u1="&#x2e;" u2="&#x115;" k="20" />
    <hkern u1="&#x2e;" u2="&#x114;" k="20" />
    <hkern u1="&#x2e;" u2="&#x113;" k="20" />
    <hkern u1="&#x2e;" u2="&#x112;" k="20" />
    <hkern u1="&#x2e;" u2="&#x111;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10c;" k="25" />
    <hkern u1="&#x2e;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2e;" u2="&#x10a;" k="25" />
    <hkern u1="&#x2e;" u2="&#x109;" k="20" />
    <hkern u1="&#x2e;" u2="&#x108;" k="25" />
    <hkern u1="&#x2e;" u2="&#x107;" k="20" />
    <hkern u1="&#x2e;" u2="&#x106;" k="25" />
    <hkern u1="&#x2e;" u2="&#x104;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x102;" k="-10" />
    <hkern u1="&#x2e;" u2="&#x100;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xff;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2e;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2e;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xea;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="135" />
    <hkern u1="&#x2e;" u2="&#xdc;" k="20" />
    <hkern u1="&#x2e;" u2="&#xdb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xda;" k="20" />
    <hkern u1="&#x2e;" u2="&#xd9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="25" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="25" />
    <hkern u1="&#x2e;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2e;" u2="&#xca;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="25" />
    <hkern u1="&#x2e;" u2="&#xc6;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc5;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc4;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc3;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc2;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc1;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xc0;" k="-10" />
    <hkern u1="&#x2e;" u2="&#xab;" k="50" />
    <hkern u1="&#x2e;" u2="&#xa9;" k="25" />
    <hkern u1="&#x2e;" u2="y" k="20" />
    <hkern u1="&#x2e;" u2="x" k="-20" />
    <hkern u1="&#x2e;" u2="w" k="65" />
    <hkern u1="&#x2e;" u2="v" k="65" />
    <hkern u1="&#x2e;" u2="u" k="20" />
    <hkern u1="&#x2e;" u2="t" k="20" />
    <hkern u1="&#x2e;" u2="q" k="20" />
    <hkern u1="&#x2e;" u2="o" k="20" />
    <hkern u1="&#x2e;" u2="g" k="20" />
    <hkern u1="&#x2e;" u2="f" k="10" />
    <hkern u1="&#x2e;" u2="e" k="20" />
    <hkern u1="&#x2e;" u2="d" k="20" />
    <hkern u1="&#x2e;" u2="c" k="20" />
    <hkern u1="&#x2e;" u2="Z" k="-30" />
    <hkern u1="&#x2e;" u2="Y" k="135" />
    <hkern u1="&#x2e;" u2="X" k="-40" />
    <hkern u1="&#x2e;" u2="W" k="55" />
    <hkern u1="&#x2e;" u2="V" k="110" />
    <hkern u1="&#x2e;" u2="U" k="20" />
    <hkern u1="&#x2e;" u2="T" k="110" />
    <hkern u1="&#x2e;" u2="S" k="-20" />
    <hkern u1="&#x2e;" u2="Q" k="25" />
    <hkern u1="&#x2e;" u2="O" k="25" />
    <hkern u1="&#x2e;" u2="J" k="-10" />
    <hkern u1="&#x2e;" u2="G" k="25" />
    <hkern u1="&#x2e;" u2="E" k="20" />
    <hkern u1="&#x2e;" u2="C" k="25" />
    <hkern u1="&#x2e;" u2="A" k="-10" />
    <hkern u1="&#x2f;" u2="&#x1ff;" k="65" />
    <hkern u1="&#x2f;" u2="&#x153;" k="65" />
    <hkern u1="&#x2f;" u2="&#x151;" k="65" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x123;" k="65" />
    <hkern u1="&#x2f;" u2="&#x121;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="65" />
    <hkern u1="&#x2f;" u2="&#x119;" k="65" />
    <hkern u1="&#x2f;" u2="&#x117;" k="65" />
    <hkern u1="&#x2f;" u2="&#x115;" k="65" />
    <hkern u1="&#x2f;" u2="&#x113;" k="65" />
    <hkern u1="&#x2f;" u2="&#x111;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="65" />
    <hkern u1="&#x2f;" u2="&#x109;" k="65" />
    <hkern u1="&#x2f;" u2="&#x107;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="65" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="65" />
    <hkern u1="&#x2f;" u2="&#xea;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="65" />
    <hkern u1="&#x2f;" u2="q" k="65" />
    <hkern u1="&#x2f;" u2="o" k="65" />
    <hkern u1="&#x2f;" u2="g" k="65" />
    <hkern u1="&#x2f;" u2="e" k="65" />
    <hkern u1="&#x2f;" u2="d" k="65" />
    <hkern u1="&#x2f;" u2="c" k="65" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="180" />
    <hkern u1="&#x40;" u2="&#x2026;" k="35" />
    <hkern u1="&#x40;" u2="&#x2e;" k="35" />
    <hkern u1="&#x40;" u2="&#x2c;" k="35" />
    <hkern u1="A" u2="&#x2026;" k="-10" />
    <hkern u1="A" u2="&#x164;" k="25" />
    <hkern u1="A" u2="&#x162;" k="25" />
    <hkern u1="A" u2="V" k="10" />
    <hkern u1="A" u2="&#x2e;" k="-10" />
    <hkern u1="B" u2="&#x2026;" k="-10" />
    <hkern u1="B" u2="X" k="6" />
    <hkern u1="B" u2="&#x2e;" k="-10" />
    <hkern u1="B" u2="&#x2c;" k="-10" />
    <hkern u1="C" u2="&#x2026;" k="-20" />
    <hkern u1="C" u2="&#x2014;" k="75" />
    <hkern u1="C" u2="&#x2013;" k="75" />
    <hkern u1="C" u2="&#x1e85;" k="10" />
    <hkern u1="C" u2="&#x1e83;" k="10" />
    <hkern u1="C" u2="&#x1e81;" k="10" />
    <hkern u1="C" u2="&#x1ff;" k="10" />
    <hkern u1="C" u2="&#x1fe;" k="20" />
    <hkern u1="C" u2="&#x175;" k="10" />
    <hkern u1="C" u2="&#x153;" k="10" />
    <hkern u1="C" u2="&#x152;" k="20" />
    <hkern u1="C" u2="&#x151;" k="10" />
    <hkern u1="C" u2="&#x150;" k="20" />
    <hkern u1="C" u2="&#x14f;" k="10" />
    <hkern u1="C" u2="&#x14e;" k="20" />
    <hkern u1="C" u2="&#x14d;" k="10" />
    <hkern u1="C" u2="&#x14c;" k="20" />
    <hkern u1="C" u2="&#x123;" k="10" />
    <hkern u1="C" u2="&#x122;" k="20" />
    <hkern u1="C" u2="&#x121;" k="10" />
    <hkern u1="C" u2="&#x120;" k="20" />
    <hkern u1="C" u2="&#x11f;" k="10" />
    <hkern u1="C" u2="&#x11e;" k="20" />
    <hkern u1="C" u2="&#x11d;" k="10" />
    <hkern u1="C" u2="&#x11c;" k="20" />
    <hkern u1="C" u2="&#x11b;" k="10" />
    <hkern u1="C" u2="&#x119;" k="10" />
    <hkern u1="C" u2="&#x117;" k="10" />
    <hkern u1="C" u2="&#x115;" k="10" />
    <hkern u1="C" u2="&#x113;" k="10" />
    <hkern u1="C" u2="&#x111;" k="10" />
    <hkern u1="C" u2="&#x10f;" k="10" />
    <hkern u1="C" u2="&#x10d;" k="10" />
    <hkern u1="C" u2="&#x10c;" k="20" />
    <hkern u1="C" u2="&#x10b;" k="10" />
    <hkern u1="C" u2="&#x10a;" k="20" />
    <hkern u1="C" u2="&#x109;" k="10" />
    <hkern u1="C" u2="&#x108;" k="20" />
    <hkern u1="C" u2="&#x107;" k="10" />
    <hkern u1="C" u2="&#x106;" k="20" />
    <hkern u1="C" u2="&#xf8;" k="10" />
    <hkern u1="C" u2="&#xf6;" k="10" />
    <hkern u1="C" u2="&#xf5;" k="10" />
    <hkern u1="C" u2="&#xf4;" k="10" />
    <hkern u1="C" u2="&#xf3;" k="10" />
    <hkern u1="C" u2="&#xf2;" k="10" />
    <hkern u1="C" u2="&#xf0;" k="10" />
    <hkern u1="C" u2="&#xef;" k="-10" />
    <hkern u1="C" u2="&#xeb;" k="10" />
    <hkern u1="C" u2="&#xea;" k="10" />
    <hkern u1="C" u2="&#xe9;" k="10" />
    <hkern u1="C" u2="&#xe8;" k="10" />
    <hkern u1="C" u2="&#xe7;" k="10" />
    <hkern u1="C" u2="&#xd8;" k="20" />
    <hkern u1="C" u2="&#xd6;" k="20" />
    <hkern u1="C" u2="&#xd5;" k="20" />
    <hkern u1="C" u2="&#xd4;" k="20" />
    <hkern u1="C" u2="&#xd3;" k="20" />
    <hkern u1="C" u2="&#xd2;" k="20" />
    <hkern u1="C" u2="&#xc7;" k="20" />
    <hkern u1="C" u2="&#xa9;" k="20" />
    <hkern u1="C" u2="z" k="-10" />
    <hkern u1="C" u2="x" k="-10" />
    <hkern u1="C" u2="v" k="20" />
    <hkern u1="C" u2="q" k="10" />
    <hkern u1="C" u2="o" k="10" />
    <hkern u1="C" u2="g" k="10" />
    <hkern u1="C" u2="e" k="10" />
    <hkern u1="C" u2="d" k="10" />
    <hkern u1="C" u2="_" k="-20" />
    <hkern u1="C" u2="Q" k="20" />
    <hkern u1="C" u2="O" k="20" />
    <hkern u1="C" u2="G" k="20" />
    <hkern u1="C" u2="&#x2e;" k="-20" />
    <hkern u1="D" u2="&#x2026;" k="25" />
    <hkern u1="D" u2="&#x2014;" k="-15" />
    <hkern u1="D" u2="&#x2013;" k="-15" />
    <hkern u1="D" u2="&#x1fe;" k="-5" />
    <hkern u1="D" u2="&#x164;" k="15" />
    <hkern u1="D" u2="&#x162;" k="15" />
    <hkern u1="D" u2="&#x152;" k="-5" />
    <hkern u1="D" u2="&#x150;" k="-5" />
    <hkern u1="D" u2="&#x14e;" k="-5" />
    <hkern u1="D" u2="&#x14c;" k="-5" />
    <hkern u1="D" u2="&#x134;" k="-20" />
    <hkern u1="D" u2="&#x122;" k="-5" />
    <hkern u1="D" u2="&#x120;" k="-5" />
    <hkern u1="D" u2="&#x11e;" k="-5" />
    <hkern u1="D" u2="&#x11c;" k="-5" />
    <hkern u1="D" u2="&#x10c;" k="-5" />
    <hkern u1="D" u2="&#x10a;" k="-5" />
    <hkern u1="D" u2="&#x108;" k="-5" />
    <hkern u1="D" u2="&#x106;" k="-5" />
    <hkern u1="D" u2="&#xd8;" k="-5" />
    <hkern u1="D" u2="&#xd6;" k="-5" />
    <hkern u1="D" u2="&#xd5;" k="-5" />
    <hkern u1="D" u2="&#xd4;" k="-5" />
    <hkern u1="D" u2="&#xd3;" k="-5" />
    <hkern u1="D" u2="&#xd2;" k="-5" />
    <hkern u1="D" u2="&#xc7;" k="-5" />
    <hkern u1="D" u2="&#xa9;" k="-5" />
    <hkern u1="D" u2="X" k="10" />
    <hkern u1="D" u2="V" k="10" />
    <hkern u1="D" u2="Q" k="-5" />
    <hkern u1="D" u2="O" k="-5" />
    <hkern u1="D" u2="G" k="-5" />
    <hkern u1="D" u2="&#x2e;" k="25" />
    <hkern u1="E" u2="&#x2026;" k="-20" />
    <hkern u1="E" u2="&#x1fe;" k="7" />
    <hkern u1="E" u2="&#x152;" k="7" />
    <hkern u1="E" u2="&#x150;" k="7" />
    <hkern u1="E" u2="&#x14e;" k="7" />
    <hkern u1="E" u2="&#x14c;" k="7" />
    <hkern u1="E" u2="&#x122;" k="7" />
    <hkern u1="E" u2="&#x120;" k="7" />
    <hkern u1="E" u2="&#x11e;" k="7" />
    <hkern u1="E" u2="&#x11c;" k="7" />
    <hkern u1="E" u2="&#x10c;" k="7" />
    <hkern u1="E" u2="&#x10a;" k="7" />
    <hkern u1="E" u2="&#x108;" k="7" />
    <hkern u1="E" u2="&#x106;" k="7" />
    <hkern u1="E" u2="&#xd8;" k="7" />
    <hkern u1="E" u2="&#xd6;" k="7" />
    <hkern u1="E" u2="&#xd5;" k="7" />
    <hkern u1="E" u2="&#xd4;" k="7" />
    <hkern u1="E" u2="&#xd3;" k="7" />
    <hkern u1="E" u2="&#xd2;" k="7" />
    <hkern u1="E" u2="&#xc7;" k="7" />
    <hkern u1="E" u2="&#xa9;" k="7" />
    <hkern u1="E" u2="Q" k="7" />
    <hkern u1="E" u2="O" k="7" />
    <hkern u1="E" u2="G" k="7" />
    <hkern u1="E" u2="&#x2e;" k="-20" />
    <hkern u1="F" u2="&#x2026;" k="100" />
    <hkern u1="F" u2="&#x1ff;" k="15" />
    <hkern u1="F" u2="&#x1fd;" k="30" />
    <hkern u1="F" u2="&#x1fc;" k="15" />
    <hkern u1="F" u2="&#x1fb;" k="30" />
    <hkern u1="F" u2="&#x1fa;" k="15" />
    <hkern u1="F" u2="&#x159;" k="10" />
    <hkern u1="F" u2="&#x157;" k="10" />
    <hkern u1="F" u2="&#x155;" k="10" />
    <hkern u1="F" u2="&#x153;" k="15" />
    <hkern u1="F" u2="&#x151;" k="15" />
    <hkern u1="F" u2="&#x14f;" k="15" />
    <hkern u1="F" u2="&#x14d;" k="15" />
    <hkern u1="F" u2="&#x14b;" k="10" />
    <hkern u1="F" u2="&#x148;" k="10" />
    <hkern u1="F" u2="&#x146;" k="10" />
    <hkern u1="F" u2="&#x144;" k="10" />
    <hkern u1="F" u2="&#x123;" k="15" />
    <hkern u1="F" u2="&#x121;" k="15" />
    <hkern u1="F" u2="&#x11f;" k="15" />
    <hkern u1="F" u2="&#x11d;" k="15" />
    <hkern u1="F" u2="&#x11b;" k="15" />
    <hkern u1="F" u2="&#x119;" k="15" />
    <hkern u1="F" u2="&#x117;" k="15" />
    <hkern u1="F" u2="&#x115;" k="15" />
    <hkern u1="F" u2="&#x113;" k="15" />
    <hkern u1="F" u2="&#x111;" k="15" />
    <hkern u1="F" u2="&#x10f;" k="15" />
    <hkern u1="F" u2="&#x10d;" k="15" />
    <hkern u1="F" u2="&#x10b;" k="15" />
    <hkern u1="F" u2="&#x109;" k="15" />
    <hkern u1="F" u2="&#x107;" k="15" />
    <hkern u1="F" u2="&#x105;" k="30" />
    <hkern u1="F" u2="&#x104;" k="15" />
    <hkern u1="F" u2="&#x103;" k="30" />
    <hkern u1="F" u2="&#x102;" k="15" />
    <hkern u1="F" u2="&#x101;" k="30" />
    <hkern u1="F" u2="&#x100;" k="15" />
    <hkern u1="F" u2="&#xf8;" k="15" />
    <hkern u1="F" u2="&#xf6;" k="15" />
    <hkern u1="F" u2="&#xf5;" k="15" />
    <hkern u1="F" u2="&#xf4;" k="15" />
    <hkern u1="F" u2="&#xf3;" k="15" />
    <hkern u1="F" u2="&#xf2;" k="15" />
    <hkern u1="F" u2="&#xf1;" k="10" />
    <hkern u1="F" u2="&#xf0;" k="15" />
    <hkern u1="F" u2="&#xef;" k="-20" />
    <hkern u1="F" u2="&#xee;" k="-10" />
    <hkern u1="F" u2="&#xeb;" k="15" />
    <hkern u1="F" u2="&#xea;" k="15" />
    <hkern u1="F" u2="&#xe9;" k="15" />
    <hkern u1="F" u2="&#xe8;" k="15" />
    <hkern u1="F" u2="&#xe7;" k="15" />
    <hkern u1="F" u2="&#xe6;" k="30" />
    <hkern u1="F" u2="&#xe5;" k="30" />
    <hkern u1="F" u2="&#xe4;" k="30" />
    <hkern u1="F" u2="&#xe3;" k="30" />
    <hkern u1="F" u2="&#xe2;" k="30" />
    <hkern u1="F" u2="&#xe1;" k="30" />
    <hkern u1="F" u2="&#xe0;" k="30" />
    <hkern u1="F" u2="&#xc6;" k="15" />
    <hkern u1="F" u2="&#xc5;" k="15" />
    <hkern u1="F" u2="&#xc4;" k="15" />
    <hkern u1="F" u2="&#xc3;" k="15" />
    <hkern u1="F" u2="&#xc2;" k="15" />
    <hkern u1="F" u2="&#xc1;" k="15" />
    <hkern u1="F" u2="&#xc0;" k="15" />
    <hkern u1="F" u2="r" k="10" />
    <hkern u1="F" u2="q" k="15" />
    <hkern u1="F" u2="p" k="10" />
    <hkern u1="F" u2="o" k="15" />
    <hkern u1="F" u2="n" k="10" />
    <hkern u1="F" u2="m" k="10" />
    <hkern u1="F" u2="g" k="15" />
    <hkern u1="F" u2="e" k="15" />
    <hkern u1="F" u2="d" k="15" />
    <hkern u1="F" u2="c" k="15" />
    <hkern u1="F" u2="a" k="30" />
    <hkern u1="F" u2="_" k="85" />
    <hkern u1="F" u2="A" k="15" />
    <hkern u1="F" u2="&#x2f;" k="75" />
    <hkern u1="F" u2="&#x2e;" k="100" />
    <hkern u1="F" u2="&#x2c;" k="100" />
    <hkern u1="G" u2="&#x2026;" k="-20" />
    <hkern u1="G" u2="&#x164;" k="20" />
    <hkern u1="G" u2="&#x162;" k="20" />
    <hkern u1="G" u2="T" k="20" />
    <hkern u1="G" u2="&#x2e;" k="-20" />
    <hkern u1="G" u2="&#x2c;" k="-20" />
    <hkern u1="K" u2="&#x2026;" k="-50" />
    <hkern u1="K" u2="&#x2014;" k="10" />
    <hkern u1="K" u2="&#x2013;" k="10" />
    <hkern u1="K" u2="&#x1fe;" k="20" />
    <hkern u1="K" u2="&#x152;" k="20" />
    <hkern u1="K" u2="&#x150;" k="20" />
    <hkern u1="K" u2="&#x14e;" k="20" />
    <hkern u1="K" u2="&#x14c;" k="20" />
    <hkern u1="K" u2="&#x122;" k="20" />
    <hkern u1="K" u2="&#x120;" k="20" />
    <hkern u1="K" u2="&#x11e;" k="20" />
    <hkern u1="K" u2="&#x11c;" k="20" />
    <hkern u1="K" u2="&#x10c;" k="20" />
    <hkern u1="K" u2="&#x10a;" k="20" />
    <hkern u1="K" u2="&#x108;" k="20" />
    <hkern u1="K" u2="&#x106;" k="20" />
    <hkern u1="K" u2="&#xef;" k="-20" />
    <hkern u1="K" u2="&#xd8;" k="20" />
    <hkern u1="K" u2="&#xd6;" k="20" />
    <hkern u1="K" u2="&#xd5;" k="20" />
    <hkern u1="K" u2="&#xd4;" k="20" />
    <hkern u1="K" u2="&#xd3;" k="20" />
    <hkern u1="K" u2="&#xd2;" k="20" />
    <hkern u1="K" u2="&#xc7;" k="20" />
    <hkern u1="K" u2="&#xa9;" k="20" />
    <hkern u1="K" u2="_" k="-50" />
    <hkern u1="K" u2="Q" k="20" />
    <hkern u1="K" u2="O" k="20" />
    <hkern u1="K" u2="G" k="20" />
    <hkern u1="K" u2="&#x2e;" k="-50" />
    <hkern u1="L" u2="&#x2122;" k="65" />
    <hkern u1="L" u2="&#x2026;" k="-40" />
    <hkern u1="L" u2="&#x201d;" k="90" />
    <hkern u1="L" u2="&#x201c;" k="90" />
    <hkern u1="L" u2="&#x2014;" k="90" />
    <hkern u1="L" u2="&#x2013;" k="90" />
    <hkern u1="L" u2="&#x1ef2;" k="105" />
    <hkern u1="L" u2="&#x1e85;" k="30" />
    <hkern u1="L" u2="&#x1e84;" k="55" />
    <hkern u1="L" u2="&#x1e83;" k="30" />
    <hkern u1="L" u2="&#x1e82;" k="55" />
    <hkern u1="L" u2="&#x1e81;" k="30" />
    <hkern u1="L" u2="&#x1e80;" k="55" />
    <hkern u1="L" u2="&#x1fe;" k="20" />
    <hkern u1="L" u2="&#x1fd;" k="-30" />
    <hkern u1="L" u2="&#x1fb;" k="-30" />
    <hkern u1="L" u2="&#x178;" k="105" />
    <hkern u1="L" u2="&#x176;" k="105" />
    <hkern u1="L" u2="&#x175;" k="30" />
    <hkern u1="L" u2="&#x174;" k="55" />
    <hkern u1="L" u2="&#x164;" k="75" />
    <hkern u1="L" u2="&#x162;" k="75" />
    <hkern u1="L" u2="&#x152;" k="20" />
    <hkern u1="L" u2="&#x150;" k="20" />
    <hkern u1="L" u2="&#x14e;" k="20" />
    <hkern u1="L" u2="&#x14c;" k="20" />
    <hkern u1="L" u2="&#x122;" k="20" />
    <hkern u1="L" u2="&#x120;" k="20" />
    <hkern u1="L" u2="&#x11e;" k="20" />
    <hkern u1="L" u2="&#x11c;" k="20" />
    <hkern u1="L" u2="&#x10c;" k="20" />
    <hkern u1="L" u2="&#x10a;" k="20" />
    <hkern u1="L" u2="&#x108;" k="20" />
    <hkern u1="L" u2="&#x106;" k="20" />
    <hkern u1="L" u2="&#x105;" k="-30" />
    <hkern u1="L" u2="&#x103;" k="-30" />
    <hkern u1="L" u2="&#x101;" k="-30" />
    <hkern u1="L" u2="&#xe6;" k="-30" />
    <hkern u1="L" u2="&#xe5;" k="-30" />
    <hkern u1="L" u2="&#xe4;" k="-30" />
    <hkern u1="L" u2="&#xe3;" k="-30" />
    <hkern u1="L" u2="&#xe2;" k="-30" />
    <hkern u1="L" u2="&#xe1;" k="-30" />
    <hkern u1="L" u2="&#xe0;" k="-30" />
    <hkern u1="L" u2="&#xdd;" k="105" />
    <hkern u1="L" u2="&#xd8;" k="20" />
    <hkern u1="L" u2="&#xd6;" k="20" />
    <hkern u1="L" u2="&#xd5;" k="20" />
    <hkern u1="L" u2="&#xd4;" k="20" />
    <hkern u1="L" u2="&#xd3;" k="20" />
    <hkern u1="L" u2="&#xd2;" k="20" />
    <hkern u1="L" u2="&#xc7;" k="20" />
    <hkern u1="L" u2="&#xa9;" k="20" />
    <hkern u1="L" u2="v" k="40" />
    <hkern u1="L" u2="_" k="-50" />
    <hkern u1="L" u2="V" k="85" />
    <hkern u1="L" u2="Q" k="20" />
    <hkern u1="L" u2="O" k="20" />
    <hkern u1="L" u2="G" k="20" />
    <hkern u1="L" u2="&#x2e;" k="-40" />
    <hkern u1="L" u2="&#x2a;" k="60" />
    <hkern u1="L" u2="&#x27;" k="100" />
    <hkern u1="L" u2="&#x22;" k="100" />
    <hkern u1="O" u2="&#x2026;" k="25" />
    <hkern u1="O" u2="&#x2014;" k="-15" />
    <hkern u1="O" u2="&#x2013;" k="-15" />
    <hkern u1="O" u2="&#x1fe;" k="-5" />
    <hkern u1="O" u2="&#x164;" k="15" />
    <hkern u1="O" u2="&#x162;" k="15" />
    <hkern u1="O" u2="&#x152;" k="-5" />
    <hkern u1="O" u2="&#x150;" k="-5" />
    <hkern u1="O" u2="&#x14e;" k="-5" />
    <hkern u1="O" u2="&#x14c;" k="-5" />
    <hkern u1="O" u2="&#x134;" k="-20" />
    <hkern u1="O" u2="&#x122;" k="-5" />
    <hkern u1="O" u2="&#x120;" k="-5" />
    <hkern u1="O" u2="&#x11e;" k="-5" />
    <hkern u1="O" u2="&#x11c;" k="-5" />
    <hkern u1="O" u2="&#x10c;" k="-5" />
    <hkern u1="O" u2="&#x10a;" k="-5" />
    <hkern u1="O" u2="&#x108;" k="-5" />
    <hkern u1="O" u2="&#x106;" k="-5" />
    <hkern u1="O" u2="&#xd8;" k="-5" />
    <hkern u1="O" u2="&#xd6;" k="-5" />
    <hkern u1="O" u2="&#xd5;" k="-5" />
    <hkern u1="O" u2="&#xd4;" k="-5" />
    <hkern u1="O" u2="&#xd3;" k="-5" />
    <hkern u1="O" u2="&#xd2;" k="-5" />
    <hkern u1="O" u2="&#xc7;" k="-5" />
    <hkern u1="O" u2="&#xa9;" k="-5" />
    <hkern u1="O" u2="X" k="10" />
    <hkern u1="O" u2="V" k="10" />
    <hkern u1="O" u2="T" k="15" />
    <hkern u1="O" u2="Q" k="-5" />
    <hkern u1="O" u2="O" k="-5" />
    <hkern u1="O" u2="J" k="-20" />
    <hkern u1="O" u2="G" k="-5" />
    <hkern u1="O" u2="C" k="-5" />
    <hkern u1="O" u2="&#x2e;" k="25" />
    <hkern u1="O" u2="&#x2d;" k="-15" />
    <hkern u1="O" u2="&#x2c;" k="25" />
    <hkern u1="P" u2="&#x2026;" k="140" />
    <hkern u1="P" u2="&#x2014;" k="10" />
    <hkern u1="P" u2="&#x2013;" k="10" />
    <hkern u1="P" u2="&#x1ff;" k="15" />
    <hkern u1="P" u2="&#x1fd;" k="10" />
    <hkern u1="P" u2="&#x1fc;" k="10" />
    <hkern u1="P" u2="&#x1fb;" k="10" />
    <hkern u1="P" u2="&#x1fa;" k="10" />
    <hkern u1="P" u2="&#x153;" k="15" />
    <hkern u1="P" u2="&#x151;" k="15" />
    <hkern u1="P" u2="&#x14f;" k="15" />
    <hkern u1="P" u2="&#x14d;" k="15" />
    <hkern u1="P" u2="&#x123;" k="15" />
    <hkern u1="P" u2="&#x121;" k="15" />
    <hkern u1="P" u2="&#x11f;" k="15" />
    <hkern u1="P" u2="&#x11d;" k="15" />
    <hkern u1="P" u2="&#x11b;" k="15" />
    <hkern u1="P" u2="&#x119;" k="15" />
    <hkern u1="P" u2="&#x117;" k="15" />
    <hkern u1="P" u2="&#x115;" k="15" />
    <hkern u1="P" u2="&#x113;" k="15" />
    <hkern u1="P" u2="&#x111;" k="15" />
    <hkern u1="P" u2="&#x10f;" k="15" />
    <hkern u1="P" u2="&#x10d;" k="15" />
    <hkern u1="P" u2="&#x10b;" k="15" />
    <hkern u1="P" u2="&#x109;" k="15" />
    <hkern u1="P" u2="&#x107;" k="15" />
    <hkern u1="P" u2="&#x105;" k="10" />
    <hkern u1="P" u2="&#x104;" k="10" />
    <hkern u1="P" u2="&#x103;" k="10" />
    <hkern u1="P" u2="&#x102;" k="10" />
    <hkern u1="P" u2="&#x101;" k="10" />
    <hkern u1="P" u2="&#x100;" k="10" />
    <hkern u1="P" u2="&#xf8;" k="15" />
    <hkern u1="P" u2="&#xf6;" k="15" />
    <hkern u1="P" u2="&#xf5;" k="15" />
    <hkern u1="P" u2="&#xf4;" k="15" />
    <hkern u1="P" u2="&#xf3;" k="15" />
    <hkern u1="P" u2="&#xf2;" k="15" />
    <hkern u1="P" u2="&#xf0;" k="15" />
    <hkern u1="P" u2="&#xeb;" k="15" />
    <hkern u1="P" u2="&#xea;" k="15" />
    <hkern u1="P" u2="&#xe9;" k="15" />
    <hkern u1="P" u2="&#xe8;" k="15" />
    <hkern u1="P" u2="&#xe7;" k="15" />
    <hkern u1="P" u2="&#xe6;" k="10" />
    <hkern u1="P" u2="&#xe5;" k="10" />
    <hkern u1="P" u2="&#xe4;" k="10" />
    <hkern u1="P" u2="&#xe3;" k="10" />
    <hkern u1="P" u2="&#xe2;" k="10" />
    <hkern u1="P" u2="&#xe1;" k="10" />
    <hkern u1="P" u2="&#xe0;" k="10" />
    <hkern u1="P" u2="&#xc6;" k="10" />
    <hkern u1="P" u2="&#xc5;" k="10" />
    <hkern u1="P" u2="&#xc4;" k="10" />
    <hkern u1="P" u2="&#xc3;" k="10" />
    <hkern u1="P" u2="&#xc2;" k="10" />
    <hkern u1="P" u2="&#xc1;" k="10" />
    <hkern u1="P" u2="&#xc0;" k="10" />
    <hkern u1="P" u2="q" k="15" />
    <hkern u1="P" u2="o" k="15" />
    <hkern u1="P" u2="g" k="15" />
    <hkern u1="P" u2="e" k="15" />
    <hkern u1="P" u2="d" k="15" />
    <hkern u1="P" u2="c" k="15" />
    <hkern u1="P" u2="a" k="10" />
    <hkern u1="P" u2="_" k="100" />
    <hkern u1="P" u2="A" k="10" />
    <hkern u1="P" u2="&#x2f;" k="45" />
    <hkern u1="P" u2="&#x2e;" k="140" />
    <hkern u1="P" u2="&#x2d;" k="10" />
    <hkern u1="P" u2="&#x2c;" k="140" />
    <hkern u1="Q" u2="&#x2026;" k="25" />
    <hkern u1="Q" u2="&#x2014;" k="-15" />
    <hkern u1="Q" u2="&#x2013;" k="-15" />
    <hkern u1="Q" u2="&#x1fe;" k="-5" />
    <hkern u1="Q" u2="&#x164;" k="15" />
    <hkern u1="Q" u2="&#x162;" k="15" />
    <hkern u1="Q" u2="&#x152;" k="-5" />
    <hkern u1="Q" u2="&#x150;" k="-5" />
    <hkern u1="Q" u2="&#x14e;" k="-5" />
    <hkern u1="Q" u2="&#x14c;" k="-5" />
    <hkern u1="Q" u2="&#x134;" k="-20" />
    <hkern u1="Q" u2="&#x122;" k="-5" />
    <hkern u1="Q" u2="&#x120;" k="-5" />
    <hkern u1="Q" u2="&#x11e;" k="-5" />
    <hkern u1="Q" u2="&#x11c;" k="-5" />
    <hkern u1="Q" u2="&#x10c;" k="-5" />
    <hkern u1="Q" u2="&#x10a;" k="-5" />
    <hkern u1="Q" u2="&#x108;" k="-5" />
    <hkern u1="Q" u2="&#x106;" k="-5" />
    <hkern u1="Q" u2="&#xd8;" k="-5" />
    <hkern u1="Q" u2="&#xd6;" k="-5" />
    <hkern u1="Q" u2="&#xd5;" k="-5" />
    <hkern u1="Q" u2="&#xd4;" k="-5" />
    <hkern u1="Q" u2="&#xd3;" k="-5" />
    <hkern u1="Q" u2="&#xd2;" k="-5" />
    <hkern u1="Q" u2="&#xc7;" k="-5" />
    <hkern u1="Q" u2="&#xa9;" k="-5" />
    <hkern u1="Q" u2="X" k="10" />
    <hkern u1="Q" u2="V" k="15" />
    <hkern u1="Q" u2="T" k="15" />
    <hkern u1="Q" u2="Q" k="-5" />
    <hkern u1="Q" u2="O" k="-5" />
    <hkern u1="Q" u2="J" k="-20" />
    <hkern u1="Q" u2="G" k="-5" />
    <hkern u1="Q" u2="C" k="-5" />
    <hkern u1="Q" u2="&#x2e;" k="25" />
    <hkern u1="Q" u2="&#x2d;" k="-15" />
    <hkern u1="Q" u2="&#x2c;" k="25" />
    <hkern u1="R" u2="&#x2026;" k="-20" />
    <hkern u1="R" u2="&#x164;" k="15" />
    <hkern u1="R" u2="&#x162;" k="15" />
    <hkern u1="R" u2="_" k="-30" />
    <hkern u1="R" u2="&#x2e;" k="-20" />
    <hkern u1="S" u2="&#x2026;" k="-10" />
    <hkern u1="S" u2="&#x1ef2;" k="25" />
    <hkern u1="S" u2="&#x218;" k="10" />
    <hkern u1="S" u2="&#x1fe;" k="5" />
    <hkern u1="S" u2="&#x178;" k="25" />
    <hkern u1="S" u2="&#x176;" k="25" />
    <hkern u1="S" u2="&#x164;" k="15" />
    <hkern u1="S" u2="&#x162;" k="15" />
    <hkern u1="S" u2="&#x160;" k="10" />
    <hkern u1="S" u2="&#x15e;" k="10" />
    <hkern u1="S" u2="&#x15c;" k="10" />
    <hkern u1="S" u2="&#x15a;" k="10" />
    <hkern u1="S" u2="&#x152;" k="5" />
    <hkern u1="S" u2="&#x150;" k="5" />
    <hkern u1="S" u2="&#x14e;" k="5" />
    <hkern u1="S" u2="&#x14c;" k="5" />
    <hkern u1="S" u2="&#x122;" k="5" />
    <hkern u1="S" u2="&#x120;" k="5" />
    <hkern u1="S" u2="&#x11e;" k="5" />
    <hkern u1="S" u2="&#x11c;" k="5" />
    <hkern u1="S" u2="&#x10c;" k="5" />
    <hkern u1="S" u2="&#x10a;" k="5" />
    <hkern u1="S" u2="&#x108;" k="5" />
    <hkern u1="S" u2="&#x106;" k="5" />
    <hkern u1="S" u2="&#xdd;" k="25" />
    <hkern u1="S" u2="&#xd8;" k="5" />
    <hkern u1="S" u2="&#xd6;" k="5" />
    <hkern u1="S" u2="&#xd5;" k="5" />
    <hkern u1="S" u2="&#xd4;" k="5" />
    <hkern u1="S" u2="&#xd3;" k="5" />
    <hkern u1="S" u2="&#xd2;" k="5" />
    <hkern u1="S" u2="&#xc7;" k="5" />
    <hkern u1="S" u2="&#xa9;" k="5" />
    <hkern u1="S" u2="Q" k="5" />
    <hkern u1="S" u2="O" k="5" />
    <hkern u1="S" u2="G" k="5" />
    <hkern u1="S" u2="&#x2e;" k="-10" />
    <hkern u1="T" u2="&#x2026;" k="110" />
    <hkern u1="T" u2="&#x2014;" k="110" />
    <hkern u1="T" u2="&#x2013;" k="110" />
    <hkern u1="T" u2="&#x1ef3;" k="75" />
    <hkern u1="T" u2="&#x1e85;" k="55" />
    <hkern u1="T" u2="&#x1e83;" k="55" />
    <hkern u1="T" u2="&#x1e81;" k="55" />
    <hkern u1="T" u2="&#x219;" k="80" />
    <hkern u1="T" u2="&#x1ff;" k="75" />
    <hkern u1="T" u2="&#x1fe;" k="15" />
    <hkern u1="T" u2="&#x1fd;" k="75" />
    <hkern u1="T" u2="&#x1fc;" k="25" />
    <hkern u1="T" u2="&#x1fb;" k="75" />
    <hkern u1="T" u2="&#x1fa;" k="25" />
    <hkern u1="T" u2="&#x177;" k="75" />
    <hkern u1="T" u2="&#x175;" k="55" />
    <hkern u1="T" u2="&#x173;" k="75" />
    <hkern u1="T" u2="&#x171;" k="75" />
    <hkern u1="T" u2="&#x16f;" k="75" />
    <hkern u1="T" u2="&#x16d;" k="75" />
    <hkern u1="T" u2="&#x16b;" k="75" />
    <hkern u1="T" u2="&#x169;" k="75" />
    <hkern u1="T" u2="&#x161;" k="80" />
    <hkern u1="T" u2="&#x15f;" k="80" />
    <hkern u1="T" u2="&#x15d;" k="80" />
    <hkern u1="T" u2="&#x15b;" k="80" />
    <hkern u1="T" u2="&#x159;" k="75" />
    <hkern u1="T" u2="&#x157;" k="75" />
    <hkern u1="T" u2="&#x155;" k="75" />
    <hkern u1="T" u2="&#x153;" k="75" />
    <hkern u1="T" u2="&#x152;" k="15" />
    <hkern u1="T" u2="&#x151;" k="75" />
    <hkern u1="T" u2="&#x150;" k="15" />
    <hkern u1="T" u2="&#x14f;" k="75" />
    <hkern u1="T" u2="&#x14e;" k="15" />
    <hkern u1="T" u2="&#x14d;" k="75" />
    <hkern u1="T" u2="&#x14c;" k="15" />
    <hkern u1="T" u2="&#x14b;" k="75" />
    <hkern u1="T" u2="&#x148;" k="75" />
    <hkern u1="T" u2="&#x146;" k="75" />
    <hkern u1="T" u2="&#x144;" k="75" />
    <hkern u1="T" u2="&#x123;" k="75" />
    <hkern u1="T" u2="&#x122;" k="15" />
    <hkern u1="T" u2="&#x121;" k="75" />
    <hkern u1="T" u2="&#x120;" k="15" />
    <hkern u1="T" u2="&#x11f;" k="75" />
    <hkern u1="T" u2="&#x11e;" k="15" />
    <hkern u1="T" u2="&#x11d;" k="75" />
    <hkern u1="T" u2="&#x11c;" k="15" />
    <hkern u1="T" u2="&#x11b;" k="75" />
    <hkern u1="T" u2="&#x119;" k="75" />
    <hkern u1="T" u2="&#x117;" k="75" />
    <hkern u1="T" u2="&#x115;" k="75" />
    <hkern u1="T" u2="&#x113;" k="75" />
    <hkern u1="T" u2="&#x111;" k="75" />
    <hkern u1="T" u2="&#x10f;" k="75" />
    <hkern u1="T" u2="&#x10d;" k="75" />
    <hkern u1="T" u2="&#x10c;" k="15" />
    <hkern u1="T" u2="&#x10b;" k="75" />
    <hkern u1="T" u2="&#x10a;" k="15" />
    <hkern u1="T" u2="&#x109;" k="75" />
    <hkern u1="T" u2="&#x108;" k="15" />
    <hkern u1="T" u2="&#x107;" k="75" />
    <hkern u1="T" u2="&#x106;" k="15" />
    <hkern u1="T" u2="&#x105;" k="75" />
    <hkern u1="T" u2="&#x104;" k="25" />
    <hkern u1="T" u2="&#x103;" k="75" />
    <hkern u1="T" u2="&#x102;" k="25" />
    <hkern u1="T" u2="&#x101;" k="75" />
    <hkern u1="T" u2="&#x100;" k="25" />
    <hkern u1="T" u2="&#xff;" k="75" />
    <hkern u1="T" u2="&#xfd;" k="75" />
    <hkern u1="T" u2="&#xfc;" k="75" />
    <hkern u1="T" u2="&#xfb;" k="75" />
    <hkern u1="T" u2="&#xfa;" k="75" />
    <hkern u1="T" u2="&#xf9;" k="75" />
    <hkern u1="T" u2="&#xf8;" k="75" />
    <hkern u1="T" u2="&#xf6;" k="75" />
    <hkern u1="T" u2="&#xf5;" k="75" />
    <hkern u1="T" u2="&#xf4;" k="75" />
    <hkern u1="T" u2="&#xf3;" k="75" />
    <hkern u1="T" u2="&#xf2;" k="75" />
    <hkern u1="T" u2="&#xf1;" k="75" />
    <hkern u1="T" u2="&#xf0;" k="75" />
    <hkern u1="T" u2="&#xef;" k="-30" />
    <hkern u1="T" u2="&#xee;" k="-20" />
    <hkern u1="T" u2="&#xeb;" k="75" />
    <hkern u1="T" u2="&#xea;" k="75" />
    <hkern u1="T" u2="&#xe9;" k="75" />
    <hkern u1="T" u2="&#xe8;" k="75" />
    <hkern u1="T" u2="&#xe7;" k="75" />
    <hkern u1="T" u2="&#xe6;" k="75" />
    <hkern u1="T" u2="&#xe5;" k="75" />
    <hkern u1="T" u2="&#xe4;" k="75" />
    <hkern u1="T" u2="&#xe3;" k="75" />
    <hkern u1="T" u2="&#xe2;" k="75" />
    <hkern u1="T" u2="&#xe1;" k="75" />
    <hkern u1="T" u2="&#xe0;" k="75" />
    <hkern u1="T" u2="&#xd8;" k="15" />
    <hkern u1="T" u2="&#xd6;" k="15" />
    <hkern u1="T" u2="&#xd5;" k="15" />
    <hkern u1="T" u2="&#xd4;" k="15" />
    <hkern u1="T" u2="&#xd3;" k="15" />
    <hkern u1="T" u2="&#xd2;" k="15" />
    <hkern u1="T" u2="&#xc7;" k="15" />
    <hkern u1="T" u2="&#xc6;" k="25" />
    <hkern u1="T" u2="&#xc5;" k="25" />
    <hkern u1="T" u2="&#xc4;" k="25" />
    <hkern u1="T" u2="&#xc3;" k="25" />
    <hkern u1="T" u2="&#xc2;" k="25" />
    <hkern u1="T" u2="&#xc1;" k="25" />
    <hkern u1="T" u2="&#xc0;" k="25" />
    <hkern u1="T" u2="&#xa9;" k="15" />
    <hkern u1="T" u2="z" k="80" />
    <hkern u1="T" u2="y" k="75" />
    <hkern u1="T" u2="x" k="55" />
    <hkern u1="T" u2="v" k="40" />
    <hkern u1="T" u2="r" k="75" />
    <hkern u1="T" u2="q" k="75" />
    <hkern u1="T" u2="p" k="75" />
    <hkern u1="T" u2="o" k="75" />
    <hkern u1="T" u2="n" k="75" />
    <hkern u1="T" u2="g" k="75" />
    <hkern u1="T" u2="e" k="75" />
    <hkern u1="T" u2="d" k="75" />
    <hkern u1="T" u2="_" k="45" />
    <hkern u1="T" u2="Q" k="15" />
    <hkern u1="T" u2="O" k="15" />
    <hkern u1="T" u2="G" k="15" />
    <hkern u1="T" u2="&#x2f;" k="85" />
    <hkern u1="T" u2="&#x2e;" k="110" />
    <hkern u1="U" u2="&#x2026;" k="-10" />
    <hkern u1="U" u2="&#x2e;" k="-10" />
    <hkern u1="U" u2="&#x2c;" k="-10" />
    <hkern u1="V" u2="&#x2026;" k="110" />
    <hkern u1="V" u2="&#x2014;" k="35" />
    <hkern u1="V" u2="&#x2013;" k="35" />
    <hkern u1="V" u2="&#x1ff;" k="20" />
    <hkern u1="V" u2="&#x1fe;" k="10" />
    <hkern u1="V" u2="&#x1fd;" k="20" />
    <hkern u1="V" u2="&#x1fb;" k="20" />
    <hkern u1="V" u2="&#x159;" k="20" />
    <hkern u1="V" u2="&#x157;" k="20" />
    <hkern u1="V" u2="&#x155;" k="20" />
    <hkern u1="V" u2="&#x153;" k="20" />
    <hkern u1="V" u2="&#x152;" k="10" />
    <hkern u1="V" u2="&#x151;" k="20" />
    <hkern u1="V" u2="&#x150;" k="10" />
    <hkern u1="V" u2="&#x14f;" k="20" />
    <hkern u1="V" u2="&#x14e;" k="10" />
    <hkern u1="V" u2="&#x14d;" k="20" />
    <hkern u1="V" u2="&#x14c;" k="10" />
    <hkern u1="V" u2="&#x14b;" k="20" />
    <hkern u1="V" u2="&#x148;" k="20" />
    <hkern u1="V" u2="&#x146;" k="20" />
    <hkern u1="V" u2="&#x144;" k="20" />
    <hkern u1="V" u2="&#x123;" k="20" />
    <hkern u1="V" u2="&#x122;" k="10" />
    <hkern u1="V" u2="&#x121;" k="20" />
    <hkern u1="V" u2="&#x120;" k="10" />
    <hkern u1="V" u2="&#x11f;" k="20" />
    <hkern u1="V" u2="&#x11e;" k="10" />
    <hkern u1="V" u2="&#x11d;" k="20" />
    <hkern u1="V" u2="&#x11c;" k="10" />
    <hkern u1="V" u2="&#x11b;" k="20" />
    <hkern u1="V" u2="&#x119;" k="20" />
    <hkern u1="V" u2="&#x117;" k="20" />
    <hkern u1="V" u2="&#x115;" k="20" />
    <hkern u1="V" u2="&#x113;" k="20" />
    <hkern u1="V" u2="&#x111;" k="20" />
    <hkern u1="V" u2="&#x10f;" k="20" />
    <hkern u1="V" u2="&#x10d;" k="20" />
    <hkern u1="V" u2="&#x10c;" k="10" />
    <hkern u1="V" u2="&#x10b;" k="20" />
    <hkern u1="V" u2="&#x10a;" k="10" />
    <hkern u1="V" u2="&#x109;" k="20" />
    <hkern u1="V" u2="&#x108;" k="10" />
    <hkern u1="V" u2="&#x107;" k="20" />
    <hkern u1="V" u2="&#x106;" k="10" />
    <hkern u1="V" u2="&#x105;" k="20" />
    <hkern u1="V" u2="&#x103;" k="20" />
    <hkern u1="V" u2="&#x101;" k="20" />
    <hkern u1="V" u2="&#xf8;" k="20" />
    <hkern u1="V" u2="&#xf6;" k="20" />
    <hkern u1="V" u2="&#xf5;" k="20" />
    <hkern u1="V" u2="&#xf4;" k="20" />
    <hkern u1="V" u2="&#xf3;" k="20" />
    <hkern u1="V" u2="&#xf2;" k="20" />
    <hkern u1="V" u2="&#xf1;" k="20" />
    <hkern u1="V" u2="&#xf0;" k="20" />
    <hkern u1="V" u2="&#xef;" k="-40" />
    <hkern u1="V" u2="&#xee;" k="-20" />
    <hkern u1="V" u2="&#xeb;" k="20" />
    <hkern u1="V" u2="&#xea;" k="20" />
    <hkern u1="V" u2="&#xe9;" k="20" />
    <hkern u1="V" u2="&#xe8;" k="20" />
    <hkern u1="V" u2="&#xe7;" k="20" />
    <hkern u1="V" u2="&#xe6;" k="20" />
    <hkern u1="V" u2="&#xe5;" k="20" />
    <hkern u1="V" u2="&#xe4;" k="20" />
    <hkern u1="V" u2="&#xe3;" k="20" />
    <hkern u1="V" u2="&#xe2;" k="20" />
    <hkern u1="V" u2="&#xe1;" k="20" />
    <hkern u1="V" u2="&#xe0;" k="20" />
    <hkern u1="V" u2="&#xd8;" k="10" />
    <hkern u1="V" u2="&#xd6;" k="10" />
    <hkern u1="V" u2="&#xd5;" k="10" />
    <hkern u1="V" u2="&#xd4;" k="10" />
    <hkern u1="V" u2="&#xd3;" k="10" />
    <hkern u1="V" u2="&#xd2;" k="10" />
    <hkern u1="V" u2="&#xc7;" k="10" />
    <hkern u1="V" u2="&#xa9;" k="10" />
    <hkern u1="V" u2="r" k="20" />
    <hkern u1="V" u2="q" k="20" />
    <hkern u1="V" u2="p" k="20" />
    <hkern u1="V" u2="o" k="20" />
    <hkern u1="V" u2="n" k="20" />
    <hkern u1="V" u2="m" k="20" />
    <hkern u1="V" u2="g" k="20" />
    <hkern u1="V" u2="e" k="20" />
    <hkern u1="V" u2="d" k="15" />
    <hkern u1="V" u2="c" k="15" />
    <hkern u1="V" u2="a" k="20" />
    <hkern u1="V" u2="_" k="65" />
    <hkern u1="V" u2="X" k="-10" />
    <hkern u1="V" u2="Q" k="-10" />
    <hkern u1="V" u2="O" k="-10" />
    <hkern u1="V" u2="G" k="-20" />
    <hkern u1="V" u2="C" k="-20" />
    <hkern u1="V" u2="&#x2f;" k="55" />
    <hkern u1="V" u2="&#x2e;" k="110" />
    <hkern u1="V" u2="&#x2d;" k="35" />
    <hkern u1="V" u2="&#x2c;" k="110" />
    <hkern u1="W" u2="&#x2026;" k="55" />
    <hkern u1="W" u2="&#x2014;" k="20" />
    <hkern u1="W" u2="&#x2013;" k="20" />
    <hkern u1="W" u2="&#x1ff;" k="10" />
    <hkern u1="W" u2="&#x1fd;" k="10" />
    <hkern u1="W" u2="&#x1fb;" k="10" />
    <hkern u1="W" u2="&#x153;" k="10" />
    <hkern u1="W" u2="&#x151;" k="10" />
    <hkern u1="W" u2="&#x14f;" k="10" />
    <hkern u1="W" u2="&#x14d;" k="10" />
    <hkern u1="W" u2="&#x123;" k="10" />
    <hkern u1="W" u2="&#x121;" k="10" />
    <hkern u1="W" u2="&#x11f;" k="10" />
    <hkern u1="W" u2="&#x11d;" k="10" />
    <hkern u1="W" u2="&#x11b;" k="10" />
    <hkern u1="W" u2="&#x119;" k="10" />
    <hkern u1="W" u2="&#x117;" k="10" />
    <hkern u1="W" u2="&#x115;" k="10" />
    <hkern u1="W" u2="&#x113;" k="10" />
    <hkern u1="W" u2="&#x111;" k="10" />
    <hkern u1="W" u2="&#x10f;" k="10" />
    <hkern u1="W" u2="&#x10d;" k="10" />
    <hkern u1="W" u2="&#x10b;" k="10" />
    <hkern u1="W" u2="&#x109;" k="10" />
    <hkern u1="W" u2="&#x107;" k="10" />
    <hkern u1="W" u2="&#x105;" k="10" />
    <hkern u1="W" u2="&#x103;" k="10" />
    <hkern u1="W" u2="&#x101;" k="10" />
    <hkern u1="W" u2="&#xf8;" k="10" />
    <hkern u1="W" u2="&#xf6;" k="10" />
    <hkern u1="W" u2="&#xf5;" k="10" />
    <hkern u1="W" u2="&#xf4;" k="10" />
    <hkern u1="W" u2="&#xf3;" k="10" />
    <hkern u1="W" u2="&#xf2;" k="10" />
    <hkern u1="W" u2="&#xf0;" k="10" />
    <hkern u1="W" u2="&#xef;" k="-40" />
    <hkern u1="W" u2="&#xee;" k="-20" />
    <hkern u1="W" u2="&#xeb;" k="10" />
    <hkern u1="W" u2="&#xea;" k="10" />
    <hkern u1="W" u2="&#xe9;" k="10" />
    <hkern u1="W" u2="&#xe8;" k="10" />
    <hkern u1="W" u2="&#xe7;" k="10" />
    <hkern u1="W" u2="&#xe6;" k="10" />
    <hkern u1="W" u2="&#xe5;" k="10" />
    <hkern u1="W" u2="&#xe4;" k="10" />
    <hkern u1="W" u2="&#xe3;" k="10" />
    <hkern u1="W" u2="&#xe2;" k="10" />
    <hkern u1="W" u2="&#xe1;" k="10" />
    <hkern u1="W" u2="&#xe0;" k="10" />
    <hkern u1="W" u2="q" k="10" />
    <hkern u1="W" u2="o" k="10" />
    <hkern u1="W" u2="g" k="10" />
    <hkern u1="W" u2="e" k="10" />
    <hkern u1="W" u2="d" k="10" />
    <hkern u1="W" u2="_" k="45" />
    <hkern u1="W" u2="&#x2f;" k="55" />
    <hkern u1="W" u2="&#x2e;" k="55" />
    <hkern u1="X" u2="&#x2026;" k="-40" />
    <hkern u1="X" u2="&#x2014;" k="65" />
    <hkern u1="X" u2="&#x2013;" k="65" />
    <hkern u1="X" u2="&#x1ef2;" k="10" />
    <hkern u1="X" u2="&#x1fe;" k="10" />
    <hkern u1="X" u2="&#x178;" k="10" />
    <hkern u1="X" u2="&#x176;" k="10" />
    <hkern u1="X" u2="&#x152;" k="10" />
    <hkern u1="X" u2="&#x150;" k="10" />
    <hkern u1="X" u2="&#x14e;" k="10" />
    <hkern u1="X" u2="&#x14c;" k="10" />
    <hkern u1="X" u2="&#x122;" k="10" />
    <hkern u1="X" u2="&#x120;" k="10" />
    <hkern u1="X" u2="&#x11e;" k="10" />
    <hkern u1="X" u2="&#x11c;" k="10" />
    <hkern u1="X" u2="&#x11a;" k="5" />
    <hkern u1="X" u2="&#x118;" k="5" />
    <hkern u1="X" u2="&#x116;" k="5" />
    <hkern u1="X" u2="&#x114;" k="5" />
    <hkern u1="X" u2="&#x112;" k="5" />
    <hkern u1="X" u2="&#x10c;" k="10" />
    <hkern u1="X" u2="&#x10a;" k="10" />
    <hkern u1="X" u2="&#x108;" k="10" />
    <hkern u1="X" u2="&#x106;" k="10" />
    <hkern u1="X" u2="&#xef;" k="-40" />
    <hkern u1="X" u2="&#xdd;" k="10" />
    <hkern u1="X" u2="&#xd8;" k="10" />
    <hkern u1="X" u2="&#xd6;" k="10" />
    <hkern u1="X" u2="&#xd5;" k="10" />
    <hkern u1="X" u2="&#xd4;" k="10" />
    <hkern u1="X" u2="&#xd3;" k="10" />
    <hkern u1="X" u2="&#xd2;" k="10" />
    <hkern u1="X" u2="&#xcb;" k="5" />
    <hkern u1="X" u2="&#xca;" k="5" />
    <hkern u1="X" u2="&#xc9;" k="5" />
    <hkern u1="X" u2="&#xc8;" k="5" />
    <hkern u1="X" u2="&#xc7;" k="10" />
    <hkern u1="X" u2="&#xa9;" k="10" />
    <hkern u1="X" u2="_" k="-60" />
    <hkern u1="X" u2="Q" k="10" />
    <hkern u1="X" u2="O" k="10" />
    <hkern u1="X" u2="G" k="10" />
    <hkern u1="X" u2="E" k="5" />
    <hkern u1="X" u2="C" k="10" />
    <hkern u1="X" u2="&#x2e;" k="-40" />
    <hkern u1="X" u2="&#x2d;" k="65" />
    <hkern u1="X" u2="&#x2c;" k="-40" />
    <hkern u1="Y" u2="X" k="-25" />
    <hkern u1="Y" u2="V" k="-15" />
    <hkern u1="Z" u2="&#x2026;" k="-30" />
    <hkern u1="Z" u2="&#x2014;" k="100" />
    <hkern u1="Z" u2="&#x2013;" k="100" />
    <hkern u1="Z" u2="&#x1fe;" k="15" />
    <hkern u1="Z" u2="&#x152;" k="15" />
    <hkern u1="Z" u2="&#x150;" k="15" />
    <hkern u1="Z" u2="&#x14e;" k="15" />
    <hkern u1="Z" u2="&#x14c;" k="15" />
    <hkern u1="Z" u2="&#x122;" k="15" />
    <hkern u1="Z" u2="&#x120;" k="15" />
    <hkern u1="Z" u2="&#x11e;" k="15" />
    <hkern u1="Z" u2="&#x11c;" k="15" />
    <hkern u1="Z" u2="&#x10c;" k="15" />
    <hkern u1="Z" u2="&#x10a;" k="15" />
    <hkern u1="Z" u2="&#x108;" k="15" />
    <hkern u1="Z" u2="&#x106;" k="15" />
    <hkern u1="Z" u2="&#xd8;" k="15" />
    <hkern u1="Z" u2="&#xd6;" k="15" />
    <hkern u1="Z" u2="&#xd5;" k="15" />
    <hkern u1="Z" u2="&#xd4;" k="15" />
    <hkern u1="Z" u2="&#xd3;" k="15" />
    <hkern u1="Z" u2="&#xd2;" k="15" />
    <hkern u1="Z" u2="&#xc7;" k="15" />
    <hkern u1="Z" u2="&#xa9;" k="15" />
    <hkern u1="Z" u2="Q" k="15" />
    <hkern u1="Z" u2="O" k="15" />
    <hkern u1="Z" u2="G" k="15" />
    <hkern u1="Z" u2="&#x2e;" k="-30" />
    <hkern u1="[" u2="&#x134;" k="-50" />
    <hkern u1="[" u2="j" k="-40" />
    <hkern u1="[" u2="J" k="-50" />
    <hkern u1="_" u2="&#x1ef2;" k="50" />
    <hkern u1="_" u2="&#x1e85;" k="35" />
    <hkern u1="_" u2="&#x1e84;" k="45" />
    <hkern u1="_" u2="&#x1e83;" k="35" />
    <hkern u1="_" u2="&#x1e82;" k="45" />
    <hkern u1="_" u2="&#x1e81;" k="35" />
    <hkern u1="_" u2="&#x1e80;" k="45" />
    <hkern u1="_" u2="&#x178;" k="50" />
    <hkern u1="_" u2="&#x176;" k="50" />
    <hkern u1="_" u2="&#x175;" k="35" />
    <hkern u1="_" u2="&#x174;" k="45" />
    <hkern u1="_" u2="&#x164;" k="45" />
    <hkern u1="_" u2="&#x162;" k="45" />
    <hkern u1="_" u2="&#x135;" k="-65" />
    <hkern u1="_" u2="&#x134;" k="-70" />
    <hkern u1="_" u2="&#xdd;" k="50" />
    <hkern u1="_" u2="x" k="-50" />
    <hkern u1="_" u2="w" k="35" />
    <hkern u1="_" u2="v" k="35" />
    <hkern u1="_" u2="j" k="-65" />
    <hkern u1="_" u2="Y" k="50" />
    <hkern u1="_" u2="X" k="-50" />
    <hkern u1="_" u2="W" k="45" />
    <hkern u1="_" u2="V" k="65" />
    <hkern u1="_" u2="T" k="45" />
    <hkern u1="_" u2="J" k="-70" />
    <hkern u1="a" u2="&#x1ef2;" k="30" />
    <hkern u1="a" u2="&#x1e84;" k="10" />
    <hkern u1="a" u2="&#x1e82;" k="10" />
    <hkern u1="a" u2="&#x1e80;" k="10" />
    <hkern u1="a" u2="&#x178;" k="30" />
    <hkern u1="a" u2="&#x176;" k="30" />
    <hkern u1="a" u2="&#x174;" k="10" />
    <hkern u1="a" u2="&#x164;" k="80" />
    <hkern u1="a" u2="&#x162;" k="80" />
    <hkern u1="a" u2="&#xdd;" k="30" />
    <hkern u1="a" u2="Y" k="30" />
    <hkern u1="a" u2="W" k="10" />
    <hkern u1="a" u2="V" k="20" />
    <hkern u1="a" u2="T" k="80" />
    <hkern u1="b" u2="&#x2026;" k="20" />
    <hkern u1="b" u2="&#x1e84;" k="10" />
    <hkern u1="b" u2="&#x1e82;" k="10" />
    <hkern u1="b" u2="&#x1e80;" k="10" />
    <hkern u1="b" u2="&#x174;" k="10" />
    <hkern u1="b" u2="&#x164;" k="75" />
    <hkern u1="b" u2="&#x162;" k="75" />
    <hkern u1="b" u2="z" k="20" />
    <hkern u1="b" u2="x" k="10" />
    <hkern u1="b" u2="V" k="20" />
    <hkern u1="b" u2="&#x2e;" k="20" />
    <hkern u1="c" u2="&#x2026;" k="-20" />
    <hkern u1="c" u2="&#x201c;" k="-20" />
    <hkern u1="c" u2="&#x1ff;" k="10" />
    <hkern u1="c" u2="&#x164;" k="40" />
    <hkern u1="c" u2="&#x162;" k="40" />
    <hkern u1="c" u2="&#x153;" k="10" />
    <hkern u1="c" u2="&#x151;" k="10" />
    <hkern u1="c" u2="&#x14f;" k="10" />
    <hkern u1="c" u2="&#x14d;" k="10" />
    <hkern u1="c" u2="&#x123;" k="10" />
    <hkern u1="c" u2="&#x121;" k="10" />
    <hkern u1="c" u2="&#x11f;" k="10" />
    <hkern u1="c" u2="&#x11d;" k="10" />
    <hkern u1="c" u2="&#x11b;" k="10" />
    <hkern u1="c" u2="&#x119;" k="10" />
    <hkern u1="c" u2="&#x117;" k="10" />
    <hkern u1="c" u2="&#x115;" k="10" />
    <hkern u1="c" u2="&#x113;" k="10" />
    <hkern u1="c" u2="&#x111;" k="10" />
    <hkern u1="c" u2="&#x10f;" k="10" />
    <hkern u1="c" u2="&#x10d;" k="10" />
    <hkern u1="c" u2="&#x10b;" k="10" />
    <hkern u1="c" u2="&#x109;" k="10" />
    <hkern u1="c" u2="&#x107;" k="10" />
    <hkern u1="c" u2="&#xf8;" k="10" />
    <hkern u1="c" u2="&#xf6;" k="10" />
    <hkern u1="c" u2="&#xf5;" k="10" />
    <hkern u1="c" u2="&#xf4;" k="10" />
    <hkern u1="c" u2="&#xf3;" k="10" />
    <hkern u1="c" u2="&#xf2;" k="10" />
    <hkern u1="c" u2="&#xf0;" k="10" />
    <hkern u1="c" u2="&#xeb;" k="10" />
    <hkern u1="c" u2="&#xea;" k="10" />
    <hkern u1="c" u2="&#xe9;" k="10" />
    <hkern u1="c" u2="&#xe8;" k="10" />
    <hkern u1="c" u2="&#xe7;" k="10" />
    <hkern u1="c" u2="x" k="-13" />
    <hkern u1="c" u2="q" k="10" />
    <hkern u1="c" u2="o" k="10" />
    <hkern u1="c" u2="g" k="10" />
    <hkern u1="c" u2="e" k="10" />
    <hkern u1="c" u2="d" k="10" />
    <hkern u1="c" u2="&#x2e;" k="-20" />
    <hkern u1="e" u2="&#x1e84;" k="10" />
    <hkern u1="e" u2="&#x1e82;" k="10" />
    <hkern u1="e" u2="&#x1e80;" k="10" />
    <hkern u1="e" u2="&#x219;" k="-5" />
    <hkern u1="e" u2="&#x174;" k="10" />
    <hkern u1="e" u2="&#x164;" k="70" />
    <hkern u1="e" u2="&#x162;" k="70" />
    <hkern u1="e" u2="&#x161;" k="-5" />
    <hkern u1="e" u2="&#x15f;" k="-5" />
    <hkern u1="e" u2="&#x15d;" k="-5" />
    <hkern u1="e" u2="&#x15b;" k="-5" />
    <hkern u1="e" u2="V" k="30" />
    <hkern u1="f" u2="&#x2122;" k="-75" />
    <hkern u1="f" u2="&#x2026;" k="95" />
    <hkern u1="f" u2="&#x201d;" k="-45" />
    <hkern u1="f" u2="&#x201c;" k="-30" />
    <hkern u1="f" u2="&#x2019;" k="-45" />
    <hkern u1="f" u2="&#x2018;" k="-30" />
    <hkern u1="f" u2="&#x2014;" k="20" />
    <hkern u1="f" u2="&#x2013;" k="20" />
    <hkern u1="f" u2="&#x1ef2;" k="-55" />
    <hkern u1="f" u2="&#x1e84;" k="-75" />
    <hkern u1="f" u2="&#x1e82;" k="-75" />
    <hkern u1="f" u2="&#x1e80;" k="-75" />
    <hkern u1="f" u2="&#x1ff;" k="10" />
    <hkern u1="f" u2="&#x1fd;" k="10" />
    <hkern u1="f" u2="&#x1fb;" k="10" />
    <hkern u1="f" u2="&#x17d;" k="-45" />
    <hkern u1="f" u2="&#x17b;" k="-45" />
    <hkern u1="f" u2="&#x179;" k="-45" />
    <hkern u1="f" u2="&#x178;" k="-55" />
    <hkern u1="f" u2="&#x176;" k="-55" />
    <hkern u1="f" u2="&#x174;" k="-75" />
    <hkern u1="f" u2="&#x164;" k="-75" />
    <hkern u1="f" u2="&#x162;" k="-75" />
    <hkern u1="f" u2="&#x153;" k="10" />
    <hkern u1="f" u2="&#x151;" k="10" />
    <hkern u1="f" u2="&#x14f;" k="10" />
    <hkern u1="f" u2="&#x14d;" k="10" />
    <hkern u1="f" u2="&#x123;" k="10" />
    <hkern u1="f" u2="&#x121;" k="10" />
    <hkern u1="f" u2="&#x11f;" k="10" />
    <hkern u1="f" u2="&#x11d;" k="10" />
    <hkern u1="f" u2="&#x11b;" k="10" />
    <hkern u1="f" u2="&#x119;" k="10" />
    <hkern u1="f" u2="&#x117;" k="10" />
    <hkern u1="f" u2="&#x115;" k="10" />
    <hkern u1="f" u2="&#x113;" k="10" />
    <hkern u1="f" u2="&#x111;" k="10" />
    <hkern u1="f" u2="&#x10f;" k="10" />
    <hkern u1="f" u2="&#x10d;" k="10" />
    <hkern u1="f" u2="&#x10b;" k="10" />
    <hkern u1="f" u2="&#x109;" k="10" />
    <hkern u1="f" u2="&#x107;" k="10" />
    <hkern u1="f" u2="&#x105;" k="10" />
    <hkern u1="f" u2="&#x103;" k="10" />
    <hkern u1="f" u2="&#x101;" k="10" />
    <hkern u1="f" u2="&#xf8;" k="10" />
    <hkern u1="f" u2="&#xf6;" k="10" />
    <hkern u1="f" u2="&#xf5;" k="10" />
    <hkern u1="f" u2="&#xf4;" k="10" />
    <hkern u1="f" u2="&#xf3;" k="10" />
    <hkern u1="f" u2="&#xf2;" k="10" />
    <hkern u1="f" u2="&#xf0;" k="10" />
    <hkern u1="f" u2="&#xee;" k="-20" />
    <hkern u1="f" u2="&#xeb;" k="10" />
    <hkern u1="f" u2="&#xea;" k="10" />
    <hkern u1="f" u2="&#xe9;" k="10" />
    <hkern u1="f" u2="&#xe8;" k="10" />
    <hkern u1="f" u2="&#xe7;" k="10" />
    <hkern u1="f" u2="&#xe6;" k="10" />
    <hkern u1="f" u2="&#xe5;" k="10" />
    <hkern u1="f" u2="&#xe4;" k="10" />
    <hkern u1="f" u2="&#xe3;" k="10" />
    <hkern u1="f" u2="&#xe2;" k="10" />
    <hkern u1="f" u2="&#xe1;" k="10" />
    <hkern u1="f" u2="&#xe0;" k="10" />
    <hkern u1="f" u2="&#xdd;" k="-55" />
    <hkern u1="f" u2="&#xba;" k="-20" />
    <hkern u1="f" u2="&#xaa;" k="-30" />
    <hkern u1="f" u2="&#x7d;" k="-80" />
    <hkern u1="f" u2="q" k="10" />
    <hkern u1="f" u2="o" k="10" />
    <hkern u1="f" u2="g" k="10" />
    <hkern u1="f" u2="e" k="10" />
    <hkern u1="f" u2="d" k="10" />
    <hkern u1="f" u2="c" k="10" />
    <hkern u1="f" u2="a" k="10" />
    <hkern u1="f" u2="]" k="-90" />
    <hkern u1="f" u2="\" k="-50" />
    <hkern u1="f" u2="Z" k="-45" />
    <hkern u1="f" u2="Y" k="-55" />
    <hkern u1="f" u2="X" k="-55" />
    <hkern u1="f" u2="W" k="-75" />
    <hkern u1="f" u2="V" k="-75" />
    <hkern u1="f" u2="T" k="-75" />
    <hkern u1="f" u2="&#x3f;" k="-30" />
    <hkern u1="f" u2="&#x2f;" k="10" />
    <hkern u1="f" u2="&#x2e;" k="95" />
    <hkern u1="f" u2="&#x2d;" k="20" />
    <hkern u1="f" u2="&#x2c;" k="95" />
    <hkern u1="f" u2="&#x2a;" k="-50" />
    <hkern u1="f" u2="&#x29;" k="-70" />
    <hkern u1="f" u2="&#x27;" k="-20" />
    <hkern u1="f" u2="&#x21;" k="-45" />
    <hkern u1="g" u2="&#x164;" k="70" />
    <hkern u1="g" u2="&#x162;" k="70" />
    <hkern u1="g" u2="V" k="20" />
    <hkern u1="h" u2="&#x164;" k="80" />
    <hkern u1="h" u2="&#x162;" k="80" />
    <hkern u1="k" u2="&#x2026;" k="-30" />
    <hkern u1="k" u2="&#x2014;" k="10" />
    <hkern u1="k" u2="&#x2013;" k="10" />
    <hkern u1="k" u2="&#x1ff;" k="8" />
    <hkern u1="k" u2="&#x1fd;" k="-10" />
    <hkern u1="k" u2="&#x1fb;" k="-10" />
    <hkern u1="k" u2="&#x164;" k="30" />
    <hkern u1="k" u2="&#x162;" k="30" />
    <hkern u1="k" u2="&#x153;" k="8" />
    <hkern u1="k" u2="&#x151;" k="8" />
    <hkern u1="k" u2="&#x14f;" k="8" />
    <hkern u1="k" u2="&#x14d;" k="8" />
    <hkern u1="k" u2="&#x123;" k="8" />
    <hkern u1="k" u2="&#x121;" k="8" />
    <hkern u1="k" u2="&#x11f;" k="8" />
    <hkern u1="k" u2="&#x11d;" k="8" />
    <hkern u1="k" u2="&#x11b;" k="8" />
    <hkern u1="k" u2="&#x119;" k="8" />
    <hkern u1="k" u2="&#x117;" k="8" />
    <hkern u1="k" u2="&#x115;" k="8" />
    <hkern u1="k" u2="&#x113;" k="8" />
    <hkern u1="k" u2="&#x111;" k="8" />
    <hkern u1="k" u2="&#x10f;" k="8" />
    <hkern u1="k" u2="&#x10d;" k="8" />
    <hkern u1="k" u2="&#x10b;" k="8" />
    <hkern u1="k" u2="&#x109;" k="8" />
    <hkern u1="k" u2="&#x107;" k="8" />
    <hkern u1="k" u2="&#x105;" k="-10" />
    <hkern u1="k" u2="&#x103;" k="-10" />
    <hkern u1="k" u2="&#x101;" k="-10" />
    <hkern u1="k" u2="&#xf8;" k="8" />
    <hkern u1="k" u2="&#xf6;" k="8" />
    <hkern u1="k" u2="&#xf5;" k="8" />
    <hkern u1="k" u2="&#xf4;" k="8" />
    <hkern u1="k" u2="&#xf3;" k="8" />
    <hkern u1="k" u2="&#xf2;" k="8" />
    <hkern u1="k" u2="&#xf0;" k="8" />
    <hkern u1="k" u2="&#xeb;" k="8" />
    <hkern u1="k" u2="&#xea;" k="8" />
    <hkern u1="k" u2="&#xe9;" k="8" />
    <hkern u1="k" u2="&#xe8;" k="8" />
    <hkern u1="k" u2="&#xe7;" k="8" />
    <hkern u1="k" u2="&#xe6;" k="-20" />
    <hkern u1="k" u2="&#xe5;" k="-10" />
    <hkern u1="k" u2="&#xe4;" k="-10" />
    <hkern u1="k" u2="&#xe3;" k="-10" />
    <hkern u1="k" u2="&#xe2;" k="-10" />
    <hkern u1="k" u2="&#xe1;" k="-10" />
    <hkern u1="k" u2="&#xe0;" k="-10" />
    <hkern u1="k" u2="q" k="8" />
    <hkern u1="k" u2="o" k="8" />
    <hkern u1="k" u2="g" k="8" />
    <hkern u1="k" u2="e" k="8" />
    <hkern u1="k" u2="d" k="8" />
    <hkern u1="k" u2="&#x2e;" k="-30" />
    <hkern u1="m" u2="&#x164;" k="80" />
    <hkern u1="m" u2="&#x162;" k="80" />
    <hkern u1="m" u2="T" k="80" />
    <hkern u1="n" u2="&#x164;" k="80" />
    <hkern u1="n" u2="&#x162;" k="80" />
    <hkern u1="n" u2="T" k="80" />
    <hkern u1="o" u2="&#x2026;" k="20" />
    <hkern u1="o" u2="&#x1e84;" k="10" />
    <hkern u1="o" u2="&#x1e82;" k="10" />
    <hkern u1="o" u2="&#x1e80;" k="10" />
    <hkern u1="o" u2="&#x174;" k="10" />
    <hkern u1="o" u2="&#x164;" k="75" />
    <hkern u1="o" u2="&#x162;" k="75" />
    <hkern u1="o" u2="z" k="20" />
    <hkern u1="o" u2="x" k="10" />
    <hkern u1="o" u2="W" k="10" />
    <hkern u1="o" u2="V" k="20" />
    <hkern u1="o" u2="T" k="75" />
    <hkern u1="o" u2="&#x2e;" k="20" />
    <hkern u1="o" u2="&#x2c;" k="20" />
    <hkern u1="p" u2="&#x2026;" k="20" />
    <hkern u1="p" u2="&#x1e84;" k="10" />
    <hkern u1="p" u2="&#x1e82;" k="10" />
    <hkern u1="p" u2="&#x1e80;" k="10" />
    <hkern u1="p" u2="&#x174;" k="10" />
    <hkern u1="p" u2="&#x164;" k="75" />
    <hkern u1="p" u2="&#x162;" k="75" />
    <hkern u1="p" u2="z" k="20" />
    <hkern u1="p" u2="x" k="10" />
    <hkern u1="p" u2="W" k="10" />
    <hkern u1="p" u2="V" k="20" />
    <hkern u1="p" u2="T" k="75" />
    <hkern u1="p" u2="&#x2e;" k="20" />
    <hkern u1="p" u2="&#x2c;" k="20" />
    <hkern u1="q" u2="&#x164;" k="70" />
    <hkern u1="q" u2="&#x162;" k="70" />
    <hkern u1="q" u2="V" k="20" />
    <hkern u1="q" u2="T" k="70" />
    <hkern u1="r" g2="fl" k="-20" />
    <hkern u1="r" g2="fi" k="-20" />
    <hkern u1="r" u2="&#x2026;" k="105" />
    <hkern u1="r" u2="&#x201c;" k="-40" />
    <hkern u1="r" u2="&#x2014;" k="40" />
    <hkern u1="r" u2="&#x2013;" k="40" />
    <hkern u1="r" u2="&#x1e85;" k="-30" />
    <hkern u1="r" u2="&#x1e83;" k="-30" />
    <hkern u1="r" u2="&#x1e81;" k="-30" />
    <hkern u1="r" u2="&#x1ff;" k="15" />
    <hkern u1="r" u2="&#x1fd;" k="15" />
    <hkern u1="r" u2="&#x1fb;" k="15" />
    <hkern u1="r" u2="&#x17f;" k="-20" />
    <hkern u1="r" u2="&#x175;" k="-30" />
    <hkern u1="r" u2="&#x167;" k="-20" />
    <hkern u1="r" u2="&#x165;" k="-20" />
    <hkern u1="r" u2="&#x163;" k="-20" />
    <hkern u1="r" u2="&#x153;" k="15" />
    <hkern u1="r" u2="&#x151;" k="15" />
    <hkern u1="r" u2="&#x14f;" k="15" />
    <hkern u1="r" u2="&#x14d;" k="15" />
    <hkern u1="r" u2="&#x123;" k="15" />
    <hkern u1="r" u2="&#x121;" k="15" />
    <hkern u1="r" u2="&#x11f;" k="15" />
    <hkern u1="r" u2="&#x11d;" k="15" />
    <hkern u1="r" u2="&#x11b;" k="15" />
    <hkern u1="r" u2="&#x119;" k="15" />
    <hkern u1="r" u2="&#x117;" k="15" />
    <hkern u1="r" u2="&#x115;" k="15" />
    <hkern u1="r" u2="&#x113;" k="15" />
    <hkern u1="r" u2="&#x111;" k="15" />
    <hkern u1="r" u2="&#x10f;" k="15" />
    <hkern u1="r" u2="&#x10d;" k="15" />
    <hkern u1="r" u2="&#x10b;" k="15" />
    <hkern u1="r" u2="&#x109;" k="15" />
    <hkern u1="r" u2="&#x107;" k="15" />
    <hkern u1="r" u2="&#x105;" k="15" />
    <hkern u1="r" u2="&#x103;" k="15" />
    <hkern u1="r" u2="&#x101;" k="15" />
    <hkern u1="r" u2="&#xf8;" k="15" />
    <hkern u1="r" u2="&#xf6;" k="15" />
    <hkern u1="r" u2="&#xf5;" k="15" />
    <hkern u1="r" u2="&#xf4;" k="15" />
    <hkern u1="r" u2="&#xf3;" k="15" />
    <hkern u1="r" u2="&#xf2;" k="15" />
    <hkern u1="r" u2="&#xf0;" k="15" />
    <hkern u1="r" u2="&#xeb;" k="15" />
    <hkern u1="r" u2="&#xea;" k="15" />
    <hkern u1="r" u2="&#xe9;" k="15" />
    <hkern u1="r" u2="&#xe8;" k="15" />
    <hkern u1="r" u2="&#xe7;" k="15" />
    <hkern u1="r" u2="&#xe6;" k="25" />
    <hkern u1="r" u2="&#xe5;" k="15" />
    <hkern u1="r" u2="&#xe4;" k="15" />
    <hkern u1="r" u2="&#xe3;" k="15" />
    <hkern u1="r" u2="&#xe2;" k="15" />
    <hkern u1="r" u2="&#xe1;" k="15" />
    <hkern u1="r" u2="&#xe0;" k="15" />
    <hkern u1="r" u2="v" k="-30" />
    <hkern u1="r" u2="q" k="15" />
    <hkern u1="r" u2="o" k="15" />
    <hkern u1="r" u2="g" k="15" />
    <hkern u1="r" u2="e" k="15" />
    <hkern u1="r" u2="d" k="10" />
    <hkern u1="r" u2="&#x2f;" k="50" />
    <hkern u1="r" u2="&#x2e;" k="105" />
    <hkern u1="s" u2="&#x201c;" k="-20" />
    <hkern u1="s" u2="&#x219;" k="20" />
    <hkern u1="s" u2="&#x164;" k="70" />
    <hkern u1="s" u2="&#x162;" k="70" />
    <hkern u1="s" u2="&#x161;" k="20" />
    <hkern u1="s" u2="&#x15f;" k="20" />
    <hkern u1="s" u2="&#x15d;" k="20" />
    <hkern u1="s" u2="&#x15b;" k="20" />
    <hkern u1="t" u2="&#x2026;" k="-30" />
    <hkern u1="t" u2="&#x201c;" k="-40" />
    <hkern u1="t" u2="&#x2018;" k="-40" />
    <hkern u1="t" u2="&#x2014;" k="35" />
    <hkern u1="t" u2="&#x2013;" k="35" />
    <hkern u1="t" u2="&#x1ff;" k="10" />
    <hkern u1="t" u2="&#x153;" k="10" />
    <hkern u1="t" u2="&#x151;" k="10" />
    <hkern u1="t" u2="&#x14f;" k="10" />
    <hkern u1="t" u2="&#x14d;" k="10" />
    <hkern u1="t" u2="&#x123;" k="10" />
    <hkern u1="t" u2="&#x121;" k="10" />
    <hkern u1="t" u2="&#x11f;" k="10" />
    <hkern u1="t" u2="&#x11d;" k="10" />
    <hkern u1="t" u2="&#x11b;" k="10" />
    <hkern u1="t" u2="&#x119;" k="10" />
    <hkern u1="t" u2="&#x117;" k="10" />
    <hkern u1="t" u2="&#x115;" k="10" />
    <hkern u1="t" u2="&#x113;" k="10" />
    <hkern u1="t" u2="&#x111;" k="10" />
    <hkern u1="t" u2="&#x10f;" k="10" />
    <hkern u1="t" u2="&#x10d;" k="10" />
    <hkern u1="t" u2="&#x10b;" k="10" />
    <hkern u1="t" u2="&#x109;" k="10" />
    <hkern u1="t" u2="&#x107;" k="10" />
    <hkern u1="t" u2="&#xf8;" k="10" />
    <hkern u1="t" u2="&#xf6;" k="10" />
    <hkern u1="t" u2="&#xf5;" k="10" />
    <hkern u1="t" u2="&#xf4;" k="10" />
    <hkern u1="t" u2="&#xf3;" k="10" />
    <hkern u1="t" u2="&#xf2;" k="10" />
    <hkern u1="t" u2="&#xf0;" k="10" />
    <hkern u1="t" u2="&#xeb;" k="10" />
    <hkern u1="t" u2="&#xea;" k="10" />
    <hkern u1="t" u2="&#xe9;" k="10" />
    <hkern u1="t" u2="&#xe8;" k="10" />
    <hkern u1="t" u2="&#xe7;" k="10" />
    <hkern u1="t" u2="x" k="-20" />
    <hkern u1="t" u2="q" k="10" />
    <hkern u1="t" u2="o" k="10" />
    <hkern u1="t" u2="g" k="10" />
    <hkern u1="t" u2="e" k="10" />
    <hkern u1="t" u2="d" k="10" />
    <hkern u1="t" u2="c" k="10" />
    <hkern u1="t" u2="&#x2e;" k="-30" />
    <hkern u1="t" u2="&#x2d;" k="35" />
    <hkern u1="t" u2="&#x2c;" k="-30" />
    <hkern u1="u" u2="&#x164;" k="81" />
    <hkern u1="u" u2="&#x162;" k="81" />
    <hkern u1="u" u2="T" k="81" />
    <hkern u1="v" u2="&#x2026;" k="65" />
    <hkern u1="v" u2="&#x201c;" k="-40" />
    <hkern u1="v" u2="&#x2018;" k="-40" />
    <hkern u1="v" u2="&#x164;" k="55" />
    <hkern u1="v" u2="&#x162;" k="55" />
    <hkern u1="v" u2="_" k="35" />
    <hkern u1="v" u2="T" k="30" />
    <hkern u1="v" u2="&#x2f;" k="35" />
    <hkern u1="v" u2="&#x2e;" k="65" />
    <hkern u1="v" u2="&#x2c;" k="65" />
    <hkern u1="w" u2="&#x2026;" k="65" />
    <hkern u1="w" u2="&#x201c;" k="-40" />
    <hkern u1="w" u2="&#x2014;" k="10" />
    <hkern u1="w" u2="&#x2013;" k="10" />
    <hkern u1="w" u2="&#x164;" k="55" />
    <hkern u1="w" u2="&#x162;" k="55" />
    <hkern u1="w" u2="_" k="35" />
    <hkern u1="w" u2="&#x2f;" k="20" />
    <hkern u1="w" u2="&#x2e;" k="65" />
    <hkern u1="x" u2="&#x2026;" k="-20" />
    <hkern u1="x" u2="&#x201c;" k="-40" />
    <hkern u1="x" u2="&#x2018;" k="-40" />
    <hkern u1="x" u2="&#x2014;" k="20" />
    <hkern u1="x" u2="&#x2013;" k="20" />
    <hkern u1="x" u2="&#x1ff;" k="10" />
    <hkern u1="x" u2="&#x164;" k="55" />
    <hkern u1="x" u2="&#x162;" k="55" />
    <hkern u1="x" u2="&#x153;" k="10" />
    <hkern u1="x" u2="&#x151;" k="10" />
    <hkern u1="x" u2="&#x14f;" k="10" />
    <hkern u1="x" u2="&#x14d;" k="10" />
    <hkern u1="x" u2="&#x123;" k="10" />
    <hkern u1="x" u2="&#x121;" k="10" />
    <hkern u1="x" u2="&#x11f;" k="10" />
    <hkern u1="x" u2="&#x11d;" k="10" />
    <hkern u1="x" u2="&#x11b;" k="10" />
    <hkern u1="x" u2="&#x119;" k="10" />
    <hkern u1="x" u2="&#x117;" k="10" />
    <hkern u1="x" u2="&#x115;" k="10" />
    <hkern u1="x" u2="&#x113;" k="10" />
    <hkern u1="x" u2="&#x111;" k="10" />
    <hkern u1="x" u2="&#x10f;" k="10" />
    <hkern u1="x" u2="&#x10d;" k="10" />
    <hkern u1="x" u2="&#x10b;" k="10" />
    <hkern u1="x" u2="&#x109;" k="10" />
    <hkern u1="x" u2="&#x107;" k="10" />
    <hkern u1="x" u2="&#xf8;" k="10" />
    <hkern u1="x" u2="&#xf6;" k="10" />
    <hkern u1="x" u2="&#xf5;" k="10" />
    <hkern u1="x" u2="&#xf4;" k="10" />
    <hkern u1="x" u2="&#xf3;" k="10" />
    <hkern u1="x" u2="&#xf2;" k="10" />
    <hkern u1="x" u2="&#xf0;" k="10" />
    <hkern u1="x" u2="&#xeb;" k="10" />
    <hkern u1="x" u2="&#xea;" k="10" />
    <hkern u1="x" u2="&#xe9;" k="10" />
    <hkern u1="x" u2="&#xe8;" k="10" />
    <hkern u1="x" u2="&#xe7;" k="10" />
    <hkern u1="x" u2="q" k="10" />
    <hkern u1="x" u2="o" k="10" />
    <hkern u1="x" u2="g" k="10" />
    <hkern u1="x" u2="e" k="10" />
    <hkern u1="x" u2="d" k="10" />
    <hkern u1="x" u2="c" k="10" />
    <hkern u1="x" u2="_" k="-50" />
    <hkern u1="x" u2="T" k="55" />
    <hkern u1="x" u2="&#x2e;" k="-20" />
    <hkern u1="x" u2="&#x2d;" k="20" />
    <hkern u1="x" u2="&#x2c;" k="-20" />
    <hkern u1="y" u2="&#x164;" k="70" />
    <hkern u1="y" u2="&#x162;" k="70" />
    <hkern u1="y" u2="V" k="20" />
    <hkern u1="y" u2="T" k="70" />
    <hkern u1="z" u2="&#x2014;" k="35" />
    <hkern u1="z" u2="&#x2013;" k="35" />
    <hkern u1="z" u2="&#x164;" k="60" />
    <hkern u1="z" u2="&#x162;" k="60" />
    <hkern u1="&#x7b;" u2="&#x134;" k="-50" />
    <hkern u1="&#x7b;" u2="j" k="-40" />
    <hkern u1="&#x7b;" u2="J" k="-50" />
    <hkern u1="&#xa9;" u2="&#x2026;" k="25" />
    <hkern u1="&#xa9;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x164;" k="15" />
    <hkern u1="&#xa9;" u2="&#x162;" k="15" />
    <hkern u1="&#xa9;" u2="&#x152;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x150;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x134;" k="-20" />
    <hkern u1="&#xa9;" u2="&#x122;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x120;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x108;" k="-5" />
    <hkern u1="&#xa9;" u2="&#x106;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xa9;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xa9;" u2="X" k="10" />
    <hkern u1="&#xa9;" u2="V" k="10" />
    <hkern u1="&#xa9;" u2="T" k="15" />
    <hkern u1="&#xa9;" u2="Q" k="-5" />
    <hkern u1="&#xa9;" u2="O" k="-5" />
    <hkern u1="&#xa9;" u2="J" k="-20" />
    <hkern u1="&#xa9;" u2="G" k="-5" />
    <hkern u1="&#xa9;" u2="C" k="-5" />
    <hkern u1="&#xa9;" u2="&#x2e;" k="25" />
    <hkern u1="&#xa9;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xa9;" u2="&#x2c;" k="25" />
    <hkern u1="&#xaa;" u2="&#x2026;" k="130" />
    <hkern u1="&#xaa;" u2="&#x2e;" k="130" />
    <hkern u1="&#xaa;" u2="&#x2c;" k="130" />
    <hkern u1="&#xae;" u2="&#x2026;" k="120" />
    <hkern u1="&#xae;" u2="&#x2e;" k="120" />
    <hkern u1="&#xae;" u2="&#x2c;" k="120" />
    <hkern u1="&#xb0;" u2="&#x2026;" k="165" />
    <hkern u1="&#xb0;" u2="&#x2e;" k="165" />
    <hkern u1="&#xb0;" u2="&#x2c;" k="165" />
    <hkern u1="&#xba;" u2="&#x2026;" k="130" />
    <hkern u1="&#xba;" u2="&#x2e;" k="130" />
    <hkern u1="&#xba;" u2="&#x2c;" k="130" />
    <hkern u1="&#xbb;" u2="&#x2026;" k="50" />
    <hkern u1="&#xbb;" u2="&#x2e;" k="50" />
    <hkern u1="&#xbb;" u2="&#x2c;" k="50" />
    <hkern u1="&#xbf;" u2="&#x2026;" k="50" />
    <hkern u1="&#xbf;" u2="&#x134;" k="-40" />
    <hkern u1="&#xbf;" u2="j" k="-40" />
    <hkern u1="&#xbf;" u2="J" k="-40" />
    <hkern u1="&#xbf;" u2="&#x2e;" k="50" />
    <hkern u1="&#xbf;" u2="&#x2c;" k="-15" />
    <hkern u1="&#xc0;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc0;" u2="&#x164;" k="25" />
    <hkern u1="&#xc0;" u2="&#x162;" k="25" />
    <hkern u1="&#xc0;" u2="V" k="10" />
    <hkern u1="&#xc0;" u2="T" k="25" />
    <hkern u1="&#xc0;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc0;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc1;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc1;" u2="&#x164;" k="25" />
    <hkern u1="&#xc1;" u2="&#x162;" k="25" />
    <hkern u1="&#xc1;" u2="V" k="10" />
    <hkern u1="&#xc1;" u2="T" k="25" />
    <hkern u1="&#xc1;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc1;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc2;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc2;" u2="&#x164;" k="25" />
    <hkern u1="&#xc2;" u2="&#x162;" k="25" />
    <hkern u1="&#xc2;" u2="V" k="10" />
    <hkern u1="&#xc2;" u2="T" k="25" />
    <hkern u1="&#xc2;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc2;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc3;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc3;" u2="&#x164;" k="25" />
    <hkern u1="&#xc3;" u2="&#x162;" k="25" />
    <hkern u1="&#xc3;" u2="V" k="10" />
    <hkern u1="&#xc3;" u2="T" k="25" />
    <hkern u1="&#xc3;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc3;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc4;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc4;" u2="&#x164;" k="25" />
    <hkern u1="&#xc4;" u2="&#x162;" k="25" />
    <hkern u1="&#xc4;" u2="V" k="10" />
    <hkern u1="&#xc4;" u2="T" k="25" />
    <hkern u1="&#xc4;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc4;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc5;" u2="&#x2026;" k="-10" />
    <hkern u1="&#xc5;" u2="&#x164;" k="25" />
    <hkern u1="&#xc5;" u2="&#x162;" k="25" />
    <hkern u1="&#xc5;" u2="V" k="10" />
    <hkern u1="&#xc5;" u2="T" k="25" />
    <hkern u1="&#xc5;" u2="&#x2e;" k="-10" />
    <hkern u1="&#xc5;" u2="&#x2c;" k="-10" />
    <hkern u1="&#xc6;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc6;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc6;" u2="&#x152;" k="7" />
    <hkern u1="&#xc6;" u2="&#x150;" k="7" />
    <hkern u1="&#xc6;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc6;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x122;" k="7" />
    <hkern u1="&#xc6;" u2="&#x120;" k="7" />
    <hkern u1="&#xc6;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc6;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc6;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc6;" u2="&#x108;" k="7" />
    <hkern u1="&#xc6;" u2="&#x106;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc6;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc6;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc6;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc6;" u2="Q" k="7" />
    <hkern u1="&#xc6;" u2="O" k="7" />
    <hkern u1="&#xc6;" u2="G" k="7" />
    <hkern u1="&#xc6;" u2="C" k="7" />
    <hkern u1="&#xc6;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc6;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xc7;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc7;" u2="&#x2014;" k="75" />
    <hkern u1="&#xc7;" u2="&#x2013;" k="75" />
    <hkern u1="&#xc7;" u2="&#x1e85;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1e83;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1e81;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1ff;" k="10" />
    <hkern u1="&#xc7;" u2="&#x1fe;" k="20" />
    <hkern u1="&#xc7;" u2="&#x175;" k="10" />
    <hkern u1="&#xc7;" u2="&#x153;" k="10" />
    <hkern u1="&#xc7;" u2="&#x152;" k="20" />
    <hkern u1="&#xc7;" u2="&#x151;" k="10" />
    <hkern u1="&#xc7;" u2="&#x150;" k="20" />
    <hkern u1="&#xc7;" u2="&#x14f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x14e;" k="20" />
    <hkern u1="&#xc7;" u2="&#x14d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x14c;" k="20" />
    <hkern u1="&#xc7;" u2="&#x123;" k="10" />
    <hkern u1="&#xc7;" u2="&#x122;" k="20" />
    <hkern u1="&#xc7;" u2="&#x121;" k="10" />
    <hkern u1="&#xc7;" u2="&#x120;" k="20" />
    <hkern u1="&#xc7;" u2="&#x11f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x11e;" k="20" />
    <hkern u1="&#xc7;" u2="&#x11d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x11c;" k="20" />
    <hkern u1="&#xc7;" u2="&#x11b;" k="10" />
    <hkern u1="&#xc7;" u2="&#x119;" k="10" />
    <hkern u1="&#xc7;" u2="&#x117;" k="10" />
    <hkern u1="&#xc7;" u2="&#x115;" k="10" />
    <hkern u1="&#xc7;" u2="&#x113;" k="10" />
    <hkern u1="&#xc7;" u2="&#x111;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10f;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10d;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10c;" k="20" />
    <hkern u1="&#xc7;" u2="&#x10b;" k="10" />
    <hkern u1="&#xc7;" u2="&#x10a;" k="20" />
    <hkern u1="&#xc7;" u2="&#x109;" k="10" />
    <hkern u1="&#xc7;" u2="&#x108;" k="20" />
    <hkern u1="&#xc7;" u2="&#x107;" k="10" />
    <hkern u1="&#xc7;" u2="&#x106;" k="20" />
    <hkern u1="&#xc7;" u2="&#xf8;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc7;" u2="&#xef;" k="-10" />
    <hkern u1="&#xc7;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc7;" u2="&#xea;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc7;" u2="&#xd8;" k="20" />
    <hkern u1="&#xc7;" u2="&#xd6;" k="20" />
    <hkern u1="&#xc7;" u2="&#xd5;" k="20" />
    <hkern u1="&#xc7;" u2="&#xd4;" k="20" />
    <hkern u1="&#xc7;" u2="&#xd3;" k="20" />
    <hkern u1="&#xc7;" u2="&#xd2;" k="20" />
    <hkern u1="&#xc7;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc7;" u2="&#xa9;" k="20" />
    <hkern u1="&#xc7;" u2="z" k="-10" />
    <hkern u1="&#xc7;" u2="x" k="-10" />
    <hkern u1="&#xc7;" u2="w" k="10" />
    <hkern u1="&#xc7;" u2="v" k="10" />
    <hkern u1="&#xc7;" u2="q" k="10" />
    <hkern u1="&#xc7;" u2="o" k="10" />
    <hkern u1="&#xc7;" u2="g" k="10" />
    <hkern u1="&#xc7;" u2="e" k="10" />
    <hkern u1="&#xc7;" u2="d" k="10" />
    <hkern u1="&#xc7;" u2="c" k="10" />
    <hkern u1="&#xc7;" u2="_" k="-20" />
    <hkern u1="&#xc7;" u2="Q" k="20" />
    <hkern u1="&#xc7;" u2="O" k="20" />
    <hkern u1="&#xc7;" u2="G" k="20" />
    <hkern u1="&#xc7;" u2="C" k="20" />
    <hkern u1="&#xc7;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="75" />
    <hkern u1="&#xc7;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xc8;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc8;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc8;" u2="&#x152;" k="7" />
    <hkern u1="&#xc8;" u2="&#x150;" k="7" />
    <hkern u1="&#xc8;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc8;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x122;" k="7" />
    <hkern u1="&#xc8;" u2="&#x120;" k="7" />
    <hkern u1="&#xc8;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc8;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc8;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc8;" u2="&#x108;" k="7" />
    <hkern u1="&#xc8;" u2="&#x106;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc8;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc8;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc8;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc8;" u2="Q" k="7" />
    <hkern u1="&#xc8;" u2="O" k="7" />
    <hkern u1="&#xc8;" u2="G" k="7" />
    <hkern u1="&#xc8;" u2="C" k="7" />
    <hkern u1="&#xc8;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc8;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xc9;" u2="&#x152;" k="7" />
    <hkern u1="&#xc9;" u2="&#x150;" k="7" />
    <hkern u1="&#xc9;" u2="&#x14e;" k="7" />
    <hkern u1="&#xc9;" u2="&#x14c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x122;" k="7" />
    <hkern u1="&#xc9;" u2="&#x120;" k="7" />
    <hkern u1="&#xc9;" u2="&#x11e;" k="7" />
    <hkern u1="&#xc9;" u2="&#x11c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x10c;" k="7" />
    <hkern u1="&#xc9;" u2="&#x10a;" k="7" />
    <hkern u1="&#xc9;" u2="&#x108;" k="7" />
    <hkern u1="&#xc9;" u2="&#x106;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd8;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd6;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd5;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd4;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd3;" k="7" />
    <hkern u1="&#xc9;" u2="&#xd2;" k="7" />
    <hkern u1="&#xc9;" u2="&#xc7;" k="7" />
    <hkern u1="&#xc9;" u2="&#xa9;" k="7" />
    <hkern u1="&#xc9;" u2="Q" k="7" />
    <hkern u1="&#xc9;" u2="O" k="7" />
    <hkern u1="&#xc9;" u2="G" k="7" />
    <hkern u1="&#xc9;" u2="C" k="7" />
    <hkern u1="&#xc9;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xc9;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xca;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xca;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xca;" u2="&#x152;" k="7" />
    <hkern u1="&#xca;" u2="&#x150;" k="7" />
    <hkern u1="&#xca;" u2="&#x14e;" k="7" />
    <hkern u1="&#xca;" u2="&#x14c;" k="7" />
    <hkern u1="&#xca;" u2="&#x122;" k="7" />
    <hkern u1="&#xca;" u2="&#x120;" k="7" />
    <hkern u1="&#xca;" u2="&#x11e;" k="7" />
    <hkern u1="&#xca;" u2="&#x11c;" k="7" />
    <hkern u1="&#xca;" u2="&#x10c;" k="7" />
    <hkern u1="&#xca;" u2="&#x10a;" k="7" />
    <hkern u1="&#xca;" u2="&#x108;" k="7" />
    <hkern u1="&#xca;" u2="&#x106;" k="7" />
    <hkern u1="&#xca;" u2="&#xd8;" k="7" />
    <hkern u1="&#xca;" u2="&#xd6;" k="7" />
    <hkern u1="&#xca;" u2="&#xd5;" k="7" />
    <hkern u1="&#xca;" u2="&#xd4;" k="7" />
    <hkern u1="&#xca;" u2="&#xd3;" k="7" />
    <hkern u1="&#xca;" u2="&#xd2;" k="7" />
    <hkern u1="&#xca;" u2="&#xc7;" k="7" />
    <hkern u1="&#xca;" u2="&#xa9;" k="7" />
    <hkern u1="&#xca;" u2="Q" k="7" />
    <hkern u1="&#xca;" u2="O" k="7" />
    <hkern u1="&#xca;" u2="G" k="7" />
    <hkern u1="&#xca;" u2="C" k="7" />
    <hkern u1="&#xca;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xca;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x1fe;" k="7" />
    <hkern u1="&#xcb;" u2="&#x152;" k="7" />
    <hkern u1="&#xcb;" u2="&#x150;" k="7" />
    <hkern u1="&#xcb;" u2="&#x14e;" k="7" />
    <hkern u1="&#xcb;" u2="&#x14c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x122;" k="7" />
    <hkern u1="&#xcb;" u2="&#x120;" k="7" />
    <hkern u1="&#xcb;" u2="&#x11e;" k="7" />
    <hkern u1="&#xcb;" u2="&#x11c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x10c;" k="7" />
    <hkern u1="&#xcb;" u2="&#x10a;" k="7" />
    <hkern u1="&#xcb;" u2="&#x108;" k="7" />
    <hkern u1="&#xcb;" u2="&#x106;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd8;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd6;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd5;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd4;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd3;" k="7" />
    <hkern u1="&#xcb;" u2="&#xd2;" k="7" />
    <hkern u1="&#xcb;" u2="&#xc7;" k="7" />
    <hkern u1="&#xcb;" u2="&#xa9;" k="7" />
    <hkern u1="&#xcb;" u2="Q" k="7" />
    <hkern u1="&#xcb;" u2="O" k="7" />
    <hkern u1="&#xcb;" u2="G" k="7" />
    <hkern u1="&#xcb;" u2="C" k="7" />
    <hkern u1="&#xcb;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xcb;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xd0;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd0;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x164;" k="15" />
    <hkern u1="&#xd0;" u2="&#x162;" k="15" />
    <hkern u1="&#xd0;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd0;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd0;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd0;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd0;" u2="X" k="10" />
    <hkern u1="&#xd0;" u2="V" k="10" />
    <hkern u1="&#xd0;" u2="T" k="15" />
    <hkern u1="&#xd0;" u2="Q" k="-5" />
    <hkern u1="&#xd0;" u2="O" k="-5" />
    <hkern u1="&#xd0;" u2="J" k="-20" />
    <hkern u1="&#xd0;" u2="G" k="-5" />
    <hkern u1="&#xd0;" u2="C" k="-5" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd0;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd2;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd2;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x164;" k="15" />
    <hkern u1="&#xd2;" u2="&#x162;" k="15" />
    <hkern u1="&#xd2;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd2;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd2;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd2;" u2="X" k="10" />
    <hkern u1="&#xd2;" u2="V" k="10" />
    <hkern u1="&#xd2;" u2="T" k="15" />
    <hkern u1="&#xd2;" u2="Q" k="-5" />
    <hkern u1="&#xd2;" u2="O" k="-5" />
    <hkern u1="&#xd2;" u2="J" k="-20" />
    <hkern u1="&#xd2;" u2="G" k="-5" />
    <hkern u1="&#xd2;" u2="C" k="-5" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd2;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd3;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd3;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x164;" k="15" />
    <hkern u1="&#xd3;" u2="&#x162;" k="15" />
    <hkern u1="&#xd3;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd3;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd3;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd3;" u2="X" k="10" />
    <hkern u1="&#xd3;" u2="V" k="10" />
    <hkern u1="&#xd3;" u2="T" k="15" />
    <hkern u1="&#xd3;" u2="Q" k="-5" />
    <hkern u1="&#xd3;" u2="O" k="-5" />
    <hkern u1="&#xd3;" u2="J" k="-20" />
    <hkern u1="&#xd3;" u2="G" k="-5" />
    <hkern u1="&#xd3;" u2="C" k="-5" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd3;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd4;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd4;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x164;" k="15" />
    <hkern u1="&#xd4;" u2="&#x162;" k="15" />
    <hkern u1="&#xd4;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd4;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd4;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd4;" u2="X" k="10" />
    <hkern u1="&#xd4;" u2="V" k="10" />
    <hkern u1="&#xd4;" u2="T" k="15" />
    <hkern u1="&#xd4;" u2="Q" k="-5" />
    <hkern u1="&#xd4;" u2="O" k="-5" />
    <hkern u1="&#xd4;" u2="J" k="-20" />
    <hkern u1="&#xd4;" u2="G" k="-5" />
    <hkern u1="&#xd4;" u2="C" k="-5" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd4;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd5;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd5;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x164;" k="15" />
    <hkern u1="&#xd5;" u2="&#x162;" k="15" />
    <hkern u1="&#xd5;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd5;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd5;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd5;" u2="X" k="10" />
    <hkern u1="&#xd5;" u2="V" k="10" />
    <hkern u1="&#xd5;" u2="T" k="15" />
    <hkern u1="&#xd5;" u2="Q" k="-5" />
    <hkern u1="&#xd5;" u2="O" k="-5" />
    <hkern u1="&#xd5;" u2="J" k="-20" />
    <hkern u1="&#xd5;" u2="G" k="-5" />
    <hkern u1="&#xd5;" u2="C" k="-5" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd5;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd6;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd6;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x164;" k="15" />
    <hkern u1="&#xd6;" u2="&#x162;" k="15" />
    <hkern u1="&#xd6;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd6;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd6;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd6;" u2="X" k="10" />
    <hkern u1="&#xd6;" u2="V" k="10" />
    <hkern u1="&#xd6;" u2="T" k="15" />
    <hkern u1="&#xd6;" u2="Q" k="-5" />
    <hkern u1="&#xd6;" u2="O" k="-5" />
    <hkern u1="&#xd6;" u2="J" k="-20" />
    <hkern u1="&#xd6;" u2="G" k="-5" />
    <hkern u1="&#xd6;" u2="C" k="-5" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd6;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="25" />
    <hkern u1="&#xd8;" u2="&#x2026;" k="25" />
    <hkern u1="&#xd8;" u2="&#x2014;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x2013;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x164;" k="15" />
    <hkern u1="&#xd8;" u2="&#x162;" k="15" />
    <hkern u1="&#xd8;" u2="&#x152;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x150;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x14e;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x14c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x134;" k="-20" />
    <hkern u1="&#xd8;" u2="&#x122;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x120;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x11e;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x11c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x10c;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x10a;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x108;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x106;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd8;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd6;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd5;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd4;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd3;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xd2;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xc7;" k="-5" />
    <hkern u1="&#xd8;" u2="&#xa9;" k="-5" />
    <hkern u1="&#xd8;" u2="X" k="10" />
    <hkern u1="&#xd8;" u2="V" k="10" />
    <hkern u1="&#xd8;" u2="T" k="15" />
    <hkern u1="&#xd8;" u2="Q" k="-5" />
    <hkern u1="&#xd8;" u2="O" k="-5" />
    <hkern u1="&#xd8;" u2="J" k="-20" />
    <hkern u1="&#xd8;" u2="G" k="-5" />
    <hkern u1="&#xd8;" u2="C" k="-5" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="25" />
    <hkern u1="&#xd8;" u2="&#x2d;" k="-15" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="25" />
    <hkern u1="&#xdd;" u2="X" k="-25" />
    <hkern u1="&#xdd;" u2="V" k="-15" />
    <hkern u1="&#xe6;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe6;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe6;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe6;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x174;" k="10" />
    <hkern u1="&#xe6;" u2="&#x164;" k="70" />
    <hkern u1="&#xe6;" u2="&#x162;" k="70" />
    <hkern u1="&#xe6;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe6;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe6;" u2="s" k="5" />
    <hkern u1="&#xe6;" u2="W" k="10" />
    <hkern u1="&#xe6;" u2="V" k="30" />
    <hkern u1="&#xe6;" u2="T" k="70" />
    <hkern u1="&#xe7;" u2="&#x2026;" k="-20" />
    <hkern u1="&#xe7;" u2="&#x201c;" k="-20" />
    <hkern u1="&#xe7;" u2="&#x2018;" k="-20" />
    <hkern u1="&#xe7;" u2="&#x1ff;" k="10" />
    <hkern u1="&#xe7;" u2="&#x164;" k="40" />
    <hkern u1="&#xe7;" u2="&#x162;" k="40" />
    <hkern u1="&#xe7;" u2="&#x153;" k="10" />
    <hkern u1="&#xe7;" u2="&#x151;" k="10" />
    <hkern u1="&#xe7;" u2="&#x14f;" k="10" />
    <hkern u1="&#xe7;" u2="&#x14d;" k="10" />
    <hkern u1="&#xe7;" u2="&#x123;" k="10" />
    <hkern u1="&#xe7;" u2="&#x121;" k="10" />
    <hkern u1="&#xe7;" u2="&#x11f;" k="10" />
    <hkern u1="&#xe7;" u2="&#x11d;" k="10" />
    <hkern u1="&#xe7;" u2="&#x11b;" k="10" />
    <hkern u1="&#xe7;" u2="&#x119;" k="10" />
    <hkern u1="&#xe7;" u2="&#x117;" k="10" />
    <hkern u1="&#xe7;" u2="&#x115;" k="10" />
    <hkern u1="&#xe7;" u2="&#x113;" k="10" />
    <hkern u1="&#xe7;" u2="&#x111;" k="10" />
    <hkern u1="&#xe7;" u2="&#x10f;" k="10" />
    <hkern u1="&#xe7;" u2="&#x10d;" k="10" />
    <hkern u1="&#xe7;" u2="&#x10b;" k="10" />
    <hkern u1="&#xe7;" u2="&#x109;" k="10" />
    <hkern u1="&#xe7;" u2="&#x107;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf8;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf6;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf5;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf4;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf3;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf2;" k="10" />
    <hkern u1="&#xe7;" u2="&#xf0;" k="10" />
    <hkern u1="&#xe7;" u2="&#xeb;" k="10" />
    <hkern u1="&#xe7;" u2="&#xea;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe9;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe8;" k="10" />
    <hkern u1="&#xe7;" u2="&#xe7;" k="10" />
    <hkern u1="&#xe7;" u2="x" k="-13" />
    <hkern u1="&#xe7;" u2="q" k="10" />
    <hkern u1="&#xe7;" u2="o" k="10" />
    <hkern u1="&#xe7;" u2="g" k="10" />
    <hkern u1="&#xe7;" u2="e" k="10" />
    <hkern u1="&#xe7;" u2="d" k="10" />
    <hkern u1="&#xe7;" u2="c" k="10" />
    <hkern u1="&#xe7;" u2="T" k="40" />
    <hkern u1="&#xe7;" u2="&#x2e;" k="-20" />
    <hkern u1="&#xe7;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xe8;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe8;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe8;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe8;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x174;" k="10" />
    <hkern u1="&#xe8;" u2="&#x164;" k="70" />
    <hkern u1="&#xe8;" u2="&#x162;" k="70" />
    <hkern u1="&#xe8;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe8;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe8;" u2="s" k="-5" />
    <hkern u1="&#xe8;" u2="W" k="10" />
    <hkern u1="&#xe8;" u2="V" k="30" />
    <hkern u1="&#xe8;" u2="T" k="70" />
    <hkern u1="&#xe9;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xe9;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xe9;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xe9;" u2="&#x219;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x174;" k="10" />
    <hkern u1="&#xe9;" u2="&#x164;" k="70" />
    <hkern u1="&#xe9;" u2="&#x162;" k="70" />
    <hkern u1="&#xe9;" u2="&#x161;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xe9;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xe9;" u2="s" k="-5" />
    <hkern u1="&#xe9;" u2="W" k="10" />
    <hkern u1="&#xe9;" u2="V" k="30" />
    <hkern u1="&#xe9;" u2="T" k="70" />
    <hkern u1="&#xea;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xea;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xea;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xea;" u2="&#x219;" k="-5" />
    <hkern u1="&#xea;" u2="&#x174;" k="10" />
    <hkern u1="&#xea;" u2="&#x164;" k="70" />
    <hkern u1="&#xea;" u2="&#x162;" k="70" />
    <hkern u1="&#xea;" u2="&#x161;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xea;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xea;" u2="s" k="-5" />
    <hkern u1="&#xea;" u2="W" k="10" />
    <hkern u1="&#xea;" u2="V" k="30" />
    <hkern u1="&#xea;" u2="T" k="70" />
    <hkern u1="&#xeb;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xeb;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xeb;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xeb;" u2="&#x219;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x174;" k="10" />
    <hkern u1="&#xeb;" u2="&#x164;" k="70" />
    <hkern u1="&#xeb;" u2="&#x162;" k="70" />
    <hkern u1="&#xeb;" u2="&#x161;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15f;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15d;" k="-5" />
    <hkern u1="&#xeb;" u2="&#x15b;" k="-5" />
    <hkern u1="&#xeb;" u2="s" k="-5" />
    <hkern u1="&#xeb;" u2="W" k="10" />
    <hkern u1="&#xeb;" u2="V" k="30" />
    <hkern u1="&#xeb;" u2="T" k="70" />
    <hkern u1="&#xf1;" u2="&#x164;" k="80" />
    <hkern u1="&#xf1;" u2="&#x162;" k="80" />
    <hkern u1="&#xf1;" u2="T" k="80" />
    <hkern u1="&#xf2;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf2;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf2;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf2;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf2;" u2="&#x174;" k="10" />
    <hkern u1="&#xf2;" u2="&#x164;" k="75" />
    <hkern u1="&#xf2;" u2="&#x162;" k="75" />
    <hkern u1="&#xf2;" u2="z" k="20" />
    <hkern u1="&#xf2;" u2="x" k="10" />
    <hkern u1="&#xf2;" u2="W" k="10" />
    <hkern u1="&#xf2;" u2="V" k="20" />
    <hkern u1="&#xf2;" u2="T" k="75" />
    <hkern u1="&#xf2;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf2;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf3;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf3;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf3;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf3;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf3;" u2="&#x174;" k="10" />
    <hkern u1="&#xf3;" u2="&#x164;" k="75" />
    <hkern u1="&#xf3;" u2="&#x162;" k="75" />
    <hkern u1="&#xf3;" u2="z" k="20" />
    <hkern u1="&#xf3;" u2="x" k="10" />
    <hkern u1="&#xf3;" u2="W" k="10" />
    <hkern u1="&#xf3;" u2="V" k="20" />
    <hkern u1="&#xf3;" u2="T" k="75" />
    <hkern u1="&#xf3;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf3;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf4;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf4;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf4;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf4;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf4;" u2="&#x174;" k="10" />
    <hkern u1="&#xf4;" u2="&#x164;" k="75" />
    <hkern u1="&#xf4;" u2="&#x162;" k="75" />
    <hkern u1="&#xf4;" u2="z" k="20" />
    <hkern u1="&#xf4;" u2="x" k="10" />
    <hkern u1="&#xf4;" u2="W" k="10" />
    <hkern u1="&#xf4;" u2="V" k="20" />
    <hkern u1="&#xf4;" u2="T" k="75" />
    <hkern u1="&#xf4;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf4;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf5;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf5;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf5;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf5;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf5;" u2="&#x174;" k="10" />
    <hkern u1="&#xf5;" u2="&#x164;" k="75" />
    <hkern u1="&#xf5;" u2="&#x162;" k="75" />
    <hkern u1="&#xf5;" u2="z" k="20" />
    <hkern u1="&#xf5;" u2="x" k="10" />
    <hkern u1="&#xf5;" u2="W" k="10" />
    <hkern u1="&#xf5;" u2="V" k="20" />
    <hkern u1="&#xf5;" u2="T" k="75" />
    <hkern u1="&#xf5;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf5;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf6;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf6;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf6;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf6;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf6;" u2="&#x174;" k="10" />
    <hkern u1="&#xf6;" u2="&#x164;" k="75" />
    <hkern u1="&#xf6;" u2="&#x162;" k="75" />
    <hkern u1="&#xf6;" u2="z" k="20" />
    <hkern u1="&#xf6;" u2="x" k="10" />
    <hkern u1="&#xf6;" u2="W" k="10" />
    <hkern u1="&#xf6;" u2="V" k="20" />
    <hkern u1="&#xf6;" u2="T" k="75" />
    <hkern u1="&#xf6;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf6;" u2="&#x2c;" k="20" />
    <hkern u1="&#xf8;" u2="&#x2026;" k="20" />
    <hkern u1="&#xf8;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf8;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf8;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf8;" u2="&#x174;" k="10" />
    <hkern u1="&#xf8;" u2="&#x164;" k="75" />
    <hkern u1="&#xf8;" u2="&#x162;" k="75" />
    <hkern u1="&#xf8;" u2="z" k="20" />
    <hkern u1="&#xf8;" u2="x" k="10" />
    <hkern u1="&#xf8;" u2="W" k="10" />
    <hkern u1="&#xf8;" u2="V" k="20" />
    <hkern u1="&#xf8;" u2="T" k="75" />
    <hkern u1="&#xf8;" u2="&#x2e;" k="20" />
    <hkern u1="&#xf8;" u2="&#x2c;" k="20" />
    <hkern u1="&#xfd;" u2="&#x164;" k="70" />
    <hkern u1="&#xfd;" u2="&#x162;" k="70" />
    <hkern u1="&#xfd;" u2="V" k="20" />
    <hkern u1="&#xfd;" u2="T" k="70" />
    <hkern u1="&#xfe;" u2="&#x2026;" k="20" />
    <hkern u1="&#xfe;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xfe;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xfe;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xfe;" u2="&#x174;" k="10" />
    <hkern u1="&#xfe;" u2="&#x164;" k="75" />
    <hkern u1="&#xfe;" u2="&#x162;" k="75" />
    <hkern u1="&#xfe;" u2="z" k="20" />
    <hkern u1="&#xfe;" u2="x" k="10" />
    <hkern u1="&#xfe;" u2="W" k="10" />
    <hkern u1="&#xfe;" u2="V" k="20" />
    <hkern u1="&#xfe;" u2="T" k="75" />
    <hkern u1="&#xfe;" u2="&#x2e;" k="20" />
    <hkern u1="&#xfe;" u2="&#x2c;" k="20" />
    <hkern u1="&#xff;" u2="&#x164;" k="70" />
    <hkern u1="&#xff;" u2="&#x162;" k="70" />
    <hkern u1="&#xff;" u2="V" k="20" />
    <hkern u1="&#xff;" u2="T" k="70" />
    <hkern u1="&#x100;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x100;" u2="&#x164;" k="25" />
    <hkern u1="&#x100;" u2="&#x162;" k="25" />
    <hkern u1="&#x100;" u2="V" k="10" />
    <hkern u1="&#x100;" u2="T" k="25" />
    <hkern u1="&#x100;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x100;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x102;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x102;" u2="&#x164;" k="25" />
    <hkern u1="&#x102;" u2="&#x162;" k="25" />
    <hkern u1="&#x102;" u2="V" k="10" />
    <hkern u1="&#x102;" u2="T" k="25" />
    <hkern u1="&#x102;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x102;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x104;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x104;" u2="&#x164;" k="25" />
    <hkern u1="&#x104;" u2="&#x162;" k="25" />
    <hkern u1="&#x104;" u2="V" k="10" />
    <hkern u1="&#x104;" u2="T" k="25" />
    <hkern u1="&#x104;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x106;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x106;" u2="&#x2014;" k="75" />
    <hkern u1="&#x106;" u2="&#x2013;" k="75" />
    <hkern u1="&#x106;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x106;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x106;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x106;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x106;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x106;" u2="&#x175;" k="10" />
    <hkern u1="&#x106;" u2="&#x153;" k="10" />
    <hkern u1="&#x106;" u2="&#x152;" k="20" />
    <hkern u1="&#x106;" u2="&#x151;" k="10" />
    <hkern u1="&#x106;" u2="&#x150;" k="20" />
    <hkern u1="&#x106;" u2="&#x14f;" k="10" />
    <hkern u1="&#x106;" u2="&#x14e;" k="20" />
    <hkern u1="&#x106;" u2="&#x14d;" k="10" />
    <hkern u1="&#x106;" u2="&#x14c;" k="20" />
    <hkern u1="&#x106;" u2="&#x123;" k="10" />
    <hkern u1="&#x106;" u2="&#x122;" k="20" />
    <hkern u1="&#x106;" u2="&#x121;" k="10" />
    <hkern u1="&#x106;" u2="&#x120;" k="20" />
    <hkern u1="&#x106;" u2="&#x11f;" k="10" />
    <hkern u1="&#x106;" u2="&#x11e;" k="20" />
    <hkern u1="&#x106;" u2="&#x11d;" k="10" />
    <hkern u1="&#x106;" u2="&#x11c;" k="20" />
    <hkern u1="&#x106;" u2="&#x11b;" k="10" />
    <hkern u1="&#x106;" u2="&#x119;" k="10" />
    <hkern u1="&#x106;" u2="&#x117;" k="10" />
    <hkern u1="&#x106;" u2="&#x115;" k="10" />
    <hkern u1="&#x106;" u2="&#x113;" k="10" />
    <hkern u1="&#x106;" u2="&#x111;" k="10" />
    <hkern u1="&#x106;" u2="&#x10f;" k="10" />
    <hkern u1="&#x106;" u2="&#x10d;" k="10" />
    <hkern u1="&#x106;" u2="&#x10c;" k="20" />
    <hkern u1="&#x106;" u2="&#x10b;" k="10" />
    <hkern u1="&#x106;" u2="&#x10a;" k="20" />
    <hkern u1="&#x106;" u2="&#x109;" k="10" />
    <hkern u1="&#x106;" u2="&#x108;" k="20" />
    <hkern u1="&#x106;" u2="&#x107;" k="10" />
    <hkern u1="&#x106;" u2="&#x106;" k="20" />
    <hkern u1="&#x106;" u2="&#xf8;" k="10" />
    <hkern u1="&#x106;" u2="&#xf6;" k="10" />
    <hkern u1="&#x106;" u2="&#xf5;" k="10" />
    <hkern u1="&#x106;" u2="&#xf4;" k="10" />
    <hkern u1="&#x106;" u2="&#xf3;" k="10" />
    <hkern u1="&#x106;" u2="&#xf2;" k="10" />
    <hkern u1="&#x106;" u2="&#xf0;" k="10" />
    <hkern u1="&#x106;" u2="&#xef;" k="-10" />
    <hkern u1="&#x106;" u2="&#xeb;" k="10" />
    <hkern u1="&#x106;" u2="&#xea;" k="10" />
    <hkern u1="&#x106;" u2="&#xe9;" k="10" />
    <hkern u1="&#x106;" u2="&#xe8;" k="10" />
    <hkern u1="&#x106;" u2="&#xe7;" k="10" />
    <hkern u1="&#x106;" u2="&#xd8;" k="20" />
    <hkern u1="&#x106;" u2="&#xd6;" k="20" />
    <hkern u1="&#x106;" u2="&#xd5;" k="20" />
    <hkern u1="&#x106;" u2="&#xd4;" k="20" />
    <hkern u1="&#x106;" u2="&#xd3;" k="20" />
    <hkern u1="&#x106;" u2="&#xd2;" k="20" />
    <hkern u1="&#x106;" u2="&#xc7;" k="20" />
    <hkern u1="&#x106;" u2="&#xa9;" k="20" />
    <hkern u1="&#x106;" u2="z" k="-10" />
    <hkern u1="&#x106;" u2="x" k="-10" />
    <hkern u1="&#x106;" u2="w" k="10" />
    <hkern u1="&#x106;" u2="v" k="10" />
    <hkern u1="&#x106;" u2="q" k="10" />
    <hkern u1="&#x106;" u2="o" k="10" />
    <hkern u1="&#x106;" u2="g" k="10" />
    <hkern u1="&#x106;" u2="e" k="10" />
    <hkern u1="&#x106;" u2="d" k="10" />
    <hkern u1="&#x106;" u2="c" k="10" />
    <hkern u1="&#x106;" u2="_" k="-20" />
    <hkern u1="&#x106;" u2="Q" k="20" />
    <hkern u1="&#x106;" u2="O" k="20" />
    <hkern u1="&#x106;" u2="G" k="20" />
    <hkern u1="&#x106;" u2="C" k="20" />
    <hkern u1="&#x106;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x106;" u2="&#x2d;" k="75" />
    <hkern u1="&#x106;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x107;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x107;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x107;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x107;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x107;" u2="&#x164;" k="40" />
    <hkern u1="&#x107;" u2="&#x162;" k="40" />
    <hkern u1="&#x107;" u2="&#x153;" k="10" />
    <hkern u1="&#x107;" u2="&#x151;" k="10" />
    <hkern u1="&#x107;" u2="&#x14f;" k="10" />
    <hkern u1="&#x107;" u2="&#x14d;" k="10" />
    <hkern u1="&#x107;" u2="&#x123;" k="10" />
    <hkern u1="&#x107;" u2="&#x121;" k="10" />
    <hkern u1="&#x107;" u2="&#x11f;" k="10" />
    <hkern u1="&#x107;" u2="&#x11d;" k="10" />
    <hkern u1="&#x107;" u2="&#x11b;" k="10" />
    <hkern u1="&#x107;" u2="&#x119;" k="10" />
    <hkern u1="&#x107;" u2="&#x117;" k="10" />
    <hkern u1="&#x107;" u2="&#x115;" k="10" />
    <hkern u1="&#x107;" u2="&#x113;" k="10" />
    <hkern u1="&#x107;" u2="&#x111;" k="10" />
    <hkern u1="&#x107;" u2="&#x10f;" k="10" />
    <hkern u1="&#x107;" u2="&#x10d;" k="10" />
    <hkern u1="&#x107;" u2="&#x10b;" k="10" />
    <hkern u1="&#x107;" u2="&#x109;" k="10" />
    <hkern u1="&#x107;" u2="&#x107;" k="10" />
    <hkern u1="&#x107;" u2="&#xf8;" k="10" />
    <hkern u1="&#x107;" u2="&#xf6;" k="10" />
    <hkern u1="&#x107;" u2="&#xf5;" k="10" />
    <hkern u1="&#x107;" u2="&#xf4;" k="10" />
    <hkern u1="&#x107;" u2="&#xf3;" k="10" />
    <hkern u1="&#x107;" u2="&#xf2;" k="10" />
    <hkern u1="&#x107;" u2="&#xf0;" k="10" />
    <hkern u1="&#x107;" u2="&#xeb;" k="10" />
    <hkern u1="&#x107;" u2="&#xea;" k="10" />
    <hkern u1="&#x107;" u2="&#xe9;" k="10" />
    <hkern u1="&#x107;" u2="&#xe8;" k="10" />
    <hkern u1="&#x107;" u2="&#xe7;" k="10" />
    <hkern u1="&#x107;" u2="x" k="-13" />
    <hkern u1="&#x107;" u2="q" k="10" />
    <hkern u1="&#x107;" u2="o" k="10" />
    <hkern u1="&#x107;" u2="g" k="10" />
    <hkern u1="&#x107;" u2="e" k="10" />
    <hkern u1="&#x107;" u2="d" k="10" />
    <hkern u1="&#x107;" u2="c" k="10" />
    <hkern u1="&#x107;" u2="T" k="40" />
    <hkern u1="&#x107;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x107;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x108;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x108;" u2="&#x2014;" k="75" />
    <hkern u1="&#x108;" u2="&#x2013;" k="75" />
    <hkern u1="&#x108;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x108;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x108;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x108;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x108;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x108;" u2="&#x175;" k="10" />
    <hkern u1="&#x108;" u2="&#x153;" k="10" />
    <hkern u1="&#x108;" u2="&#x152;" k="20" />
    <hkern u1="&#x108;" u2="&#x151;" k="10" />
    <hkern u1="&#x108;" u2="&#x150;" k="20" />
    <hkern u1="&#x108;" u2="&#x14f;" k="10" />
    <hkern u1="&#x108;" u2="&#x14e;" k="20" />
    <hkern u1="&#x108;" u2="&#x14d;" k="10" />
    <hkern u1="&#x108;" u2="&#x14c;" k="20" />
    <hkern u1="&#x108;" u2="&#x123;" k="10" />
    <hkern u1="&#x108;" u2="&#x122;" k="20" />
    <hkern u1="&#x108;" u2="&#x121;" k="10" />
    <hkern u1="&#x108;" u2="&#x120;" k="20" />
    <hkern u1="&#x108;" u2="&#x11f;" k="10" />
    <hkern u1="&#x108;" u2="&#x11e;" k="20" />
    <hkern u1="&#x108;" u2="&#x11d;" k="10" />
    <hkern u1="&#x108;" u2="&#x11c;" k="20" />
    <hkern u1="&#x108;" u2="&#x11b;" k="10" />
    <hkern u1="&#x108;" u2="&#x119;" k="10" />
    <hkern u1="&#x108;" u2="&#x117;" k="10" />
    <hkern u1="&#x108;" u2="&#x115;" k="10" />
    <hkern u1="&#x108;" u2="&#x113;" k="10" />
    <hkern u1="&#x108;" u2="&#x111;" k="10" />
    <hkern u1="&#x108;" u2="&#x10f;" k="10" />
    <hkern u1="&#x108;" u2="&#x10d;" k="10" />
    <hkern u1="&#x108;" u2="&#x10c;" k="20" />
    <hkern u1="&#x108;" u2="&#x10b;" k="10" />
    <hkern u1="&#x108;" u2="&#x10a;" k="20" />
    <hkern u1="&#x108;" u2="&#x109;" k="10" />
    <hkern u1="&#x108;" u2="&#x108;" k="20" />
    <hkern u1="&#x108;" u2="&#x107;" k="10" />
    <hkern u1="&#x108;" u2="&#x106;" k="20" />
    <hkern u1="&#x108;" u2="&#xf8;" k="10" />
    <hkern u1="&#x108;" u2="&#xf6;" k="10" />
    <hkern u1="&#x108;" u2="&#xf5;" k="10" />
    <hkern u1="&#x108;" u2="&#xf4;" k="10" />
    <hkern u1="&#x108;" u2="&#xf3;" k="10" />
    <hkern u1="&#x108;" u2="&#xf2;" k="10" />
    <hkern u1="&#x108;" u2="&#xf0;" k="10" />
    <hkern u1="&#x108;" u2="&#xef;" k="-10" />
    <hkern u1="&#x108;" u2="&#xeb;" k="10" />
    <hkern u1="&#x108;" u2="&#xea;" k="10" />
    <hkern u1="&#x108;" u2="&#xe9;" k="10" />
    <hkern u1="&#x108;" u2="&#xe8;" k="10" />
    <hkern u1="&#x108;" u2="&#xe7;" k="10" />
    <hkern u1="&#x108;" u2="&#xd8;" k="20" />
    <hkern u1="&#x108;" u2="&#xd6;" k="20" />
    <hkern u1="&#x108;" u2="&#xd5;" k="20" />
    <hkern u1="&#x108;" u2="&#xd4;" k="20" />
    <hkern u1="&#x108;" u2="&#xd3;" k="20" />
    <hkern u1="&#x108;" u2="&#xd2;" k="20" />
    <hkern u1="&#x108;" u2="&#xc7;" k="20" />
    <hkern u1="&#x108;" u2="&#xa9;" k="20" />
    <hkern u1="&#x108;" u2="z" k="-10" />
    <hkern u1="&#x108;" u2="x" k="-10" />
    <hkern u1="&#x108;" u2="w" k="10" />
    <hkern u1="&#x108;" u2="v" k="10" />
    <hkern u1="&#x108;" u2="q" k="10" />
    <hkern u1="&#x108;" u2="o" k="10" />
    <hkern u1="&#x108;" u2="g" k="10" />
    <hkern u1="&#x108;" u2="e" k="10" />
    <hkern u1="&#x108;" u2="d" k="10" />
    <hkern u1="&#x108;" u2="c" k="10" />
    <hkern u1="&#x108;" u2="_" k="-20" />
    <hkern u1="&#x108;" u2="Q" k="20" />
    <hkern u1="&#x108;" u2="O" k="20" />
    <hkern u1="&#x108;" u2="G" k="20" />
    <hkern u1="&#x108;" u2="C" k="20" />
    <hkern u1="&#x108;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x108;" u2="&#x2d;" k="75" />
    <hkern u1="&#x108;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x109;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x109;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x109;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x109;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x109;" u2="&#x164;" k="40" />
    <hkern u1="&#x109;" u2="&#x162;" k="40" />
    <hkern u1="&#x109;" u2="&#x153;" k="10" />
    <hkern u1="&#x109;" u2="&#x151;" k="10" />
    <hkern u1="&#x109;" u2="&#x14f;" k="10" />
    <hkern u1="&#x109;" u2="&#x14d;" k="10" />
    <hkern u1="&#x109;" u2="&#x123;" k="10" />
    <hkern u1="&#x109;" u2="&#x121;" k="10" />
    <hkern u1="&#x109;" u2="&#x11f;" k="10" />
    <hkern u1="&#x109;" u2="&#x11d;" k="10" />
    <hkern u1="&#x109;" u2="&#x11b;" k="10" />
    <hkern u1="&#x109;" u2="&#x119;" k="10" />
    <hkern u1="&#x109;" u2="&#x117;" k="10" />
    <hkern u1="&#x109;" u2="&#x115;" k="10" />
    <hkern u1="&#x109;" u2="&#x113;" k="10" />
    <hkern u1="&#x109;" u2="&#x111;" k="10" />
    <hkern u1="&#x109;" u2="&#x10f;" k="10" />
    <hkern u1="&#x109;" u2="&#x10d;" k="10" />
    <hkern u1="&#x109;" u2="&#x10b;" k="10" />
    <hkern u1="&#x109;" u2="&#x109;" k="10" />
    <hkern u1="&#x109;" u2="&#x107;" k="10" />
    <hkern u1="&#x109;" u2="&#xf8;" k="10" />
    <hkern u1="&#x109;" u2="&#xf6;" k="10" />
    <hkern u1="&#x109;" u2="&#xf5;" k="10" />
    <hkern u1="&#x109;" u2="&#xf4;" k="10" />
    <hkern u1="&#x109;" u2="&#xf3;" k="10" />
    <hkern u1="&#x109;" u2="&#xf2;" k="10" />
    <hkern u1="&#x109;" u2="&#xf0;" k="10" />
    <hkern u1="&#x109;" u2="&#xeb;" k="10" />
    <hkern u1="&#x109;" u2="&#xea;" k="10" />
    <hkern u1="&#x109;" u2="&#xe9;" k="10" />
    <hkern u1="&#x109;" u2="&#xe8;" k="10" />
    <hkern u1="&#x109;" u2="&#xe7;" k="10" />
    <hkern u1="&#x109;" u2="x" k="-13" />
    <hkern u1="&#x109;" u2="q" k="10" />
    <hkern u1="&#x109;" u2="o" k="10" />
    <hkern u1="&#x109;" u2="g" k="10" />
    <hkern u1="&#x109;" u2="e" k="10" />
    <hkern u1="&#x109;" u2="d" k="10" />
    <hkern u1="&#x109;" u2="c" k="10" />
    <hkern u1="&#x109;" u2="T" k="40" />
    <hkern u1="&#x109;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x109;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10a;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x10a;" u2="&#x2014;" k="75" />
    <hkern u1="&#x10a;" u2="&#x2013;" k="75" />
    <hkern u1="&#x10a;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x10a;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x10a;" u2="&#x175;" k="10" />
    <hkern u1="&#x10a;" u2="&#x153;" k="10" />
    <hkern u1="&#x10a;" u2="&#x152;" k="20" />
    <hkern u1="&#x10a;" u2="&#x151;" k="10" />
    <hkern u1="&#x10a;" u2="&#x150;" k="20" />
    <hkern u1="&#x10a;" u2="&#x14f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x14e;" k="20" />
    <hkern u1="&#x10a;" u2="&#x14d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x14c;" k="20" />
    <hkern u1="&#x10a;" u2="&#x123;" k="10" />
    <hkern u1="&#x10a;" u2="&#x122;" k="20" />
    <hkern u1="&#x10a;" u2="&#x121;" k="10" />
    <hkern u1="&#x10a;" u2="&#x120;" k="20" />
    <hkern u1="&#x10a;" u2="&#x11f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x11e;" k="20" />
    <hkern u1="&#x10a;" u2="&#x11d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x11c;" k="20" />
    <hkern u1="&#x10a;" u2="&#x11b;" k="10" />
    <hkern u1="&#x10a;" u2="&#x119;" k="10" />
    <hkern u1="&#x10a;" u2="&#x117;" k="10" />
    <hkern u1="&#x10a;" u2="&#x115;" k="10" />
    <hkern u1="&#x10a;" u2="&#x113;" k="10" />
    <hkern u1="&#x10a;" u2="&#x111;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10f;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10d;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10c;" k="20" />
    <hkern u1="&#x10a;" u2="&#x10b;" k="10" />
    <hkern u1="&#x10a;" u2="&#x10a;" k="20" />
    <hkern u1="&#x10a;" u2="&#x109;" k="10" />
    <hkern u1="&#x10a;" u2="&#x108;" k="20" />
    <hkern u1="&#x10a;" u2="&#x107;" k="10" />
    <hkern u1="&#x10a;" u2="&#x106;" k="20" />
    <hkern u1="&#x10a;" u2="&#xf8;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf6;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf5;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf4;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf3;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf2;" k="10" />
    <hkern u1="&#x10a;" u2="&#xf0;" k="10" />
    <hkern u1="&#x10a;" u2="&#xef;" k="-10" />
    <hkern u1="&#x10a;" u2="&#xeb;" k="10" />
    <hkern u1="&#x10a;" u2="&#xea;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe9;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe8;" k="10" />
    <hkern u1="&#x10a;" u2="&#xe7;" k="10" />
    <hkern u1="&#x10a;" u2="&#xd8;" k="20" />
    <hkern u1="&#x10a;" u2="&#xd6;" k="20" />
    <hkern u1="&#x10a;" u2="&#xd5;" k="20" />
    <hkern u1="&#x10a;" u2="&#xd4;" k="20" />
    <hkern u1="&#x10a;" u2="&#xd3;" k="20" />
    <hkern u1="&#x10a;" u2="&#xd2;" k="20" />
    <hkern u1="&#x10a;" u2="&#xc7;" k="20" />
    <hkern u1="&#x10a;" u2="&#xa9;" k="20" />
    <hkern u1="&#x10a;" u2="z" k="-10" />
    <hkern u1="&#x10a;" u2="x" k="-10" />
    <hkern u1="&#x10a;" u2="w" k="10" />
    <hkern u1="&#x10a;" u2="v" k="10" />
    <hkern u1="&#x10a;" u2="q" k="10" />
    <hkern u1="&#x10a;" u2="o" k="10" />
    <hkern u1="&#x10a;" u2="g" k="10" />
    <hkern u1="&#x10a;" u2="e" k="10" />
    <hkern u1="&#x10a;" u2="d" k="10" />
    <hkern u1="&#x10a;" u2="c" k="10" />
    <hkern u1="&#x10a;" u2="_" k="-20" />
    <hkern u1="&#x10a;" u2="Q" k="20" />
    <hkern u1="&#x10a;" u2="O" k="20" />
    <hkern u1="&#x10a;" u2="G" k="20" />
    <hkern u1="&#x10a;" u2="C" k="20" />
    <hkern u1="&#x10a;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x10a;" u2="&#x2d;" k="75" />
    <hkern u1="&#x10a;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x10b;" u2="&#x164;" k="40" />
    <hkern u1="&#x10b;" u2="&#x162;" k="40" />
    <hkern u1="&#x10b;" u2="&#x153;" k="10" />
    <hkern u1="&#x10b;" u2="&#x151;" k="10" />
    <hkern u1="&#x10b;" u2="&#x14f;" k="10" />
    <hkern u1="&#x10b;" u2="&#x14d;" k="10" />
    <hkern u1="&#x10b;" u2="&#x123;" k="10" />
    <hkern u1="&#x10b;" u2="&#x121;" k="10" />
    <hkern u1="&#x10b;" u2="&#x11f;" k="10" />
    <hkern u1="&#x10b;" u2="&#x11d;" k="10" />
    <hkern u1="&#x10b;" u2="&#x11b;" k="10" />
    <hkern u1="&#x10b;" u2="&#x119;" k="10" />
    <hkern u1="&#x10b;" u2="&#x117;" k="10" />
    <hkern u1="&#x10b;" u2="&#x115;" k="10" />
    <hkern u1="&#x10b;" u2="&#x113;" k="10" />
    <hkern u1="&#x10b;" u2="&#x111;" k="10" />
    <hkern u1="&#x10b;" u2="&#x10f;" k="10" />
    <hkern u1="&#x10b;" u2="&#x10d;" k="10" />
    <hkern u1="&#x10b;" u2="&#x10b;" k="10" />
    <hkern u1="&#x10b;" u2="&#x109;" k="10" />
    <hkern u1="&#x10b;" u2="&#x107;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf8;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf6;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf5;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf4;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf3;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf2;" k="10" />
    <hkern u1="&#x10b;" u2="&#xf0;" k="10" />
    <hkern u1="&#x10b;" u2="&#xeb;" k="10" />
    <hkern u1="&#x10b;" u2="&#xea;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe9;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe8;" k="10" />
    <hkern u1="&#x10b;" u2="&#xe7;" k="10" />
    <hkern u1="&#x10b;" u2="x" k="-13" />
    <hkern u1="&#x10b;" u2="q" k="10" />
    <hkern u1="&#x10b;" u2="o" k="10" />
    <hkern u1="&#x10b;" u2="g" k="10" />
    <hkern u1="&#x10b;" u2="e" k="10" />
    <hkern u1="&#x10b;" u2="d" k="10" />
    <hkern u1="&#x10b;" u2="c" k="10" />
    <hkern u1="&#x10b;" u2="T" k="40" />
    <hkern u1="&#x10b;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x10b;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10c;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x10c;" u2="&#x2014;" k="75" />
    <hkern u1="&#x10c;" u2="&#x2013;" k="75" />
    <hkern u1="&#x10c;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x10c;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x10c;" u2="&#x175;" k="10" />
    <hkern u1="&#x10c;" u2="&#x153;" k="10" />
    <hkern u1="&#x10c;" u2="&#x152;" k="20" />
    <hkern u1="&#x10c;" u2="&#x151;" k="10" />
    <hkern u1="&#x10c;" u2="&#x150;" k="20" />
    <hkern u1="&#x10c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x14e;" k="20" />
    <hkern u1="&#x10c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x14c;" k="20" />
    <hkern u1="&#x10c;" u2="&#x123;" k="10" />
    <hkern u1="&#x10c;" u2="&#x122;" k="20" />
    <hkern u1="&#x10c;" u2="&#x121;" k="10" />
    <hkern u1="&#x10c;" u2="&#x120;" k="20" />
    <hkern u1="&#x10c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x11e;" k="20" />
    <hkern u1="&#x10c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x11c;" k="20" />
    <hkern u1="&#x10c;" u2="&#x11b;" k="10" />
    <hkern u1="&#x10c;" u2="&#x119;" k="10" />
    <hkern u1="&#x10c;" u2="&#x117;" k="10" />
    <hkern u1="&#x10c;" u2="&#x115;" k="10" />
    <hkern u1="&#x10c;" u2="&#x113;" k="10" />
    <hkern u1="&#x10c;" u2="&#x111;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10f;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10c;" k="20" />
    <hkern u1="&#x10c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x10c;" u2="&#x10a;" k="20" />
    <hkern u1="&#x10c;" u2="&#x109;" k="10" />
    <hkern u1="&#x10c;" u2="&#x108;" k="20" />
    <hkern u1="&#x10c;" u2="&#x107;" k="10" />
    <hkern u1="&#x10c;" u2="&#x106;" k="20" />
    <hkern u1="&#x10c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x10c;" u2="&#xf0;" k="10" />
    <hkern u1="&#x10c;" u2="&#xef;" k="-10" />
    <hkern u1="&#x10c;" u2="&#xeb;" k="10" />
    <hkern u1="&#x10c;" u2="&#xea;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe9;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe8;" k="10" />
    <hkern u1="&#x10c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x10c;" u2="&#xd8;" k="20" />
    <hkern u1="&#x10c;" u2="&#xd6;" k="20" />
    <hkern u1="&#x10c;" u2="&#xd5;" k="20" />
    <hkern u1="&#x10c;" u2="&#xd4;" k="20" />
    <hkern u1="&#x10c;" u2="&#xd3;" k="20" />
    <hkern u1="&#x10c;" u2="&#xd2;" k="20" />
    <hkern u1="&#x10c;" u2="&#xc7;" k="20" />
    <hkern u1="&#x10c;" u2="&#xa9;" k="20" />
    <hkern u1="&#x10c;" u2="z" k="-10" />
    <hkern u1="&#x10c;" u2="x" k="-10" />
    <hkern u1="&#x10c;" u2="w" k="10" />
    <hkern u1="&#x10c;" u2="v" k="10" />
    <hkern u1="&#x10c;" u2="q" k="10" />
    <hkern u1="&#x10c;" u2="o" k="10" />
    <hkern u1="&#x10c;" u2="g" k="10" />
    <hkern u1="&#x10c;" u2="e" k="10" />
    <hkern u1="&#x10c;" u2="d" k="10" />
    <hkern u1="&#x10c;" u2="c" k="10" />
    <hkern u1="&#x10c;" u2="_" k="-20" />
    <hkern u1="&#x10c;" u2="Q" k="20" />
    <hkern u1="&#x10c;" u2="O" k="20" />
    <hkern u1="&#x10c;" u2="G" k="20" />
    <hkern u1="&#x10c;" u2="C" k="20" />
    <hkern u1="&#x10c;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x10c;" u2="&#x2d;" k="75" />
    <hkern u1="&#x10c;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x10d;" u2="&#x164;" k="40" />
    <hkern u1="&#x10d;" u2="&#x162;" k="40" />
    <hkern u1="&#x10d;" u2="&#x153;" k="10" />
    <hkern u1="&#x10d;" u2="&#x151;" k="10" />
    <hkern u1="&#x10d;" u2="&#x14f;" k="10" />
    <hkern u1="&#x10d;" u2="&#x14d;" k="10" />
    <hkern u1="&#x10d;" u2="&#x123;" k="10" />
    <hkern u1="&#x10d;" u2="&#x121;" k="10" />
    <hkern u1="&#x10d;" u2="&#x11f;" k="10" />
    <hkern u1="&#x10d;" u2="&#x11d;" k="10" />
    <hkern u1="&#x10d;" u2="&#x11b;" k="10" />
    <hkern u1="&#x10d;" u2="&#x119;" k="10" />
    <hkern u1="&#x10d;" u2="&#x117;" k="10" />
    <hkern u1="&#x10d;" u2="&#x115;" k="10" />
    <hkern u1="&#x10d;" u2="&#x113;" k="10" />
    <hkern u1="&#x10d;" u2="&#x111;" k="10" />
    <hkern u1="&#x10d;" u2="&#x10f;" k="10" />
    <hkern u1="&#x10d;" u2="&#x10d;" k="10" />
    <hkern u1="&#x10d;" u2="&#x10b;" k="10" />
    <hkern u1="&#x10d;" u2="&#x109;" k="10" />
    <hkern u1="&#x10d;" u2="&#x107;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf8;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf6;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf5;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf4;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf3;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf2;" k="10" />
    <hkern u1="&#x10d;" u2="&#xf0;" k="10" />
    <hkern u1="&#x10d;" u2="&#xeb;" k="10" />
    <hkern u1="&#x10d;" u2="&#xea;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe9;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe8;" k="10" />
    <hkern u1="&#x10d;" u2="&#xe7;" k="10" />
    <hkern u1="&#x10d;" u2="x" k="-13" />
    <hkern u1="&#x10d;" u2="q" k="10" />
    <hkern u1="&#x10d;" u2="o" k="10" />
    <hkern u1="&#x10d;" u2="g" k="10" />
    <hkern u1="&#x10d;" u2="e" k="10" />
    <hkern u1="&#x10d;" u2="d" k="10" />
    <hkern u1="&#x10d;" u2="c" k="10" />
    <hkern u1="&#x10d;" u2="T" k="40" />
    <hkern u1="&#x10d;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x10d;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x10e;" u2="&#x2026;" k="25" />
    <hkern u1="&#x10e;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x164;" k="15" />
    <hkern u1="&#x10e;" u2="&#x162;" k="15" />
    <hkern u1="&#x10e;" u2="&#x152;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x150;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x134;" k="-20" />
    <hkern u1="&#x10e;" u2="&#x122;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x120;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x108;" k="-5" />
    <hkern u1="&#x10e;" u2="&#x106;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x10e;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x10e;" u2="X" k="10" />
    <hkern u1="&#x10e;" u2="V" k="10" />
    <hkern u1="&#x10e;" u2="T" k="15" />
    <hkern u1="&#x10e;" u2="Q" k="-5" />
    <hkern u1="&#x10e;" u2="O" k="-5" />
    <hkern u1="&#x10e;" u2="J" k="-20" />
    <hkern u1="&#x10e;" u2="G" k="-5" />
    <hkern u1="&#x10e;" u2="C" k="-5" />
    <hkern u1="&#x10e;" u2="&#x2e;" k="25" />
    <hkern u1="&#x10e;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x10e;" u2="&#x2c;" k="25" />
    <hkern u1="&#x110;" u2="&#x2026;" k="25" />
    <hkern u1="&#x110;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x110;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x110;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x110;" u2="&#x164;" k="15" />
    <hkern u1="&#x110;" u2="&#x162;" k="15" />
    <hkern u1="&#x110;" u2="&#x152;" k="-5" />
    <hkern u1="&#x110;" u2="&#x150;" k="-5" />
    <hkern u1="&#x110;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x110;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x134;" k="-20" />
    <hkern u1="&#x110;" u2="&#x122;" k="-5" />
    <hkern u1="&#x110;" u2="&#x120;" k="-5" />
    <hkern u1="&#x110;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x110;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x110;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x110;" u2="&#x108;" k="-5" />
    <hkern u1="&#x110;" u2="&#x106;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x110;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x110;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x110;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x110;" u2="X" k="10" />
    <hkern u1="&#x110;" u2="V" k="10" />
    <hkern u1="&#x110;" u2="T" k="15" />
    <hkern u1="&#x110;" u2="Q" k="-5" />
    <hkern u1="&#x110;" u2="O" k="-5" />
    <hkern u1="&#x110;" u2="J" k="-20" />
    <hkern u1="&#x110;" u2="G" k="-5" />
    <hkern u1="&#x110;" u2="C" k="-5" />
    <hkern u1="&#x110;" u2="&#x2e;" k="25" />
    <hkern u1="&#x110;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x110;" u2="&#x2c;" k="25" />
    <hkern u1="&#x112;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x112;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x112;" u2="&#x152;" k="7" />
    <hkern u1="&#x112;" u2="&#x150;" k="7" />
    <hkern u1="&#x112;" u2="&#x14e;" k="7" />
    <hkern u1="&#x112;" u2="&#x14c;" k="7" />
    <hkern u1="&#x112;" u2="&#x122;" k="7" />
    <hkern u1="&#x112;" u2="&#x120;" k="7" />
    <hkern u1="&#x112;" u2="&#x11e;" k="7" />
    <hkern u1="&#x112;" u2="&#x11c;" k="7" />
    <hkern u1="&#x112;" u2="&#x10c;" k="7" />
    <hkern u1="&#x112;" u2="&#x10a;" k="7" />
    <hkern u1="&#x112;" u2="&#x108;" k="7" />
    <hkern u1="&#x112;" u2="&#x106;" k="7" />
    <hkern u1="&#x112;" u2="&#xd8;" k="7" />
    <hkern u1="&#x112;" u2="&#xd6;" k="7" />
    <hkern u1="&#x112;" u2="&#xd5;" k="7" />
    <hkern u1="&#x112;" u2="&#xd4;" k="7" />
    <hkern u1="&#x112;" u2="&#xd3;" k="7" />
    <hkern u1="&#x112;" u2="&#xd2;" k="7" />
    <hkern u1="&#x112;" u2="&#xc7;" k="7" />
    <hkern u1="&#x112;" u2="&#xa9;" k="7" />
    <hkern u1="&#x112;" u2="Q" k="7" />
    <hkern u1="&#x112;" u2="O" k="7" />
    <hkern u1="&#x112;" u2="G" k="7" />
    <hkern u1="&#x112;" u2="C" k="7" />
    <hkern u1="&#x112;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x112;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x113;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x113;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x113;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x113;" u2="&#x219;" k="-5" />
    <hkern u1="&#x113;" u2="&#x174;" k="10" />
    <hkern u1="&#x113;" u2="&#x164;" k="70" />
    <hkern u1="&#x113;" u2="&#x162;" k="70" />
    <hkern u1="&#x113;" u2="&#x161;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x113;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x113;" u2="s" k="-5" />
    <hkern u1="&#x113;" u2="W" k="10" />
    <hkern u1="&#x113;" u2="V" k="30" />
    <hkern u1="&#x113;" u2="T" k="70" />
    <hkern u1="&#x114;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x114;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x114;" u2="&#x152;" k="7" />
    <hkern u1="&#x114;" u2="&#x150;" k="7" />
    <hkern u1="&#x114;" u2="&#x14e;" k="7" />
    <hkern u1="&#x114;" u2="&#x14c;" k="7" />
    <hkern u1="&#x114;" u2="&#x122;" k="7" />
    <hkern u1="&#x114;" u2="&#x120;" k="7" />
    <hkern u1="&#x114;" u2="&#x11e;" k="7" />
    <hkern u1="&#x114;" u2="&#x11c;" k="7" />
    <hkern u1="&#x114;" u2="&#x10c;" k="7" />
    <hkern u1="&#x114;" u2="&#x10a;" k="7" />
    <hkern u1="&#x114;" u2="&#x108;" k="7" />
    <hkern u1="&#x114;" u2="&#x106;" k="7" />
    <hkern u1="&#x114;" u2="&#xd8;" k="7" />
    <hkern u1="&#x114;" u2="&#xd6;" k="7" />
    <hkern u1="&#x114;" u2="&#xd5;" k="7" />
    <hkern u1="&#x114;" u2="&#xd4;" k="7" />
    <hkern u1="&#x114;" u2="&#xd3;" k="7" />
    <hkern u1="&#x114;" u2="&#xd2;" k="7" />
    <hkern u1="&#x114;" u2="&#xc7;" k="7" />
    <hkern u1="&#x114;" u2="&#xa9;" k="7" />
    <hkern u1="&#x114;" u2="Q" k="7" />
    <hkern u1="&#x114;" u2="O" k="7" />
    <hkern u1="&#x114;" u2="G" k="7" />
    <hkern u1="&#x114;" u2="C" k="7" />
    <hkern u1="&#x114;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x114;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x115;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x115;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x115;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x115;" u2="&#x219;" k="-5" />
    <hkern u1="&#x115;" u2="&#x174;" k="10" />
    <hkern u1="&#x115;" u2="&#x164;" k="70" />
    <hkern u1="&#x115;" u2="&#x162;" k="70" />
    <hkern u1="&#x115;" u2="&#x161;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x115;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x115;" u2="s" k="-5" />
    <hkern u1="&#x115;" u2="W" k="10" />
    <hkern u1="&#x115;" u2="V" k="30" />
    <hkern u1="&#x115;" u2="T" k="70" />
    <hkern u1="&#x116;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x116;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x116;" u2="&#x152;" k="7" />
    <hkern u1="&#x116;" u2="&#x150;" k="7" />
    <hkern u1="&#x116;" u2="&#x14e;" k="7" />
    <hkern u1="&#x116;" u2="&#x14c;" k="7" />
    <hkern u1="&#x116;" u2="&#x122;" k="7" />
    <hkern u1="&#x116;" u2="&#x120;" k="7" />
    <hkern u1="&#x116;" u2="&#x11e;" k="7" />
    <hkern u1="&#x116;" u2="&#x11c;" k="7" />
    <hkern u1="&#x116;" u2="&#x10c;" k="7" />
    <hkern u1="&#x116;" u2="&#x10a;" k="7" />
    <hkern u1="&#x116;" u2="&#x108;" k="7" />
    <hkern u1="&#x116;" u2="&#x106;" k="7" />
    <hkern u1="&#x116;" u2="&#xd8;" k="7" />
    <hkern u1="&#x116;" u2="&#xd6;" k="7" />
    <hkern u1="&#x116;" u2="&#xd5;" k="7" />
    <hkern u1="&#x116;" u2="&#xd4;" k="7" />
    <hkern u1="&#x116;" u2="&#xd3;" k="7" />
    <hkern u1="&#x116;" u2="&#xd2;" k="7" />
    <hkern u1="&#x116;" u2="&#xc7;" k="7" />
    <hkern u1="&#x116;" u2="&#xa9;" k="7" />
    <hkern u1="&#x116;" u2="Q" k="7" />
    <hkern u1="&#x116;" u2="O" k="7" />
    <hkern u1="&#x116;" u2="G" k="7" />
    <hkern u1="&#x116;" u2="C" k="7" />
    <hkern u1="&#x116;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x116;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x117;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x117;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x117;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x117;" u2="&#x219;" k="-5" />
    <hkern u1="&#x117;" u2="&#x174;" k="10" />
    <hkern u1="&#x117;" u2="&#x164;" k="70" />
    <hkern u1="&#x117;" u2="&#x162;" k="70" />
    <hkern u1="&#x117;" u2="&#x161;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x117;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x117;" u2="s" k="-5" />
    <hkern u1="&#x117;" u2="W" k="10" />
    <hkern u1="&#x117;" u2="V" k="30" />
    <hkern u1="&#x117;" u2="T" k="70" />
    <hkern u1="&#x118;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x118;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x118;" u2="&#x152;" k="7" />
    <hkern u1="&#x118;" u2="&#x150;" k="7" />
    <hkern u1="&#x118;" u2="&#x14e;" k="7" />
    <hkern u1="&#x118;" u2="&#x14c;" k="7" />
    <hkern u1="&#x118;" u2="&#x122;" k="7" />
    <hkern u1="&#x118;" u2="&#x120;" k="7" />
    <hkern u1="&#x118;" u2="&#x11e;" k="7" />
    <hkern u1="&#x118;" u2="&#x11c;" k="7" />
    <hkern u1="&#x118;" u2="&#x10c;" k="7" />
    <hkern u1="&#x118;" u2="&#x10a;" k="7" />
    <hkern u1="&#x118;" u2="&#x108;" k="7" />
    <hkern u1="&#x118;" u2="&#x106;" k="7" />
    <hkern u1="&#x118;" u2="&#xd8;" k="7" />
    <hkern u1="&#x118;" u2="&#xd6;" k="7" />
    <hkern u1="&#x118;" u2="&#xd5;" k="7" />
    <hkern u1="&#x118;" u2="&#xd4;" k="7" />
    <hkern u1="&#x118;" u2="&#xd3;" k="7" />
    <hkern u1="&#x118;" u2="&#xd2;" k="7" />
    <hkern u1="&#x118;" u2="&#xc7;" k="7" />
    <hkern u1="&#x118;" u2="&#xa9;" k="7" />
    <hkern u1="&#x118;" u2="Q" k="7" />
    <hkern u1="&#x118;" u2="O" k="7" />
    <hkern u1="&#x118;" u2="G" k="7" />
    <hkern u1="&#x118;" u2="C" k="7" />
    <hkern u1="&#x118;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x119;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x119;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x119;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x119;" u2="&#x219;" k="-5" />
    <hkern u1="&#x119;" u2="&#x174;" k="10" />
    <hkern u1="&#x119;" u2="&#x164;" k="70" />
    <hkern u1="&#x119;" u2="&#x162;" k="70" />
    <hkern u1="&#x119;" u2="&#x161;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x119;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x119;" u2="s" k="-5" />
    <hkern u1="&#x119;" u2="W" k="10" />
    <hkern u1="&#x119;" u2="V" k="30" />
    <hkern u1="&#x119;" u2="T" k="70" />
    <hkern u1="&#x11a;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x11a;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x11a;" u2="&#x152;" k="7" />
    <hkern u1="&#x11a;" u2="&#x150;" k="7" />
    <hkern u1="&#x11a;" u2="&#x14e;" k="7" />
    <hkern u1="&#x11a;" u2="&#x14c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x122;" k="7" />
    <hkern u1="&#x11a;" u2="&#x120;" k="7" />
    <hkern u1="&#x11a;" u2="&#x11e;" k="7" />
    <hkern u1="&#x11a;" u2="&#x11c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x10c;" k="7" />
    <hkern u1="&#x11a;" u2="&#x10a;" k="7" />
    <hkern u1="&#x11a;" u2="&#x108;" k="7" />
    <hkern u1="&#x11a;" u2="&#x106;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd8;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd6;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd5;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd4;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd3;" k="7" />
    <hkern u1="&#x11a;" u2="&#xd2;" k="7" />
    <hkern u1="&#x11a;" u2="&#xc7;" k="7" />
    <hkern u1="&#x11a;" u2="&#xa9;" k="7" />
    <hkern u1="&#x11a;" u2="Q" k="7" />
    <hkern u1="&#x11a;" u2="O" k="7" />
    <hkern u1="&#x11a;" u2="G" k="7" />
    <hkern u1="&#x11a;" u2="C" k="7" />
    <hkern u1="&#x11a;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x11a;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x11b;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x11b;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x11b;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x11b;" u2="&#x219;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x174;" k="10" />
    <hkern u1="&#x11b;" u2="&#x164;" k="70" />
    <hkern u1="&#x11b;" u2="&#x162;" k="70" />
    <hkern u1="&#x11b;" u2="&#x161;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x11b;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x11b;" u2="s" k="-5" />
    <hkern u1="&#x11b;" u2="W" k="10" />
    <hkern u1="&#x11b;" u2="V" k="30" />
    <hkern u1="&#x11b;" u2="T" k="70" />
    <hkern u1="&#x11d;" u2="&#x164;" k="70" />
    <hkern u1="&#x11d;" u2="&#x162;" k="70" />
    <hkern u1="&#x11d;" u2="V" k="20" />
    <hkern u1="&#x11d;" u2="T" k="70" />
    <hkern u1="&#x11f;" u2="&#x164;" k="70" />
    <hkern u1="&#x11f;" u2="&#x162;" k="70" />
    <hkern u1="&#x11f;" u2="V" k="20" />
    <hkern u1="&#x11f;" u2="T" k="70" />
    <hkern u1="&#x121;" u2="&#x164;" k="70" />
    <hkern u1="&#x121;" u2="&#x162;" k="70" />
    <hkern u1="&#x121;" u2="V" k="20" />
    <hkern u1="&#x121;" u2="T" k="70" />
    <hkern u1="&#x123;" u2="&#x164;" k="70" />
    <hkern u1="&#x123;" u2="&#x162;" k="70" />
    <hkern u1="&#x123;" u2="V" k="20" />
    <hkern u1="&#x123;" u2="T" k="70" />
    <hkern u1="&#x125;" u2="&#x164;" k="80" />
    <hkern u1="&#x125;" u2="&#x162;" k="80" />
    <hkern u1="&#x125;" u2="T" k="80" />
    <hkern u1="&#x127;" u2="&#x164;" k="80" />
    <hkern u1="&#x127;" u2="&#x162;" k="80" />
    <hkern u1="&#x127;" u2="T" k="80" />
    <hkern u1="&#x136;" u2="&#x2026;" k="-50" />
    <hkern u1="&#x136;" u2="&#x2014;" k="10" />
    <hkern u1="&#x136;" u2="&#x2013;" k="10" />
    <hkern u1="&#x136;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x136;" u2="&#x152;" k="20" />
    <hkern u1="&#x136;" u2="&#x150;" k="20" />
    <hkern u1="&#x136;" u2="&#x14e;" k="20" />
    <hkern u1="&#x136;" u2="&#x14c;" k="20" />
    <hkern u1="&#x136;" u2="&#x122;" k="20" />
    <hkern u1="&#x136;" u2="&#x120;" k="20" />
    <hkern u1="&#x136;" u2="&#x11e;" k="20" />
    <hkern u1="&#x136;" u2="&#x11c;" k="20" />
    <hkern u1="&#x136;" u2="&#x10c;" k="20" />
    <hkern u1="&#x136;" u2="&#x10a;" k="20" />
    <hkern u1="&#x136;" u2="&#x108;" k="20" />
    <hkern u1="&#x136;" u2="&#x106;" k="20" />
    <hkern u1="&#x136;" u2="&#xef;" k="-20" />
    <hkern u1="&#x136;" u2="&#xd8;" k="20" />
    <hkern u1="&#x136;" u2="&#xd6;" k="20" />
    <hkern u1="&#x136;" u2="&#xd5;" k="20" />
    <hkern u1="&#x136;" u2="&#xd4;" k="20" />
    <hkern u1="&#x136;" u2="&#xd3;" k="20" />
    <hkern u1="&#x136;" u2="&#xd2;" k="20" />
    <hkern u1="&#x136;" u2="&#xc7;" k="20" />
    <hkern u1="&#x136;" u2="&#xa9;" k="20" />
    <hkern u1="&#x136;" u2="_" k="-50" />
    <hkern u1="&#x136;" u2="Q" k="20" />
    <hkern u1="&#x136;" u2="O" k="20" />
    <hkern u1="&#x136;" u2="G" k="20" />
    <hkern u1="&#x136;" u2="C" k="20" />
    <hkern u1="&#x136;" u2="&#x2e;" k="-50" />
    <hkern u1="&#x136;" u2="&#x2d;" k="10" />
    <hkern u1="&#x136;" u2="&#x2c;" k="-50" />
    <hkern u1="&#x137;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x137;" u2="&#x2014;" k="10" />
    <hkern u1="&#x137;" u2="&#x2013;" k="10" />
    <hkern u1="&#x137;" u2="&#x1ff;" k="8" />
    <hkern u1="&#x137;" u2="&#x1fd;" k="-10" />
    <hkern u1="&#x137;" u2="&#x1fb;" k="-10" />
    <hkern u1="&#x137;" u2="&#x164;" k="30" />
    <hkern u1="&#x137;" u2="&#x162;" k="30" />
    <hkern u1="&#x137;" u2="&#x153;" k="8" />
    <hkern u1="&#x137;" u2="&#x151;" k="8" />
    <hkern u1="&#x137;" u2="&#x14f;" k="8" />
    <hkern u1="&#x137;" u2="&#x14d;" k="8" />
    <hkern u1="&#x137;" u2="&#x123;" k="8" />
    <hkern u1="&#x137;" u2="&#x121;" k="8" />
    <hkern u1="&#x137;" u2="&#x11f;" k="8" />
    <hkern u1="&#x137;" u2="&#x11d;" k="8" />
    <hkern u1="&#x137;" u2="&#x11b;" k="8" />
    <hkern u1="&#x137;" u2="&#x119;" k="8" />
    <hkern u1="&#x137;" u2="&#x117;" k="8" />
    <hkern u1="&#x137;" u2="&#x115;" k="8" />
    <hkern u1="&#x137;" u2="&#x113;" k="8" />
    <hkern u1="&#x137;" u2="&#x111;" k="8" />
    <hkern u1="&#x137;" u2="&#x10f;" k="8" />
    <hkern u1="&#x137;" u2="&#x10d;" k="8" />
    <hkern u1="&#x137;" u2="&#x10b;" k="8" />
    <hkern u1="&#x137;" u2="&#x109;" k="8" />
    <hkern u1="&#x137;" u2="&#x107;" k="8" />
    <hkern u1="&#x137;" u2="&#x105;" k="-10" />
    <hkern u1="&#x137;" u2="&#x103;" k="-10" />
    <hkern u1="&#x137;" u2="&#x101;" k="-10" />
    <hkern u1="&#x137;" u2="&#xf8;" k="8" />
    <hkern u1="&#x137;" u2="&#xf6;" k="8" />
    <hkern u1="&#x137;" u2="&#xf5;" k="8" />
    <hkern u1="&#x137;" u2="&#xf4;" k="8" />
    <hkern u1="&#x137;" u2="&#xf3;" k="8" />
    <hkern u1="&#x137;" u2="&#xf2;" k="8" />
    <hkern u1="&#x137;" u2="&#xf0;" k="8" />
    <hkern u1="&#x137;" u2="&#xeb;" k="8" />
    <hkern u1="&#x137;" u2="&#xea;" k="8" />
    <hkern u1="&#x137;" u2="&#xe9;" k="8" />
    <hkern u1="&#x137;" u2="&#xe8;" k="8" />
    <hkern u1="&#x137;" u2="&#xe7;" k="8" />
    <hkern u1="&#x137;" u2="&#xe6;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe5;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe4;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe3;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe2;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe1;" k="-10" />
    <hkern u1="&#x137;" u2="&#xe0;" k="-10" />
    <hkern u1="&#x137;" u2="q" k="8" />
    <hkern u1="&#x137;" u2="o" k="8" />
    <hkern u1="&#x137;" u2="g" k="8" />
    <hkern u1="&#x137;" u2="e" k="8" />
    <hkern u1="&#x137;" u2="d" k="8" />
    <hkern u1="&#x137;" u2="c" k="8" />
    <hkern u1="&#x137;" u2="a" k="-10" />
    <hkern u1="&#x137;" u2="T" k="30" />
    <hkern u1="&#x137;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x137;" u2="&#x2d;" k="10" />
    <hkern u1="&#x137;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x139;" u2="&#x2122;" k="65" />
    <hkern u1="&#x139;" u2="&#x2026;" k="-40" />
    <hkern u1="&#x139;" u2="&#x201d;" k="90" />
    <hkern u1="&#x139;" u2="&#x201c;" k="90" />
    <hkern u1="&#x139;" u2="&#x2019;" k="90" />
    <hkern u1="&#x139;" u2="&#x2018;" k="90" />
    <hkern u1="&#x139;" u2="&#x2014;" k="90" />
    <hkern u1="&#x139;" u2="&#x2013;" k="90" />
    <hkern u1="&#x139;" u2="&#x1ef2;" k="105" />
    <hkern u1="&#x139;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x139;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x139;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x139;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x139;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x139;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x139;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x139;" u2="&#x178;" k="105" />
    <hkern u1="&#x139;" u2="&#x176;" k="105" />
    <hkern u1="&#x139;" u2="&#x175;" k="30" />
    <hkern u1="&#x139;" u2="&#x174;" k="55" />
    <hkern u1="&#x139;" u2="&#x164;" k="75" />
    <hkern u1="&#x139;" u2="&#x162;" k="75" />
    <hkern u1="&#x139;" u2="&#x152;" k="20" />
    <hkern u1="&#x139;" u2="&#x150;" k="20" />
    <hkern u1="&#x139;" u2="&#x14e;" k="20" />
    <hkern u1="&#x139;" u2="&#x14c;" k="20" />
    <hkern u1="&#x139;" u2="&#x122;" k="20" />
    <hkern u1="&#x139;" u2="&#x120;" k="20" />
    <hkern u1="&#x139;" u2="&#x11e;" k="20" />
    <hkern u1="&#x139;" u2="&#x11c;" k="20" />
    <hkern u1="&#x139;" u2="&#x10c;" k="20" />
    <hkern u1="&#x139;" u2="&#x10a;" k="20" />
    <hkern u1="&#x139;" u2="&#x108;" k="20" />
    <hkern u1="&#x139;" u2="&#x106;" k="20" />
    <hkern u1="&#x139;" u2="&#x105;" k="-30" />
    <hkern u1="&#x139;" u2="&#x103;" k="-30" />
    <hkern u1="&#x139;" u2="&#x101;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x139;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x139;" u2="&#xdd;" k="105" />
    <hkern u1="&#x139;" u2="&#xd8;" k="20" />
    <hkern u1="&#x139;" u2="&#xd6;" k="20" />
    <hkern u1="&#x139;" u2="&#xd5;" k="20" />
    <hkern u1="&#x139;" u2="&#xd4;" k="20" />
    <hkern u1="&#x139;" u2="&#xd3;" k="20" />
    <hkern u1="&#x139;" u2="&#xd2;" k="20" />
    <hkern u1="&#x139;" u2="&#xc7;" k="20" />
    <hkern u1="&#x139;" u2="&#xa9;" k="20" />
    <hkern u1="&#x139;" u2="w" k="30" />
    <hkern u1="&#x139;" u2="v" k="40" />
    <hkern u1="&#x139;" u2="a" k="-30" />
    <hkern u1="&#x139;" u2="_" k="-50" />
    <hkern u1="&#x139;" u2="Y" k="105" />
    <hkern u1="&#x139;" u2="W" k="55" />
    <hkern u1="&#x139;" u2="V" k="100" />
    <hkern u1="&#x139;" u2="T" k="75" />
    <hkern u1="&#x139;" u2="Q" k="20" />
    <hkern u1="&#x139;" u2="O" k="20" />
    <hkern u1="&#x139;" u2="G" k="20" />
    <hkern u1="&#x139;" u2="C" k="20" />
    <hkern u1="&#x139;" u2="&#x2e;" k="-40" />
    <hkern u1="&#x139;" u2="&#x2d;" k="90" />
    <hkern u1="&#x139;" u2="&#x2c;" k="-40" />
    <hkern u1="&#x139;" u2="&#x2a;" k="60" />
    <hkern u1="&#x139;" u2="&#x27;" k="100" />
    <hkern u1="&#x139;" u2="&#x22;" k="100" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="65" />
    <hkern u1="&#x13b;" u2="&#x2026;" k="-40" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="90" />
    <hkern u1="&#x13b;" u2="&#x201c;" k="90" />
    <hkern u1="&#x13b;" u2="&#x2019;" k="90" />
    <hkern u1="&#x13b;" u2="&#x2018;" k="90" />
    <hkern u1="&#x13b;" u2="&#x2014;" k="90" />
    <hkern u1="&#x13b;" u2="&#x2013;" k="90" />
    <hkern u1="&#x13b;" u2="&#x1ef2;" k="105" />
    <hkern u1="&#x13b;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x13b;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x13b;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x13b;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x13b;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x13b;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x178;" k="105" />
    <hkern u1="&#x13b;" u2="&#x176;" k="105" />
    <hkern u1="&#x13b;" u2="&#x175;" k="30" />
    <hkern u1="&#x13b;" u2="&#x174;" k="55" />
    <hkern u1="&#x13b;" u2="&#x164;" k="75" />
    <hkern u1="&#x13b;" u2="&#x162;" k="75" />
    <hkern u1="&#x13b;" u2="&#x152;" k="20" />
    <hkern u1="&#x13b;" u2="&#x150;" k="20" />
    <hkern u1="&#x13b;" u2="&#x14e;" k="20" />
    <hkern u1="&#x13b;" u2="&#x14c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x122;" k="20" />
    <hkern u1="&#x13b;" u2="&#x120;" k="20" />
    <hkern u1="&#x13b;" u2="&#x11e;" k="20" />
    <hkern u1="&#x13b;" u2="&#x11c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x10c;" k="20" />
    <hkern u1="&#x13b;" u2="&#x10a;" k="20" />
    <hkern u1="&#x13b;" u2="&#x108;" k="20" />
    <hkern u1="&#x13b;" u2="&#x106;" k="20" />
    <hkern u1="&#x13b;" u2="&#x105;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x103;" k="-30" />
    <hkern u1="&#x13b;" u2="&#x101;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x13b;" u2="&#xdd;" k="105" />
    <hkern u1="&#x13b;" u2="&#xd8;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd6;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd5;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd4;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd3;" k="20" />
    <hkern u1="&#x13b;" u2="&#xd2;" k="20" />
    <hkern u1="&#x13b;" u2="&#xc7;" k="20" />
    <hkern u1="&#x13b;" u2="&#xa9;" k="20" />
    <hkern u1="&#x13b;" u2="w" k="30" />
    <hkern u1="&#x13b;" u2="v" k="40" />
    <hkern u1="&#x13b;" u2="a" k="-30" />
    <hkern u1="&#x13b;" u2="_" k="-50" />
    <hkern u1="&#x13b;" u2="Y" k="105" />
    <hkern u1="&#x13b;" u2="W" k="55" />
    <hkern u1="&#x13b;" u2="V" k="100" />
    <hkern u1="&#x13b;" u2="T" k="75" />
    <hkern u1="&#x13b;" u2="Q" k="20" />
    <hkern u1="&#x13b;" u2="O" k="20" />
    <hkern u1="&#x13b;" u2="G" k="20" />
    <hkern u1="&#x13b;" u2="C" k="20" />
    <hkern u1="&#x13b;" u2="&#x2e;" k="-40" />
    <hkern u1="&#x13b;" u2="&#x2d;" k="90" />
    <hkern u1="&#x13b;" u2="&#x2c;" k="-40" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="60" />
    <hkern u1="&#x13b;" u2="&#x27;" k="100" />
    <hkern u1="&#x13b;" u2="&#x22;" k="100" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="65" />
    <hkern u1="&#x13d;" u2="&#x2026;" k="-40" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="90" />
    <hkern u1="&#x13d;" u2="&#x201c;" k="90" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="90" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="90" />
    <hkern u1="&#x13d;" u2="&#x2014;" k="90" />
    <hkern u1="&#x13d;" u2="&#x2013;" k="90" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="105" />
    <hkern u1="&#x13d;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x13d;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x13d;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x13d;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x13d;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x178;" k="105" />
    <hkern u1="&#x13d;" u2="&#x176;" k="105" />
    <hkern u1="&#x13d;" u2="&#x175;" k="30" />
    <hkern u1="&#x13d;" u2="&#x174;" k="55" />
    <hkern u1="&#x13d;" u2="&#x164;" k="75" />
    <hkern u1="&#x13d;" u2="&#x162;" k="75" />
    <hkern u1="&#x13d;" u2="&#x152;" k="20" />
    <hkern u1="&#x13d;" u2="&#x150;" k="20" />
    <hkern u1="&#x13d;" u2="&#x14e;" k="20" />
    <hkern u1="&#x13d;" u2="&#x14c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x122;" k="20" />
    <hkern u1="&#x13d;" u2="&#x120;" k="20" />
    <hkern u1="&#x13d;" u2="&#x11e;" k="20" />
    <hkern u1="&#x13d;" u2="&#x11c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x10c;" k="20" />
    <hkern u1="&#x13d;" u2="&#x10a;" k="20" />
    <hkern u1="&#x13d;" u2="&#x108;" k="20" />
    <hkern u1="&#x13d;" u2="&#x106;" k="20" />
    <hkern u1="&#x13d;" u2="&#x105;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x103;" k="-30" />
    <hkern u1="&#x13d;" u2="&#x101;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="105" />
    <hkern u1="&#x13d;" u2="&#xd8;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd6;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd5;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd4;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd3;" k="20" />
    <hkern u1="&#x13d;" u2="&#xd2;" k="20" />
    <hkern u1="&#x13d;" u2="&#xc7;" k="20" />
    <hkern u1="&#x13d;" u2="&#xa9;" k="20" />
    <hkern u1="&#x13d;" u2="w" k="30" />
    <hkern u1="&#x13d;" u2="v" k="40" />
    <hkern u1="&#x13d;" u2="a" k="-30" />
    <hkern u1="&#x13d;" u2="_" k="-50" />
    <hkern u1="&#x13d;" u2="Y" k="105" />
    <hkern u1="&#x13d;" u2="W" k="55" />
    <hkern u1="&#x13d;" u2="V" k="100" />
    <hkern u1="&#x13d;" u2="T" k="75" />
    <hkern u1="&#x13d;" u2="Q" k="20" />
    <hkern u1="&#x13d;" u2="O" k="20" />
    <hkern u1="&#x13d;" u2="G" k="20" />
    <hkern u1="&#x13d;" u2="C" k="20" />
    <hkern u1="&#x13d;" u2="&#x2e;" k="-40" />
    <hkern u1="&#x13d;" u2="&#x2d;" k="90" />
    <hkern u1="&#x13d;" u2="&#x2c;" k="-40" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="60" />
    <hkern u1="&#x13d;" u2="&#x27;" k="100" />
    <hkern u1="&#x13d;" u2="&#x22;" k="100" />
    <hkern u1="&#x141;" u2="&#x2122;" k="65" />
    <hkern u1="&#x141;" u2="&#x2026;" k="-40" />
    <hkern u1="&#x141;" u2="&#x201d;" k="90" />
    <hkern u1="&#x141;" u2="&#x201c;" k="90" />
    <hkern u1="&#x141;" u2="&#x2019;" k="90" />
    <hkern u1="&#x141;" u2="&#x2018;" k="90" />
    <hkern u1="&#x141;" u2="&#x2014;" k="90" />
    <hkern u1="&#x141;" u2="&#x2013;" k="90" />
    <hkern u1="&#x141;" u2="&#x1ef2;" k="105" />
    <hkern u1="&#x141;" u2="&#x1e85;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x141;" u2="&#x1e83;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x141;" u2="&#x1e81;" k="30" />
    <hkern u1="&#x141;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x141;" u2="&#x1fe;" k="20" />
    <hkern u1="&#x141;" u2="&#x1fd;" k="-30" />
    <hkern u1="&#x141;" u2="&#x1fb;" k="-30" />
    <hkern u1="&#x141;" u2="&#x178;" k="105" />
    <hkern u1="&#x141;" u2="&#x176;" k="105" />
    <hkern u1="&#x141;" u2="&#x175;" k="30" />
    <hkern u1="&#x141;" u2="&#x174;" k="55" />
    <hkern u1="&#x141;" u2="&#x164;" k="75" />
    <hkern u1="&#x141;" u2="&#x162;" k="75" />
    <hkern u1="&#x141;" u2="&#x152;" k="20" />
    <hkern u1="&#x141;" u2="&#x150;" k="20" />
    <hkern u1="&#x141;" u2="&#x14e;" k="20" />
    <hkern u1="&#x141;" u2="&#x14c;" k="20" />
    <hkern u1="&#x141;" u2="&#x122;" k="20" />
    <hkern u1="&#x141;" u2="&#x120;" k="20" />
    <hkern u1="&#x141;" u2="&#x11e;" k="20" />
    <hkern u1="&#x141;" u2="&#x11c;" k="20" />
    <hkern u1="&#x141;" u2="&#x10c;" k="20" />
    <hkern u1="&#x141;" u2="&#x10a;" k="20" />
    <hkern u1="&#x141;" u2="&#x108;" k="20" />
    <hkern u1="&#x141;" u2="&#x106;" k="20" />
    <hkern u1="&#x141;" u2="&#x105;" k="-30" />
    <hkern u1="&#x141;" u2="&#x103;" k="-30" />
    <hkern u1="&#x141;" u2="&#x101;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe6;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe5;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe3;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe2;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe1;" k="-30" />
    <hkern u1="&#x141;" u2="&#xe0;" k="-30" />
    <hkern u1="&#x141;" u2="&#xdd;" k="105" />
    <hkern u1="&#x141;" u2="&#xd8;" k="20" />
    <hkern u1="&#x141;" u2="&#xd6;" k="20" />
    <hkern u1="&#x141;" u2="&#xd5;" k="20" />
    <hkern u1="&#x141;" u2="&#xd4;" k="20" />
    <hkern u1="&#x141;" u2="&#xd3;" k="20" />
    <hkern u1="&#x141;" u2="&#xd2;" k="20" />
    <hkern u1="&#x141;" u2="&#xc7;" k="20" />
    <hkern u1="&#x141;" u2="&#xa9;" k="20" />
    <hkern u1="&#x141;" u2="w" k="30" />
    <hkern u1="&#x141;" u2="v" k="40" />
    <hkern u1="&#x141;" u2="a" k="-30" />
    <hkern u1="&#x141;" u2="_" k="-50" />
    <hkern u1="&#x141;" u2="Y" k="105" />
    <hkern u1="&#x141;" u2="W" k="55" />
    <hkern u1="&#x141;" u2="V" k="100" />
    <hkern u1="&#x141;" u2="T" k="75" />
    <hkern u1="&#x141;" u2="Q" k="20" />
    <hkern u1="&#x141;" u2="O" k="20" />
    <hkern u1="&#x141;" u2="G" k="20" />
    <hkern u1="&#x141;" u2="C" k="20" />
    <hkern u1="&#x141;" u2="&#x2e;" k="-40" />
    <hkern u1="&#x141;" u2="&#x2d;" k="90" />
    <hkern u1="&#x141;" u2="&#x2c;" k="-40" />
    <hkern u1="&#x141;" u2="&#x2a;" k="60" />
    <hkern u1="&#x141;" u2="&#x27;" k="100" />
    <hkern u1="&#x141;" u2="&#x22;" k="100" />
    <hkern u1="&#x144;" u2="&#x164;" k="80" />
    <hkern u1="&#x144;" u2="&#x162;" k="80" />
    <hkern u1="&#x144;" u2="T" k="80" />
    <hkern u1="&#x146;" u2="&#x164;" k="80" />
    <hkern u1="&#x146;" u2="&#x162;" k="80" />
    <hkern u1="&#x146;" u2="T" k="80" />
    <hkern u1="&#x148;" u2="&#x164;" k="80" />
    <hkern u1="&#x148;" u2="&#x162;" k="80" />
    <hkern u1="&#x148;" u2="T" k="80" />
    <hkern u1="&#x14b;" u2="&#x164;" k="80" />
    <hkern u1="&#x14b;" u2="&#x162;" k="80" />
    <hkern u1="&#x14b;" u2="T" k="80" />
    <hkern u1="&#x14c;" u2="&#x2026;" k="25" />
    <hkern u1="&#x14c;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x164;" k="15" />
    <hkern u1="&#x14c;" u2="&#x162;" k="15" />
    <hkern u1="&#x14c;" u2="&#x152;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x150;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x134;" k="-20" />
    <hkern u1="&#x14c;" u2="&#x122;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x120;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x108;" k="-5" />
    <hkern u1="&#x14c;" u2="&#x106;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x14c;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x14c;" u2="X" k="10" />
    <hkern u1="&#x14c;" u2="V" k="10" />
    <hkern u1="&#x14c;" u2="T" k="15" />
    <hkern u1="&#x14c;" u2="Q" k="-5" />
    <hkern u1="&#x14c;" u2="O" k="-5" />
    <hkern u1="&#x14c;" u2="J" k="-20" />
    <hkern u1="&#x14c;" u2="G" k="-5" />
    <hkern u1="&#x14c;" u2="C" k="-5" />
    <hkern u1="&#x14c;" u2="&#x2e;" k="25" />
    <hkern u1="&#x14c;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x14c;" u2="&#x2c;" k="25" />
    <hkern u1="&#x14d;" u2="&#x2026;" k="20" />
    <hkern u1="&#x14d;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x14d;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x14d;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x14d;" u2="&#x174;" k="10" />
    <hkern u1="&#x14d;" u2="&#x164;" k="75" />
    <hkern u1="&#x14d;" u2="&#x162;" k="75" />
    <hkern u1="&#x14d;" u2="z" k="20" />
    <hkern u1="&#x14d;" u2="x" k="10" />
    <hkern u1="&#x14d;" u2="W" k="10" />
    <hkern u1="&#x14d;" u2="V" k="20" />
    <hkern u1="&#x14d;" u2="T" k="75" />
    <hkern u1="&#x14d;" u2="&#x2e;" k="20" />
    <hkern u1="&#x14d;" u2="&#x2c;" k="20" />
    <hkern u1="&#x14e;" u2="&#x2026;" k="25" />
    <hkern u1="&#x14e;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x164;" k="15" />
    <hkern u1="&#x14e;" u2="&#x162;" k="15" />
    <hkern u1="&#x14e;" u2="&#x152;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x150;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x134;" k="-20" />
    <hkern u1="&#x14e;" u2="&#x122;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x120;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x108;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x106;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x14e;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x14e;" u2="X" k="10" />
    <hkern u1="&#x14e;" u2="V" k="10" />
    <hkern u1="&#x14e;" u2="T" k="15" />
    <hkern u1="&#x14e;" u2="Q" k="-5" />
    <hkern u1="&#x14e;" u2="O" k="-5" />
    <hkern u1="&#x14e;" u2="J" k="-20" />
    <hkern u1="&#x14e;" u2="G" k="-5" />
    <hkern u1="&#x14e;" u2="C" k="-5" />
    <hkern u1="&#x14e;" u2="&#x2e;" k="25" />
    <hkern u1="&#x14e;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x14e;" u2="&#x2c;" k="25" />
    <hkern u1="&#x14f;" u2="&#x2026;" k="20" />
    <hkern u1="&#x14f;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x14f;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x14f;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x14f;" u2="&#x174;" k="10" />
    <hkern u1="&#x14f;" u2="&#x164;" k="75" />
    <hkern u1="&#x14f;" u2="&#x162;" k="75" />
    <hkern u1="&#x14f;" u2="z" k="20" />
    <hkern u1="&#x14f;" u2="x" k="10" />
    <hkern u1="&#x14f;" u2="W" k="10" />
    <hkern u1="&#x14f;" u2="V" k="20" />
    <hkern u1="&#x14f;" u2="T" k="75" />
    <hkern u1="&#x14f;" u2="&#x2e;" k="20" />
    <hkern u1="&#x14f;" u2="&#x2c;" k="20" />
    <hkern u1="&#x150;" u2="&#x2026;" k="25" />
    <hkern u1="&#x150;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x150;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x150;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x150;" u2="&#x164;" k="15" />
    <hkern u1="&#x150;" u2="&#x162;" k="15" />
    <hkern u1="&#x150;" u2="&#x152;" k="-5" />
    <hkern u1="&#x150;" u2="&#x150;" k="-5" />
    <hkern u1="&#x150;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x150;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x134;" k="-20" />
    <hkern u1="&#x150;" u2="&#x122;" k="-5" />
    <hkern u1="&#x150;" u2="&#x120;" k="-5" />
    <hkern u1="&#x150;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x150;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x150;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x150;" u2="&#x108;" k="-5" />
    <hkern u1="&#x150;" u2="&#x106;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x150;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x150;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x150;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x150;" u2="X" k="10" />
    <hkern u1="&#x150;" u2="V" k="10" />
    <hkern u1="&#x150;" u2="T" k="15" />
    <hkern u1="&#x150;" u2="Q" k="-5" />
    <hkern u1="&#x150;" u2="O" k="-5" />
    <hkern u1="&#x150;" u2="J" k="-20" />
    <hkern u1="&#x150;" u2="G" k="-5" />
    <hkern u1="&#x150;" u2="C" k="-5" />
    <hkern u1="&#x150;" u2="&#x2e;" k="25" />
    <hkern u1="&#x150;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x150;" u2="&#x2c;" k="25" />
    <hkern u1="&#x151;" u2="&#x2026;" k="20" />
    <hkern u1="&#x151;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x151;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x151;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x151;" u2="&#x174;" k="10" />
    <hkern u1="&#x151;" u2="&#x164;" k="75" />
    <hkern u1="&#x151;" u2="&#x162;" k="75" />
    <hkern u1="&#x151;" u2="z" k="20" />
    <hkern u1="&#x151;" u2="x" k="10" />
    <hkern u1="&#x151;" u2="W" k="10" />
    <hkern u1="&#x151;" u2="V" k="20" />
    <hkern u1="&#x151;" u2="T" k="75" />
    <hkern u1="&#x151;" u2="&#x2e;" k="20" />
    <hkern u1="&#x151;" u2="&#x2c;" k="20" />
    <hkern u1="&#x152;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x152;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x152;" u2="&#x152;" k="7" />
    <hkern u1="&#x152;" u2="&#x150;" k="7" />
    <hkern u1="&#x152;" u2="&#x14e;" k="7" />
    <hkern u1="&#x152;" u2="&#x14c;" k="7" />
    <hkern u1="&#x152;" u2="&#x122;" k="7" />
    <hkern u1="&#x152;" u2="&#x120;" k="7" />
    <hkern u1="&#x152;" u2="&#x11e;" k="7" />
    <hkern u1="&#x152;" u2="&#x11c;" k="7" />
    <hkern u1="&#x152;" u2="&#x10c;" k="7" />
    <hkern u1="&#x152;" u2="&#x10a;" k="7" />
    <hkern u1="&#x152;" u2="&#x108;" k="7" />
    <hkern u1="&#x152;" u2="&#x106;" k="7" />
    <hkern u1="&#x152;" u2="&#xd8;" k="7" />
    <hkern u1="&#x152;" u2="&#xd6;" k="7" />
    <hkern u1="&#x152;" u2="&#xd5;" k="7" />
    <hkern u1="&#x152;" u2="&#xd4;" k="7" />
    <hkern u1="&#x152;" u2="&#xd3;" k="7" />
    <hkern u1="&#x152;" u2="&#xd2;" k="7" />
    <hkern u1="&#x152;" u2="&#xc7;" k="7" />
    <hkern u1="&#x152;" u2="&#xa9;" k="7" />
    <hkern u1="&#x152;" u2="Q" k="7" />
    <hkern u1="&#x152;" u2="O" k="7" />
    <hkern u1="&#x152;" u2="G" k="7" />
    <hkern u1="&#x152;" u2="C" k="7" />
    <hkern u1="&#x152;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x152;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x153;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x153;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x153;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x153;" u2="&#x219;" k="-5" />
    <hkern u1="&#x153;" u2="&#x174;" k="10" />
    <hkern u1="&#x153;" u2="&#x164;" k="70" />
    <hkern u1="&#x153;" u2="&#x162;" k="70" />
    <hkern u1="&#x153;" u2="&#x161;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x153;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x153;" u2="s" k="-5" />
    <hkern u1="&#x153;" u2="W" k="10" />
    <hkern u1="&#x153;" u2="V" k="30" />
    <hkern u1="&#x153;" u2="T" k="70" />
    <hkern u1="&#x154;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x154;" u2="&#x164;" k="15" />
    <hkern u1="&#x154;" u2="&#x162;" k="15" />
    <hkern u1="&#x154;" u2="_" k="-30" />
    <hkern u1="&#x154;" u2="T" k="15" />
    <hkern u1="&#x154;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x154;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x155;" g2="fl" k="-20" />
    <hkern u1="&#x155;" g2="fi" k="-20" />
    <hkern u1="&#x155;" u2="&#x2026;" k="105" />
    <hkern u1="&#x155;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x155;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x155;" u2="&#x2014;" k="40" />
    <hkern u1="&#x155;" u2="&#x2013;" k="40" />
    <hkern u1="&#x155;" u2="&#x1e85;" k="-30" />
    <hkern u1="&#x155;" u2="&#x1e83;" k="-30" />
    <hkern u1="&#x155;" u2="&#x1e81;" k="-30" />
    <hkern u1="&#x155;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x155;" u2="&#x1fd;" k="15" />
    <hkern u1="&#x155;" u2="&#x1fb;" k="15" />
    <hkern u1="&#x155;" u2="&#x17f;" k="-20" />
    <hkern u1="&#x155;" u2="&#x175;" k="-30" />
    <hkern u1="&#x155;" u2="&#x167;" k="-20" />
    <hkern u1="&#x155;" u2="&#x165;" k="-20" />
    <hkern u1="&#x155;" u2="&#x163;" k="-20" />
    <hkern u1="&#x155;" u2="&#x153;" k="15" />
    <hkern u1="&#x155;" u2="&#x151;" k="15" />
    <hkern u1="&#x155;" u2="&#x14f;" k="15" />
    <hkern u1="&#x155;" u2="&#x14d;" k="15" />
    <hkern u1="&#x155;" u2="&#x123;" k="15" />
    <hkern u1="&#x155;" u2="&#x121;" k="15" />
    <hkern u1="&#x155;" u2="&#x11f;" k="15" />
    <hkern u1="&#x155;" u2="&#x11d;" k="15" />
    <hkern u1="&#x155;" u2="&#x11b;" k="15" />
    <hkern u1="&#x155;" u2="&#x119;" k="15" />
    <hkern u1="&#x155;" u2="&#x117;" k="15" />
    <hkern u1="&#x155;" u2="&#x115;" k="15" />
    <hkern u1="&#x155;" u2="&#x113;" k="15" />
    <hkern u1="&#x155;" u2="&#x111;" k="15" />
    <hkern u1="&#x155;" u2="&#x10f;" k="15" />
    <hkern u1="&#x155;" u2="&#x10d;" k="15" />
    <hkern u1="&#x155;" u2="&#x10b;" k="15" />
    <hkern u1="&#x155;" u2="&#x109;" k="15" />
    <hkern u1="&#x155;" u2="&#x107;" k="15" />
    <hkern u1="&#x155;" u2="&#x105;" k="15" />
    <hkern u1="&#x155;" u2="&#x103;" k="15" />
    <hkern u1="&#x155;" u2="&#x101;" k="15" />
    <hkern u1="&#x155;" u2="&#xf8;" k="15" />
    <hkern u1="&#x155;" u2="&#xf6;" k="15" />
    <hkern u1="&#x155;" u2="&#xf5;" k="15" />
    <hkern u1="&#x155;" u2="&#xf4;" k="15" />
    <hkern u1="&#x155;" u2="&#xf3;" k="15" />
    <hkern u1="&#x155;" u2="&#xf2;" k="15" />
    <hkern u1="&#x155;" u2="&#xf0;" k="15" />
    <hkern u1="&#x155;" u2="&#xeb;" k="15" />
    <hkern u1="&#x155;" u2="&#xea;" k="15" />
    <hkern u1="&#x155;" u2="&#xe9;" k="15" />
    <hkern u1="&#x155;" u2="&#xe8;" k="15" />
    <hkern u1="&#x155;" u2="&#xe7;" k="15" />
    <hkern u1="&#x155;" u2="&#xe6;" k="15" />
    <hkern u1="&#x155;" u2="&#xe5;" k="15" />
    <hkern u1="&#x155;" u2="&#xe4;" k="15" />
    <hkern u1="&#x155;" u2="&#xe3;" k="15" />
    <hkern u1="&#x155;" u2="&#xe2;" k="15" />
    <hkern u1="&#x155;" u2="&#xe1;" k="15" />
    <hkern u1="&#x155;" u2="&#xe0;" k="15" />
    <hkern u1="&#x155;" u2="w" k="-30" />
    <hkern u1="&#x155;" u2="v" k="-30" />
    <hkern u1="&#x155;" u2="t" k="-20" />
    <hkern u1="&#x155;" u2="q" k="15" />
    <hkern u1="&#x155;" u2="o" k="15" />
    <hkern u1="&#x155;" u2="g" k="15" />
    <hkern u1="&#x155;" u2="f" k="-20" />
    <hkern u1="&#x155;" u2="e" k="15" />
    <hkern u1="&#x155;" u2="d" k="15" />
    <hkern u1="&#x155;" u2="c" k="15" />
    <hkern u1="&#x155;" u2="a" k="15" />
    <hkern u1="&#x155;" u2="&#x2f;" k="50" />
    <hkern u1="&#x155;" u2="&#x2e;" k="105" />
    <hkern u1="&#x155;" u2="&#x2d;" k="40" />
    <hkern u1="&#x155;" u2="&#x2c;" k="105" />
    <hkern u1="&#x156;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x156;" u2="&#x164;" k="15" />
    <hkern u1="&#x156;" u2="&#x162;" k="15" />
    <hkern u1="&#x156;" u2="_" k="-30" />
    <hkern u1="&#x156;" u2="T" k="15" />
    <hkern u1="&#x156;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x156;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x157;" g2="fl" k="-20" />
    <hkern u1="&#x157;" g2="fi" k="-20" />
    <hkern u1="&#x157;" u2="&#x2026;" k="105" />
    <hkern u1="&#x157;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x157;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x157;" u2="&#x2014;" k="40" />
    <hkern u1="&#x157;" u2="&#x2013;" k="40" />
    <hkern u1="&#x157;" u2="&#x1e85;" k="-30" />
    <hkern u1="&#x157;" u2="&#x1e83;" k="-30" />
    <hkern u1="&#x157;" u2="&#x1e81;" k="-30" />
    <hkern u1="&#x157;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x157;" u2="&#x1fd;" k="15" />
    <hkern u1="&#x157;" u2="&#x1fb;" k="15" />
    <hkern u1="&#x157;" u2="&#x17f;" k="-20" />
    <hkern u1="&#x157;" u2="&#x175;" k="-30" />
    <hkern u1="&#x157;" u2="&#x167;" k="-20" />
    <hkern u1="&#x157;" u2="&#x165;" k="-20" />
    <hkern u1="&#x157;" u2="&#x163;" k="-20" />
    <hkern u1="&#x157;" u2="&#x153;" k="15" />
    <hkern u1="&#x157;" u2="&#x151;" k="15" />
    <hkern u1="&#x157;" u2="&#x14f;" k="15" />
    <hkern u1="&#x157;" u2="&#x14d;" k="15" />
    <hkern u1="&#x157;" u2="&#x123;" k="15" />
    <hkern u1="&#x157;" u2="&#x121;" k="15" />
    <hkern u1="&#x157;" u2="&#x11f;" k="15" />
    <hkern u1="&#x157;" u2="&#x11d;" k="15" />
    <hkern u1="&#x157;" u2="&#x11b;" k="15" />
    <hkern u1="&#x157;" u2="&#x119;" k="15" />
    <hkern u1="&#x157;" u2="&#x117;" k="15" />
    <hkern u1="&#x157;" u2="&#x115;" k="15" />
    <hkern u1="&#x157;" u2="&#x113;" k="15" />
    <hkern u1="&#x157;" u2="&#x111;" k="15" />
    <hkern u1="&#x157;" u2="&#x10f;" k="15" />
    <hkern u1="&#x157;" u2="&#x10d;" k="15" />
    <hkern u1="&#x157;" u2="&#x10b;" k="15" />
    <hkern u1="&#x157;" u2="&#x109;" k="15" />
    <hkern u1="&#x157;" u2="&#x107;" k="15" />
    <hkern u1="&#x157;" u2="&#x105;" k="15" />
    <hkern u1="&#x157;" u2="&#x103;" k="15" />
    <hkern u1="&#x157;" u2="&#x101;" k="15" />
    <hkern u1="&#x157;" u2="&#xf8;" k="15" />
    <hkern u1="&#x157;" u2="&#xf6;" k="15" />
    <hkern u1="&#x157;" u2="&#xf5;" k="15" />
    <hkern u1="&#x157;" u2="&#xf4;" k="15" />
    <hkern u1="&#x157;" u2="&#xf3;" k="15" />
    <hkern u1="&#x157;" u2="&#xf2;" k="15" />
    <hkern u1="&#x157;" u2="&#xf0;" k="15" />
    <hkern u1="&#x157;" u2="&#xeb;" k="15" />
    <hkern u1="&#x157;" u2="&#xea;" k="15" />
    <hkern u1="&#x157;" u2="&#xe9;" k="15" />
    <hkern u1="&#x157;" u2="&#xe8;" k="15" />
    <hkern u1="&#x157;" u2="&#xe7;" k="15" />
    <hkern u1="&#x157;" u2="&#xe6;" k="15" />
    <hkern u1="&#x157;" u2="&#xe5;" k="15" />
    <hkern u1="&#x157;" u2="&#xe4;" k="15" />
    <hkern u1="&#x157;" u2="&#xe3;" k="15" />
    <hkern u1="&#x157;" u2="&#xe2;" k="15" />
    <hkern u1="&#x157;" u2="&#xe1;" k="15" />
    <hkern u1="&#x157;" u2="&#xe0;" k="15" />
    <hkern u1="&#x157;" u2="w" k="-30" />
    <hkern u1="&#x157;" u2="v" k="-30" />
    <hkern u1="&#x157;" u2="t" k="-20" />
    <hkern u1="&#x157;" u2="q" k="15" />
    <hkern u1="&#x157;" u2="o" k="15" />
    <hkern u1="&#x157;" u2="g" k="15" />
    <hkern u1="&#x157;" u2="f" k="-20" />
    <hkern u1="&#x157;" u2="e" k="15" />
    <hkern u1="&#x157;" u2="d" k="15" />
    <hkern u1="&#x157;" u2="c" k="15" />
    <hkern u1="&#x157;" u2="a" k="15" />
    <hkern u1="&#x157;" u2="&#x2f;" k="50" />
    <hkern u1="&#x157;" u2="&#x2e;" k="105" />
    <hkern u1="&#x157;" u2="&#x2d;" k="40" />
    <hkern u1="&#x157;" u2="&#x2c;" k="105" />
    <hkern u1="&#x158;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x158;" u2="&#x164;" k="15" />
    <hkern u1="&#x158;" u2="&#x162;" k="15" />
    <hkern u1="&#x158;" u2="_" k="-30" />
    <hkern u1="&#x158;" u2="T" k="15" />
    <hkern u1="&#x158;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x158;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x159;" g2="fl" k="-20" />
    <hkern u1="&#x159;" g2="fi" k="-20" />
    <hkern u1="&#x159;" u2="&#x2026;" k="105" />
    <hkern u1="&#x159;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x159;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x159;" u2="&#x2014;" k="40" />
    <hkern u1="&#x159;" u2="&#x2013;" k="40" />
    <hkern u1="&#x159;" u2="&#x1e85;" k="-30" />
    <hkern u1="&#x159;" u2="&#x1e83;" k="-30" />
    <hkern u1="&#x159;" u2="&#x1e81;" k="-30" />
    <hkern u1="&#x159;" u2="&#x1ff;" k="15" />
    <hkern u1="&#x159;" u2="&#x1fd;" k="15" />
    <hkern u1="&#x159;" u2="&#x1fb;" k="15" />
    <hkern u1="&#x159;" u2="&#x17f;" k="-20" />
    <hkern u1="&#x159;" u2="&#x175;" k="-30" />
    <hkern u1="&#x159;" u2="&#x167;" k="-20" />
    <hkern u1="&#x159;" u2="&#x165;" k="-20" />
    <hkern u1="&#x159;" u2="&#x163;" k="-20" />
    <hkern u1="&#x159;" u2="&#x153;" k="15" />
    <hkern u1="&#x159;" u2="&#x151;" k="15" />
    <hkern u1="&#x159;" u2="&#x14f;" k="15" />
    <hkern u1="&#x159;" u2="&#x14d;" k="15" />
    <hkern u1="&#x159;" u2="&#x123;" k="15" />
    <hkern u1="&#x159;" u2="&#x121;" k="15" />
    <hkern u1="&#x159;" u2="&#x11f;" k="15" />
    <hkern u1="&#x159;" u2="&#x11d;" k="15" />
    <hkern u1="&#x159;" u2="&#x11b;" k="15" />
    <hkern u1="&#x159;" u2="&#x119;" k="15" />
    <hkern u1="&#x159;" u2="&#x117;" k="15" />
    <hkern u1="&#x159;" u2="&#x115;" k="15" />
    <hkern u1="&#x159;" u2="&#x113;" k="15" />
    <hkern u1="&#x159;" u2="&#x111;" k="15" />
    <hkern u1="&#x159;" u2="&#x10f;" k="15" />
    <hkern u1="&#x159;" u2="&#x10d;" k="15" />
    <hkern u1="&#x159;" u2="&#x10b;" k="15" />
    <hkern u1="&#x159;" u2="&#x109;" k="15" />
    <hkern u1="&#x159;" u2="&#x107;" k="15" />
    <hkern u1="&#x159;" u2="&#x105;" k="15" />
    <hkern u1="&#x159;" u2="&#x103;" k="15" />
    <hkern u1="&#x159;" u2="&#x101;" k="15" />
    <hkern u1="&#x159;" u2="&#xf8;" k="15" />
    <hkern u1="&#x159;" u2="&#xf6;" k="15" />
    <hkern u1="&#x159;" u2="&#xf5;" k="15" />
    <hkern u1="&#x159;" u2="&#xf4;" k="15" />
    <hkern u1="&#x159;" u2="&#xf3;" k="15" />
    <hkern u1="&#x159;" u2="&#xf2;" k="15" />
    <hkern u1="&#x159;" u2="&#xf0;" k="15" />
    <hkern u1="&#x159;" u2="&#xeb;" k="15" />
    <hkern u1="&#x159;" u2="&#xea;" k="15" />
    <hkern u1="&#x159;" u2="&#xe9;" k="15" />
    <hkern u1="&#x159;" u2="&#xe8;" k="15" />
    <hkern u1="&#x159;" u2="&#xe7;" k="15" />
    <hkern u1="&#x159;" u2="&#xe6;" k="15" />
    <hkern u1="&#x159;" u2="&#xe5;" k="15" />
    <hkern u1="&#x159;" u2="&#xe4;" k="15" />
    <hkern u1="&#x159;" u2="&#xe3;" k="15" />
    <hkern u1="&#x159;" u2="&#xe2;" k="15" />
    <hkern u1="&#x159;" u2="&#xe1;" k="15" />
    <hkern u1="&#x159;" u2="&#xe0;" k="15" />
    <hkern u1="&#x159;" u2="w" k="-30" />
    <hkern u1="&#x159;" u2="v" k="-30" />
    <hkern u1="&#x159;" u2="t" k="-20" />
    <hkern u1="&#x159;" u2="q" k="15" />
    <hkern u1="&#x159;" u2="o" k="15" />
    <hkern u1="&#x159;" u2="g" k="15" />
    <hkern u1="&#x159;" u2="f" k="-20" />
    <hkern u1="&#x159;" u2="e" k="15" />
    <hkern u1="&#x159;" u2="d" k="15" />
    <hkern u1="&#x159;" u2="c" k="15" />
    <hkern u1="&#x159;" u2="a" k="15" />
    <hkern u1="&#x159;" u2="&#x2f;" k="50" />
    <hkern u1="&#x159;" u2="&#x2e;" k="105" />
    <hkern u1="&#x159;" u2="&#x2d;" k="40" />
    <hkern u1="&#x159;" u2="&#x2c;" k="105" />
    <hkern u1="&#x15a;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x15a;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x15a;" u2="&#x218;" k="10" />
    <hkern u1="&#x15a;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x15a;" u2="&#x178;" k="25" />
    <hkern u1="&#x15a;" u2="&#x176;" k="25" />
    <hkern u1="&#x15a;" u2="&#x164;" k="15" />
    <hkern u1="&#x15a;" u2="&#x162;" k="15" />
    <hkern u1="&#x15a;" u2="&#x160;" k="10" />
    <hkern u1="&#x15a;" u2="&#x15e;" k="10" />
    <hkern u1="&#x15a;" u2="&#x15c;" k="10" />
    <hkern u1="&#x15a;" u2="&#x15a;" k="10" />
    <hkern u1="&#x15a;" u2="&#x152;" k="5" />
    <hkern u1="&#x15a;" u2="&#x150;" k="5" />
    <hkern u1="&#x15a;" u2="&#x14e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x14c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x122;" k="5" />
    <hkern u1="&#x15a;" u2="&#x120;" k="5" />
    <hkern u1="&#x15a;" u2="&#x11e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x11c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x10c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x10a;" k="5" />
    <hkern u1="&#x15a;" u2="&#x108;" k="5" />
    <hkern u1="&#x15a;" u2="&#x106;" k="5" />
    <hkern u1="&#x15a;" u2="&#xdd;" k="25" />
    <hkern u1="&#x15a;" u2="&#xd8;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd6;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd5;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd4;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd3;" k="5" />
    <hkern u1="&#x15a;" u2="&#xd2;" k="5" />
    <hkern u1="&#x15a;" u2="&#xc7;" k="5" />
    <hkern u1="&#x15a;" u2="&#xa9;" k="5" />
    <hkern u1="&#x15a;" u2="Y" k="25" />
    <hkern u1="&#x15a;" u2="T" k="15" />
    <hkern u1="&#x15a;" u2="S" k="10" />
    <hkern u1="&#x15a;" u2="Q" k="5" />
    <hkern u1="&#x15a;" u2="O" k="5" />
    <hkern u1="&#x15a;" u2="G" k="5" />
    <hkern u1="&#x15a;" u2="C" k="5" />
    <hkern u1="&#x15a;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x15a;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x15b;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x15b;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x15b;" u2="&#x219;" k="20" />
    <hkern u1="&#x15b;" u2="&#x164;" k="70" />
    <hkern u1="&#x15b;" u2="&#x162;" k="70" />
    <hkern u1="&#x15b;" u2="&#x161;" k="20" />
    <hkern u1="&#x15b;" u2="&#x15f;" k="20" />
    <hkern u1="&#x15b;" u2="&#x15d;" k="20" />
    <hkern u1="&#x15b;" u2="&#x15b;" k="20" />
    <hkern u1="&#x15b;" u2="s" k="20" />
    <hkern u1="&#x15b;" u2="T" k="70" />
    <hkern u1="&#x15c;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x15c;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x15c;" u2="&#x218;" k="10" />
    <hkern u1="&#x15c;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x15c;" u2="&#x178;" k="25" />
    <hkern u1="&#x15c;" u2="&#x176;" k="25" />
    <hkern u1="&#x15c;" u2="&#x164;" k="15" />
    <hkern u1="&#x15c;" u2="&#x162;" k="15" />
    <hkern u1="&#x15c;" u2="&#x160;" k="10" />
    <hkern u1="&#x15c;" u2="&#x15e;" k="10" />
    <hkern u1="&#x15c;" u2="&#x15c;" k="10" />
    <hkern u1="&#x15c;" u2="&#x15a;" k="10" />
    <hkern u1="&#x15c;" u2="&#x152;" k="5" />
    <hkern u1="&#x15c;" u2="&#x150;" k="5" />
    <hkern u1="&#x15c;" u2="&#x14e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x14c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x122;" k="5" />
    <hkern u1="&#x15c;" u2="&#x120;" k="5" />
    <hkern u1="&#x15c;" u2="&#x11e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x11c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x10c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x10a;" k="5" />
    <hkern u1="&#x15c;" u2="&#x108;" k="5" />
    <hkern u1="&#x15c;" u2="&#x106;" k="5" />
    <hkern u1="&#x15c;" u2="&#xdd;" k="25" />
    <hkern u1="&#x15c;" u2="&#xd8;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd6;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd5;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd4;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd3;" k="5" />
    <hkern u1="&#x15c;" u2="&#xd2;" k="5" />
    <hkern u1="&#x15c;" u2="&#xc7;" k="5" />
    <hkern u1="&#x15c;" u2="&#xa9;" k="5" />
    <hkern u1="&#x15c;" u2="Y" k="25" />
    <hkern u1="&#x15c;" u2="T" k="15" />
    <hkern u1="&#x15c;" u2="S" k="10" />
    <hkern u1="&#x15c;" u2="Q" k="5" />
    <hkern u1="&#x15c;" u2="O" k="5" />
    <hkern u1="&#x15c;" u2="G" k="5" />
    <hkern u1="&#x15c;" u2="C" k="5" />
    <hkern u1="&#x15c;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x15c;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x15d;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x15d;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x15d;" u2="&#x219;" k="20" />
    <hkern u1="&#x15d;" u2="&#x164;" k="70" />
    <hkern u1="&#x15d;" u2="&#x162;" k="70" />
    <hkern u1="&#x15d;" u2="&#x161;" k="20" />
    <hkern u1="&#x15d;" u2="&#x15f;" k="20" />
    <hkern u1="&#x15d;" u2="&#x15d;" k="20" />
    <hkern u1="&#x15d;" u2="&#x15b;" k="20" />
    <hkern u1="&#x15d;" u2="s" k="20" />
    <hkern u1="&#x15d;" u2="T" k="70" />
    <hkern u1="&#x15e;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x15e;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x15e;" u2="&#x218;" k="10" />
    <hkern u1="&#x15e;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x15e;" u2="&#x178;" k="25" />
    <hkern u1="&#x15e;" u2="&#x176;" k="25" />
    <hkern u1="&#x15e;" u2="&#x164;" k="15" />
    <hkern u1="&#x15e;" u2="&#x162;" k="15" />
    <hkern u1="&#x15e;" u2="&#x160;" k="10" />
    <hkern u1="&#x15e;" u2="&#x15e;" k="10" />
    <hkern u1="&#x15e;" u2="&#x15c;" k="10" />
    <hkern u1="&#x15e;" u2="&#x15a;" k="10" />
    <hkern u1="&#x15e;" u2="&#x152;" k="5" />
    <hkern u1="&#x15e;" u2="&#x150;" k="5" />
    <hkern u1="&#x15e;" u2="&#x14e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x14c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x122;" k="5" />
    <hkern u1="&#x15e;" u2="&#x120;" k="5" />
    <hkern u1="&#x15e;" u2="&#x11e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x11c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x10c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x10a;" k="5" />
    <hkern u1="&#x15e;" u2="&#x108;" k="5" />
    <hkern u1="&#x15e;" u2="&#x106;" k="5" />
    <hkern u1="&#x15e;" u2="&#xdd;" k="25" />
    <hkern u1="&#x15e;" u2="&#xd8;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd6;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd5;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd4;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd3;" k="5" />
    <hkern u1="&#x15e;" u2="&#xd2;" k="5" />
    <hkern u1="&#x15e;" u2="&#xc7;" k="5" />
    <hkern u1="&#x15e;" u2="&#xa9;" k="5" />
    <hkern u1="&#x15e;" u2="Y" k="25" />
    <hkern u1="&#x15e;" u2="T" k="15" />
    <hkern u1="&#x15e;" u2="S" k="10" />
    <hkern u1="&#x15e;" u2="Q" k="5" />
    <hkern u1="&#x15e;" u2="O" k="5" />
    <hkern u1="&#x15e;" u2="G" k="5" />
    <hkern u1="&#x15e;" u2="C" k="5" />
    <hkern u1="&#x15e;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x15e;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x15f;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x15f;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x15f;" u2="&#x219;" k="20" />
    <hkern u1="&#x15f;" u2="&#x164;" k="70" />
    <hkern u1="&#x15f;" u2="&#x162;" k="70" />
    <hkern u1="&#x15f;" u2="&#x161;" k="20" />
    <hkern u1="&#x15f;" u2="&#x15f;" k="20" />
    <hkern u1="&#x15f;" u2="&#x15d;" k="20" />
    <hkern u1="&#x15f;" u2="&#x15b;" k="20" />
    <hkern u1="&#x15f;" u2="s" k="20" />
    <hkern u1="&#x15f;" u2="T" k="70" />
    <hkern u1="&#x160;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x160;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x160;" u2="&#x218;" k="10" />
    <hkern u1="&#x160;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x160;" u2="&#x178;" k="25" />
    <hkern u1="&#x160;" u2="&#x176;" k="25" />
    <hkern u1="&#x160;" u2="&#x164;" k="15" />
    <hkern u1="&#x160;" u2="&#x162;" k="15" />
    <hkern u1="&#x160;" u2="&#x160;" k="10" />
    <hkern u1="&#x160;" u2="&#x15e;" k="10" />
    <hkern u1="&#x160;" u2="&#x15c;" k="10" />
    <hkern u1="&#x160;" u2="&#x15a;" k="10" />
    <hkern u1="&#x160;" u2="&#x152;" k="5" />
    <hkern u1="&#x160;" u2="&#x150;" k="5" />
    <hkern u1="&#x160;" u2="&#x14e;" k="5" />
    <hkern u1="&#x160;" u2="&#x14c;" k="5" />
    <hkern u1="&#x160;" u2="&#x122;" k="5" />
    <hkern u1="&#x160;" u2="&#x120;" k="5" />
    <hkern u1="&#x160;" u2="&#x11e;" k="5" />
    <hkern u1="&#x160;" u2="&#x11c;" k="5" />
    <hkern u1="&#x160;" u2="&#x10c;" k="5" />
    <hkern u1="&#x160;" u2="&#x10a;" k="5" />
    <hkern u1="&#x160;" u2="&#x108;" k="5" />
    <hkern u1="&#x160;" u2="&#x106;" k="5" />
    <hkern u1="&#x160;" u2="&#xdd;" k="25" />
    <hkern u1="&#x160;" u2="&#xd8;" k="5" />
    <hkern u1="&#x160;" u2="&#xd6;" k="5" />
    <hkern u1="&#x160;" u2="&#xd5;" k="5" />
    <hkern u1="&#x160;" u2="&#xd4;" k="5" />
    <hkern u1="&#x160;" u2="&#xd3;" k="5" />
    <hkern u1="&#x160;" u2="&#xd2;" k="5" />
    <hkern u1="&#x160;" u2="&#xc7;" k="5" />
    <hkern u1="&#x160;" u2="&#xa9;" k="5" />
    <hkern u1="&#x160;" u2="Y" k="25" />
    <hkern u1="&#x160;" u2="T" k="15" />
    <hkern u1="&#x160;" u2="S" k="10" />
    <hkern u1="&#x160;" u2="Q" k="5" />
    <hkern u1="&#x160;" u2="O" k="5" />
    <hkern u1="&#x160;" u2="G" k="5" />
    <hkern u1="&#x160;" u2="C" k="5" />
    <hkern u1="&#x160;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x160;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x161;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x161;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x161;" u2="&#x219;" k="20" />
    <hkern u1="&#x161;" u2="&#x164;" k="70" />
    <hkern u1="&#x161;" u2="&#x162;" k="70" />
    <hkern u1="&#x161;" u2="&#x161;" k="20" />
    <hkern u1="&#x161;" u2="&#x15f;" k="20" />
    <hkern u1="&#x161;" u2="&#x15d;" k="20" />
    <hkern u1="&#x161;" u2="&#x15b;" k="20" />
    <hkern u1="&#x161;" u2="s" k="20" />
    <hkern u1="&#x161;" u2="T" k="70" />
    <hkern u1="&#x162;" u2="&#x2026;" k="110" />
    <hkern u1="&#x162;" u2="&#x2014;" k="110" />
    <hkern u1="&#x162;" u2="&#x2013;" k="110" />
    <hkern u1="&#x162;" u2="&#x1ef3;" k="75" />
    <hkern u1="&#x162;" u2="&#x1e85;" k="55" />
    <hkern u1="&#x162;" u2="&#x1e83;" k="55" />
    <hkern u1="&#x162;" u2="&#x1e81;" k="55" />
    <hkern u1="&#x162;" u2="&#x219;" k="80" />
    <hkern u1="&#x162;" u2="&#x1ff;" k="75" />
    <hkern u1="&#x162;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x162;" u2="&#x1fd;" k="75" />
    <hkern u1="&#x162;" u2="&#x1fc;" k="25" />
    <hkern u1="&#x162;" u2="&#x1fb;" k="75" />
    <hkern u1="&#x162;" u2="&#x1fa;" k="25" />
    <hkern u1="&#x162;" u2="&#x177;" k="75" />
    <hkern u1="&#x162;" u2="&#x175;" k="55" />
    <hkern u1="&#x162;" u2="&#x173;" k="75" />
    <hkern u1="&#x162;" u2="&#x171;" k="75" />
    <hkern u1="&#x162;" u2="&#x16f;" k="75" />
    <hkern u1="&#x162;" u2="&#x16d;" k="75" />
    <hkern u1="&#x162;" u2="&#x16b;" k="75" />
    <hkern u1="&#x162;" u2="&#x169;" k="75" />
    <hkern u1="&#x162;" u2="&#x161;" k="80" />
    <hkern u1="&#x162;" u2="&#x15f;" k="80" />
    <hkern u1="&#x162;" u2="&#x15d;" k="80" />
    <hkern u1="&#x162;" u2="&#x15b;" k="80" />
    <hkern u1="&#x162;" u2="&#x159;" k="75" />
    <hkern u1="&#x162;" u2="&#x157;" k="75" />
    <hkern u1="&#x162;" u2="&#x155;" k="75" />
    <hkern u1="&#x162;" u2="&#x153;" k="75" />
    <hkern u1="&#x162;" u2="&#x152;" k="15" />
    <hkern u1="&#x162;" u2="&#x151;" k="75" />
    <hkern u1="&#x162;" u2="&#x150;" k="15" />
    <hkern u1="&#x162;" u2="&#x14f;" k="75" />
    <hkern u1="&#x162;" u2="&#x14e;" k="15" />
    <hkern u1="&#x162;" u2="&#x14d;" k="75" />
    <hkern u1="&#x162;" u2="&#x14c;" k="15" />
    <hkern u1="&#x162;" u2="&#x14b;" k="75" />
    <hkern u1="&#x162;" u2="&#x148;" k="75" />
    <hkern u1="&#x162;" u2="&#x146;" k="75" />
    <hkern u1="&#x162;" u2="&#x144;" k="75" />
    <hkern u1="&#x162;" u2="&#x123;" k="75" />
    <hkern u1="&#x162;" u2="&#x122;" k="15" />
    <hkern u1="&#x162;" u2="&#x121;" k="75" />
    <hkern u1="&#x162;" u2="&#x120;" k="15" />
    <hkern u1="&#x162;" u2="&#x11f;" k="75" />
    <hkern u1="&#x162;" u2="&#x11e;" k="15" />
    <hkern u1="&#x162;" u2="&#x11d;" k="75" />
    <hkern u1="&#x162;" u2="&#x11c;" k="15" />
    <hkern u1="&#x162;" u2="&#x11b;" k="75" />
    <hkern u1="&#x162;" u2="&#x119;" k="75" />
    <hkern u1="&#x162;" u2="&#x117;" k="75" />
    <hkern u1="&#x162;" u2="&#x115;" k="75" />
    <hkern u1="&#x162;" u2="&#x113;" k="75" />
    <hkern u1="&#x162;" u2="&#x111;" k="75" />
    <hkern u1="&#x162;" u2="&#x10f;" k="75" />
    <hkern u1="&#x162;" u2="&#x10d;" k="75" />
    <hkern u1="&#x162;" u2="&#x10c;" k="15" />
    <hkern u1="&#x162;" u2="&#x10b;" k="75" />
    <hkern u1="&#x162;" u2="&#x10a;" k="15" />
    <hkern u1="&#x162;" u2="&#x109;" k="75" />
    <hkern u1="&#x162;" u2="&#x108;" k="15" />
    <hkern u1="&#x162;" u2="&#x107;" k="75" />
    <hkern u1="&#x162;" u2="&#x106;" k="15" />
    <hkern u1="&#x162;" u2="&#x105;" k="75" />
    <hkern u1="&#x162;" u2="&#x104;" k="25" />
    <hkern u1="&#x162;" u2="&#x103;" k="75" />
    <hkern u1="&#x162;" u2="&#x102;" k="25" />
    <hkern u1="&#x162;" u2="&#x101;" k="75" />
    <hkern u1="&#x162;" u2="&#x100;" k="25" />
    <hkern u1="&#x162;" u2="&#xff;" k="75" />
    <hkern u1="&#x162;" u2="&#xfd;" k="75" />
    <hkern u1="&#x162;" u2="&#xfc;" k="75" />
    <hkern u1="&#x162;" u2="&#xfb;" k="75" />
    <hkern u1="&#x162;" u2="&#xfa;" k="75" />
    <hkern u1="&#x162;" u2="&#xf9;" k="75" />
    <hkern u1="&#x162;" u2="&#xf8;" k="75" />
    <hkern u1="&#x162;" u2="&#xf6;" k="75" />
    <hkern u1="&#x162;" u2="&#xf5;" k="75" />
    <hkern u1="&#x162;" u2="&#xf4;" k="75" />
    <hkern u1="&#x162;" u2="&#xf3;" k="75" />
    <hkern u1="&#x162;" u2="&#xf2;" k="75" />
    <hkern u1="&#x162;" u2="&#xf1;" k="75" />
    <hkern u1="&#x162;" u2="&#xf0;" k="75" />
    <hkern u1="&#x162;" u2="&#xef;" k="-30" />
    <hkern u1="&#x162;" u2="&#xee;" k="-20" />
    <hkern u1="&#x162;" u2="&#xeb;" k="75" />
    <hkern u1="&#x162;" u2="&#xea;" k="75" />
    <hkern u1="&#x162;" u2="&#xe9;" k="75" />
    <hkern u1="&#x162;" u2="&#xe8;" k="75" />
    <hkern u1="&#x162;" u2="&#xe7;" k="75" />
    <hkern u1="&#x162;" u2="&#xe6;" k="75" />
    <hkern u1="&#x162;" u2="&#xe5;" k="75" />
    <hkern u1="&#x162;" u2="&#xe4;" k="75" />
    <hkern u1="&#x162;" u2="&#xe3;" k="75" />
    <hkern u1="&#x162;" u2="&#xe2;" k="75" />
    <hkern u1="&#x162;" u2="&#xe1;" k="75" />
    <hkern u1="&#x162;" u2="&#xe0;" k="75" />
    <hkern u1="&#x162;" u2="&#xd8;" k="15" />
    <hkern u1="&#x162;" u2="&#xd6;" k="15" />
    <hkern u1="&#x162;" u2="&#xd5;" k="15" />
    <hkern u1="&#x162;" u2="&#xd4;" k="15" />
    <hkern u1="&#x162;" u2="&#xd3;" k="15" />
    <hkern u1="&#x162;" u2="&#xd2;" k="15" />
    <hkern u1="&#x162;" u2="&#xc7;" k="15" />
    <hkern u1="&#x162;" u2="&#xc6;" k="25" />
    <hkern u1="&#x162;" u2="&#xc5;" k="25" />
    <hkern u1="&#x162;" u2="&#xc4;" k="25" />
    <hkern u1="&#x162;" u2="&#xc3;" k="25" />
    <hkern u1="&#x162;" u2="&#xc2;" k="25" />
    <hkern u1="&#x162;" u2="&#xc1;" k="25" />
    <hkern u1="&#x162;" u2="&#xc0;" k="25" />
    <hkern u1="&#x162;" u2="&#xa9;" k="15" />
    <hkern u1="&#x162;" u2="z" k="80" />
    <hkern u1="&#x162;" u2="y" k="75" />
    <hkern u1="&#x162;" u2="x" k="55" />
    <hkern u1="&#x162;" u2="w" k="55" />
    <hkern u1="&#x162;" u2="v" k="55" />
    <hkern u1="&#x162;" u2="u" k="75" />
    <hkern u1="&#x162;" u2="s" k="80" />
    <hkern u1="&#x162;" u2="r" k="75" />
    <hkern u1="&#x162;" u2="q" k="75" />
    <hkern u1="&#x162;" u2="p" k="75" />
    <hkern u1="&#x162;" u2="o" k="75" />
    <hkern u1="&#x162;" u2="n" k="75" />
    <hkern u1="&#x162;" u2="m" k="75" />
    <hkern u1="&#x162;" u2="g" k="75" />
    <hkern u1="&#x162;" u2="e" k="75" />
    <hkern u1="&#x162;" u2="d" k="75" />
    <hkern u1="&#x162;" u2="c" k="75" />
    <hkern u1="&#x162;" u2="a" k="75" />
    <hkern u1="&#x162;" u2="_" k="45" />
    <hkern u1="&#x162;" u2="Q" k="15" />
    <hkern u1="&#x162;" u2="O" k="15" />
    <hkern u1="&#x162;" u2="G" k="15" />
    <hkern u1="&#x162;" u2="C" k="15" />
    <hkern u1="&#x162;" u2="A" k="25" />
    <hkern u1="&#x162;" u2="&#x2f;" k="85" />
    <hkern u1="&#x162;" u2="&#x2e;" k="110" />
    <hkern u1="&#x162;" u2="&#x2d;" k="110" />
    <hkern u1="&#x162;" u2="&#x2c;" k="110" />
    <hkern u1="&#x164;" u2="&#x2026;" k="110" />
    <hkern u1="&#x164;" u2="&#x2014;" k="110" />
    <hkern u1="&#x164;" u2="&#x2013;" k="110" />
    <hkern u1="&#x164;" u2="&#x1ef3;" k="75" />
    <hkern u1="&#x164;" u2="&#x1e85;" k="55" />
    <hkern u1="&#x164;" u2="&#x1e83;" k="55" />
    <hkern u1="&#x164;" u2="&#x1e81;" k="55" />
    <hkern u1="&#x164;" u2="&#x219;" k="80" />
    <hkern u1="&#x164;" u2="&#x1ff;" k="75" />
    <hkern u1="&#x164;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x164;" u2="&#x1fd;" k="75" />
    <hkern u1="&#x164;" u2="&#x1fc;" k="25" />
    <hkern u1="&#x164;" u2="&#x1fb;" k="75" />
    <hkern u1="&#x164;" u2="&#x1fa;" k="25" />
    <hkern u1="&#x164;" u2="&#x177;" k="75" />
    <hkern u1="&#x164;" u2="&#x175;" k="55" />
    <hkern u1="&#x164;" u2="&#x173;" k="75" />
    <hkern u1="&#x164;" u2="&#x171;" k="75" />
    <hkern u1="&#x164;" u2="&#x16f;" k="75" />
    <hkern u1="&#x164;" u2="&#x16d;" k="75" />
    <hkern u1="&#x164;" u2="&#x16b;" k="75" />
    <hkern u1="&#x164;" u2="&#x169;" k="75" />
    <hkern u1="&#x164;" u2="&#x161;" k="80" />
    <hkern u1="&#x164;" u2="&#x15f;" k="80" />
    <hkern u1="&#x164;" u2="&#x15d;" k="80" />
    <hkern u1="&#x164;" u2="&#x15b;" k="80" />
    <hkern u1="&#x164;" u2="&#x159;" k="75" />
    <hkern u1="&#x164;" u2="&#x157;" k="75" />
    <hkern u1="&#x164;" u2="&#x155;" k="75" />
    <hkern u1="&#x164;" u2="&#x153;" k="75" />
    <hkern u1="&#x164;" u2="&#x152;" k="15" />
    <hkern u1="&#x164;" u2="&#x151;" k="75" />
    <hkern u1="&#x164;" u2="&#x150;" k="15" />
    <hkern u1="&#x164;" u2="&#x14f;" k="75" />
    <hkern u1="&#x164;" u2="&#x14e;" k="15" />
    <hkern u1="&#x164;" u2="&#x14d;" k="75" />
    <hkern u1="&#x164;" u2="&#x14c;" k="15" />
    <hkern u1="&#x164;" u2="&#x14b;" k="75" />
    <hkern u1="&#x164;" u2="&#x148;" k="75" />
    <hkern u1="&#x164;" u2="&#x146;" k="75" />
    <hkern u1="&#x164;" u2="&#x144;" k="75" />
    <hkern u1="&#x164;" u2="&#x123;" k="75" />
    <hkern u1="&#x164;" u2="&#x122;" k="15" />
    <hkern u1="&#x164;" u2="&#x121;" k="75" />
    <hkern u1="&#x164;" u2="&#x120;" k="15" />
    <hkern u1="&#x164;" u2="&#x11f;" k="75" />
    <hkern u1="&#x164;" u2="&#x11e;" k="15" />
    <hkern u1="&#x164;" u2="&#x11d;" k="75" />
    <hkern u1="&#x164;" u2="&#x11c;" k="15" />
    <hkern u1="&#x164;" u2="&#x11b;" k="75" />
    <hkern u1="&#x164;" u2="&#x119;" k="75" />
    <hkern u1="&#x164;" u2="&#x117;" k="75" />
    <hkern u1="&#x164;" u2="&#x115;" k="75" />
    <hkern u1="&#x164;" u2="&#x113;" k="75" />
    <hkern u1="&#x164;" u2="&#x111;" k="75" />
    <hkern u1="&#x164;" u2="&#x10f;" k="75" />
    <hkern u1="&#x164;" u2="&#x10d;" k="75" />
    <hkern u1="&#x164;" u2="&#x10c;" k="15" />
    <hkern u1="&#x164;" u2="&#x10b;" k="75" />
    <hkern u1="&#x164;" u2="&#x10a;" k="15" />
    <hkern u1="&#x164;" u2="&#x109;" k="75" />
    <hkern u1="&#x164;" u2="&#x108;" k="15" />
    <hkern u1="&#x164;" u2="&#x107;" k="75" />
    <hkern u1="&#x164;" u2="&#x106;" k="15" />
    <hkern u1="&#x164;" u2="&#x105;" k="75" />
    <hkern u1="&#x164;" u2="&#x104;" k="25" />
    <hkern u1="&#x164;" u2="&#x103;" k="75" />
    <hkern u1="&#x164;" u2="&#x102;" k="25" />
    <hkern u1="&#x164;" u2="&#x101;" k="75" />
    <hkern u1="&#x164;" u2="&#x100;" k="25" />
    <hkern u1="&#x164;" u2="&#xff;" k="75" />
    <hkern u1="&#x164;" u2="&#xfd;" k="75" />
    <hkern u1="&#x164;" u2="&#xfc;" k="75" />
    <hkern u1="&#x164;" u2="&#xfb;" k="75" />
    <hkern u1="&#x164;" u2="&#xfa;" k="75" />
    <hkern u1="&#x164;" u2="&#xf9;" k="75" />
    <hkern u1="&#x164;" u2="&#xf8;" k="75" />
    <hkern u1="&#x164;" u2="&#xf6;" k="75" />
    <hkern u1="&#x164;" u2="&#xf5;" k="75" />
    <hkern u1="&#x164;" u2="&#xf4;" k="75" />
    <hkern u1="&#x164;" u2="&#xf3;" k="75" />
    <hkern u1="&#x164;" u2="&#xf2;" k="75" />
    <hkern u1="&#x164;" u2="&#xf1;" k="75" />
    <hkern u1="&#x164;" u2="&#xf0;" k="75" />
    <hkern u1="&#x164;" u2="&#xef;" k="-30" />
    <hkern u1="&#x164;" u2="&#xee;" k="-20" />
    <hkern u1="&#x164;" u2="&#xeb;" k="75" />
    <hkern u1="&#x164;" u2="&#xea;" k="75" />
    <hkern u1="&#x164;" u2="&#xe9;" k="75" />
    <hkern u1="&#x164;" u2="&#xe8;" k="75" />
    <hkern u1="&#x164;" u2="&#xe7;" k="75" />
    <hkern u1="&#x164;" u2="&#xe6;" k="75" />
    <hkern u1="&#x164;" u2="&#xe5;" k="75" />
    <hkern u1="&#x164;" u2="&#xe4;" k="75" />
    <hkern u1="&#x164;" u2="&#xe3;" k="75" />
    <hkern u1="&#x164;" u2="&#xe2;" k="75" />
    <hkern u1="&#x164;" u2="&#xe1;" k="75" />
    <hkern u1="&#x164;" u2="&#xe0;" k="75" />
    <hkern u1="&#x164;" u2="&#xd8;" k="15" />
    <hkern u1="&#x164;" u2="&#xd6;" k="15" />
    <hkern u1="&#x164;" u2="&#xd5;" k="15" />
    <hkern u1="&#x164;" u2="&#xd4;" k="15" />
    <hkern u1="&#x164;" u2="&#xd3;" k="15" />
    <hkern u1="&#x164;" u2="&#xd2;" k="15" />
    <hkern u1="&#x164;" u2="&#xc7;" k="15" />
    <hkern u1="&#x164;" u2="&#xc6;" k="25" />
    <hkern u1="&#x164;" u2="&#xc5;" k="25" />
    <hkern u1="&#x164;" u2="&#xc4;" k="25" />
    <hkern u1="&#x164;" u2="&#xc3;" k="25" />
    <hkern u1="&#x164;" u2="&#xc2;" k="25" />
    <hkern u1="&#x164;" u2="&#xc1;" k="25" />
    <hkern u1="&#x164;" u2="&#xc0;" k="25" />
    <hkern u1="&#x164;" u2="&#xa9;" k="15" />
    <hkern u1="&#x164;" u2="z" k="80" />
    <hkern u1="&#x164;" u2="y" k="75" />
    <hkern u1="&#x164;" u2="x" k="55" />
    <hkern u1="&#x164;" u2="w" k="55" />
    <hkern u1="&#x164;" u2="v" k="55" />
    <hkern u1="&#x164;" u2="u" k="75" />
    <hkern u1="&#x164;" u2="s" k="80" />
    <hkern u1="&#x164;" u2="r" k="75" />
    <hkern u1="&#x164;" u2="q" k="75" />
    <hkern u1="&#x164;" u2="p" k="75" />
    <hkern u1="&#x164;" u2="o" k="75" />
    <hkern u1="&#x164;" u2="n" k="75" />
    <hkern u1="&#x164;" u2="m" k="75" />
    <hkern u1="&#x164;" u2="g" k="75" />
    <hkern u1="&#x164;" u2="e" k="75" />
    <hkern u1="&#x164;" u2="d" k="75" />
    <hkern u1="&#x164;" u2="c" k="75" />
    <hkern u1="&#x164;" u2="a" k="75" />
    <hkern u1="&#x164;" u2="_" k="45" />
    <hkern u1="&#x164;" u2="Q" k="15" />
    <hkern u1="&#x164;" u2="O" k="15" />
    <hkern u1="&#x164;" u2="G" k="15" />
    <hkern u1="&#x164;" u2="C" k="15" />
    <hkern u1="&#x164;" u2="A" k="25" />
    <hkern u1="&#x164;" u2="&#x2f;" k="85" />
    <hkern u1="&#x164;" u2="&#x2e;" k="110" />
    <hkern u1="&#x164;" u2="&#x2d;" k="110" />
    <hkern u1="&#x164;" u2="&#x2c;" k="110" />
    <hkern u1="&#x174;" u2="&#x2026;" k="55" />
    <hkern u1="&#x174;" u2="&#x2014;" k="20" />
    <hkern u1="&#x174;" u2="&#x2013;" k="20" />
    <hkern u1="&#x174;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x174;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x174;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x174;" u2="&#x153;" k="10" />
    <hkern u1="&#x174;" u2="&#x151;" k="10" />
    <hkern u1="&#x174;" u2="&#x14f;" k="10" />
    <hkern u1="&#x174;" u2="&#x14d;" k="10" />
    <hkern u1="&#x174;" u2="&#x123;" k="10" />
    <hkern u1="&#x174;" u2="&#x121;" k="10" />
    <hkern u1="&#x174;" u2="&#x11f;" k="10" />
    <hkern u1="&#x174;" u2="&#x11d;" k="10" />
    <hkern u1="&#x174;" u2="&#x11b;" k="10" />
    <hkern u1="&#x174;" u2="&#x119;" k="10" />
    <hkern u1="&#x174;" u2="&#x117;" k="10" />
    <hkern u1="&#x174;" u2="&#x115;" k="10" />
    <hkern u1="&#x174;" u2="&#x113;" k="10" />
    <hkern u1="&#x174;" u2="&#x111;" k="10" />
    <hkern u1="&#x174;" u2="&#x10f;" k="10" />
    <hkern u1="&#x174;" u2="&#x10d;" k="10" />
    <hkern u1="&#x174;" u2="&#x10b;" k="10" />
    <hkern u1="&#x174;" u2="&#x109;" k="10" />
    <hkern u1="&#x174;" u2="&#x107;" k="10" />
    <hkern u1="&#x174;" u2="&#x105;" k="10" />
    <hkern u1="&#x174;" u2="&#x103;" k="10" />
    <hkern u1="&#x174;" u2="&#x101;" k="10" />
    <hkern u1="&#x174;" u2="&#xf8;" k="10" />
    <hkern u1="&#x174;" u2="&#xf6;" k="10" />
    <hkern u1="&#x174;" u2="&#xf5;" k="10" />
    <hkern u1="&#x174;" u2="&#xf4;" k="10" />
    <hkern u1="&#x174;" u2="&#xf3;" k="10" />
    <hkern u1="&#x174;" u2="&#xf2;" k="10" />
    <hkern u1="&#x174;" u2="&#xf0;" k="10" />
    <hkern u1="&#x174;" u2="&#xef;" k="-40" />
    <hkern u1="&#x174;" u2="&#xee;" k="-20" />
    <hkern u1="&#x174;" u2="&#xeb;" k="10" />
    <hkern u1="&#x174;" u2="&#xea;" k="10" />
    <hkern u1="&#x174;" u2="&#xe9;" k="10" />
    <hkern u1="&#x174;" u2="&#xe8;" k="10" />
    <hkern u1="&#x174;" u2="&#xe7;" k="10" />
    <hkern u1="&#x174;" u2="&#xe6;" k="10" />
    <hkern u1="&#x174;" u2="&#xe5;" k="10" />
    <hkern u1="&#x174;" u2="&#xe4;" k="10" />
    <hkern u1="&#x174;" u2="&#xe3;" k="10" />
    <hkern u1="&#x174;" u2="&#xe2;" k="10" />
    <hkern u1="&#x174;" u2="&#xe1;" k="10" />
    <hkern u1="&#x174;" u2="&#xe0;" k="10" />
    <hkern u1="&#x174;" u2="q" k="10" />
    <hkern u1="&#x174;" u2="o" k="10" />
    <hkern u1="&#x174;" u2="g" k="10" />
    <hkern u1="&#x174;" u2="e" k="10" />
    <hkern u1="&#x174;" u2="d" k="10" />
    <hkern u1="&#x174;" u2="c" k="10" />
    <hkern u1="&#x174;" u2="a" k="10" />
    <hkern u1="&#x174;" u2="_" k="45" />
    <hkern u1="&#x174;" u2="&#x2f;" k="55" />
    <hkern u1="&#x174;" u2="&#x2e;" k="55" />
    <hkern u1="&#x174;" u2="&#x2d;" k="20" />
    <hkern u1="&#x174;" u2="&#x2c;" k="55" />
    <hkern u1="&#x175;" u2="&#x2026;" k="65" />
    <hkern u1="&#x175;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x175;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x175;" u2="&#x2014;" k="10" />
    <hkern u1="&#x175;" u2="&#x2013;" k="10" />
    <hkern u1="&#x175;" u2="&#x164;" k="55" />
    <hkern u1="&#x175;" u2="&#x162;" k="55" />
    <hkern u1="&#x175;" u2="_" k="35" />
    <hkern u1="&#x175;" u2="T" k="55" />
    <hkern u1="&#x175;" u2="&#x2f;" k="20" />
    <hkern u1="&#x175;" u2="&#x2e;" k="65" />
    <hkern u1="&#x175;" u2="&#x2d;" k="10" />
    <hkern u1="&#x175;" u2="&#x2c;" k="65" />
    <hkern u1="&#x176;" u2="X" k="-25" />
    <hkern u1="&#x176;" u2="V" k="-15" />
    <hkern u1="&#x177;" u2="&#x164;" k="70" />
    <hkern u1="&#x177;" u2="&#x162;" k="70" />
    <hkern u1="&#x177;" u2="V" k="20" />
    <hkern u1="&#x177;" u2="T" k="70" />
    <hkern u1="&#x178;" u2="X" k="-25" />
    <hkern u1="&#x178;" u2="V" k="-15" />
    <hkern u1="&#x179;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x179;" u2="&#x2014;" k="100" />
    <hkern u1="&#x179;" u2="&#x2013;" k="100" />
    <hkern u1="&#x179;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x179;" u2="&#x152;" k="15" />
    <hkern u1="&#x179;" u2="&#x150;" k="15" />
    <hkern u1="&#x179;" u2="&#x14e;" k="15" />
    <hkern u1="&#x179;" u2="&#x14c;" k="15" />
    <hkern u1="&#x179;" u2="&#x122;" k="15" />
    <hkern u1="&#x179;" u2="&#x120;" k="15" />
    <hkern u1="&#x179;" u2="&#x11e;" k="15" />
    <hkern u1="&#x179;" u2="&#x11c;" k="15" />
    <hkern u1="&#x179;" u2="&#x10c;" k="15" />
    <hkern u1="&#x179;" u2="&#x10a;" k="15" />
    <hkern u1="&#x179;" u2="&#x108;" k="15" />
    <hkern u1="&#x179;" u2="&#x106;" k="15" />
    <hkern u1="&#x179;" u2="&#xd8;" k="15" />
    <hkern u1="&#x179;" u2="&#xd6;" k="15" />
    <hkern u1="&#x179;" u2="&#xd5;" k="15" />
    <hkern u1="&#x179;" u2="&#xd4;" k="15" />
    <hkern u1="&#x179;" u2="&#xd3;" k="15" />
    <hkern u1="&#x179;" u2="&#xd2;" k="15" />
    <hkern u1="&#x179;" u2="&#xc7;" k="15" />
    <hkern u1="&#x179;" u2="&#xa9;" k="15" />
    <hkern u1="&#x179;" u2="Q" k="15" />
    <hkern u1="&#x179;" u2="O" k="15" />
    <hkern u1="&#x179;" u2="G" k="15" />
    <hkern u1="&#x179;" u2="C" k="15" />
    <hkern u1="&#x179;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x179;" u2="&#x2d;" k="100" />
    <hkern u1="&#x179;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17a;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17a;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17a;" u2="&#x164;" k="60" />
    <hkern u1="&#x17a;" u2="&#x162;" k="60" />
    <hkern u1="&#x17a;" u2="T" k="60" />
    <hkern u1="&#x17a;" u2="&#x2d;" k="35" />
    <hkern u1="&#x17b;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x17b;" u2="&#x2014;" k="100" />
    <hkern u1="&#x17b;" u2="&#x2013;" k="100" />
    <hkern u1="&#x17b;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x17b;" u2="&#x152;" k="15" />
    <hkern u1="&#x17b;" u2="&#x150;" k="15" />
    <hkern u1="&#x17b;" u2="&#x14e;" k="15" />
    <hkern u1="&#x17b;" u2="&#x14c;" k="15" />
    <hkern u1="&#x17b;" u2="&#x122;" k="15" />
    <hkern u1="&#x17b;" u2="&#x120;" k="15" />
    <hkern u1="&#x17b;" u2="&#x11e;" k="15" />
    <hkern u1="&#x17b;" u2="&#x11c;" k="15" />
    <hkern u1="&#x17b;" u2="&#x10c;" k="15" />
    <hkern u1="&#x17b;" u2="&#x10a;" k="15" />
    <hkern u1="&#x17b;" u2="&#x108;" k="15" />
    <hkern u1="&#x17b;" u2="&#x106;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd8;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd6;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd5;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd4;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd3;" k="15" />
    <hkern u1="&#x17b;" u2="&#xd2;" k="15" />
    <hkern u1="&#x17b;" u2="&#xc7;" k="15" />
    <hkern u1="&#x17b;" u2="&#xa9;" k="15" />
    <hkern u1="&#x17b;" u2="Q" k="15" />
    <hkern u1="&#x17b;" u2="O" k="15" />
    <hkern u1="&#x17b;" u2="G" k="15" />
    <hkern u1="&#x17b;" u2="C" k="15" />
    <hkern u1="&#x17b;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x17b;" u2="&#x2d;" k="100" />
    <hkern u1="&#x17b;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17c;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17c;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17c;" u2="&#x164;" k="60" />
    <hkern u1="&#x17c;" u2="&#x162;" k="60" />
    <hkern u1="&#x17c;" u2="T" k="60" />
    <hkern u1="&#x17c;" u2="&#x2d;" k="35" />
    <hkern u1="&#x17d;" u2="&#x2026;" k="-30" />
    <hkern u1="&#x17d;" u2="&#x2014;" k="100" />
    <hkern u1="&#x17d;" u2="&#x2013;" k="100" />
    <hkern u1="&#x17d;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x17d;" u2="&#x152;" k="15" />
    <hkern u1="&#x17d;" u2="&#x150;" k="15" />
    <hkern u1="&#x17d;" u2="&#x14e;" k="15" />
    <hkern u1="&#x17d;" u2="&#x14c;" k="15" />
    <hkern u1="&#x17d;" u2="&#x122;" k="15" />
    <hkern u1="&#x17d;" u2="&#x120;" k="15" />
    <hkern u1="&#x17d;" u2="&#x11e;" k="15" />
    <hkern u1="&#x17d;" u2="&#x11c;" k="15" />
    <hkern u1="&#x17d;" u2="&#x10c;" k="15" />
    <hkern u1="&#x17d;" u2="&#x10a;" k="15" />
    <hkern u1="&#x17d;" u2="&#x108;" k="15" />
    <hkern u1="&#x17d;" u2="&#x106;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="15" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="15" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="15" />
    <hkern u1="&#x17d;" u2="&#xa9;" k="15" />
    <hkern u1="&#x17d;" u2="Q" k="15" />
    <hkern u1="&#x17d;" u2="O" k="15" />
    <hkern u1="&#x17d;" u2="G" k="15" />
    <hkern u1="&#x17d;" u2="C" k="15" />
    <hkern u1="&#x17d;" u2="&#x2e;" k="-30" />
    <hkern u1="&#x17d;" u2="&#x2d;" k="100" />
    <hkern u1="&#x17d;" u2="&#x2c;" k="-30" />
    <hkern u1="&#x17e;" u2="&#x2014;" k="35" />
    <hkern u1="&#x17e;" u2="&#x2013;" k="35" />
    <hkern u1="&#x17e;" u2="&#x164;" k="60" />
    <hkern u1="&#x17e;" u2="&#x162;" k="60" />
    <hkern u1="&#x17e;" u2="T" k="60" />
    <hkern u1="&#x17e;" u2="&#x2d;" k="35" />
    <hkern u1="&#x1fa;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x1fa;" u2="&#x164;" k="25" />
    <hkern u1="&#x1fa;" u2="&#x162;" k="25" />
    <hkern u1="&#x1fa;" u2="V" k="10" />
    <hkern u1="&#x1fa;" u2="T" k="25" />
    <hkern u1="&#x1fa;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x1fa;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x1fc;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x1fc;" u2="&#x1fe;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x152;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x150;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x14e;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x14c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x122;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x120;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x11e;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x11c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x10c;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x10a;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x108;" k="7" />
    <hkern u1="&#x1fc;" u2="&#x106;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd8;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd6;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd5;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd4;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd3;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xd2;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xc7;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xa9;" k="7" />
    <hkern u1="&#x1fc;" u2="Q" k="7" />
    <hkern u1="&#x1fc;" u2="O" k="7" />
    <hkern u1="&#x1fc;" u2="G" k="7" />
    <hkern u1="&#x1fc;" u2="C" k="7" />
    <hkern u1="&#x1fc;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x1fc;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x1fd;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x219;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x174;" k="10" />
    <hkern u1="&#x1fd;" u2="&#x164;" k="70" />
    <hkern u1="&#x1fd;" u2="&#x162;" k="70" />
    <hkern u1="&#x1fd;" u2="&#x161;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15f;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15d;" k="-5" />
    <hkern u1="&#x1fd;" u2="&#x15b;" k="-5" />
    <hkern u1="&#x1fd;" u2="s" k="-5" />
    <hkern u1="&#x1fd;" u2="W" k="10" />
    <hkern u1="&#x1fd;" u2="V" k="30" />
    <hkern u1="&#x1fd;" u2="T" k="70" />
    <hkern u1="&#x1fe;" u2="&#x2026;" k="25" />
    <hkern u1="&#x1fe;" u2="&#x2014;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x2013;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x1fe;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x164;" k="15" />
    <hkern u1="&#x1fe;" u2="&#x162;" k="15" />
    <hkern u1="&#x1fe;" u2="&#x152;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x150;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x14e;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x14c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x134;" k="-20" />
    <hkern u1="&#x1fe;" u2="&#x122;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x120;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x11e;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x11c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x10c;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x10a;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x108;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x106;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd8;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd6;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd5;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd4;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd3;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xd2;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xc7;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#xa9;" k="-5" />
    <hkern u1="&#x1fe;" u2="X" k="10" />
    <hkern u1="&#x1fe;" u2="V" k="10" />
    <hkern u1="&#x1fe;" u2="T" k="15" />
    <hkern u1="&#x1fe;" u2="Q" k="-5" />
    <hkern u1="&#x1fe;" u2="O" k="-5" />
    <hkern u1="&#x1fe;" u2="J" k="-20" />
    <hkern u1="&#x1fe;" u2="G" k="-5" />
    <hkern u1="&#x1fe;" u2="C" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x2e;" k="25" />
    <hkern u1="&#x1fe;" u2="&#x2d;" k="-15" />
    <hkern u1="&#x1fe;" u2="&#x2c;" k="25" />
    <hkern u1="&#x1ff;" u2="&#x2026;" k="20" />
    <hkern u1="&#x1ff;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x174;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x164;" k="75" />
    <hkern u1="&#x1ff;" u2="&#x162;" k="75" />
    <hkern u1="&#x1ff;" u2="z" k="20" />
    <hkern u1="&#x1ff;" u2="x" k="10" />
    <hkern u1="&#x1ff;" u2="W" k="10" />
    <hkern u1="&#x1ff;" u2="V" k="20" />
    <hkern u1="&#x1ff;" u2="T" k="75" />
    <hkern u1="&#x1ff;" u2="&#x2e;" k="20" />
    <hkern u1="&#x1ff;" u2="&#x2c;" k="20" />
    <hkern u1="&#x218;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x218;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x218;" u2="&#x218;" k="10" />
    <hkern u1="&#x218;" u2="&#x1fe;" k="5" />
    <hkern u1="&#x218;" u2="&#x178;" k="25" />
    <hkern u1="&#x218;" u2="&#x176;" k="25" />
    <hkern u1="&#x218;" u2="&#x164;" k="15" />
    <hkern u1="&#x218;" u2="&#x162;" k="15" />
    <hkern u1="&#x218;" u2="&#x160;" k="10" />
    <hkern u1="&#x218;" u2="&#x15e;" k="10" />
    <hkern u1="&#x218;" u2="&#x15c;" k="10" />
    <hkern u1="&#x218;" u2="&#x15a;" k="10" />
    <hkern u1="&#x218;" u2="&#x152;" k="5" />
    <hkern u1="&#x218;" u2="&#x150;" k="5" />
    <hkern u1="&#x218;" u2="&#x14e;" k="5" />
    <hkern u1="&#x218;" u2="&#x14c;" k="5" />
    <hkern u1="&#x218;" u2="&#x122;" k="5" />
    <hkern u1="&#x218;" u2="&#x120;" k="5" />
    <hkern u1="&#x218;" u2="&#x11e;" k="5" />
    <hkern u1="&#x218;" u2="&#x11c;" k="5" />
    <hkern u1="&#x218;" u2="&#x10c;" k="5" />
    <hkern u1="&#x218;" u2="&#x10a;" k="5" />
    <hkern u1="&#x218;" u2="&#x108;" k="5" />
    <hkern u1="&#x218;" u2="&#x106;" k="5" />
    <hkern u1="&#x218;" u2="&#xdd;" k="25" />
    <hkern u1="&#x218;" u2="&#xd8;" k="5" />
    <hkern u1="&#x218;" u2="&#xd6;" k="5" />
    <hkern u1="&#x218;" u2="&#xd5;" k="5" />
    <hkern u1="&#x218;" u2="&#xd4;" k="5" />
    <hkern u1="&#x218;" u2="&#xd3;" k="5" />
    <hkern u1="&#x218;" u2="&#xd2;" k="5" />
    <hkern u1="&#x218;" u2="&#xc7;" k="5" />
    <hkern u1="&#x218;" u2="&#xa9;" k="5" />
    <hkern u1="&#x218;" u2="Y" k="25" />
    <hkern u1="&#x218;" u2="T" k="15" />
    <hkern u1="&#x218;" u2="S" k="10" />
    <hkern u1="&#x218;" u2="Q" k="5" />
    <hkern u1="&#x218;" u2="O" k="5" />
    <hkern u1="&#x218;" u2="G" k="5" />
    <hkern u1="&#x218;" u2="C" k="5" />
    <hkern u1="&#x218;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x218;" u2="&#x2c;" k="-10" />
    <hkern u1="&#x219;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x219;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x219;" u2="&#x219;" k="20" />
    <hkern u1="&#x219;" u2="&#x164;" k="70" />
    <hkern u1="&#x219;" u2="&#x162;" k="70" />
    <hkern u1="&#x219;" u2="&#x161;" k="20" />
    <hkern u1="&#x219;" u2="&#x15f;" k="20" />
    <hkern u1="&#x219;" u2="&#x15d;" k="20" />
    <hkern u1="&#x219;" u2="&#x15b;" k="20" />
    <hkern u1="&#x219;" u2="s" k="20" />
    <hkern u1="&#x219;" u2="T" k="70" />
    <hkern u1="&#x1e80;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xef;" k="-40" />
    <hkern u1="&#x1e80;" u2="&#xee;" k="-20" />
    <hkern u1="&#x1e80;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e80;" u2="q" k="10" />
    <hkern u1="&#x1e80;" u2="o" k="10" />
    <hkern u1="&#x1e80;" u2="g" k="10" />
    <hkern u1="&#x1e80;" u2="e" k="10" />
    <hkern u1="&#x1e80;" u2="d" k="10" />
    <hkern u1="&#x1e80;" u2="c" k="10" />
    <hkern u1="&#x1e80;" u2="a" k="10" />
    <hkern u1="&#x1e80;" u2="_" k="45" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e80;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e80;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e81;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e81;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e81;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e81;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e81;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e81;" u2="_" k="35" />
    <hkern u1="&#x1e81;" u2="T" k="55" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e81;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e81;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e81;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1e82;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e82;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xef;" k="-40" />
    <hkern u1="&#x1e82;" u2="&#xee;" k="-20" />
    <hkern u1="&#x1e82;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e82;" u2="q" k="10" />
    <hkern u1="&#x1e82;" u2="o" k="10" />
    <hkern u1="&#x1e82;" u2="g" k="10" />
    <hkern u1="&#x1e82;" u2="e" k="10" />
    <hkern u1="&#x1e82;" u2="d" k="10" />
    <hkern u1="&#x1e82;" u2="c" k="10" />
    <hkern u1="&#x1e82;" u2="a" k="10" />
    <hkern u1="&#x1e82;" u2="_" k="45" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e82;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e82;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e83;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e83;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e83;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e83;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e83;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e83;" u2="_" k="35" />
    <hkern u1="&#x1e83;" u2="T" k="55" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e83;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e83;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e83;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1e84;" u2="&#x2026;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2014;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x2013;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x153;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x151;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x14f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x14d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x123;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x121;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x11b;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x119;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x117;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x115;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x113;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x111;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10f;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10d;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x10b;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x109;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x107;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x105;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x103;" k="10" />
    <hkern u1="&#x1e84;" u2="&#x101;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf8;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf6;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf5;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf4;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf3;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf2;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xf0;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xef;" k="-40" />
    <hkern u1="&#x1e84;" u2="&#xee;" k="-20" />
    <hkern u1="&#x1e84;" u2="&#xeb;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xea;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe9;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe8;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe7;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe6;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe5;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe4;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe3;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe2;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe1;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xe0;" k="10" />
    <hkern u1="&#x1e84;" u2="q" k="10" />
    <hkern u1="&#x1e84;" u2="o" k="10" />
    <hkern u1="&#x1e84;" u2="g" k="10" />
    <hkern u1="&#x1e84;" u2="e" k="10" />
    <hkern u1="&#x1e84;" u2="d" k="10" />
    <hkern u1="&#x1e84;" u2="c" k="10" />
    <hkern u1="&#x1e84;" u2="a" k="10" />
    <hkern u1="&#x1e84;" u2="_" k="45" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2e;" k="55" />
    <hkern u1="&#x1e84;" u2="&#x2d;" k="20" />
    <hkern u1="&#x1e84;" u2="&#x2c;" k="55" />
    <hkern u1="&#x1e85;" u2="&#x2026;" k="65" />
    <hkern u1="&#x1e85;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x1e85;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x1e85;" u2="&#x2014;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x2013;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x164;" k="55" />
    <hkern u1="&#x1e85;" u2="&#x162;" k="55" />
    <hkern u1="&#x1e85;" u2="_" k="35" />
    <hkern u1="&#x1e85;" u2="T" k="55" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1e85;" u2="&#x2e;" k="65" />
    <hkern u1="&#x1e85;" u2="&#x2d;" k="10" />
    <hkern u1="&#x1e85;" u2="&#x2c;" k="65" />
    <hkern u1="&#x1ef2;" u2="X" k="-25" />
    <hkern u1="&#x1ef2;" u2="V" k="-15" />
    <hkern u1="&#x1ef3;" u2="&#x164;" k="70" />
    <hkern u1="&#x1ef3;" u2="&#x162;" k="70" />
    <hkern u1="&#x1ef3;" u2="V" k="20" />
    <hkern u1="&#x1ef3;" u2="T" k="70" />
    <hkern u1="&#x2013;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2013;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2013;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2013;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2013;" u2="&#x218;" k="65" />
    <hkern u1="&#x2013;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x17d;" k="65" />
    <hkern u1="&#x2013;" u2="&#x17b;" k="65" />
    <hkern u1="&#x2013;" u2="&#x179;" k="65" />
    <hkern u1="&#x2013;" u2="&#x178;" k="10" />
    <hkern u1="&#x2013;" u2="&#x176;" k="10" />
    <hkern u1="&#x2013;" u2="&#x175;" k="10" />
    <hkern u1="&#x2013;" u2="&#x174;" k="20" />
    <hkern u1="&#x2013;" u2="&#x164;" k="110" />
    <hkern u1="&#x2013;" u2="&#x162;" k="110" />
    <hkern u1="&#x2013;" u2="&#x160;" k="65" />
    <hkern u1="&#x2013;" u2="&#x15e;" k="65" />
    <hkern u1="&#x2013;" u2="&#x15c;" k="65" />
    <hkern u1="&#x2013;" u2="&#x15a;" k="65" />
    <hkern u1="&#x2013;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2013;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2013;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2013;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2013;" u2="z" k="35" />
    <hkern u1="&#x2013;" u2="x" k="20" />
    <hkern u1="&#x2013;" u2="w" k="10" />
    <hkern u1="&#x2013;" u2="Z" k="65" />
    <hkern u1="&#x2013;" u2="Y" k="10" />
    <hkern u1="&#x2013;" u2="X" k="65" />
    <hkern u1="&#x2013;" u2="W" k="20" />
    <hkern u1="&#x2013;" u2="V" k="35" />
    <hkern u1="&#x2013;" u2="T" k="110" />
    <hkern u1="&#x2013;" u2="S" k="65" />
    <hkern u1="&#x2013;" u2="Q" k="-15" />
    <hkern u1="&#x2013;" u2="O" k="-15" />
    <hkern u1="&#x2013;" u2="G" k="-15" />
    <hkern u1="&#x2013;" u2="C" k="-15" />
    <hkern u1="&#x2014;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x2014;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x2014;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x2014;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x2014;" u2="&#x218;" k="65" />
    <hkern u1="&#x2014;" u2="&#x1fe;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x17d;" k="65" />
    <hkern u1="&#x2014;" u2="&#x17b;" k="65" />
    <hkern u1="&#x2014;" u2="&#x179;" k="65" />
    <hkern u1="&#x2014;" u2="&#x178;" k="10" />
    <hkern u1="&#x2014;" u2="&#x176;" k="10" />
    <hkern u1="&#x2014;" u2="&#x175;" k="10" />
    <hkern u1="&#x2014;" u2="&#x174;" k="20" />
    <hkern u1="&#x2014;" u2="&#x164;" k="110" />
    <hkern u1="&#x2014;" u2="&#x162;" k="110" />
    <hkern u1="&#x2014;" u2="&#x160;" k="65" />
    <hkern u1="&#x2014;" u2="&#x15e;" k="65" />
    <hkern u1="&#x2014;" u2="&#x15c;" k="65" />
    <hkern u1="&#x2014;" u2="&#x15a;" k="65" />
    <hkern u1="&#x2014;" u2="&#x152;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x150;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x14e;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x14c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x122;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x120;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x11e;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x11c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x10c;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x10a;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x108;" k="-15" />
    <hkern u1="&#x2014;" u2="&#x106;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="10" />
    <hkern u1="&#x2014;" u2="&#xd8;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd6;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd5;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd4;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd3;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xd2;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xc7;" k="-15" />
    <hkern u1="&#x2014;" u2="&#xa9;" k="-15" />
    <hkern u1="&#x2014;" u2="z" k="35" />
    <hkern u1="&#x2014;" u2="x" k="20" />
    <hkern u1="&#x2014;" u2="w" k="10" />
    <hkern u1="&#x2014;" u2="Z" k="65" />
    <hkern u1="&#x2014;" u2="Y" k="10" />
    <hkern u1="&#x2014;" u2="X" k="65" />
    <hkern u1="&#x2014;" u2="W" k="20" />
    <hkern u1="&#x2014;" u2="V" k="35" />
    <hkern u1="&#x2014;" u2="T" k="110" />
    <hkern u1="&#x2014;" u2="S" k="65" />
    <hkern u1="&#x2014;" u2="Q" k="-15" />
    <hkern u1="&#x2014;" u2="O" k="-15" />
    <hkern u1="&#x2014;" u2="G" k="-15" />
    <hkern u1="&#x2014;" u2="C" k="-15" />
    <hkern u1="&#x2018;" u2="&#x2026;" k="140" />
    <hkern u1="&#x2018;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x2018;" u2="&#x153;" k="40" />
    <hkern u1="&#x2018;" u2="&#x151;" k="40" />
    <hkern u1="&#x2018;" u2="&#x14f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x14d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x123;" k="40" />
    <hkern u1="&#x2018;" u2="&#x121;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x11b;" k="40" />
    <hkern u1="&#x2018;" u2="&#x119;" k="40" />
    <hkern u1="&#x2018;" u2="&#x117;" k="40" />
    <hkern u1="&#x2018;" u2="&#x115;" k="40" />
    <hkern u1="&#x2018;" u2="&#x113;" k="40" />
    <hkern u1="&#x2018;" u2="&#x111;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10f;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10d;" k="40" />
    <hkern u1="&#x2018;" u2="&#x10b;" k="40" />
    <hkern u1="&#x2018;" u2="&#x109;" k="40" />
    <hkern u1="&#x2018;" u2="&#x107;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="40" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="40" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="40" />
    <hkern u1="&#x2018;" u2="&#xea;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="40" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="40" />
    <hkern u1="&#x2018;" u2="q" k="40" />
    <hkern u1="&#x2018;" u2="o" k="40" />
    <hkern u1="&#x2018;" u2="g" k="40" />
    <hkern u1="&#x2018;" u2="e" k="40" />
    <hkern u1="&#x2018;" u2="d" k="40" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="140" />
    <hkern u1="&#x2019;" u2="&#x2026;" k="140" />
    <hkern u1="&#x2019;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x2019;" u2="&#x153;" k="40" />
    <hkern u1="&#x2019;" u2="&#x151;" k="40" />
    <hkern u1="&#x2019;" u2="&#x14f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x14d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x123;" k="40" />
    <hkern u1="&#x2019;" u2="&#x121;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x11b;" k="40" />
    <hkern u1="&#x2019;" u2="&#x119;" k="40" />
    <hkern u1="&#x2019;" u2="&#x117;" k="40" />
    <hkern u1="&#x2019;" u2="&#x115;" k="40" />
    <hkern u1="&#x2019;" u2="&#x113;" k="40" />
    <hkern u1="&#x2019;" u2="&#x111;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10f;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10d;" k="40" />
    <hkern u1="&#x2019;" u2="&#x10b;" k="40" />
    <hkern u1="&#x2019;" u2="&#x109;" k="40" />
    <hkern u1="&#x2019;" u2="&#x107;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="40" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="40" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="40" />
    <hkern u1="&#x2019;" u2="&#xea;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="40" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="40" />
    <hkern u1="&#x2019;" u2="q" k="40" />
    <hkern u1="&#x2019;" u2="o" k="40" />
    <hkern u1="&#x2019;" u2="g" k="40" />
    <hkern u1="&#x2019;" u2="e" k="40" />
    <hkern u1="&#x2019;" u2="d" k="40" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201c;" u2="&#x2026;" k="140" />
    <hkern u1="&#x201c;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x201c;" u2="&#x153;" k="40" />
    <hkern u1="&#x201c;" u2="&#x151;" k="40" />
    <hkern u1="&#x201c;" u2="&#x14f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x14d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x123;" k="40" />
    <hkern u1="&#x201c;" u2="&#x121;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x11b;" k="40" />
    <hkern u1="&#x201c;" u2="&#x119;" k="40" />
    <hkern u1="&#x201c;" u2="&#x117;" k="40" />
    <hkern u1="&#x201c;" u2="&#x115;" k="40" />
    <hkern u1="&#x201c;" u2="&#x113;" k="40" />
    <hkern u1="&#x201c;" u2="&#x111;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10f;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10d;" k="40" />
    <hkern u1="&#x201c;" u2="&#x10b;" k="40" />
    <hkern u1="&#x201c;" u2="&#x109;" k="40" />
    <hkern u1="&#x201c;" u2="&#x107;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="40" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="40" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="40" />
    <hkern u1="&#x201c;" u2="&#xea;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="40" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="40" />
    <hkern u1="&#x201c;" u2="q" k="40" />
    <hkern u1="&#x201c;" u2="o" k="40" />
    <hkern u1="&#x201c;" u2="g" k="40" />
    <hkern u1="&#x201c;" u2="e" k="40" />
    <hkern u1="&#x201c;" u2="d" k="40" />
    <hkern u1="&#x201c;" u2="c" k="40" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201c;" u2="&#x2c;" k="140" />
    <hkern u1="&#x201d;" u2="&#x2026;" k="140" />
    <hkern u1="&#x201d;" u2="&#x1ff;" k="40" />
    <hkern u1="&#x201d;" u2="&#x153;" k="40" />
    <hkern u1="&#x201d;" u2="&#x151;" k="40" />
    <hkern u1="&#x201d;" u2="&#x14f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x14d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x123;" k="40" />
    <hkern u1="&#x201d;" u2="&#x121;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x11b;" k="40" />
    <hkern u1="&#x201d;" u2="&#x119;" k="40" />
    <hkern u1="&#x201d;" u2="&#x117;" k="40" />
    <hkern u1="&#x201d;" u2="&#x115;" k="40" />
    <hkern u1="&#x201d;" u2="&#x113;" k="40" />
    <hkern u1="&#x201d;" u2="&#x111;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10f;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10d;" k="40" />
    <hkern u1="&#x201d;" u2="&#x10b;" k="40" />
    <hkern u1="&#x201d;" u2="&#x109;" k="40" />
    <hkern u1="&#x201d;" u2="&#x107;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="40" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="40" />
    <hkern u1="&#x201d;" u2="&#xeb;" k="40" />
    <hkern u1="&#x201d;" u2="&#xea;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe9;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe8;" k="40" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="40" />
    <hkern u1="&#x201d;" u2="q" k="40" />
    <hkern u1="&#x201d;" u2="o" k="40" />
    <hkern u1="&#x201d;" u2="g" k="40" />
    <hkern u1="&#x201d;" u2="e" k="40" />
    <hkern u1="&#x201d;" u2="d" k="40" />
    <hkern u1="&#x201d;" u2="c" k="40" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="140" />
    <hkern u1="&#x201d;" u2="&#x2c;" k="140" />
    <hkern u1="&#x2026;" g2="fl" k="10" />
    <hkern u1="&#x2026;" g2="fi" k="10" />
    <hkern u1="&#x2026;" u2="&#x2039;" k="50" />
    <hkern u1="&#x2026;" u2="&#x201d;" k="60" />
    <hkern u1="&#x2026;" u2="&#x201c;" k="60" />
    <hkern u1="&#x2026;" u2="&#x2019;" k="60" />
    <hkern u1="&#x2026;" u2="&#x2018;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x2026;" u2="&#x1ef2;" k="135" />
    <hkern u1="&#x2026;" u2="&#x1e85;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e84;" k="55" />
    <hkern u1="&#x2026;" u2="&#x1e83;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e82;" k="55" />
    <hkern u1="&#x2026;" u2="&#x1e81;" k="65" />
    <hkern u1="&#x2026;" u2="&#x1e80;" k="55" />
    <hkern u1="&#x2026;" u2="&#x218;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x2026;" u2="&#x1fe;" k="25" />
    <hkern u1="&#x2026;" u2="&#x1fc;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x1fa;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x17f;" k="10" />
    <hkern u1="&#x2026;" u2="&#x17d;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x17b;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x179;" k="-30" />
    <hkern u1="&#x2026;" u2="&#x178;" k="135" />
    <hkern u1="&#x2026;" u2="&#x177;" k="20" />
    <hkern u1="&#x2026;" u2="&#x176;" k="135" />
    <hkern u1="&#x2026;" u2="&#x175;" k="65" />
    <hkern u1="&#x2026;" u2="&#x174;" k="55" />
    <hkern u1="&#x2026;" u2="&#x173;" k="20" />
    <hkern u1="&#x2026;" u2="&#x172;" k="20" />
    <hkern u1="&#x2026;" u2="&#x171;" k="20" />
    <hkern u1="&#x2026;" u2="&#x170;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16e;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16c;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x16a;" k="20" />
    <hkern u1="&#x2026;" u2="&#x169;" k="20" />
    <hkern u1="&#x2026;" u2="&#x168;" k="20" />
    <hkern u1="&#x2026;" u2="&#x167;" k="20" />
    <hkern u1="&#x2026;" u2="&#x165;" k="20" />
    <hkern u1="&#x2026;" u2="&#x164;" k="110" />
    <hkern u1="&#x2026;" u2="&#x163;" k="20" />
    <hkern u1="&#x2026;" u2="&#x162;" k="110" />
    <hkern u1="&#x2026;" u2="&#x160;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15e;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15c;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x15a;" k="-20" />
    <hkern u1="&#x2026;" u2="&#x153;" k="20" />
    <hkern u1="&#x2026;" u2="&#x152;" k="25" />
    <hkern u1="&#x2026;" u2="&#x151;" k="20" />
    <hkern u1="&#x2026;" u2="&#x150;" k="25" />
    <hkern u1="&#x2026;" u2="&#x14f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x14e;" k="25" />
    <hkern u1="&#x2026;" u2="&#x14d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x14c;" k="25" />
    <hkern u1="&#x2026;" u2="&#x134;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x123;" k="20" />
    <hkern u1="&#x2026;" u2="&#x122;" k="25" />
    <hkern u1="&#x2026;" u2="&#x121;" k="20" />
    <hkern u1="&#x2026;" u2="&#x120;" k="25" />
    <hkern u1="&#x2026;" u2="&#x11f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11e;" k="25" />
    <hkern u1="&#x2026;" u2="&#x11d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11c;" k="25" />
    <hkern u1="&#x2026;" u2="&#x11b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x11a;" k="20" />
    <hkern u1="&#x2026;" u2="&#x119;" k="20" />
    <hkern u1="&#x2026;" u2="&#x118;" k="20" />
    <hkern u1="&#x2026;" u2="&#x117;" k="20" />
    <hkern u1="&#x2026;" u2="&#x116;" k="20" />
    <hkern u1="&#x2026;" u2="&#x115;" k="20" />
    <hkern u1="&#x2026;" u2="&#x114;" k="20" />
    <hkern u1="&#x2026;" u2="&#x113;" k="20" />
    <hkern u1="&#x2026;" u2="&#x112;" k="20" />
    <hkern u1="&#x2026;" u2="&#x111;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10d;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10c;" k="25" />
    <hkern u1="&#x2026;" u2="&#x10b;" k="20" />
    <hkern u1="&#x2026;" u2="&#x10a;" k="25" />
    <hkern u1="&#x2026;" u2="&#x109;" k="20" />
    <hkern u1="&#x2026;" u2="&#x108;" k="25" />
    <hkern u1="&#x2026;" u2="&#x107;" k="20" />
    <hkern u1="&#x2026;" u2="&#x106;" k="25" />
    <hkern u1="&#x2026;" u2="&#x104;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x102;" k="-10" />
    <hkern u1="&#x2026;" u2="&#x100;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xff;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfd;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf6;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf5;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf4;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf3;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf2;" k="20" />
    <hkern u1="&#x2026;" u2="&#xf0;" k="20" />
    <hkern u1="&#x2026;" u2="&#xeb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xea;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xe7;" k="20" />
    <hkern u1="&#x2026;" u2="&#xdd;" k="135" />
    <hkern u1="&#x2026;" u2="&#xdc;" k="20" />
    <hkern u1="&#x2026;" u2="&#xdb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xda;" k="20" />
    <hkern u1="&#x2026;" u2="&#xd9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xd8;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd6;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd5;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd4;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd3;" k="25" />
    <hkern u1="&#x2026;" u2="&#xd2;" k="25" />
    <hkern u1="&#x2026;" u2="&#xcb;" k="20" />
    <hkern u1="&#x2026;" u2="&#xca;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc9;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc8;" k="20" />
    <hkern u1="&#x2026;" u2="&#xc7;" k="25" />
    <hkern u1="&#x2026;" u2="&#xc6;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc5;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc4;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc3;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc2;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc1;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xc0;" k="-10" />
    <hkern u1="&#x2026;" u2="&#xab;" k="50" />
    <hkern u1="&#x2026;" u2="&#xa9;" k="25" />
    <hkern u1="&#x2026;" u2="y" k="20" />
    <hkern u1="&#x2026;" u2="x" k="-20" />
    <hkern u1="&#x2026;" u2="w" k="65" />
    <hkern u1="&#x2026;" u2="v" k="65" />
    <hkern u1="&#x2026;" u2="u" k="20" />
    <hkern u1="&#x2026;" u2="t" k="20" />
    <hkern u1="&#x2026;" u2="q" k="20" />
    <hkern u1="&#x2026;" u2="o" k="20" />
    <hkern u1="&#x2026;" u2="g" k="20" />
    <hkern u1="&#x2026;" u2="f" k="10" />
    <hkern u1="&#x2026;" u2="e" k="20" />
    <hkern u1="&#x2026;" u2="d" k="20" />
    <hkern u1="&#x2026;" u2="c" k="20" />
    <hkern u1="&#x2026;" u2="Z" k="-30" />
    <hkern u1="&#x2026;" u2="Y" k="135" />
    <hkern u1="&#x2026;" u2="X" k="-40" />
    <hkern u1="&#x2026;" u2="W" k="55" />
    <hkern u1="&#x2026;" u2="V" k="110" />
    <hkern u1="&#x2026;" u2="U" k="20" />
    <hkern u1="&#x2026;" u2="T" k="110" />
    <hkern u1="&#x2026;" u2="S" k="-20" />
    <hkern u1="&#x2026;" u2="Q" k="25" />
    <hkern u1="&#x2026;" u2="O" k="25" />
    <hkern u1="&#x2026;" u2="J" k="-10" />
    <hkern u1="&#x2026;" u2="G" k="25" />
    <hkern u1="&#x2026;" u2="E" k="20" />
    <hkern u1="&#x2026;" u2="C" k="25" />
    <hkern u1="&#x2026;" u2="A" k="-10" />
    <hkern u1="&#x203a;" u2="&#x2026;" k="50" />
    <hkern u1="&#x203a;" u2="&#x2e;" k="50" />
    <hkern u1="&#x203a;" u2="&#x2c;" k="50" />
    <hkern u1="&#x2122;" u2="&#x2026;" k="120" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="120" />
    <hkern u1="&#x2122;" u2="&#x2c;" k="120" />
    <hkern g1="comma,period,ellipsis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute"
	k="-10" />
    <hkern g1="comma,period,ellipsis"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="25" />
    <hkern g1="comma,period,ellipsis"
	g2="E,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="J,Jcircumflex"
	k="-10" />
    <hkern g1="comma,period,ellipsis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-20" />
    <hkern g1="comma,period,ellipsis"
	g2="T,Tcommaaccent,Tcaron"
	k="110" />
    <hkern g1="comma,period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="comma,period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="135" />
    <hkern g1="comma,period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-30" />
    <hkern g1="comma,period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="f,longs,fi,fl"
	k="10" />
    <hkern g1="comma,period,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="65" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteleft,quotedblleft"
	k="60" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteright,quotedblright"
	k="60" />
    <hkern g1="hyphen,endash,emdash"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-15" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="65" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron"
	k="110" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="65" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="hyphen,endash,emdash"
	k="75" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-5" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="-20" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="15" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,period,ellipsis"
	k="25" />
    <hkern g1="D,O,Q,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,endash,emdash"
	k="-15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,period,ellipsis"
	k="-50" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="T,Tcommaaccent,Tcaron"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="105" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteleft,quotedblleft"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteright,quotedblright"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="comma,period,ellipsis"
	k="-40" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen,endash,emdash"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="-30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="comma,period,ellipsis"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="15" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-25" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="u,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,ycircumflex,ygrave"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="comma,period,ellipsis"
	k="110" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="hyphen,endash,emdash"
	k="110" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,ellipsis"
	k="55" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute"
	k="-25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="-25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,ellipsis"
	k="-30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="100" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="75" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="T,Tcommaaccent,Tcaron"
	k="40" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="-5" />
    <hkern g1="g,q,y,yacute,ydieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,ycircumflex,ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="g,q,y,yacute,ydieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="25" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="T,Tcommaaccent,Tcaron"
	k="80" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="k,kcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="30" />
    <hkern g1="k,kcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="8" />
    <hkern g1="k,kcommaaccent"
	g2="comma,period,ellipsis"
	k="-30" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="k,kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="-10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="f,longs,fi,fl"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,ellipsis"
	k="105" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="40" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="70" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,ellipsis"
	k="65" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="60" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,ellipsis"
	k="140" />
    <hkern g1="quoteright,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,ellipsis"
	k="140" />
  </font>
</defs></svg>
