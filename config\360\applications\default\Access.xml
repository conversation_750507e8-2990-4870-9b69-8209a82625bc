<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<AccessConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" lastModifiedDate="0" appLastModifiedDate="1751968237065" xsi:noNamespaceSchemaLocation="">
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="J9L2H9rv" id="index" lastModifiedDate="1751968237065" synchronized="false">
        <Parameters/>
        <Properties/>
        <SubFeeds/>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="1uZm7puk" id="search" lastModifiedDate="1751968237065" synchronized="false">
        <Parameters>
            <Parameter name="q" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="KLLJl58a" id="cloudview" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="per_page" secured="false">10</Parameter>
                    <Parameter name="q" secured="false">${page.params['q']}</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="timezone" secured="false">${page.params['timezone']}</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:50000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="enableSecurity" secured="false">false</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>cache</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <WhiteListPatterns/>
    </Feed>
    <CustomComponentList>
        <component>com.exalead.gwt.bconsole.recommend.access.RecommendationTrigger</component>
    </CustomComponentList>
    <ConcurrencyPolicies threadPoolSize="16"/>
    <Options>
        <Option name="reporterName" secured="false" value="mashup-api-reporting"/>
    </Options>
</AccessConfiguration>
