<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<res:ResourceManagerConfig xmlns:bee="exa:exa.bee" xmlns:res="exa:com.exalead.mercury.mami.resources.v10" xmlns:config="exa:exa.bee.config" resourceDir="data:///build/resources" version="0">
    <res:ResourceGroup name="plugins" roles="searcher,analyzer,connectorserver,gateway,consolidation,turboservices" waitOnSync="true">
        <res:Resource name="enovia-er-connector-plugin-v6" type="raw" description="" author=""/>
        <res:Resource name="enovia-java-security-source" type="raw" description="" author=""/>
        <res:Resource name="plma-security-bridge" type="raw" description="" author=""/>
        <res:Resource name="plma-tools-core" type="raw" description="" author=""/>
    </res:ResourceGroup>
    <res:ResourceGroup name="dictionary-dict0" roles="Gateway,Analy<PERSON>,Searcher" waitOnSync="false">
        <res:Resource name="dict0" type="raw"/>
    </res:ResourceGroup>
</res:ResourceManagerConfig>
