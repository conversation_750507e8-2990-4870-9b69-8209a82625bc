<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<MashupUI xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" lastModifiedDate="0" appLastModifiedDate="1751968237065" type="mobile" xsi:noNamespaceSchemaLocation="">
    <MashupPage id="index" lastModifiedDate="1751968237065" migratorVersion="-1">
        <Options/>
        <Layout width="100" widthFormat="%">
            <Table>
                <ColsConfig>
                    <ColConfig idx="1000000.0" width="100"/>
                </ColsConfig>
                <Rows>
                    <Row rowIdx="1000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="1000000.0" rowEnd="2000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="mobileHeaderSearch" enable="true" wuid="9Schb79N" compositeWidget="false">
<Options>
    <Option name="inputName" secured="false" value="q"/>
    <Option name="searchLabel" secured="false" value="Search..."/>
    <Option name="action" secured="false" value="search"/>
    <Option name="geoEnable" secured="false" value="false"/>
    <Option name="geoLatitude" secured="false" value="lat"/>
    <Option name="geoLongitude" secured="false" value="lon"/>
    <Option name="geoMaximumAge" secured="false" value="5000"/>
    <Option name="iconEnable" secured="false" value="false"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="2000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="2000000.0" rowEnd="3000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="emptyBlock" enable="true" wuid="H2DKxHX7" compositeWidget="false">
<Options>
    <Option name="height" secured="false" value="25"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                            <Widget useWidgetId="logo" enable="true" wuid="jWLXpbQp" compositeWidget="false">
<Options>
    <Option name="urlImage" secured="false" value="/resources/logo/images/cvlogo_medium.png"/>
    <Option name="cssClass" secured="false" value="centered"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                            <Widget useWidgetId="emptyBlock" enable="true" wuid="iDMWiplF" compositeWidget="false">
<Options>
    <Option name="height" secured="false" value="25"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                            <Widget useWidgetId="mobileList" enable="true" wuid="c5lJWJbK" compositeWidget="false">
<Options>
    <Option name="listType" secured="false" value="unordered"/>
</Options>
<UseFeeds useParentEntry="false"/>
<Widget useWidgetId="mobileListItem" enable="true" wuid="1oX9w8YV" compositeWidget="false">
    <Options>
        <Option name="itemType" secured="false" value="link"/>
        <Option name="itemAction" secured="false" value="about"/>
        <Option name="itemText" secured="false" value="About"/>
    </Options>
    <UseFeeds useParentEntry="false"/>
</Widget>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="3000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="3000000.0" rowEnd="4000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                        </Cell>
                    </Row>
                </Rows>
            </Table>
        </Layout>
    </MashupPage>
    <MashupPage id="search" lastModifiedDate="1751968237065" migratorVersion="-1">
        <Options/>
        <Layout width="100" widthFormat="%">
            <Table>
                <ColsConfig>
                    <ColConfig idx="1000000.0" width="100"/>
                </ColsConfig>
                <Rows>
                    <Row rowIdx="1000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="1000000.0" rowEnd="2000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="mobileHeaderSearch" enable="true" wuid="SAGGQszr" compositeWidget="false">
<Options>
    <Option name="inputName" secured="false" value="q"/>
    <Option name="searchLabel" secured="false" value="Search..."/>
    <Option name="action" secured="false" value="search"/>
    <Option name="geoEnable" secured="false" value="false"/>
    <Option name="geoLatitude" secured="false" value="lat"/>
    <Option name="geoLongitude" secured="false" value="lon"/>
    <Option name="geoMaximumAge" secured="false" value="5000"/>
    <Option name="iconEnable" secured="false" value="true"/>
    <Option name="iconSrc" secured="false" value="images/icon.png"/>
    <Option name="iconUrl" secured="false" value="index"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="2000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="2000000.0" rowEnd="3000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="navigationHeader" enable="true" wuid="WdgZnxfy" compositeWidget="false">
<Options>
    <Option name="jspPath" secured="false" value="default.jsp"/>
    <Option name="templateBasePath" secured="false" value="templates/"/>
    <Option name="sortableMetas" secured="false" value="${i18n['relevance']}##text_relevance##desc"/>
    <Option name="sortableMetas" secured="false" value="${i18n['date']}##document_lastmodifieddate##desc"/>
    <Option name="sortableMetas" secured="false" value="${i18n['size']}##document_file_size##desc"/>
    <Option name="spellSuggestionEnable" secured="false" value="true"/>
    <Option name="spellSuggestionQueryParameter" secured="false" value="q"/>
    <Option name="spellSuggestionText" secured="false" value="${i18n['suggestion.didYouMean']}"/>
</Options>
<UseFeeds useParentEntry="false">
    <FeedId>cloudview</FeedId>
</UseFeeds>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="3000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="3000000.0" rowEnd="4000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="mobileResponsiveWrapper" enable="true" compositeWidget="false">
<Options>
    <Option name="minWidth" secured="false" value="650"/>
    <Option name="width" secured="false" value="70%"/>
    <Option name="position" secured="false" value="left"/>
</Options>
<UseFeeds useParentEntry="false"/>
<Widget useWidgetId="mobileDisplayHits" enable="true" wuid="uPgWm4qE" compositeWidget="false">
    <Options>
        <Option name="showHitIcon" secured="false" value="false"/>
        <Option name="showHitId" secured="false" value="false"/>
        <Option name="showTextOnTop" secured="false" value="true"/>
        <Option name="showTextOnTopTruncate" secured="false" value="500"/>
        <Option name="showPreview" secured="false" value="false"/>
        <Option name="showDownload" secured="false" value="false"/>
        <Option name="showThumbnail" secured="false" value="none"/>
        <Option name="buttonAlignment" secured="false" value="left"/>
        <Option name="showHitMetas" secured="false" value="true"/>
        <Option name="customDisplay" secured="false" value="false"/>
        <Option name="metaUrlTarget" secured="false" value="Current Page"/>
        <Option name="showEmptyMetas" secured="false" value="false"/>
        <Option name="filterMetas" secured="false" value="Exclude"/>
        <Option name="metas" secured="false" value="url, source, language, title, lastmodifieddate, displayurl, text"/>
        <Option name="sortModeMetas" secured="false" value="default"/>
        <Option name="showHitFacets" secured="false" value="false"/>
        <Option name="defaultJspPathHit" secured="false" value="default/default.jsp"/>
        <Option name="noResultsJspPathHit" secured="false" value="/WEB-INF/jsp/mobile/commons/noResults.jsp"/>
        <Option name="templateBasePath" secured="false" value="templates/"/>
        <Option name="displayType" secured="false" value="outset"/>
        <Option name="themeBackground" secured="false" value="d"/>
    </Options>
    <UseFeeds useParentEntry="false">
        <FeedId>cloudview</FeedId>
    </UseFeeds>
</Widget>
<Widget useWidgetId="pagination" enable="true" wuid="5nzukUgy" compositeWidget="false">
    <Trigger className="com.exalead.cv360.searchui.triggers.impl.RemoveIfNoEntries" enable="true"/>
    <Options>
        <Option name="jspPath" secured="false" value="default.jsp"/>
        <Option name="nbPageToShowInPagination" secured="false" value="9"/>
        <Option name="templateBasePath" secured="false" value="templates/"/>
    </Options>
    <UseFeeds useParentEntry="false">
        <FeedId>cloudview</FeedId>
    </UseFeeds>
</Widget>
                            </Widget>
                            <Widget useWidgetId="mobileResponsiveWrapper" enable="true" compositeWidget="false">
<Options>
    <Option name="minWidth" secured="false" value="650"/>
    <Option name="width" secured="false" value="29%"/>
    <Option name="position" secured="false" value="right"/>
</Options>
<UseFeeds useParentEntry="false"/>
<Widget useWidgetId="mobileRefines" enable="true" wuid="LqXZiZeB" compositeWidget="false">
    <Options>
        <Option name="title" secured="false" value="${i18n['refines.title']}"/>
        <Option name="filter" secured="false" value="No filtering"/>
        <Option name="values" secured="false" value="count"/>
        <Option name="facetTemplateDirectory" secured="false" value="default"/>
        <Option name="nbSubFacets" secured="false" value="1000"/>
        <Option name="templateBasePath" secured="false" value="templates/"/>
        <Option name="noResultsJspPathHit" secured="false" value="/WEB-INF/jsp/commons/noFacets.jsp"/>
    </Options>
    <UseFeeds useParentEntry="false">
        <FeedId>cloudview</FeedId>
    </UseFeeds>
</Widget>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="4000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="4000000.0" rowEnd="5000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                        </Cell>
                    </Row>
                    <Row rowIdx="5000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="5000000.0" rowEnd="6000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                        </Cell>
                    </Row>
                </Rows>
            </Table>
        </Layout>
    </MashupPage>
    <MashupPage id="about" lastModifiedDate="1751968237065" migratorVersion="-1">
        <Options/>
        <Layout width="100" widthFormat="%">
            <Table>
                <ColsConfig>
                    <ColConfig idx="1000000.0" width="100"/>
                </ColsConfig>
                <Rows>
                    <Row rowIdx="1000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="1000000.0" rowEnd="2000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="mobileHeaderSearch" enable="true" wuid="k5AZhtml" compositeWidget="false">
<Options>
    <Option name="inputName" secured="false" value="q"/>
    <Option name="searchLabel" secured="false" value="Search..."/>
    <Option name="action" secured="false" value="search"/>
    <Option name="geoEnable" secured="false" value="false"/>
    <Option name="geoLatitude" secured="false" value="lat"/>
    <Option name="geoLongitude" secured="false" value="lon"/>
    <Option name="geoMaximumAge" secured="false" value="5000"/>
    <Option name="iconEnable" secured="false" value="true"/>
    <Option name="iconSrc" secured="false" value="images/icon.png"/>
    <Option name="iconUrl" secured="false" value="index"/>
</Options>
<UseFeeds useParentEntry="false"/>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="2000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="2000000.0" rowEnd="3000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                            <Widget useWidgetId="mobileCollapsible" enable="true" wuid="8r0HoRxY" compositeWidget="false">
<Options>
    <Option name="header" secured="false" value="Lorem Ipsum"/>
    <Option name="collapsed" secured="false" value="true"/>
</Options>
<UseFeeds useParentEntry="false"/>
<Widget useWidgetId="customHtml" enable="true" wuid="B0RLGPzT" compositeWidget="false">
    <Options>
        <Option name="htmlRules" secured="false" value="Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."/>
        <Option name="disableWidgetTag" secured="false" value="true"/>
    </Options>
    <UseFeeds useParentEntry="false"/>
</Widget>
                            </Widget>
                        </Cell>
                    </Row>
                    <Row rowIdx="3000000.0">
                        <Options/>
                        <Cell colSpan="1" rowSpan="1" colStart="0.0" colEnd="1000000.0" rowStart="3000000.0" rowEnd="4000000.0">
                            <Options>
<Option name="valign" secured="false" value="top"/>
<Option name="align" secured="false" value="left"/>
                            </Options>
                        </Cell>
                    </Row>
                </Rows>
            </Table>
        </Layout>
    </MashupPage>
    <SpringControllers/>
    <Options>
        <Option name="reporterName" secured="false" value="mashup-ui-reporting"/>
        <Option name="displayStackTrace" secured="false" value="true"/>
        <Option name="sitemapEnabled" secured="false" value="false"/>
        <Option name="cssTheme" secured="false" value="theme_mobile"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/jquery-1.12.3.min.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/jquery-migrate-1.4.0.min.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/jquery.spinner.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/buildUrl.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/oop.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/mashupAjaxClient.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/mashupI18N.js"/>
        <Option name="jsFile" secured="false" value="/resources/javascript/exa/core.js"/>
        <Option name="usersCheck" secured="false" value="false"/>
    </Options>
    <CustomComponentList/>
</MashupUI>
