#Plugin properties
#Tue Jul 08 15:22:16 IST 2025
component.0.class=com.exalead.apps.plma.security.modules.ConsolidationSecuritySource
component.0.description=Consolidation security source
component.0.interface.0.class=com.exalead.apps.plma.security.modules.ConsolidationSecuritySource
component.0.interface.1.class=com.exalead.apps.plma.security.bridge.BridgeSecuritySource
component.0.interface.2.class=com.exalead.mercury.component.CVComponent
component.0.interface.3.class=com.exalead.security.sources.common.SecuritySource
component.0.interface.4.class=com.exalead.mercury.component.CVComponent
component.0.interface.5.class=java.lang.Object
component.0.label=Consolidation security source
component.1.class=com.exalead.apps.plma.security.modules.PNOSecuritySource
component.1.description=ENOVIA PNO (WS) security source
component.1.interface.0.class=com.exalead.apps.plma.security.modules.PNOSecuritySource
component.1.interface.1.class=com.exalead.apps.plma.security.bridge.BridgeSecuritySource
component.1.interface.2.class=com.exalead.mercury.component.CVComponent
component.1.interface.3.class=com.exalead.security.sources.common.SecuritySource
component.1.interface.4.class=com.exalead.mercury.component.CVComponent
component.1.interface.5.class=java.lang.Object
component.1.label=ENOVIA PNO (WS) security source
component.2.class=com.exalead.apps.plma.security.modules.SAPISecuritySource
component.2.description=SAPI security source
component.2.interface.0.class=com.exalead.apps.plma.security.modules.SAPISecuritySource
component.2.interface.1.class=com.exalead.apps.plma.security.bridge.BridgeSecuritySource
component.2.interface.2.class=com.exalead.mercury.component.CVComponent
component.2.interface.3.class=com.exalead.security.sources.common.SecuritySource
component.2.interface.4.class=com.exalead.mercury.component.CVComponent
component.2.interface.5.class=java.lang.Object
component.2.label=SAPI security source
debug.classes.count=302
debug.destination.path=D\:\\WS\\15000_PIU\\bin\\..\\tmp\\plugin-install
debug.jar.count=3
lastmodified=1751968336518
plugin.reverse-classloading=false
timestamp=1751968336509
