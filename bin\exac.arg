${NGINSTALLDIR}/${NGARCH}/bin/exa
-lib
${NGINSTALLDIR}/exabin/mercury.lib
-outbin
${NGDATADIR}/exabin/mercury.bin
-srcpath
${NGDATADIR}/src/exa
-p
com.exalead.bee.ken
-p
com.exalead.box.multibox.helper
-p
com.exalead.box3.bee
-p
com.exalead.box3.dump
-p
com.exalead.box3.utils
-p
com.exalead.cloudview.indexing.helper
-p
com.exalead.crawl.box
-p
com.exalead.crawl.dns
-p
com.exalead.crawl.fifo
-p
com.exalead.crawl.fastfilter
-p
com.exalead.crawl.http.hostbox
-p
com.exalead.crawl.http.httpget
-p
com.exalead.crawl.http.robots
-p
com.exalead.crawl.log
-p
com.exalead.crawl.mime.html
-p
com.exalead.crawl.mime.rss
-p
com.exalead.crawl.mime.sitemap
-p
com.exalead.crawl.mime.text
-p
com.exalead.crawl.proxy
-p
com.exalead.crawl.proxy.helper
-p
com.exalead.crawl.proxy.v10
-p
com.exalead.crawl.scheduler.web
-p
com.exalead.ds.share2.http.helper
-p
com.exalead.mercury.cache
-p
com.exalead.mercury.dih
-p
com.exalead.mercury.feedfetcher
-p
com.exalead.mercury.hostagent
-p
com.exalead.mercury.fetch.dns
-p
com.exalead.mercury.fetch.helper
-p
com.exalead.mercury.fetch.http
-p
com.exalead.mercury.mami.crawl
-p
com.exalead.mercury.mami.deploy
-p
com.exalead.mercury.mami.fetch
-p
com.exalead.mercury.util
-p
com.exalead.mercury.util.windows
-p
com.exalead.mot.components.sentimentanalyzer
-p
com.exalead.reporting.sqlite.helper
-p
com.exalead.reporting.sqlite.messages
-p
com.exalead.util.mime
-p
exa.log.append
-p
exa.io.http
