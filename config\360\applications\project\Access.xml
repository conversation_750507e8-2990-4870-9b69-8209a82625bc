<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<AccessConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" lastModifiedDate="1507719026" appLastModifiedDate="1614259010022" xsi:noNamespaceSchemaLocation="">
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="28AgcssV" id="index" lastModifiedDate="1614325726772" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="h1vKEmcG" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">${if page.params['bo_type']}hierarchical_types:${page.params['bo_type']}${/if} ${if page.params['q']}${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##related_project,current,collaborative_space,owner,type</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,vf_afd,vf_efd,vf_afd_present,vf_efd_present,vf_act_est_same_interval,rpn_probability,rpn_impact,real_rpn,idle</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##vroot##Top/plan_finish_date</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##expr##project_planned_finish_date</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_week##false</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_week##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.forecast_count##SUM(#length(project_actual_finish_date)?0:1)</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##vroot##Top/act_finish_date</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##expr##vf_afd</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_week##false</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">rpn##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##vroot##Top/rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##expr##real_rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##range##0,4,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##range##5,14,Yellow</Parameter>
                    <Parameter name="f" secured="false">rpn##range##15,25,Red</Parameter>
                    <Parameter name="f" secured="false">rpn##sort##explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##explicit_sort_order_values##Red,Yellow,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##type##multi</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##vroot##Top/risk_by_project</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##1.id##rpn</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##in_hits##false</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="idleness##idle##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                    <Parameter name="typeFilterNames" secured="false" value="typeRangeFilter"></Parameter>
                    <Parameter name="typeFilter" secured="false" value="program_originated##(originated &gt; &quot;${page.params['inputStartDate']}&quot; AND originated &lt; &quot;${page.params['inputEndDate']}&quot;)"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.scurve.CreateMergedFacetForSCurve" enable="true">
                    <Parameter name="newFacetId" secured="false" value="scurve"></Parameter>
                    <Parameter name="plannedFacetId" secured="false" value="plan_finish_date"></Parameter>
                    <Parameter name="plannedAgg" secured="false" value="count"></Parameter>
                    <Parameter name="actualFacetId" secured="false" value="act_finish_date"></Parameter>
                    <Parameter name="actualAgg" secured="false" value="count"></Parameter>
                    <Parameter name="forecastFacetId" secured="false" value="est_finish_date"></Parameter>
                    <Parameter name="forecastAgg" secured="false" value="forecast_count"></Parameter>
                    <Parameter name="forceJoinForecast" secured="false" value="false"></Parameter>
                    <Parameter name="forecastVariance" secured="false" value="true"></Parameter>
                    <Parameter name="debugTodayVal" secured="false" value="${page.params['today']}"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>per_page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="edbbGgBL" id="open_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">${if page.params['bo_type']}hierarchical_types:${page.params['bo_type']}${/if} document_is_open:1</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,lateness</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="lateness##lateness##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('open_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                    <Parameter name="typeFilterNames" secured="false" value="typeRangeFilter"></Parameter>
                    <Parameter name="typeFilter" secured="false" value="program_originated##(originated &gt; &quot;${page.params['inputStartDate']}&quot; AND originated &lt; &quot;${page.params['inputEndDate']}&quot;)"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="KgB5sFRK" id="history_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">${if page.params['bo_type']}hierarchical_types:${page.params['bo_type']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">NOT history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##current, history_weekofyear,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isSnapshot,isTransition</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.history##SUM(isSnapshot)</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.transition##SUM(isTransition)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('history_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                    <Parameter name="typeFilterNames" secured="false" value="typeRangeFilter"></Parameter>
                    <Parameter name="typeFilter" secured="false" value="program_originated##(originated &gt; &quot;${page.params['inputStartDate']}&quot; AND originated &lt; &quot;${page.params['inputEndDate']}&quot;)"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="false">
            <Parameter name="facetList" secured="false" value="owner##CATEGORY##project"></Parameter>
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="search" lastModifiedDate="1618986470895" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
            <Parameter name="related_bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="N3a7kZvW" id="hit" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND id:${page.params['hit']} ${if page.params['q']}OPT ${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">1</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="add_highlight" secured="false">name</Parameter>
                    <Parameter name="add_highlight" secured="false">type</Parameter>
                    <Parameter name="add_highlight" secured="false">owner</Parameter>
                    <Parameter name="add_highlight" secured="false">hierarchical_types</Parameter>
                    <Parameter name="add_highlight" secured="false">problem_type</Parameter>
                    <Parameter name="add_highlight" secured="false">classification</Parameter>
                    <Parameter name="add_highlight" secured="false">issue_category</Parameter>
                    <Parameter name="add_highlight" secured="false">to_assigned_issue</Parameter>
                    <Parameter name="add_highlight" secured="false">to_reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">part_name</Parameter>
                    <Parameter name="add_highlight" secured="false">description</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="enovia_link">${app:getProperty('project', 'enovia_link')}${entry.metas['id']}</Property>
                </Properties>
                <SubFeeds>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="dRaIH5Fw" id="related_count" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">related:"${feeds['hit'].entry.metas['id']}" AND history_type=current</Parameter>
                            <Parameter name="per_page" secured="false">0</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##hierarchical_types</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                        </Properties>
                        <SubFeeds/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="9vOOGc18" id="related_objects" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">related:"${feeds['hit'].entry.metas['id']}"</Parameter>
                            <Parameter name="per_page" secured="false">10</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="restriction" secured="false">(NOT id:"${feeds['hit'].entries[0].metas['id']}") AND hierarchical_types:"${page.params['related_bo_type']}" AND history_type:current </Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_program_class">${feeds["hit"].entry.metas["program_class"]}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_id">${feeds["hit"].entry.metas['id']}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_title">${feeds["hit"].entry.metas['title']}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_type">${feeds["hit"].entry.metas['type']}</Property>
                        </Properties>
                        <SubFeeds/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                            <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) == 0}"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                            <Pattern>related_bo_type</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                </SubFeeds>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['hit']) == 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="YZQLIONp" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">${if page.params['q']}(${page.params['q']})${/if} </Parameter>
                    <Parameter name="per_page" secured="false">20</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">hierarchical_types:(project OR issue) AND history_type:current ${if page.params['viewcollection'] == 'pin'} AND (${page.params['pin_hits_q']}) ${elseif page.params['viewcollection'] == 'compare'} AND (${page.params['compare_hits_q']}) ${/if}</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">timeout##0</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,num_opening_days_project,rpn_probability,rpn_impact,real_rpn,late_issue,late_project,late,num_closure_days_project,num_opening_days_issue,num_opening_days,num_closure_days_issue,num_closure_days,age_project,age_issue,age,opened_days_issues,opened_days_project,opened_days,started_on,finish_on,due_project,idle,lateness</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">rpn##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##vroot##Top/rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##expr##real_rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##range##0,4,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##range##5,14,Yellow</Parameter>
                    <Parameter name="f" secured="false">rpn##range##15,25,Red</Parameter>
                    <Parameter name="f" secured="false">rpn##sort##explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##explicit_sort_order_values##Red,Yellow,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">due_facet##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">due_facet##vroot##Top/due_project</Parameter>
                    <Parameter name="f" secured="false">due_facet##expr##due_project</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##-100000000,-1,Overdue</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##0,24,Tomorrow</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##25,168,Next Week</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##169,100000000,After</Parameter>
                    <Parameter name="add_hit_meta" secured="false">opened_days</Parameter>
                    <Parameter name="add_hit_meta" secured="false">name:description_summarized,index_field:document_description,summarize:true </Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="age_days">${entry.metas['opened_days']} ?{${str:length(entry.metas['opened_days'])} &gt; 0 ? ?{${entry.metas['opened_days']} != '1' ? ${i18n['plma.project.days']} : ${i18n['plma.project.day']}} : }</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) &gt; 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_issue##late_issue##late"></Parameter>
                    <Parameter name="facets" secured="false" value="late_risk##late##late"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##late##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##lateness##lateness"></Parameter>
                    <Parameter name="facets" secured="false" value="idleness##idle##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="issue_lateness##late_issue##lateness"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task_start##started_on##late"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task_end##finish_on##late"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.OverrideSortMetaTrigger" enable="true">
                    <Parameter name="dpName" secured="false" value="document_sort"></Parameter>
                    <Parameter name="dpMetaName" secured="false" value="sort_current"></Parameter>
                    <Parameter name="sortToReplace" secured="false" value="document_current"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="GkMHIikr" id="history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:transition ${if page.params['hit']}AND id:${page.params['hit']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">20</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facet##changes</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="synthesis" secured="false">enabled</Parameter>
                    <Parameter name="s" secured="false">desc(document_history_order)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) &gt; 0}"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="c4WIoKnK" id="selection" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND (${foreach page.params['selectId']} ${if loop.index != 0} OR ${/if} id:${loop.element} ${/foreach})</Parameter>
                    <Parameter name="per_page" secured="false">${app:getProperty('project', 'multiselection_limit')}</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['selectId']) == 0}"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="rpn##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_issue##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_risk##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="idleness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="issue_lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_task_end##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_task_start##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="due_facet##CATEGORY##project"></Parameter>
            <Parameter name="removeFeedList" secured="false" value="hit"></Parameter>
            <Parameter name="removeFeedList" secured="false" value="history"></Parameter>
            <Parameter name="removeFeedList" secured="false" value="selection"></Parameter>
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" id="login" lastModifiedDate="1751969342726" synchronized="false">
        <Parameters/>
        <Properties/>
        <SubFeeds/>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="schedule_task" lastModifiedDate="1646201637652" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="OzfEgj67" id="hit" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND id:${page.params['hit']} ${if page.params['q']}OPT ${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">1</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="add_highlight" secured="false">name</Parameter>
                    <Parameter name="add_highlight" secured="false">type</Parameter>
                    <Parameter name="add_highlight" secured="false">owner</Parameter>
                    <Parameter name="add_highlight" secured="false">hierarchical_types</Parameter>
                    <Parameter name="add_highlight" secured="false">problem_type</Parameter>
                    <Parameter name="add_highlight" secured="false">classification</Parameter>
                    <Parameter name="add_highlight" secured="false">issue_category</Parameter>
                    <Parameter name="add_highlight" secured="false">to_assigned_issue</Parameter>
                    <Parameter name="add_highlight" secured="false">to_reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">part_name</Parameter>
                    <Parameter name="add_highlight" secured="false">description</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="enovia_link">${app:getProperty('project', 'enovia_link')}${entry.metas['id']}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['hit']) == 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="lKzWZmfV" id="open_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task AND (document_is_open:1) ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')}</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,num_opening_days_project,num_opening_days_issue,num_opening_days,lateness,num_closure_days_project,num_closure_days_issue,num_closure_days</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.age##AVG(num_opening_days*1.0)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.age##AVG(num_opening_days*1.0)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.age##AVG(num_opening_days*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.age##AVG(num_opening_days*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.age##AVG(num_opening_days*1.0)</Parameter>
                    <Parameter name="add_hit_meta" secured="false">lateness</Parameter>
                    <Parameter name="add_hit_meta" secured="false">name:description_summarized,index_field:document_description,summarize:true </Parameter>
                    <Parameter name="customSort" secured="false">s##asc##project_estimated_start_date</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('open_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}&#10;"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##lateness##late"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="3muOO5wp" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:snapshot</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,lateness,isLate,isCritical,isSnapshot,isOpenSnapshot,isOpenLateSnapshot,isOpenCriticalSnapshot,isOpenLateCriticalSnapshot,num_closure_days_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.history##SUM(isOpenSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.open##SUM(isOpenSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.openlate##SUM(isOpenLateSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.opencritical##SUM(isOpenCriticalSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.openlatecritical##SUM(isOpenLateCriticalSnapshot)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}&#10;"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##lateness##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="3muOO5wp" id="project_history_transition" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task changes:current ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:transition</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isTransition,isOpenTransition,lateness,num_closure_days_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.transition##SUM(isOpenTransition)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history_transition', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}&#10;"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##lateness##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="DBPFEraH" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},status_by_proj,status_by_org,status_by_own,status_by_assi,status_by_prog,completion_by_org,completion_by_own,completion_by_assi,completion_by_proj,completion_by_prog</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,task_age,finish_or_now,started_on,finished_on,vf_afd,vf_efd,vf_efd_present,vf_afd_present,vf_act_est_same_interval,real_duration,num_closure_days_project,num_closure_days_issue,num_closure_days,on_time_project,due_soon_project,late_project,missed_project,actual_start_date_present,estimated_start_date_present,not_started_project,not_planned_project,lateness</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,current,project_estimated_start_date,project_estimated_finish_date,project_actual_start_date,project_actual_finish_date</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.avg##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.completion_by_duration##SUM(project_percent_complete*real_duration*task_is_leaf_task)</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.total_duration##SUM(real_duration*task_is_leaf_task)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion_by_duration##SUM(project_percent_complete*real_duration*task_is_leaf_task)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.total_duration##SUM(real_duration*task_is_leaf_task)</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##vroot##Top/plan_finish_date</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##expr##project_planned_finish_date</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_month##false</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_week##true</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_week##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.forecast_count##SUM(#length(project_actual_finish_date)?0:1)</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##vroot##Top/act_finish_date</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##expr##vf_afd</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_month##false</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_week##true</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">ts_transition_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">ts_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##vroot##Top/duration_by_org</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##0.id##project_related_organization</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##vroot##Top/duration_by_own</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##0.id##owner</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##vroot##Top/duration_by_assi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##0.id##task_mgmt_assignee</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##vroot##Top/duration_by_proj</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##vroot##Top/duration_by_prog</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##0.id##related_program</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="add_hit_meta" secured="false">finish_or_now</Parameter>
                    <Parameter name="add_hit_meta" secured="false">finished_on</Parameter>
                    <Parameter name="add_hit_meta" secured="false">name:description_summarized,index_field:document_description,summarize:true </Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##lateness##late"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.scurve.CreateMergedFacetForSCurve" enable="true">
                    <Parameter name="newFacetId" secured="false" value="scurve"></Parameter>
                    <Parameter name="plannedFacetId" secured="false" value="plan_finish_date"></Parameter>
                    <Parameter name="plannedAgg" secured="false" value="count"></Parameter>
                    <Parameter name="actualFacetId" secured="false" value="act_finish_date"></Parameter>
                    <Parameter name="actualAgg" secured="false" value="count"></Parameter>
                    <Parameter name="forecastFacetId" secured="false" value="est_finish_date"></Parameter>
                    <Parameter name="forecastAgg" secured="false" value="forecast_count"></Parameter>
                    <Parameter name="forceJoinForecast" secured="false" value="false"></Parameter>
                    <Parameter name="forecastVariance" secured="false" value="true"></Parameter>
                    <Parameter name="debugTodayVal" secured="false" value="${page.params['today']}"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>per_page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="project_percent_complete"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##open_project"></Parameter>
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="schedule_my_schedule" lastModifiedDate="1646201865698" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="OIzH768z" id="hit" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND id:${page.params['hit']} ${if page.params['q']}OPT ${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">1</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="add_highlight" secured="false">name</Parameter>
                    <Parameter name="add_highlight" secured="false">type</Parameter>
                    <Parameter name="add_highlight" secured="false">owner</Parameter>
                    <Parameter name="add_highlight" secured="false">hierarchical_types</Parameter>
                    <Parameter name="add_highlight" secured="false">problem_type</Parameter>
                    <Parameter name="add_highlight" secured="false">classification</Parameter>
                    <Parameter name="add_highlight" secured="false">issue_category</Parameter>
                    <Parameter name="add_highlight" secured="false">to_assigned_issue</Parameter>
                    <Parameter name="add_highlight" secured="false">to_reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">part_name</Parameter>
                    <Parameter name="add_highlight" secured="false">description</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="enovia_link">${app:getProperty('project', 'enovia_link')}${entry.metas['id']}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['hit']) == 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="0GL4g9fe" id="open_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task AND document_is_open:1 ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')}</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,num_opening_days_project,lateness,num_closure_days_project,idle</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">true</Parameter>
                    <Parameter name="proxyAuthenticationMode" secured="false">none</Parameter>
                    <Parameter name="proxyUsername" secured="false">admin</Parameter>
                    <Parameter name="proxyPassword" secured="true">XoFrxSB4QYObGRS2Cft7CVOaVD9u2MjfcOwc7fpb1cgKvoM89y3ZwOoEdIPeyxMQi+JeTmKxS/Hi+15Xktk72VkizB0TBw9ZeTVavPgGmd8mmqSxLrjUMNLWaEQRY67PWQySl4cUGFoJyh3K+6Mo8ctDw67KIt1WSFq5Y81LFMJwTLVoQ0QAGAcGevt8O6F7nTYDz4cJMgq4ryYUaIoyGTfMPpzxUUPCC/hXwrpAUtQUP03863Y/fyjF8l+W85Lkvvcxh6E73wjMn1LNvgPgP9dB2avf3dJjIbtGLpdVvkHfsCE3m6AsNWLMTaOx3hcfGeKWzMe6ELMMfiHyZdXIDg==</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="customSort" secured="false">s##asc##project_estimated_start_date</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUserInQuery" enable="true">
                    <Parameter name="metaName" secured="false" value="project_owner_id"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="idleness##idle##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##lateness##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##lateness##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('open_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="QhoQLGf8" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">NOT history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,lateness,isLate,isCritical,isTransition,isSnapshot,isOpenSnapshot,isOpenLateSnapshot,isOpenCriticalSnapshot,isOpenLateCriticalSnapshot,num_closure_days_project,idle</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.history##SUM(isSnapshot)</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.transition##SUM(isTransition)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.open##SUM(isOpenSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.openlate##SUM(isOpenLateSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.opencritical##SUM(isOpenCriticalSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.openlatecritical##SUM(isOpenLateCriticalSnapshot)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUserInQuery" enable="true">
                    <Parameter name="metaName" secured="false" value="project_owner_id"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="idleness##idle##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##lateness##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##lateness##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="o5eDXuou" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},status_by_proj</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,task_age,finish_or_now,started_on,finished_on,vf_afd,vf_efd,vf_afd_present,vf_act_est_same_interval,real_duration,num_closure_days_project,on_time_project,due_soon_project,missed_project,not_started_project,not_planned_project,idle</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,current,project_estimated_start_date,project_estimated_finish_date,project_actual_start_date,project_actual_finish_date</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.avg##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.est_count##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_finish##SUM(vf_act_est_same_interval ? 1 : 0 )</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_remain##SUM(vf_act_est_same_interval ? 0 : 1 )</Parameter>
                    <Parameter name="f" secured="false">current##aggr.completion##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">status_by_proj##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion_by_duration##SUM(project_percent_complete*real_duration)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUserInQuery" enable="true">
                    <Parameter name="metaName" secured="false" value="project_owner_id"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="idleness##idle##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##finished_on##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##finished_on##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>per_page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="idleness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="idleness##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="idleness##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##open_project"></Parameter>
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="schedule_project" lastModifiedDate="1646201830187" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="n3Nd3oir" id="hit" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND id:${page.params['hit']} ${if page.params['q']}OPT ${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">1</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="add_highlight" secured="false">name</Parameter>
                    <Parameter name="add_highlight" secured="false">type</Parameter>
                    <Parameter name="add_highlight" secured="false">owner</Parameter>
                    <Parameter name="add_highlight" secured="false">hierarchical_types</Parameter>
                    <Parameter name="add_highlight" secured="false">problem_type</Parameter>
                    <Parameter name="add_highlight" secured="false">classification</Parameter>
                    <Parameter name="add_highlight" secured="false">issue_category</Parameter>
                    <Parameter name="add_highlight" secured="false">to_assigned_issue</Parameter>
                    <Parameter name="add_highlight" secured="false">to_reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">part_name</Parameter>
                    <Parameter name="add_highlight" secured="false">description</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="enovia_link">${app:getProperty('project', 'enovia_link')}${entry.metas['id']}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['hit']) == 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="soowf2zN" id="open_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:project_space AND document_is_open:1 ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},project_space_classification</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,num_opening_days_project,num_closure_days_project,due_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">due_facet##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">due_facet##vroot##Top/due_facet</Parameter>
                    <Parameter name="f" secured="false">due_facet##expr##due_project</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##-100000000,-1,Overdue</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##0,24,Tomorrow</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##25,168,Next Week</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##169,100000000,After</Parameter>
                    <Parameter name="customSort" secured="false">s##asc##project_estimated_start_date</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('open_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="4UNCtbfK" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:project_space ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:snapshot</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current,project_space_classification</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isSnapshot,isOverdueSnapshot,isDueTomorrowSnapshot,isDueNextWeekSnapshot,isDueAfterSnapshot,num_closure_days_project,due_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.history##SUM(isSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.overdue##SUM(isOverdueSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.tomorrow##SUM(isDueTomorrowSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.nextweek##SUM(isDueNextWeekSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.after##SUM(isDueAfterSnapshot)</Parameter>
                    <Parameter name="f" secured="false">due_facet##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">due_facet##vroot##Top/due_facet</Parameter>
                    <Parameter name="f" secured="false">due_facet##expr##due_project</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##-100000000,-1,Overdue</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##0,24,Tomorrow</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##25,168,Next Week</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##169,100000000,After</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="4UNCtbfK" id="project_history_transition" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:project_space changes:current ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:transition</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current,project_space_classification</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isTransition,num_closure_days_project,due_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.transition##SUM(isTransition)</Parameter>
                    <Parameter name="f" secured="false">due_facet##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">due_facet##vroot##Top/due_facet</Parameter>
                    <Parameter name="f" secured="false">due_facet##expr##due_project</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##-100000000,-1,Overdue</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##0,24,Tomorrow</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##25,168,Next Week</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##169,100000000,After</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history_transition', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="xNBGHb2d" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:project_space ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">timeout##0</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},status_by_org,status_by_own,status_by_proj,status_by_prog,completion_by_org,completion_by_own,completion_by_proj,completion_by_prog,project_space_classification</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,task_age,started_on,finish_on,vf_afd,vf_efd,vf_afd_present,vf_efd_present,vf_act_est_same_interval,real_duration,num_closure_days_project,on_time_project,due_soon_project,late_project,missed_project,actual_start_date_present,estimated_start_date_present,not_started_project,not_planned_project,due_project</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,current,project_estimated_start_date,project_estimated_finish_date,project_actual_start_date,project_actual_finish_date</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.avg##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion_by_duration##SUM(project_percent_complete*real_duration)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##vroot##Top/plan_finish_date</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##expr##project_planned_finish_date</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_week##false</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">plan_finish_date##month_fmt##%b %Y</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_week##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##month_fmt##%b %Y</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.forecast_count##SUM(#length(project_actual_finish_date)?0:1)</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##vroot##Top/act_finish_date</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##expr##vf_afd</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_week##false</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">act_finish_date##month_fmt##%b %Y</Parameter>
                    <Parameter name="f" secured="false">ts_transition_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">ts_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##vroot##Top/duration_by_org</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##0.id##project_related_organization</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##vroot##Top/duration_by_own</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##0.id##owner</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##vroot##Top/duration_by_proj</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##vroot##Top/duration_by_prog</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##0.id##related_program</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">due_facet##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">due_facet##vroot##Top/due_facet</Parameter>
                    <Parameter name="f" secured="false">due_facet##expr##due_project</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##-100000000,-1,Overdue</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##0,24,Tomorrow</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##25,168,Next Week</Parameter>
                    <Parameter name="f" secured="false">due_facet##range##169,100000000,After</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##finish_on##late"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.scurve.CreateMergedFacetForSCurve" enable="true">
                    <Parameter name="newFacetId" secured="false" value="scurve"></Parameter>
                    <Parameter name="plannedFacetId" secured="false" value="plan_finish_date"></Parameter>
                    <Parameter name="plannedAgg" secured="false" value="count"></Parameter>
                    <Parameter name="actualFacetId" secured="false" value="act_finish_date"></Parameter>
                    <Parameter name="actualAgg" secured="false" value="count"></Parameter>
                    <Parameter name="forecastFacetId" secured="false" value="est_finish_date"></Parameter>
                    <Parameter name="forecastAgg" secured="false" value="forecast_count"></Parameter>
                    <Parameter name="forceJoinForecast" secured="false" value="false"></Parameter>
                    <Parameter name="forecastVariance" secured="false" value="true"></Parameter>
                    <Parameter name="debugTodayVal" secured="false" value="${page.params['today']}"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>per_page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="due_facet##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="due_facet##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="due_facet##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="due_facet##CATEGORY##project_history_transition"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##open_project"></Parameter>
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="health_risk" lastModifiedDate="1646166963291" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="Q0xAuRWc" id="hit" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND id:${page.params['hit']} ${if page.params['q']}OPT ${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">1</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="add_highlight" secured="false">name</Parameter>
                    <Parameter name="add_highlight" secured="false">type</Parameter>
                    <Parameter name="add_highlight" secured="false">owner</Parameter>
                    <Parameter name="add_highlight" secured="false">hierarchical_types</Parameter>
                    <Parameter name="add_highlight" secured="false">problem_type</Parameter>
                    <Parameter name="add_highlight" secured="false">classification</Parameter>
                    <Parameter name="add_highlight" secured="false">issue_category</Parameter>
                    <Parameter name="add_highlight" secured="false">to_assigned_issue</Parameter>
                    <Parameter name="add_highlight" secured="false">to_reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">part_name</Parameter>
                    <Parameter name="add_highlight" secured="false">description</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="enovia_link">${app:getProperty('project', 'enovia_link')}${entry.metas['id']}</Property>
                </Properties>
                <SubFeeds>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="oXPd7BeX" id="related_count" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">related:"${feeds['hit'].entry.metas['id']}" AND history_type=current</Parameter>
                            <Parameter name="per_page" secured="false">0</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},risk_probability,risk_impact,status_by_org,status_by_own,status_by_proj,status_by_prog,risk_prob_impact_matrix,hierarchical_types</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="f" secured="false">rpn##type##num_explicit</Parameter>
                            <Parameter name="f" secured="false">rpn##vroot##Top/rpn</Parameter>
                            <Parameter name="f" secured="false">rpn##expr##real_rpn</Parameter>
                            <Parameter name="f" secured="false">rpn##range##0,4,Green</Parameter>
                            <Parameter name="f" secured="false">rpn##range##5,14,Yellow</Parameter>
                            <Parameter name="f" secured="false">rpn##range##15,25,Red</Parameter>
                            <Parameter name="f" secured="false">rpn##sort##explicit</Parameter>
                            <Parameter name="f" secured="false">rpn##explicit_sort_order_values##Red,Yellow,Green</Parameter>
                            <Parameter name="f" secured="false">rpn##aggr.total##SUM(1)</Parameter>
                            <Parameter name="f" secured="false">rpn##in_hits##true</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                        </Properties>
                        <SubFeeds/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                            <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="lm8CTwPH" id="related_objects" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">related:"${feeds['hit'].entry.metas['id']}"</Parameter>
                            <Parameter name="per_page" secured="false">10</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="restriction" secured="false">(NOT id:"${feeds['hit'].entries[0].metas['id']}") AND hierarchical_types:"${page.params['related_bo_type']}" AND history_type:current </Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},risk_probability,risk_impact,status_by_org,status_by_own,status_by_proj,status_by_prog,risk_prob_impact_matrix</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="f" secured="false">rpn##type##num_explicit</Parameter>
                            <Parameter name="f" secured="false">rpn##vroot##Top/rpn</Parameter>
                            <Parameter name="f" secured="false">rpn##expr##real_rpn</Parameter>
                            <Parameter name="f" secured="false">rpn##range##0,4,Green</Parameter>
                            <Parameter name="f" secured="false">rpn##range##5,14,Yellow</Parameter>
                            <Parameter name="f" secured="false">rpn##range##15,25,Red</Parameter>
                            <Parameter name="f" secured="false">rpn##sort##explicit</Parameter>
                            <Parameter name="f" secured="false">rpn##explicit_sort_order_values##Red,Yellow,Green</Parameter>
                            <Parameter name="f" secured="false">rpn##aggr.total##SUM(1)</Parameter>
                            <Parameter name="f" secured="false">rpn##in_hits##true</Parameter>
                            <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_program_class">${feeds["hit"].entry.metas["program_class"]}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_id">${feeds["hit"].entry.metas['id']}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_title">${feeds["hit"].entry.metas['title']}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_type">${feeds["hit"].entry.metas['type']}</Property>
                        </Properties>
                        <SubFeeds/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                            <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) == 0}"></Parameter>
                        </Trigger>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                            <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                            <Pattern>related_bo_type</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                </SubFeeds>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['hit']) == 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="tERiDGk5" id="open_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:risk AND document_is_open:1 ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},risk_probability,risk_impact</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,num_opening_days_project,late,rpn_probability,rpn_impact,real_rpn,num_closure_days_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">rpn##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##vroot##Top/rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##expr##real_rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##range##0,4,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##range##5,14,Yellow</Parameter>
                    <Parameter name="f" secured="false">rpn##range##15,25,Red</Parameter>
                    <Parameter name="f" secured="false">rpn##sort##explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##explicit_sort_order_values##Red,Yellow,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="customSort" secured="false">s##asc##project_estimated_start_date</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_risk##late##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##late##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('open_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="noMiR87V" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:risk ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:snapshot</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,risk_impact,risk_probability</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,lateness,isSnapshot,isLate,isOpenSnapshot,isOpenLateSnapshot,rpn_probability,rpn_impact,real_rpn,isGreenSnapshot,isYellowSnapshot,isRedSnapshot,num_closure_days_project,late</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.open##SUM(isOpenSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.late##SUM(isOpenLateSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.red##SUM(isRedSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.yellow##SUM(isYellowSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.green##SUM(isGreenSnapshot)</Parameter>
                    <Parameter name="f" secured="false">rpn##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##vroot##Top/rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##expr##real_rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##range##0,4,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##range##5,14,Yellow</Parameter>
                    <Parameter name="f" secured="false">rpn##range##15,25,Red</Parameter>
                    <Parameter name="f" secured="false">rpn##sort##explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##explicit_sort_order_values##Red,Yellow,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_risk##late##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##late##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="cyFzWYbA" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:risk ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},risk_probability,risk_impact,status_by_org,status_by_own,status_by_proj,status_by_prog,risk_prob_impact_matrix</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,task_age,started_on,finish_on,late,vf_asd,vf_afd,vf_efd,vf_esd,vf_afd_present,vf_act_est_same_interval,real_duration,num_closure_days_project,on_time_project,due_soon_project,late_project,missed_project,not_started_project,not_planned_project,rpn_probability,rpn_impact,real_rpn</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.avg##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion_by_duration##SUM(project_percent_complete*real_duration)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.est_count##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_finish##SUM(vf_act_est_same_interval ? 1 : 0 )</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_remain##SUM(vf_act_est_same_interval ? 0 : 1 )</Parameter>
                    <Parameter name="f" secured="false">ts_transition_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">ts_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">rpn_by_org##type##multi</Parameter>
                    <Parameter name="f" secured="false">rpn_by_org##vroot##Top/rpn_by_org</Parameter>
                    <Parameter name="f" secured="false">rpn_by_org##1.id##rpn</Parameter>
                    <Parameter name="f" secured="false">rpn_by_org##0.id##project_related_organization</Parameter>
                    <Parameter name="f" secured="false">rpn_by_org##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">rpn_by_org##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">rpn_by_own##type##multi</Parameter>
                    <Parameter name="f" secured="false">rpn_by_own##vroot##Top/rpn_by_own</Parameter>
                    <Parameter name="f" secured="false">rpn_by_own##1.id##rpn</Parameter>
                    <Parameter name="f" secured="false">rpn_by_own##0.id##owner</Parameter>
                    <Parameter name="f" secured="false">rpn_by_own##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">rpn_by_own##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">rpn_by_proj##type##multi</Parameter>
                    <Parameter name="f" secured="false">rpn_by_proj##vroot##Top/rpn_by_proj</Parameter>
                    <Parameter name="f" secured="false">rpn_by_proj##1.id##rpn</Parameter>
                    <Parameter name="f" secured="false">rpn_by_proj##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">rpn_by_proj##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">rpn_by_proj##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">rpn_by_prog##type##multi</Parameter>
                    <Parameter name="f" secured="false">rpn_by_prog##vroot##Top/rpn_by_prog</Parameter>
                    <Parameter name="f" secured="false">rpn_by_prog##1.id##rpn</Parameter>
                    <Parameter name="f" secured="false">rpn_by_prog##0.id##related_program</Parameter>
                    <Parameter name="f" secured="false">rpn_by_prog##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">rpn_by_prog##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##vroot##Top/duration_by_org</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##0.id##project_related_organization</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##vroot##Top/duration_by_own</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##0.id##owner</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##vroot##Top/duration_by_proj</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##vroot##Top/duration_by_prog</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##0.id##related_program</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">rpn##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##vroot##Top/rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##expr##real_rpn</Parameter>
                    <Parameter name="f" secured="false">rpn##range##0,4,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##range##5,14,Yellow</Parameter>
                    <Parameter name="f" secured="false">rpn##range##15,25,Red</Parameter>
                    <Parameter name="f" secured="false">rpn##sort##explicit</Parameter>
                    <Parameter name="f" secured="false">rpn##explicit_sort_order_values##Red,Yellow,Green</Parameter>
                    <Parameter name="f" secured="false">rpn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##type##multi</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##vroot##Top/risk_by_project</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##1.id##rpn</Parameter>
                    <Parameter name="f" secured="false">risk_by_project##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##vroot##Top/closuredatedyn</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_quarter##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_month##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_week##true</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##sort##date</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##missing_intervals##true</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##week_fmt##%Y %V</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##max_per_level##0</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##vroot##Top/openingdatedyn</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##expr##vf_esd</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_quarter##false</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_month##false</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_week##true</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##sort##date</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##week_fmt##%Y %V</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##missing_intervals##true</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##max_per_level##0</Parameter>
                    <Parameter name="f" secured="false">rpn_opened##type##multi</Parameter>
                    <Parameter name="f" secured="false">rpn_opened##vroot##top/rpn_opened</Parameter>
                    <Parameter name="f" secured="false">rpn_opened##0.id##openingdatedyn</Parameter>
                    <Parameter name="f" secured="false">rpn_opened##1.id##rpn</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="4pV0WUwY" id="prj_spaces" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">hierarchical_types:project_space AND id:(${foreach cat in feeds['project'].facets['related_project'].flat}${str:split('/',cat.id)[2]} ${if loop.hasNext} BOR ${/if} ${/foreach})</Parameter>
                            <Parameter name="per_page" secured="false">0</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="restriction" secured="false">history_type:current</Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##related_program,related_project</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,type,project_estimated_start_date,project_estimated_finish_date,project_actual_start_date,project_actual_finish_date</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">synthesis##false</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                        </Properties>
                        <SubFeeds>
                            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="Bsv5QO9A" id="related_data" lastModifiedDate="-1" synchronized="false">
<Parameters>
    <Parameter name="q" secured="false">related:"${feeds['prj_spaces'].entry.metas['id']}"</Parameter>
    <Parameter name="per_page" secured="false">-1</Parameter>
    <Parameter name="l" secured="false">en</Parameter>
    <Parameter name="restriction" secured="false">hierarchical_types:"${page.params['related_bo_type']}" AND history_type:current </Parameter>
    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},risk_probability,risk_impact,status_by_org,status_by_own,status_by_proj,status_by_prog,risk_prob_impact_matrix</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">synthesis##false</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,type,current,project_estimated_start_date,project_estimated_finish_date,issue_actual_start_date,issue_actual_end_date,originated,owner</Parameter>
    <Parameter name="logic" secured="false">sl_project</Parameter>
    <Parameter name="stagingMode" secured="false">false</Parameter>
    <Parameter name="page" secured="false">1</Parameter>
    <Parameter name="enableProxy" secured="false">false</Parameter>
    <Parameter name="defaultQuery" secured="false">#all</Parameter>
    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
    <Parameter name="command" secured="false">search-api</Parameter>
    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
    <Parameter name="f" secured="false">rpn##type##num_explicit</Parameter>
    <Parameter name="f" secured="false">rpn##vroot##Top/rpn</Parameter>
    <Parameter name="f" secured="false">rpn##expr##real_rpn</Parameter>
    <Parameter name="f" secured="false">rpn##range##0,4,Green</Parameter>
    <Parameter name="f" secured="false">rpn##range##5,14,Yellow</Parameter>
    <Parameter name="f" secured="false">rpn##range##15,25,Red</Parameter>
    <Parameter name="f" secured="false">rpn##sort##explicit</Parameter>
    <Parameter name="f" secured="false">rpn##explicit_sort_order_values##Red,Yellow,Green</Parameter>
    <Parameter name="f" secured="false">rpn##aggr.total##SUM(1)</Parameter>
    <Parameter name="f" secured="false">rpn##in_hits##true</Parameter>
    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
    <Parameter name="enableSecurity" secured="false">true</Parameter>
</Parameters>
<Properties>
    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
    <Property kind="META" mappingType="PRE" name="parent_id">${feeds["prj_spaces"].entry.metas['id']}</Property>
    <Property kind="META" mappingType="PRE" name="parent_name">${feeds["prj_spaces"].entry.metas['name']}</Property>
</Properties>
<SubFeeds/>
<Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
    <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) == 0}"></Parameter>
</Trigger>
<Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
</Trigger>
<WhiteListPatterns>
    <Pattern>r</Pattern>
    <Pattern>zr</Pattern>
    <Pattern>cr</Pattern>
    <Pattern>s</Pattern>
    <Pattern>page</Pattern>
    <Pattern>related_bo_type</Pattern>
</WhiteListPatterns>
                            </Feed>
                        </SubFeeds>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                            <Parameter name="condition" secured="false" value="${page.params['plmaPlanningDataReq'] != true &amp;&amp; feeds['project'].facets['related_project'].flat.length &gt; 0}"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                            <Pattern>per_page</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                </SubFeeds>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CreateCustomAggregationTrigger" enable="true">
                    <Parameter name="facet" secured="false" value="openingdatedyn"></Parameter>
                    <Parameter name="aggr" secured="false" value="totalopen"></Parameter>
                    <Parameter name="newAggr" secured="false" value="openingdatedyn.total - closuredatedyn.total"></Parameter>
                    <Parameter name="isCumul" secured="false" value="true"></Parameter>
                    <Parameter name="sortMode" secured="false" value="alphanum_asc"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_risk##late##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##late##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>per_page</Pattern>
                    <Pattern>maxPerLevelParamFacetName</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="false">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="late_risk##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_risk##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_risk##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="rpn##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="rpn##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="rpn##CATEGORY##related_data"></Parameter>
            <Parameter name="facetList" secured="false" value="rpn##CATEGORY##related_count"></Parameter>
            <Parameter name="facetList" secured="false" value="rpn##CATEGORY##related_objects"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="removeFeedList" secured="false" value="hit"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${page.params['plmaPlanningDataReq'] == true}##related_data"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${str:length(page.params['hit']) &gt; 0}##related_objects"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${str:length(page.params['hit']) &gt; 0}##related_count"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="health_issue" lastModifiedDate="1646159607341" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="PPcgzKyG" id="hit" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND id:${page.params['hit']} ${if page.params['q']}OPT ${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">1</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="add_highlight" secured="false">name</Parameter>
                    <Parameter name="add_highlight" secured="false">type</Parameter>
                    <Parameter name="add_highlight" secured="false">owner</Parameter>
                    <Parameter name="add_highlight" secured="false">hierarchical_types</Parameter>
                    <Parameter name="add_highlight" secured="false">problem_type</Parameter>
                    <Parameter name="add_highlight" secured="false">classification</Parameter>
                    <Parameter name="add_highlight" secured="false">issue_category</Parameter>
                    <Parameter name="add_highlight" secured="false">to_assigned_issue</Parameter>
                    <Parameter name="add_highlight" secured="false">to_reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">part_name</Parameter>
                    <Parameter name="add_highlight" secured="false">description</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="enovia_link">${app:getProperty('project', 'enovia_link')}${entry.metas['id']}</Property>
                </Properties>
                <SubFeeds>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="K8v91wm4" id="related_count" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">related:"${feeds['hit'].entry.metas['id']}" AND history_type=current</Parameter>
                            <Parameter name="per_page" secured="false">0</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},issue_priority,issue_to_reporting_organization,issue_to_assigned_issue,issue_actual_start_date,issue_actual_end_date,status_by_org_issue,status_by_own,status_by_assi_issue,status_by_proj,status_by_prog,prio_by_org,prio_by_own,prio_by_assi,prio_by_proj,prio_by_prog,hierarchical_types</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                        </Properties>
                        <SubFeeds/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                            <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="MxR00fcf" id="related_objects" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">related:"${feeds['hit'].entry.metas['id']}"</Parameter>
                            <Parameter name="per_page" secured="false">10</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="restriction" secured="false">(NOT id:"${feeds['hit'].entries[0].metas['id']}") AND hierarchical_types:"${page.params['related_bo_type']}" AND history_type:current </Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},issue_priority,issue_to_reporting_organization,issue_to_assigned_issue,issue_actual_start_date,issue_actual_end_date,status_by_org_issue,status_by_own,status_by_assi_issue,status_by_proj,status_by_prog,prio_by_org,prio_by_own,prio_by_assi,prio_by_proj,prio_by_prog</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_program_class">${feeds["hit"].entry.metas["program_class"]}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_id">${feeds["hit"].entry.metas['id']}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_title">${feeds["hit"].entry.metas['title']}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_type">${feeds["hit"].entry.metas['type']}</Property>
                        </Properties>
                        <SubFeeds/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                            <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) == 0}"></Parameter>
                        </Trigger>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                            <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                            <Pattern>related_bo_type</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                </SubFeeds>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['hit']) == 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="xWNdYNT8" id="open_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:issue AND document_is_open:1 ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},issue_priority,issue_to_reporting_organization,issue_to_assigned_issue,issue_actual_start_date,issue_actual_end_date</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,num_opening_days_issue,late_issue,num_closure_days_issue</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.age##AVG(num_opening_days_issue*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.age##AVG(num_opening_days_issue*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.age##AVG(num_opening_days_issue*1.0)</Parameter>
                    <Parameter name="f" secured="false">issue_to_reporting_organization##aggr.age##AVG(num_opening_days_issue*1.0)</Parameter>
                    <Parameter name="f" secured="false">issue_to_assigned_issue##aggr.age##AVG(num_opening_days_issue*1.0)</Parameter>
                    <Parameter name="customSort" secured="false">s##asc##project_estimated_start_date</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_issue##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="issue_lateness##late_issue##lateness"></Parameter>
                    <Parameter name="facets" secured="false" value="late_issue##late_issue##late_issue"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('open_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="1yHjIDbH" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:issue ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:snapshot</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},issue_priority,history_weekofyear,issue_actual_start_date,issue_actual_end_date,issue_to_assigned_issue,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,late_issue,num_closure_days_issue,isSnapshot,isIssueLate,isHigh,isOpenSnapshot,isOpenIssueLateSnapshot,isHighSnapshot</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.open##SUM(isOpenSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.late##SUM(isOpenIssueLateSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.high##SUM(isHighSnapshot)</Parameter>
                    <Parameter name="customSort" secured="false">s##asc##project_estimated_start_date</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_issue##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_issue##late_issue##late"></Parameter>
                    <Parameter name="facets" secured="false" value="issue_lateness##late_issue##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="zydLmpup" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:issue ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">timeout##0</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},issue_priority,issue_to_reporting_organization,issue_to_assigned_issue,issue_actual_start_date,issue_actual_end_date,status_by_org_issue,status_by_own,status_by_assi_issue,status_by_proj,status_by_prog,prio_by_org,prio_by_own,prio_by_assi,prio_by_proj,prio_by_prog</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,task_age,started_on,finish_on,late_issue,vf_asd,vf_afd,vf_efd,vf_afd_present,vf_act_est_same_interval,num_closure_days_issue</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.est_count##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_finish##SUM(vf_act_est_same_interval ? 1 : 0 )</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_remain##SUM(vf_act_est_same_interval ? 0 : 1 )</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##vroot##Top/duration_by_org</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##0.id##issue_to_reporting_organization</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##vroot##Top/duration_by_own</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##0.id##owner</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##vroot##Top/duration_by_assi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##0.id##issue_to_assigned_issue</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##vroot##Top/duration_by_proj</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##vroot##Top/duration_by_prog</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##0.id##related_program</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##vroot##Top/closuredatedyn</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##expr##vf_afd</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_quarter##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_month##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_week##true</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##sort##date</Parameter>
                    <Parameter name="f" secured="false">closuredatedyn##week_fmt##%Y %V</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##vroot##Top/openingdatedyn</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##expr##vf_asd</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_quarter##false</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_month##false</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_week##true</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##aggr.total##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##sort##date</Parameter>
                    <Parameter name="f" secured="false">openingdatedyn##week_fmt##%Y %V</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="EdB5zW6V" id="prj_spaces" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">hierarchical_types:project_space AND id:(${foreach cat in feeds['project'].facets['related_project'].flat}${str:split('/',cat.id)[2]} ${if loop.hasNext} BOR ${/if} ${/foreach})</Parameter>
                            <Parameter name="per_page" secured="false">0</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="restriction" secured="false">history_type:current</Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##related_program,related_project</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,type,project_estimated_start_date,project_estimated_finish_date,project_actual_start_date,project_actual_finish_date</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">synthesis##false</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                        </Properties>
                        <SubFeeds>
                            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="MxR00fcf" id="related_data" lastModifiedDate="-1" synchronized="false">
<Parameters>
    <Parameter name="q" secured="false">related:"${feeds['prj_spaces'].entry.metas['id']}"</Parameter>
    <Parameter name="per_page" secured="false">-1</Parameter>
    <Parameter name="l" secured="false">en</Parameter>
    <Parameter name="restriction" secured="false">hierarchical_types:"${page.params['related_bo_type']}" AND history_type:current </Parameter>
    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},issue_priority,issue_to_reporting_organization,issue_to_assigned_issue,issue_actual_start_date,issue_actual_end_date,status_by_org_issue,status_by_own,status_by_assi_issue,status_by_proj,status_by_prog,prio_by_org,prio_by_own,prio_by_assi,prio_by_proj,prio_by_prog</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">synthesis##false</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,type,current,project_estimated_start_date,project_estimated_finish_date,issue_actual_start_date,issue_actual_end_date,originated,owner</Parameter>
    <Parameter name="logic" secured="false">sl_project</Parameter>
    <Parameter name="stagingMode" secured="false">false</Parameter>
    <Parameter name="page" secured="false">1</Parameter>
    <Parameter name="enableProxy" secured="false">false</Parameter>
    <Parameter name="defaultQuery" secured="false">#all</Parameter>
    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
    <Parameter name="command" secured="false">search-api</Parameter>
    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
    <Parameter name="enableSecurity" secured="false">true</Parameter>
</Parameters>
<Properties>
    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
    <Property kind="META" mappingType="PRE" name="parent_id">${feeds["prj_spaces"].entry.metas['id']}</Property>
    <Property kind="META" mappingType="PRE" name="parent_name">${feeds["prj_spaces"].entry.metas['name']}</Property>
</Properties>
<SubFeeds/>
<Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
    <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) == 0}"></Parameter>
</Trigger>
<Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
</Trigger>
<WhiteListPatterns>
    <Pattern>r</Pattern>
    <Pattern>zr</Pattern>
    <Pattern>cr</Pattern>
    <Pattern>s</Pattern>
    <Pattern>page</Pattern>
    <Pattern>related_bo_type</Pattern>
</WhiteListPatterns>
                            </Feed>
                        </SubFeeds>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                            <Parameter name="condition" secured="false" value="${page.params['plmaPlanningDataReq'] != true &amp;&amp; feeds['project'].facets['related_project'].flat.length &gt; 0}"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                            <Pattern>per_page</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                </SubFeeds>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_issue##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_issue##late_issue##late"></Parameter>
                    <Parameter name="facets" secured="false" value="issue_lateness##late_issue##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>per_page</Pattern>
                    <Pattern>maxPerLevelParamFacetName</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="late_issue##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_issue##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="issue_lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="issue_lateness##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="issue_lateness##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##open_project"></Parameter>
            <Parameter name="removeFeedList" secured="false" value="hit"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${page.params['plmaPlanningDataReq'] == true}##related_data"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${str:length(page.params['hit']) &gt; 0}##related_objects"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${str:length(page.params['hit']) &gt; 0}##related_count"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="schedule_deliverable" lastModifiedDate="1614325726772" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="WRAt7Ll3" id="open_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:deliverable AND document_is_open:1 ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')}</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,num_opening_days_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="customSort" secured="false">s##asc##project_estimated_start_date</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('open_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}&#10;"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="ntRZVe9O" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:deliverable ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:snapshot</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isSnapshot,isDocumentSnapshot,isInWorkSnapshot,isReleasedSnapshot</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.history##SUM(isSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.doc##SUM(isDocumentSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.inwork##SUM(isInWorkSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.released##SUM(isReleasedSnapshot)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="ntRZVe9O" id="project_history_transition" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:deliverable changes:current ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:transition</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isTransition</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.transition##SUM(isTransition)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history_transition', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="4dBPDlrl" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:deliverable ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">timeout##0</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},status_by_own,status_by_proj,status_by_prog,type_by_proj</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="false">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="health_assessment" lastModifiedDate="1646163207556" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="fmdOiMCI" id="hit" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND id:${page.params['hit']} ${if page.params['q']}OPT ${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">1</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="add_highlight" secured="false">name</Parameter>
                    <Parameter name="add_highlight" secured="false">type</Parameter>
                    <Parameter name="add_highlight" secured="false">owner</Parameter>
                    <Parameter name="add_highlight" secured="false">hierarchical_types</Parameter>
                    <Parameter name="add_highlight" secured="false">problem_type</Parameter>
                    <Parameter name="add_highlight" secured="false">classification</Parameter>
                    <Parameter name="add_highlight" secured="false">issue_category</Parameter>
                    <Parameter name="add_highlight" secured="false">to_assigned_issue</Parameter>
                    <Parameter name="add_highlight" secured="false">to_reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">part_name</Parameter>
                    <Parameter name="add_highlight" secured="false">description</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="enovia_link">${app:getProperty('project', 'enovia_link')}${entry.metas['id']}</Property>
                </Properties>
                <SubFeeds>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="ZlCTWdhp" id="related_count" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">related:"${feeds['hit'].entry.metas['id']}" AND history_type=current</Parameter>
                            <Parameter name="per_page" secured="false">0</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,assessment_assess_status,assessment_finance_status,assessment_resource_status,assessment_risk_status,assessment_schedule_status,assessment_quality_status,assessment_procurement_status,assessment_stakeholder_status,assessment_communication_status,assessment_scope_status,hierarchical_types</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                        </Properties>
                        <SubFeeds/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                            <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="9GXBJ15Z" id="related_objects" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">related:"${feeds['hit'].entry.metas['id']}"</Parameter>
                            <Parameter name="per_page" secured="false">10</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="restriction" secured="false">(NOT id:"${feeds['hit'].entries[0].metas['id']}") AND hierarchical_types:"${page.params['related_bo_type']}" AND history_type:current </Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,assessment_assess_status,assessment_finance_status,assessment_resource_status,assessment_risk_status,assessment_schedule_status,assessment_quality_status,assessment_procurement_status,assessment_stakeholder_status,assessment_communication_status,assessment_scope_status</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_program_class">${feeds["hit"].entry.metas["program_class"]}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_id">${feeds["hit"].entry.metas['id']}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_title">${feeds["hit"].entry.metas['title']}</Property>
                            <Property kind="META" mappingType="PRE" name="parent_type">${feeds["hit"].entry.metas['type']}</Property>
                        </Properties>
                        <SubFeeds/>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                            <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) == 0}"></Parameter>
                        </Trigger>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                            <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                            <Pattern>related_bo_type</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                </SubFeeds>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['hit']) == 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="DBP5HytK" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:assessment ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">NOT history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,assessment_assess_status,assessment_finance_status,assessment_resource_status,assessment_risk_status,assessment_schedule_status,assessment_quality_status,assessment_procurement_status,assessment_stakeholder_status,assessment_communication_status,assessment_scope_status,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isTransition,isSnapshot,isGreenAssessmentSnapshot,isYellowAssessmentSnapshot,isRedAssessmentSnapshot</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.history##SUM(isSnapshot)</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.transition##SUM(isTransition)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.green##SUM(isGreenAssessmentSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.yellow##SUM(isYellowAssessmentSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.red##SUM(isRedAssessmentSnapshot)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="khtKrLwL" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:assessment ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">timeout##0</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,assessment_assess_status,assessment_finance_status,assessment_resource_status,assessment_risk_status,assessment_schedule_status,assessment_quality_status,assessment_procurement_status,assessment_stakeholder_status,assessment_communication_status,assessment_scope_status</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,finance_avg,risk_avg,resource_avg,overall_avg,schedule_avg,quality_avg,procurement_avg,stakeholder_avg,communication_avg,scope_avg</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.finance##AVG(finance_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.risk##AVG(risk_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.resource##AVG(resource_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.schedule##AVG(schedule_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.overall##AVG(overall_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.quality##AVG(quality_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.procurement##AVG(procurement_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.stakeholder##AVG(stakeholder_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.communication##AVG(communication_avg)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.scope##AVG(scope_avg)</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds>
                    <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="x0F71Nm2" id="prj_spaces" lastModifiedDate="-1" synchronized="false">
                        <Parameters>
                            <Parameter name="q" secured="false">hierarchical_types:project_space AND id:(${foreach cat in feeds['project'].facets['related_project'].flat}${str:split('/',cat.id)[2]} ${if loop.hasNext} BOR ${/if} ${/foreach})</Parameter>
                            <Parameter name="per_page" secured="false">0</Parameter>
                            <Parameter name="l" secured="false">en</Parameter>
                            <Parameter name="restriction" secured="false">history_type:current</Parameter>
                            <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##related_program,related_project</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,type,project_estimated_start_date,project_estimated_finish_date,project_actual_start_date,project_actual_finish_date</Parameter>
                            <Parameter name="additionalQueryParameters" secured="false">synthesis##false</Parameter>
                            <Parameter name="logic" secured="false">sl_project</Parameter>
                            <Parameter name="stagingMode" secured="false">false</Parameter>
                            <Parameter name="page" secured="false">1</Parameter>
                            <Parameter name="enableProxy" secured="false">false</Parameter>
                            <Parameter name="defaultQuery" secured="false">#all</Parameter>
                            <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                            <Parameter name="command" secured="false">search-api</Parameter>
                            <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                            <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                            <Parameter name="enableSecurity" secured="false">true</Parameter>
                        </Parameters>
                        <Properties>
                            <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                            <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                            <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                        </Properties>
                        <SubFeeds>
                            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="8NtlasLg" id="related_data" lastModifiedDate="-1" synchronized="false">
<Parameters>
    <Parameter name="q" secured="false">related:"${feeds['prj_spaces'].entry.metas['id']}"</Parameter>
    <Parameter name="per_page" secured="false">-1</Parameter>
    <Parameter name="l" secured="false">en</Parameter>
    <Parameter name="restriction" secured="false">hierarchical_types:"${page.params['related_bo_type']}" AND history_type:current </Parameter>
    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,assessment_assess_status,assessment_finance_status,assessment_resource_status,assessment_risk_status,assessment_schedule_status,assessment_quality_status,assessment_procurement_status,assessment_stakeholder_status,assessment_communication_status,assessment_scope_status</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">synthesis##false</Parameter>
    <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,type,current,project_estimated_start_date,project_estimated_finish_date,issue_actual_start_date,issue_actual_end_date,originated,owner</Parameter>
    <Parameter name="logic" secured="false">sl_project</Parameter>
    <Parameter name="stagingMode" secured="false">false</Parameter>
    <Parameter name="page" secured="false">1</Parameter>
    <Parameter name="enableProxy" secured="false">false</Parameter>
    <Parameter name="defaultQuery" secured="false">#all</Parameter>
    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
    <Parameter name="command" secured="false">search-api</Parameter>
    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
    <Parameter name="enableSecurity" secured="false">true</Parameter>
</Parameters>
<Properties>
    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
    <Property kind="META" mappingType="PRE" name="parent_id">${feeds["prj_spaces"].entry.metas['id']}</Property>
    <Property kind="META" mappingType="PRE" name="parent_name">${feeds["prj_spaces"].entry.metas['name']}</Property>
</Properties>
<SubFeeds/>
<Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
    <Parameter name="condition" secured="false" value="${str:length(page.params['related_bo_type']) == 0}"></Parameter>
</Trigger>
<Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
</Trigger>
<WhiteListPatterns>
    <Pattern>r</Pattern>
    <Pattern>zr</Pattern>
    <Pattern>cr</Pattern>
    <Pattern>s</Pattern>
    <Pattern>page</Pattern>
    <Pattern>related_bo_type</Pattern>
</WhiteListPatterns>
                            </Feed>
                        </SubFeeds>
                        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                            <Parameter name="condition" secured="false" value="${page.params['plmaPlanningDataReq'] != true &amp;&amp; feeds['project'].facets['related_project'].flat.length &gt; 0}"></Parameter>
                        </Trigger>
                        <WhiteListPatterns>
                            <Pattern>r</Pattern>
                            <Pattern>zr</Pattern>
                            <Pattern>cr</Pattern>
                            <Pattern>s</Pattern>
                            <Pattern>page</Pattern>
                            <Pattern>per_page</Pattern>
                        </WhiteListPatterns>
                    </Feed>
                </SubFeeds>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="false">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>per_page</Pattern>
                    <Pattern>maxPerLevelParamFacetName</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="false">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="finance##${facet.aggrs[&quot;finance_total&quot;] / facet.aggrs[&quot;count&quot;]}##${category.aggrs[&quot;finance_total&quot;] / category.aggrs[&quot;count&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="##CATEGORY##"></Parameter>
            <Parameter name="removeFeedList" secured="false" value="hit"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${page.params['plmaPlanningDataReq'] == true}##related_data"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${str:length(page.params['hit']) &gt; 0}##related_objects"></Parameter>
            <Parameter name="addFeedsConditional" secured="false" value="${str:length(page.params['hit']) &gt; 0}##related_count"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="schedule_gate" lastModifiedDate="1646201923522" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.BusinessItemFeed" embed="true" enable="true" fuid="snnNSp8j" id="hit" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">history_type:current AND id:${page.params['hit']} ${if page.params['q']}OPT ${page.params['q']}${/if}</Parameter>
                    <Parameter name="per_page" secured="false">1</Parameter>
                    <Parameter name="l" secured="false">en</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="add_highlight" secured="false">name</Parameter>
                    <Parameter name="add_highlight" secured="false">type</Parameter>
                    <Parameter name="add_highlight" secured="false">owner</Parameter>
                    <Parameter name="add_highlight" secured="false">hierarchical_types</Parameter>
                    <Parameter name="add_highlight" secured="false">problem_type</Parameter>
                    <Parameter name="add_highlight" secured="false">classification</Parameter>
                    <Parameter name="add_highlight" secured="false">issue_category</Parameter>
                    <Parameter name="add_highlight" secured="false">to_assigned_issue</Parameter>
                    <Parameter name="add_highlight" secured="false">to_reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">reporting_organization</Parameter>
                    <Parameter name="add_highlight" secured="false">part_name</Parameter>
                    <Parameter name="add_highlight" secured="false">description</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['description']!+x}</Property>
                    <Property kind="META" mappingType="PRE" name="enovia_link">${app:getProperty('project', 'enovia_link')}${entry.metas['id']}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${str:length(page.params['hit']) == 0}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.AddUnitOfMeasurementToMetas" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="ISwYoGL0" id="open_project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:gate document_is_open:1 ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')}</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,num_opening_days_project,lateness,num_closure_days_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:1000</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.age##AVG(num_opening_days_project*1.0)</Parameter>
                    <Parameter name="customSort" secured="false">s##asc##project_estimated_start_date</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('open_project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##lateness##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="8VdTKG59" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:gate ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">NOT history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,lateness,started_on,finish_on,isComplete,isCritical,isTransition,isSnapshot,isCompleteSnapshot,isCriticalSnapshot,isOnTimeFinishSnapshot,isOnTimeStartSnapshot,num_closure_days_project</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.history##SUM(isSnapshot)</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.transition##SUM(isTransition)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.ontimestart##SUM(isOnTimeStartSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.ontimeend##SUM(isOnTimeFinishSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.complete##SUM(isCompleteSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.critical##SUM(isCriticalSnapshot)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task_end##finish_on##late"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task_start##started_on##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##lateness##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot; ||  page.params[&quot;isRequestFromTimelineController&quot;] == &quot;true&quot;}&#10; true&#10;${elseif (list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1)}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="9PhiJbyX" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:gate ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">timeout##0</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},status_by_org,status_by_own,status_by_assi,status_by_prog,status_by_proj,completion_by_org,completion_by_own,completion_by_assi,completion_by_proj,completion_by_prog</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,lateness,task_age,started_on,finish_on,vf_afd,vf_efd,vf_afd_present,vf_act_est_same_interval,real_duration,num_closure_days_project,on_time_project,due_soon_project,late_project,missed_project,not_started_project,not_planned_project</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_hit_metas##id,name,current,project_estimated_start_date,project_estimated_finish_date,project_actual_start_date,project_actual_finish_date</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.avg##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion_by_duration##SUM(project_percent_complete*real_duration)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion_simple##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.est_count##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_finish##SUM(vf_act_est_same_interval ? 1 : 0 )</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_remain##SUM(vf_act_est_same_interval ? 0 : 1 )</Parameter>
                    <Parameter name="f" secured="false">ts_transition_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">ts_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##vroot##Top/duration_by_org</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##0.id##project_related_organization</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##vroot##Top/duration_by_own</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##0.id##owner</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##vroot##Top/duration_by_assi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##0.id##task_mgmt_assignee</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##vroot##Top/duration_by_proj</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##vroot##Top/duration_by_prog</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##0.id##related_program</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##in_hits##false</Parameter>
                    <Parameter name="remove_hit_meta" secured="false">history</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task_end##finish_on##late"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task_start##started_on##late"></Parameter>
                    <Parameter name="facets" secured="false" value="lateness##lateness##lateness"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                    <Pattern>per_page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="late_task_end##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##open_project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_task_start##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="late_task_start##CATEGORY##project_history"></Parameter>
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="resource_workload" lastModifiedDate="1614325726772" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="GFdfKN46" id="project_history" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task ${if page.params['q']} AND (${page.params['q']})${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:snapshot</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},history_weekofyear,week_current,week_complete</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isSnapshot,lateness,num_closure_days_project,allocated_resources,completeSnap,assigned,remaining_days,remaining_allocated,remaining_effort,overdue,overdone,computed_total_effort,computed_total_allocation</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">week_current##aggr.history##SUM(isSnapshot)</Parameter>
                    <Parameter name="f" secured="false">week_complete##aggr.snapshot##SUM(isSnapshot)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##sort##-alphanum</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.assigned_resources##SUM(assigned)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.diff_planned_needed##SUM(remaining_effort - remaining_allocated)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.overdue##SUM(overdue)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.overdone##SUM(overdone)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.complete##AVG(completeSnap)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.effort##SUM(computed_total_effort)</Parameter>
                    <Parameter name="f" secured="false">history_weekofyear##aggr.allocation##SUM(computed_total_allocation)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project_history', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##lateness##late"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="R1Omw2u0" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},status_by_proj,status_by_org,status_by_own,status_by_assi,status_by_prog,completion_by_org,completion_by_own,completion_by_assi,completion_by_proj,completion_by_prog</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,task_age,finish_or_now,started_on,finished_on,vf_afd,vf_efd,vf_efd_present,vf_afd_present,vf_act_est_same_interval,real_duration,num_closure_days_project,on_time_project,due_soon_project,late_project,missed_project,actual_start_date_present,estimated_start_date_present,not_started_project,not_planned_project,lateness,sum_effort,allocated_resources,remaining_days,ellapsed_days,int_ellapsed_days,daily_effort,progression_after_one_week,progression_after_two_week,progression_after_three_week,progression_after_four_week,remaining_days_first_week,remaining_days_second_week,remaining_days_third_week,remaining_days_fourth_week,effort_after_one_week,effort_after_two_week,effort_after_three_week,effort_after_four_week,allocation_after_one_week,allocation_after_two_week,allocation_after_three_week,allocation_after_four_week</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.avg##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion_by_duration##SUM(project_percent_complete*real_duration)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.est_count##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_finish##SUM(vf_act_est_same_interval ? 1 : 0 )</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_remain##SUM(vf_act_est_same_interval ? 0 : 1 )</Parameter>
                    <Parameter name="f" secured="false">ts_transition_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">ts_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">status_by_proj##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##vroot##Top/duration_by_org</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##0.id##project_related_organization</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##vroot##Top/duration_by_own</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##0.id##owner</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##vroot##Top/duration_by_assi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##0.id##task_mgmt_assignee</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##vroot##Top/duration_by_proj</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##vroot##Top/duration_by_prog</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##0.id##related_program</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.progression_after_one_week##SUM(progression_after_one_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.progression_after_two_week##SUM(progression_after_two_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.progression_after_three_week##SUM(progression_after_three_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.progression_after_four_week##SUM(progression_after_four_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.nb_task##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.effort_after_one_week##SUM(effort_after_one_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.effort_after_two_week##SUM(effort_after_two_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.effort_after_three_week##SUM(effort_after_three_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.effort_after_four_week##SUM(effort_after_four_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.allocation_after_one_week##SUM(allocation_after_one_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.allocation_after_two_week##SUM(allocation_after_two_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.allocation_after_three_week##SUM(allocation_after_three_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.allocation_after_four_week##SUM(allocation_after_four_week)</Parameter>
                    <Parameter name="add_hit_meta" secured="false">finish_or_now</Parameter>
                    <Parameter name="add_hit_meta" secured="false">finished_on</Parameter>
                    <Parameter name="add_hit_meta" secured="false">name:description_summarized,index_field:document_description,summarize:true </Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="true">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                    <Parameter name="facets" secured="false" value="late_task##lateness##late"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="axWgGREn" id="effort" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:effort ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},effort_woy_effort</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">effort_woy_effort##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">effort_woy_effort##aggr.total_effort##SUM(effort_total_effort)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('effort', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.date_range.AdaptDataForRangeTrigger" enable="true">
                    <Parameter name="mode" secured="false" value="snapshot"></Parameter>
                    <Parameter name="param" secured="false" value="rangedate"></Parameter>
                    <Parameter name="snapDate" secured="false" value="history_datetime"></Parameter>
                    <Parameter name="intervalMode" secured="false" value="week"></Parameter>
                    <Parameter name="restriction" secured="false" value="history_type:snapshot"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="false">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true">
            <Parameter name="exceptFeeds" secured="false" value="effort"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project_history"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="history_weekofyear"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="assigned_percent##${facet.aggrs[&quot;assigned_resources&quot;] / feeds[&quot;project_history&quot;].facets[&quot;task_mgmt_assignee&quot;].leaves.length}##${category.aggrs[&quot;assigned_resources&quot;] / feeds[&quot;project_history&quot;].facets[&quot;task_mgmt_assignee&quot;].leaves.length}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="true">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="type"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="progression_after_one_week##${facet.aggrs[&quot;progression_after_one_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}##${facet.aggrs[&quot;progression_after_one_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="progression_after_two_week##${facet.aggrs[&quot;progression_after_two_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}##${facet.aggrs[&quot;progression_after_two_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="progression_after_three_week##${facet.aggrs[&quot;progression_after_three_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}##${facet.aggrs[&quot;progression_after_three_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="progression_after_four_week##${facet.aggrs[&quot;progression_after_four_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}##${facet.aggrs[&quot;progression_after_four_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##open_project"></Parameter>
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <Feed className="com.exalead.access.basefeeds.PageFeed" embed="true" enable="true" fuid="" id="resource_management" lastModifiedDate="1614325726772" synchronized="false">
        <Parameters>
            <Parameter name="startdate" secured="false">2015/01/01</Parameter>
            <Parameter name="endate" secured="false">2016/12/01</Parameter>
            <Parameter name="q" secured="false"></Parameter>
            <Parameter name="bo_type" secured="false"></Parameter>
        </Parameters>
        <Properties/>
        <SubFeeds>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="false" fuid="JSDZdsbw" id="project" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">hierarchical_types:task ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="restriction" secured="false">history_type:current</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},status_by_proj,status_by_org,status_by_own,status_by_assi,status_by_prog,completion_by_org,completion_by_own,completion_by_assi,completion_by_proj,completion_by_prog</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,task_age,finish_or_now,started_on,finished_on,vf_afd,vf_efd,vf_efd_present,vf_afd_present,vf_act_est_same_interval,real_duration,num_closure_days_project,on_time_project,due_soon_project,late_project,missed_project,actual_start_date_present,estimated_start_date_present,not_started_project,not_planned_project,lateness,remaining_days,ellapsed_days,progression_after_one_week,progression_after_two_week,progression_after_three_week,progression_after_four_week,remaining_days_first_week,remaining_days_second_week,remaining_days_third_week,remaining_days_fourth_week,effort_after_one_week,effort_after_two_week,effort_after_three_week,effort_after_four_week,allocation_after_one_week,allocation_after_two_week,allocation_after_three_week,allocation_after_four_week</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">project_percent_complete##aggr.avg##AVG(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion_by_duration##SUM(project_percent_complete*real_duration)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##type##dyndate</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##vroot##Top/est_finish_date</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##expr##vf_efd</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.est_count##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_finish##SUM(vf_act_est_same_interval ? 1 : 0 )</Parameter>
                    <Parameter name="f" secured="false">est_finish_date##aggr.act_remain##SUM(vf_act_est_same_interval ? 0 : 1 )</Parameter>
                    <Parameter name="f" secured="false">ts_transition_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">ts_state##sort##alphanum</Parameter>
                    <Parameter name="f" secured="false">status_by_proj##aggr.total_duration##SUM(real_duration)</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##vroot##Top/duration_by_org</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##0.id##project_related_organization</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_org##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##vroot##Top/duration_by_own</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##0.id##owner</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_own##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##vroot##Top/duration_by_assi</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##0.id##task_mgmt_assignee</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_assi##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##vroot##Top/duration_by_proj</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##0.id##related_project</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_proj##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##type##multi</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##vroot##Top/duration_by_prog</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##1.id##closure_days_custom</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##0.id##related_program</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##additional_tree_representation_ds##true</Parameter>
                    <Parameter name="f" secured="false">duration_by_prog##in_hits##false</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">project_related_organization##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">owner##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">task_mgmt_assignee##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.ontime##SUM(on_time_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.due##SUM(due_soon_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.late##SUM(late_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.missed##SUM(missed_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notstarted##SUM(not_started_project)</Parameter>
                    <Parameter name="f" secured="false">related_program##aggr.notplanned##SUM(not_planned_project)</Parameter>
                    <Parameter name="f" secured="false">late_task##type##num_explicit</Parameter>
                    <Parameter name="f" secured="false">late_task##vroot##Top/late_task</Parameter>
                    <Parameter name="f" secured="false">late_task##expr##lateness</Parameter>
                    <Parameter name="f" secured="false">late_task##range##1,1000000,late</Parameter>
                    <Parameter name="f" secured="false">late_task##range##-1000000,0,early</Parameter>
                    <Parameter name="f" secured="false">type##aggr.progression_after_one_week##SUM(progression_after_one_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.progression_after_two_week##SUM(progression_after_two_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.progression_after_three_week##SUM(progression_after_three_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.progression_after_four_week##SUM(progression_after_four_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.nb_task##SUM(1)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.effort_after_one_week##SUM(effort_after_one_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.effort_after_two_week##SUM(effort_after_two_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.effort_after_three_week##SUM(effort_after_three_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.effort_after_four_week##SUM(effort_after_four_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.allocation_after_one_week##SUM(allocation_after_one_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.allocation_after_two_week##SUM(allocation_after_two_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.allocation_after_three_week##SUM(allocation_after_three_week)</Parameter>
                    <Parameter name="f" secured="false">type##aggr.allocation_after_four_week##SUM(allocation_after_four_week)</Parameter>
                    <Parameter name="add_hit_meta" secured="false">finish_or_now</Parameter>
                    <Parameter name="add_hit_meta" secured="false">finished_on</Parameter>
                    <Parameter name="add_hit_meta" secured="false">name:description_summarized,index_field:document_description,summarize:true </Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('project', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="false">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                    <Parameter name="typeFilterNames" secured="false" value="typeRangeFilter"></Parameter>
                    <Parameter name="typeFilterNames" secured="false" value="project_actual_start_date"></Parameter>
                    <Parameter name="typeFilterNames" secured="false" value="project_actual_finish_date"></Parameter>
                    <Parameter name="typeFilterNames" secured="false" value="project_estimated_start_date"></Parameter>
                    <Parameter name="typeFilterNames" secured="false" value="project_estimated_finish_date"></Parameter>
                    <Parameter name="typeFilter" secured="false" value="program_originated##(originated &gt; &quot;${page.params['inputStartDate']}&quot; AND originated &lt; &quot;${page.params['inputEndDate']}&quot;)"></Parameter>
                    <Parameter name="typeFilter" secured="false" value="project_actual_start_date##(project_actual_start_date &gt; &quot;${page.params['startactstart']}&quot; AND project_actual_start_date &lt; &quot;${page.params['endactstart']}&quot;)"></Parameter>
                    <Parameter name="typeFilter" secured="false" value="project_actual_finish_date##(project_actual_finish_date &gt; &quot;${page.params['startactfinish']}&quot; AND project_actual_finish_date &lt; &quot;${page.params['endactfinish']}&quot;)"></Parameter>
                    <Parameter name="typeFilter" secured="false" value="project_estimated_start_date##(project_estimated_start_date &gt; &quot;${page.params['starteststart']}&quot; AND project_estimated_start_date &lt; &quot;${page.params['endeststart']}&quot;)"></Parameter>
                    <Parameter name="typeFilter" secured="false" value="project_estimated_finish_date##(project_estimated_finish_date &gt; &quot;${page.params['startestfinish']}&quot; AND project_estimated_finish_date &lt; &quot;${page.params['endestfinish']}&quot;)"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="false">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days_project##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.CleanFeedOnParameter" enable="true">
                    <Parameter name="parameterName" secured="false" value="cleanFeedParameter"></Parameter>
                    <Parameter name="overrideFacetsLimit" secured="false" value="true"></Parameter>
                </Trigger>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
            <Feed className="com.exalead.access.basefeeds.CloudviewFeed" embed="true" enable="true" fuid="vOdd1vyf" id="allocated" lastModifiedDate="-1" synchronized="false">
                <Parameters>
                    <Parameter name="q" secured="false">(type:Allocated OR type:(Resource Plan)) ${if page.params['q']} AND (${page.params['q']}) ${/if}</Parameter>
                    <Parameter name="per_page" secured="false">0</Parameter>
                    <Parameter name="l" secured="false">${page.params['lang']|'en'}</Parameter>
                    <Parameter name="key_prefix_handler" secured="false">rawurl</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_facets##${app:getProperty('project', 'base_facet_list')},resource_rel_fte,resource_fte_monthofyear,resource_skill,allocated_resource_resource_assignee,month_by_skill,skill_by_project,people_by_project</Parameter>
                    <Parameter name="additionalQueryParameters" secured="false">use_logic_virtual_fields##text_relevance,isAllocated,isRequest</Parameter>
                    <Parameter name="logic" secured="false">sl_project</Parameter>
                    <Parameter name="stagingMode" secured="false">false</Parameter>
                    <Parameter name="page" secured="false">1</Parameter>
                    <Parameter name="enableProxy" secured="false">false</Parameter>
                    <Parameter name="defaultQuery" secured="false">#all</Parameter>
                    <Parameter name="additionalDefaultQueryParameters" secured="false">limits##max_fetched_hits_per_slice:0</Parameter>
                    <Parameter name="command" secured="false">search-api</Parameter>
                    <Parameter name="apiConfig" secured="false">sapi0</Parameter>
                    <Parameter name="max_number_of_connections" secured="false">8</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##aggr.request##SUM(isRequest)</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##aggr.allocated##SUM(isAllocated)</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##sort##date</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##enable_month##true</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##enable_year##false</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##enable_quarter##false</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##enable_week##false</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##enable_day##false</Parameter>
                    <Parameter name="f" secured="false">resource_fte_monthofyear##month_fmt##%Y %b</Parameter>
                    <Parameter name="f" secured="false">month_by_skill##aggr.request##SUM(isRequest)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.fte##SUM(isAllocated)</Parameter>
                    <Parameter name="f" secured="false">related_project##aggr.completion##MAX(project_percent_complete)</Parameter>
                    <Parameter name="f" secured="false">skill_by_project##aggr.allocated##SUM(isAllocated)</Parameter>
                    <Parameter name="f" secured="false">people_by_project##aggr.allocated##SUM(isAllocated)</Parameter>
                    <Parameter name="f" secured="false">resource_skill##aggr.allocated##SUM(isAllocated)</Parameter>
                    <Parameter name="f" secured="false">resource_skill##aggr.request##SUM(isRequest)</Parameter>
                    <Parameter name="enableSecurity" secured="false">true</Parameter>
                </Parameters>
                <Properties>
                    <Property kind="TITLE" mappingType="PRE" name="TITLE">${entry.metas['title']!+h|entry.metas['url']!+h}</Property>
                    <Property kind="IMAGE" mappingType="PRE" name="IMAGE"></Property>
                    <Property kind="CONTENT" mappingType="PRE" name="CONTENT">${entry.metas['text']!+x}</Property>
                </Properties>
                <SubFeeds/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreFeedTrigger" enable="true">
                    <Parameter name="condition" secured="false" value="${if page.params[&quot;isRequestFromAjax&quot;] == &quot;false&quot;}&#10; true&#10;${elseif list:find('allocated', page.params[&quot;ajaxRequestFeeds&quot;]) == -1}&#10; true&#10;${else}&#10; false&#10;${/if}"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.ParameterBasedQueryRewriter" enable="true">
                    <Parameter name="simpleDateRefineParam" secured="false" value="date_facet_refine"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.createDynamicRangeFacet.CreateDynamicRangeFacetTrigger" enable="false">
                    <Parameter name="facets" secured="false" value="closure_days_custom##num_closure_days##weeks"></Parameter>
                </Trigger>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetFacetSortFromConfigTrigger" enable="true"/>
                <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.IgnoreSortParametersTrigger" enable="true"/>
                <WhiteListPatterns>
                    <Pattern>r</Pattern>
                    <Pattern>zr</Pattern>
                    <Pattern>cr</Pattern>
                    <Pattern>s</Pattern>
                    <Pattern>page</Pattern>
                </WhiteListPatterns>
            </Feed>
        </SubFeeds>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.SetAnalyticsFeedParameters" enable="true"/>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="false">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="related_project"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="completion##${facet.aggrs[&quot;completion_by_duration&quot;] / facet.aggrs[&quot;total_duration&quot;]}##${category.aggrs[&quot;completion_by_duration&quot;] / category.aggrs[&quot;total_duration&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="false">
            <Parameter name="targetFeedName" secured="false" value="project_history"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="history_weekofyear"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="assigned_percent##${facet.aggrs[&quot;assigned_resources&quot;] / feeds[&quot;project_history&quot;].facets[&quot;task_mgmt_assignee&quot;].leaves.length}##${category.aggrs[&quot;assigned_resources&quot;] / feeds[&quot;project_history&quot;].facets[&quot;task_mgmt_assignee&quot;].leaves.length}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.access.basetriggers.AggregationBuilderTrigger" enable="false">
            <Parameter name="targetFeedName" secured="false" value="project"></Parameter>
            <Parameter name="targetFacetName" secured="false" value="type"></Parameter>
            <Parameter name="facetIterationMode" secured="false" value="ALL"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="progression_after_one_week##${facet.aggrs[&quot;progression_after_one_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}##${facet.aggrs[&quot;progression_after_one_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="progression_after_two_week##${facet.aggrs[&quot;progression_after_two_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}##${facet.aggrs[&quot;progression_after_two_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="progression_after_three_week##${facet.aggrs[&quot;progression_after_three_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}##${facet.aggrs[&quot;progression_after_three_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}"></Parameter>
            <Parameter name="aggregationsToBuild" secured="false" value="progression_after_four_week##${facet.aggrs[&quot;progression_after_four_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}##${facet.aggrs[&quot;progression_after_four_week&quot;] / facet.aggrs[&quot;nb_task&quot;]}"></Parameter>
        </Trigger>
        <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.MakeApplicationRefineTrigger" enable="true">
            <Parameter name="facetList" secured="false" value="late_task##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="lateness##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project_history"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##project"></Parameter>
            <Parameter name="facetList" secured="false" value="closure_days_custom##CATEGORY##open_project"></Parameter>
            <Parameter name="loadDateFacetParameter" secured="false" value="cleanFeedParameter"></Parameter>
        </Trigger>
        <WhiteListPatterns/>
    </Feed>
    <CustomComponentList>
        <component>com.exalead.gwt.bconsole.recommend.access.RecommendationTrigger</component>
    </CustomComponentList>
    <ConcurrencyPolicies threadPoolSize="16"/>
    <Trigger className="com.exalead.apps.plma_tools_api.custom.mashup.feed.trigger.LimitFacetCategoriesTrigger" enable="true">
        <Parameter name="condition" secured="false" value="true"></Parameter>
        <Parameter name="limit" secured="false" value="10"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="history_weekofyear"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="week_current"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="assessment_assess_status"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="closure_days_custom"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="project_percent_complete"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="lateness"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="idleness_custom"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="openingdatedyn"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="resource_fte_monthofyear"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="hierarchical_types"></Parameter>
        <Parameter name="exceptFacets" secured="false" value="issue_to_reporting_organization"></Parameter>
        <Parameter name="exceptFacetsFromParam" secured="false" value="maxPerLevelParamFacetName"></Parameter>
    </Trigger>
    <Options>
        <Option name="reporterName" secured="false" value="mashup-api-reporting"></Option>
    </Options>
</AccessConfiguration>
