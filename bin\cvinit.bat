@echo off

set DATA=%~dp0..

for /F %%i in ('type "%DATA%\bin\ngstart.env"') do set %%i
title %NGHOSTNAME%:%NGBASEPORT%

set MERCURYBIN=%NGINSTALLDIR%\amd64-win64\bin\mercury.bin

if "x%SECURE%" == "x1" (
    set SSL=s
    set SSLOPT=--ssl
)
set NGHOSTAGENTURL=http%SSL%://%NGHOSTNAME%:%NGHOSTAGENTPORT%

if "x%1%"=="x" goto error
:again
if "x%1%"=="x" goto end
if "%1%"=="start" goto start
if "%1%"=="wait-started" goto waitstarted
if "%1%"=="stop" goto stop
if "%1%"=="restart" goto restart
if "%1%"=="status" goto status
if "%1%"=="detach" goto detach
if "%1%"=="attach" goto attach
if "%1%"=="check-indexes-up" goto checkindexesup

if "%1%"=="set-alive" goto setalive
if "%1%"=="set-not-alive" goto setnotalive
if "%1%"=="is-alive" goto isalive
goto usage
:endcommand
shift
goto again

:start
echo ** Starting CloudView ...
"%NGINSTALLDIR%\tools\java-install.exe" "com.exalead.cloudview.admintools.RebuildDataDirFromInstallDir" --ifVersionChange "%DATA%" "%NGINSTALLDIR%"
if ERRORLEVEL 1 goto error

"%DATA%\bin\exa.exe" -bin "%MERCURYBIN%" com.exalead.mercury.util.waitForLockRelease "%DATA%\run\hostagent\daemon.pid" "10"
if ERRORLEVEL 1 goto starterror2
 
"%DATA%\bin\exa.exe" -bin "%MERCURYBIN%" com.exalead.mercury.util.sanityCheck
if not ERRORLEVEL 0 goto starterror
"%DATA%\bin\cv.exe"
if not ERRORLEVEL 0 goto starterror
rem Under windows, we can only launch on foreground.
echo ** CloudView stopped successfully.
goto endcommand
:starterror
echo ** CloudView failed to start.
goto error
:starterror2
echo ** An instance is already running on %NGHOSTNAME%:%NGBASEPORT%
goto error

:stop
echo ** Stopping CloudView ...
"%DATA%\bin\cvcommand.exe" :%NGHOSTAGENTPORT% /ken StopRuntime
"%DATA%\bin\exa.exe" -bin "%MERCURYBIN%" com.exalead.mercury.util.waitForLockRelease "%DATA%\run\hostagent\daemon.pid" "10"
if not ERRORLEVEL 0 goto stoperror
echo ** CloudView stopped
goto endcommand
:stoperror
echo ** CloudView failed to stop.
goto error

:waitstarted
"%DATA%\bin\java.exe" com.exalead.cloudview.admintools.WaitProcessesStarted %NGHOSTAGENTURL% 600
if not ERRORLEVEL 0 goto waitstartederror
goto end
:waitstartederror
echo ** Failed to wait for processes to be started
goto error 

:restart
echo ** Stopping CloudView ...
"%DATA%\bin\cvcommand.exe" %SSLOPT% %NGHOSTNAME%:%NGHOSTAGENTPORT% /ken StopRuntime
if not ERRORLEVEL 0 goto stoperror
echo ** CloudView has been successfully stopped.
echo ** Starting CloudView ...
"%DATA%\bin\cv.exe"
if not ERRORLEVEL 0 goto starterror
echo ** CloudView stopped successfully.
goto endcommand

:status
"%DATA%\bin\cvcommand.exe" %SSLOPT% %NGHOSTNAME%:%NGHOSTAGENTPORT% /ken ListAllProcessesText
if not ERRORLEVEL 0 goto statuserror
goto endcommand
:statuserror
echo ** Failed to get CloudView status.
goto error

:detach
echo "" > "%DATA%\detached_from_master"
if not ERRORLEVEL 0 goto detacherror
goto endcommand
:detacherror
echo ** Failed to detach from master
goto error

:attach
del "%DATA%\detached_from_master"
if not ERRORLEVEL 0 goto detacherror
goto endcommand
:detacherror
echo ** Failed to detach from master
goto error

:checkindexesup
"%DATA%\bin\java.exe" com.exalead.cloudview.admintools.IndexStatusChecker %NGGATEWAYHOSTPORT% %*
if not ERRORLEVEL 0 goto checkindexesuperror
goto end
:checkindexesuperror
echo ** Indexes not all up, or error
goto error



:setalive
"%DATA%\bin\java.exe" com.exalead.cloudview.admintools.HostAliveControl set-alive
if not ERRORLEVEL 0 goto setaliveerror
goto end
:setaliveerror
echo ** Error
goto error

:setnotalive
"%DATA%\bin\java.exe" com.exalead.cloudview.admintools.HostAliveControl set-not-alive
if not ERRORLEVEL 0 goto setnotaliveerror
goto end
:setnotaliveerror
echo ** Error
goto error

:isalive
"%DATA%\bin\java.exe" com.exalead.cloudview.admintools.HostAliveControl is-alive
if not ERRORLEVEL 0 goto isaliveerror
goto end
:isaliveerror
echo ** Error
goto error

:usage
  echo "Usage : cvinit.bat COMMAND" 
  echo "  - start:         Start CloudView" 
  echo "  - stop:          Stop CloudView" 
  echo "  - restart:       Restart CloudView" 
  echo "  - status:        Get the status of the CloudView processes" 
  echo "  - wait-started:  Waits until all CloudView processes are started" 
  echo "                   Return code is 1 if not all processes could be started" 
  echo ""  
  echo "  - detach:       Detach this CloudView host from its master, so that it stops receiving configuration updates" 
  echo "  - attach:       Attach this CloudView host to its master, so that it receives configuration updates" 
  echo "" 
  echo "  - check-indexes-up [global]:" 
  echo "                      Checks that all indexes on this host are up and ready to answer queries." 
  echo "                      Return code is 0 if all indexes are up, 1 else" 
  echo "                      If 'global' argument is given, checks all indexes on all hosts" 
  echo "" 
  echo "  - set-not-alive:    Forces all 'isAlive' health monitors on this host as down (to be used with a loadbalancer)" 
  echo "  - set-alive:        Don't force all 'isAlive' health monitors on this host as down (to be used with a loadbalancer)" 
  echo "  - is-alive:         Returns whether the 'isAlive' health monitors have been forced as down"

:error
:end
