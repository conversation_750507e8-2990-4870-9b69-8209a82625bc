#
# CloudView Plugin Manifest
# Generated on 2025/07/08 15:15:10
#

cloudview.version=CV25.119602
plugin.author=EXALEAD PLM Analytics R&D
plugin.components.consobox=com.exalead.consobox.aggregation.FetchAggProcessor,com.exalead.consobox.transformation.AddArcTransformationProcessor,com.exalead.consobox.transformation.RegexpToStringTransformationProcessor,com.exalead.apps.plma_tools.core.consobox.SortMetaAggregationProcessor,com.exalead.consobox.transformation.DeleteArcTransformationProcessor,com.exalead.consobox.aggregation.DocTimestampAggregationProcessor,com.exalead.consobox.aggregation.NumericalAggregationTraverser,com.exalead.consobox.transformation.HierarchicalTypesSetter,com.exalead.apps.plma_tools.core.consobox.resource.ResourceFTETransformationProcessor,com.exalead.cloudview.consolidationapi.processors.java.IJavaDeleteAggregationProcessor,com.exalead.apps.plma_tools.core.consobox.transformation.BizDaysTransformationProcessor,com.exalead.consobox.aggregation.HistoricalAggregationProcessor,com.exalead.consobox.aggregation.TraverserAggregationProcessor,com.exalead.apps.plma_tools.core.consobox.DeleteChildDocumentAggregationProcessor,com.exalead.consobox.aggregation.CategoriesCleanAggregationProcessor,com.exalead.consobox.transformation.CategoriesCleanTransformationProcessor,com.exalead.consobox.transformation.DocTimestampTransformationProcessor,com.exalead.apps.plma_tools.core.consobox.MetaValueMappingAggregationProcessor,com.exalead.consobox.aggregation.AggregateMetasProcessor,com.exalead.consobox.aggregation.ValueSelectorAggregationProcessor,com.exalead.apps.plma_tools.core.consobox.enovia.HistoryAggregationProcessor,com.exalead.consobox.transformation.MetaMappingsTransformationProcessor,com.exalead.consobox.aggregation.RegexpToStringAggregationProcessor,com.exalead.consobox.commons.CustomTransformationProcessor,com.exalead.consobox.transformation.DiscardTransformationProcessor,com.exalead.consobox.aggregation.PathToCategoriesAggProcessor,com.exalead.consobox.aggregation.DiscardAggregationProcessor,com.exalead.consobox.transformation.DeleteMetasTransformationProcessor,com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesAggregationProcessor,com.exalead.consobox.aggregation.HistoryAggregationProcessor,com.exalead.consobox.transformation.ConditionalTypeSetterTransformationProcessor,com.exalead.consobox.transformation.ValueSelectorTransformationProcessor,com.exalead.consobox.transformation.CreateMetasOrDirectivesTransformationProcessor,com.exalead.apps.plma_tools.core.consobox.HistoricalStatesAggregationProcessor,com.exalead.consobox.commons.CustomDeleteAggProcessor,com.exalead.apps.plma_tools.core.consobox.NumericalAggregationProcessor,com.exalead.apps.plma_tools.core.consobox.enovia.HistoryTransformationProcessor,com.exalead.cloudview.consolidationapi.processors.java.IJavaAllUpdatesTransformationProcessor,com.exalead.consobox.aggregation.CreateMetasOrDirectivesAggregationProcessor,com.exalead.consobox.commons.CustomAggregationProcessor
plugin.components.diagnostic=com.exalead.tools.diagnostics.modules.AbstractDiagnosticsModule,com.exalead.tools.diagnostics.modules.UnusedRamFields,com.exalead.tools.diagnostics.modules.ConsolidationProcsConfigModule,com.exalead.tools.diagnostics.modules.IndexingConfigModule,com.exalead.tools.diagnostics.modules.DataModelsCheckModule,com.exalead.tools.diagnostics.modules.DataModelsOptionsModule,com.exalead.tools.diagnostics.modules.ConsolidationTriggersModule,com.exalead.tools.diagnostics.modules.AccessPagesComplexity,com.exalead.tools.diagnostics.modules.RamUsageModule,com.exalead.tools.diagnostics.modules.ConsolidationMatchingRulesModule,com.exalead.tools.diagnostics.modules.ERConnectorConfigModule,com.exalead.tools.diagnostics.modules.ReportingConfigModule,com.exalead.tools.diagnostics.modules.ConsolidationMetasAggPathsModule,com.exalead.tools.diagnostics.modules.ConnectorsConfigModule
plugin.components.documentProcessor=com.exalead.analysis.date.WeekOfYearDocumentProcessor,com.exalead.analysis.date.SimpleDateFormaterDocProc,com.exalead.analysis.date.JodaSimpleDateFormaterDocProc
plugin.components.pushAPIFilter=com.exalead.papifilter.TimestampPAPIFilter
plugin.copyright=Dassault Systemes
plugin.description=Custom CV core components (Document processors,Connectors...)[Compiled 2025/07/08 15:15:10]
plugin.name=plma-tools-core
plugin.processes.restart=connector,indexingserver,consobox,diagnostic
plugin.reverse-classloading=true
plugin.type=core
plugin.version=427.5.0-SNAPSHOT
